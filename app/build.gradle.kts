import com.android.build.gradle.internal.dsl.SigningConfig
import java.util.*

plugins {
    id("com.android.application")
    id("io.sentry.android.gradle") version "5.9.0"
    id("com.google.gms.google-services")
    id("com.google.firebase.crashlytics")
    kotlin("android")
    kotlin("kapt")
}

buildscript {
    repositories {
        google()
        mavenCentral()
        maven {
            url = uri("https://maven.aliyun.com/repository/jcenter")
        }
    }
}

repositories {
    google()
    mavenCentral()
    maven {
        url = uri("https://jitpack.io")
    }
    maven {
        url = uri("https://maven.aliyun.com/repository/jcenter")
    }
}

fun createSigningConfig(name: String, keyFileName: String): SigningConfig {
    val userHome = System.getProperty("user.home", "~")
    val keyFilePath = "$userHome/.signing/$keyFileName"

    android.signingConfigs.create(name) {
        val props = Properties()
        props.load(File(keyFilePath).inputStream())
        val keyPrefix = "wdbook"
        this.storeFile = File("$userHome/${props["$keyPrefix.storeFile"]}")
        this.storePassword = "${props["$keyPrefix.storePassword"]}"
        this.keyAlias = "${props["$keyPrefix.keyAlias"]}"
        this.keyPassword = "${props["$keyPrefix.keyPassword"]}"
    }
    return android.signingConfigs.getByName(name)
}

android {
    compileSdk = Versions.COMPILE_SDK
    buildToolsVersion = Versions.BUILD_TOOL
    namespace = "com.wedevote.wdbook"

    defaultConfig {
        minSdk = Versions.MIN_SDK
        targetSdk = Versions.TARGET_SDK
        versionCode = 44
        versionName = "3.8"

        ndk {
            abiFilters.add("armeabi-v7a")
            abiFilters.add("arm64-v8a")
        }
    }

    flavorDimensions += listOf("app", "store")
    productFlavors {
        create("wdbooktest") {
            dimension = "app"
            applicationId = "com.wedevote.test.wdbook"
            isDefault = true
            signingConfig = createSigningConfig(name, "wdbook.properties")
        }
        create("wdbookinner") {
            dimension = "app"
            applicationId = "com.wedevote.inner.wdbook"
            signingConfig = createSigningConfig(name, "wdbook-prod.properties")
        }
        create("wdbookprod") {
            dimension = "app"
            applicationId = "com.wedevote.wdbook"
            signingConfig = createSigningConfig(name, "wdbook-prod.properties")
        }
        create("GooglePlay") {
            dimension = "store"
            manifestPlaceholders["desc"] = "Google Play"
        }
        create("Website") {
            dimension = "store"
            manifestPlaceholders["desc"] = "Website"
        }
    }

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_17.toString()
    }

    sourceSets["main"].apply {
        java.srcDir("src/main/java")
        jniLibs.srcDirs("src/main/libs")
        res.srcDirs(
            "src/main/res",

            "src/main/res/version3_0",

            "src/main/res/drawable",
            "src/main/res/drawable/selector",
            "src/main/res/drawable/shape",
            "src/main/res/drawable/layerlist",
            "src/main/res/drawable/icons",

            "src/main/res/layout",
            "src/main/res/layout/mine",
            "src/main/res/layout/widgets",
            "src/main/res/layout/coupon",
            "src/main/res/layout/shelf",
            "src/main/res/layout/store",
            "src/main/res/layout/microWidget",
            "src/main/res/layout/dialog",
            "src/main/res/layout/account",
            "src/main/res/layout/notification",
            "src/main/res/layout/cart",
            "src/main/res/layout/read"
        )
    }

    buildTypes {
        getByName("release") {
            proguardFiles(getDefaultProguardFile("proguard-android-optimize.txt"), "proguard-rules.pro")
            isMinifyEnabled = true
            applicationVariants.all {
                this.outputs.map { it as com.android.build.gradle.internal.api.BaseVariantOutputImpl }
                    .forEach { output ->
                        val flavor = this.productFlavors.first { it.dimension.equals("store") }?.name
                        output.outputFileName = "app-${flavor}-${this.buildType.name}.apk"
                    }
            }
        }
        getByName("debug") {
            proguardFiles(getDefaultProguardFile("proguard-android-optimize.txt"), "proguard-rules.pro")
            isMinifyEnabled = false
        }
    }

    buildFeatures {
        buildConfig = true
    }
}

val appFlavors = android.productFlavors.matching { it.dimension == "app" }.map { it.name }
val storeFlavors = android.productFlavors.matching { it.dimension == "store" }.map { it.name }

fun capitalize(value: String): String {
    return value.replaceFirstChar { if (it.isLowerCase()) it.titlecase(Locale.getDefault()) else it.toString() }
}

// Function to create custom tasks dynamically
fun createBuildTask(appFlavor: String, buildType: String) {
    val taskName = "assemble${capitalize(appFlavor)}${capitalize(buildType)}"
    tasks.register(taskName) {
        storeFlavors.forEach { storeFlavor ->
            dependsOn("assemble${capitalize(appFlavor)}${capitalize(storeFlavor)}${capitalize(buildType)}")
        }
    }
}

// Create custom tasks for each app flavor and build type
appFlavors.forEach { appFlavor ->
    createBuildTask(appFlavor, "release")
}

dependencies {
    api(fileTree(mapOf("dir" to "libs", "include" to listOf("*.jar", "*.aar"))))
    implementation(Dependencies.Kotlin.STDLIB)
    implementation(Dependencies.AndroidX.CORE)
    implementation(Dependencies.AndroidX.APP_COMPAT)
    implementation(Dependencies.AndroidX.CONSTRAINT_LAYOUT)
    implementation(Dependencies.AndroidX.RECYCLER_VIEW)
    /*Eventbus 资源lib*/
    implementation(Dependencies.EVENT_BUS)
    implementation(Dependencies.AndroidX.CARD_VIEW)
    implementation(Dependencies.VIEW_PAGER_2)
    implementation(Dependencies.GSON)
    // Coroutine
    implementation(Dependencies.Kotlin.COROUTINES_CORE)
    implementation(Dependencies.Kotlin.COROUTINES_ANDROID)

    implementation(project(":libbase"))
    implementation(project(":liblogger"))
    implementation(project(":libtools"))
    implementation(project(":libwidget"))
    implementation(project(":libdialogs"))
    implementation(project(":libdownloader"))
    implementation(project(":librefresh"))

    implementation(project(":shared"))

    /*Stripe支付的lib*/
    api("com.stripe:stripe-android:16.0.0")
    implementation("com.facebook.stetho:stetho:1.6.0")
    implementation("com.facebook.stetho:stetho-okhttp3:1.6.0")
    implementation("com.squareup.okhttp3:logging-interceptor:4.12.0")

    /*Paypal支付的SDK*/
    implementation("com.braintreepayments.api:paypal:5.1.0")

    implementation("com.google.android.flexbox:flexbox:3.0.0")

    // Required
    // implementation project(':payment')
    implementation("com.github.yuansfer:yuansfer-payment-android:1.3.2")

    /*侧滑删除的控件*/
    implementation("com.chauthai.swipereveallayout:swipe-reveal-layout:1.4.1")
    implementation("androidx.webkit:webkit:1.12.1")

    kapt("android.arch.persistence.room:compiler:1.1.1")

    // 轮播图
    implementation("com.youth.banner:banner:2.1.0")
    /*流利说的下载工具*/
    implementation("com.liulishuo.filedownloader:library:1.7.7")

    // 基础依赖包，必须要依赖
    implementation("com.geyifeng.immersionbar:immersionbar:3.2.2")
    // Kotlin 扩展（可选）
    implementation("com.geyifeng.immersionbar:immersionbar-ktx:3.2.2")
    // Fragment 快速实现（可选）已废弃
    implementation("com.geyifeng.immersionbar:immersionbar-components:3.2.2")

    // Glide
    implementation(Dependencies.GLIDE)
    kapt(Dependencies.GLIDE_COMPILER)
    implementation(Dependencies.GLIDE_TRANSFORMATIONS)

    // 网络状态监听
    implementation("com.sunchen:netstatusbus:0.1.5")

    // 滑动选择器
    implementation("com.contrarywind:Android-PickerView:4.1.9")

    // Amazon AWS Pinpoint
    implementation("com.amazonaws:aws-android-sdk-core:2.75.0")
    implementation("com.amazonaws:aws-android-sdk-pinpoint:2.75.0")

    // 扫描二维码
    implementation(Dependencies.ZXING_CORE)

    // Microsoft clarity
    implementation(Dependencies.CLARITY)

    // Firebase
    implementation(platform(Dependencies.Firebase.bom))
    implementation(Dependencies.Firebase.analytics)
    implementation(Dependencies.Firebase.crashlytics)

    /*******测试相关***********************************************(****************/
    // Core library
    androidTestImplementation(Dependencies.AndroidXTest.CORE)
    // AndroidJUnitRunner and JUnit Rules
    androidTestImplementation(Dependencies.AndroidXTest.RUNNER)
    androidTestImplementation(Dependencies.AndroidXTest.RULES)

    // Assertions
    androidTestImplementation("androidx.test.ext:junit:1.2.1")
    androidTestImplementation("androidx.test.ext:truth:1.6.0")
    androidTestImplementation("com.google.truth:truth:1.0")

    // Espresso dependencies
    androidTestImplementation("androidx.test.espresso:espresso-core:3.6.1")
    androidTestImplementation("androidx.test.espresso:espresso-contrib:3.6.1")
    androidTestImplementation("androidx.test.espresso:espresso-intents:3.6.1")
    androidTestImplementation("androidx.test.espresso:espresso-accessibility:3.6.1")
    androidTestImplementation("androidx.test.espresso:espresso-web:3.6.1")
    androidTestImplementation("androidx.test.espresso.idling:idling-concurrent:3.6.1")
    androidTestImplementation("androidx.test.espresso:espresso-idling-resource:3.6.1")
}
