# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}

# Uncomment this to preserve the line number information for
# debugging stack traces.
#-keepattributes SourceFile,LineNumberTable

# If you keep the line number information, uncomment this to
# hide the original source file name.
#-renamesourcefileattribute SourceFile

-keep public class * extends android.app.Activity
-keep public class * extends android.app.Application
-keep public class * extends android.app.Service
-keep public class * extends android.content.BroadcastReceiver
-keep public class * extends android.content.ContentProvider
-keep public class * extends java.lang.Throwable {*;}
-keep public class * extends java.lang.Exception {*;}

-keep class com.aquila.lib.api.** { *; }
-keep class com.wedevote.wdbook.entity.** { *; }
-keep class com.wedevote.wdbook.network.** { *; }
-keep class com.wedevote.wdbook.constants.** { *; }
-keep class com.aquila.lib.download.** { *; }
-keep class com.wedevote.wdbook.ui.read.BibleVerseEntity { *; }

# Retain generic signatures of TypeToken and its subclasses with R8 version 3.0 and higher.
-keep,allowobfuscation,allowshrinking class com.google.gson.reflect.TypeToken
-keep,allowobfuscation,allowshrinking class * extends com.google.gson.reflect.TypeToken

#***********clarity*******************************************************************************************************************************************
-keep class com.microsoft.clarity.** { *; }

#***********pinpoint*******************************************************************************************************************************************
-keep class com.amazonaws.** { *; }

#***********Stripe模块*******************************************************************************************************************************************
# Rules for IconTextInputLayout
-keep class com.google.android.material.textfield.TextInputLayout { *; }
-keep class com.google.android.material.internal.CollapsingTextHelper { *; }

# Rules for BouncyCastle
-keep class org.bouncycastle.jcajce.provider.** { *; }
-keep class !org.bouncycastle.jce.provider.X509LDAPCertStoreSpi,org.bouncycastle.jce.provider.** { *; }

-keep class com.stripe.android.** { *; }
-dontwarn com.stripe.android.view.**

# Rules for Kotlin Coroutines
# https://github.com/Kotlin/kotlinx.coroutines/blob/master/ui/kotlinx-coroutines-android/example-app/app/proguard-rules.pro
-keepnames class kotlinx.coroutines.internal.MainDispatcherFactory {}
-keepnames class kotlinx.coroutines.CoroutineExceptionHandler {}
-keepnames class kotlinx.coroutines.android.AndroidExceptionPreHandler {}
-keepnames class kotlinx.coroutines.android.AndroidDispatcherFactory {}

-keepclassmembernames class kotlinx.** {
    volatile <fields>;
}
#***************Stripe模块***************************************************************************************************************************************

#***************AliPay模块***************************************************************************************************************************************

-keep class com.alipay.android.app.IAlixPay{*;}
-keep class com.alipay.android.app.IAlixPay$Stub{*;}
-keep class com.alipay.android.app.IRemoteServiceCallback{*;}
-keep class com.alipay.android.app.IRemoteServiceCallback$Stub{*;}
-keep class com.alipay.sdk.app.PayTask{ public *;}
-keep class com.alipay.sdk.app.AuthTask{ public *;}
-keep class com.alipay.sdk.app.H5PayCallback {
    <fields>;
    <methods>;
}
#***************AliPay模块***************************************************************************************************************************************

#***************腾信WCDB 模块*************************************************************************************************

-keep class com.tencent.wcdb.** { *; }

#***************流利说下载工具的混淆*************************************************************************************************************
-dontwarn com.liulishuo.okdownload.**
-keep class com.liulishuo.okdownload.**{*;}
-dontwarn edu.umd.cs.**
-keep class edu.umd.cs.**{*;}

#***************EventBus混淆*************************************************************************************************************

-keepattributes *Annotation*
-keepclassmembers class ** {
    @org.greenrobot.eventbus.Subscribe <methods>;
}
-keep enum org.greenrobot.eventbus.ThreadMode { *; }

# Only required if you use AsyncExecutor
-keepclassmembers class * extends org.greenrobot.eventbus.util.ThrowableFailureEvent {
    <init>(java.lang.Throwable);
}

#***************EventBus混淆*************************************************************************************************************

-keepnames class com.wedevote.wdbook.MyAppGlideModule
-keep public class * implements com.bumptech.glide.module.GlideModule
-keep class * extends com.bumptech.glide.module.AppGlideModule {
 <init>(...);
}

-dontwarn com.braintreepayments.api.dropin.DropInResult
-dontwarn com.cardinalcommerce.cardinalmobilesdk.Cardinal
-dontwarn com.cardinalcommerce.cardinalmobilesdk.models.CardinalActionCode
-dontwarn com.cardinalcommerce.cardinalmobilesdk.models.ValidateResponse
-dontwarn com.cardinalcommerce.cardinalmobilesdk.services.CardinalValidateReceiver
-dontwarn com.tencent.mm.opensdk.openapi.IWXAPI
-dontwarn com.tencent.mm.opensdk.openapi.IWXAPIEventHandler
-dontwarn javax.naming.NamingEnumeration
-dontwarn javax.naming.NamingException
-dontwarn javax.naming.directory.Attribute
-dontwarn javax.naming.directory.Attributes
-dontwarn javax.naming.directory.DirContext
-dontwarn javax.naming.directory.InitialDirContext
-dontwarn javax.naming.directory.SearchControls
-dontwarn javax.naming.directory.SearchResult
-dontwarn org.bouncycastle.jsse.BCSSLParameters
-dontwarn org.bouncycastle.jsse.BCSSLSocket
-dontwarn org.bouncycastle.jsse.provider.BouncyCastleJsseProvider
-dontwarn org.conscrypt.Conscrypt$Version
-dontwarn org.conscrypt.Conscrypt
-dontwarn org.conscrypt.ConscryptHostnameVerifier
-dontwarn org.openjsse.javax.net.ssl.SSLParameters
-dontwarn org.openjsse.javax.net.ssl.SSLSocket
-dontwarn org.openjsse.net.ssl.OpenJSSE
-dontwarn org.slf4j.impl.StaticLoggerBinder
-dontwarn org.slf4j.impl.StaticMDCBinder


#***************Paypal混淆*************************************************************************************************************
-dontwarn com.braintreepayments.api.exceptions.BraintreeError
-dontwarn com.braintreepayments.api.exceptions.ErrorWithResponse
-dontwarn com.braintreepayments.api.exceptions.GooglePaymentException
-dontwarn com.braintreepayments.api.interfaces.BraintreeCancelListener
-dontwarn com.braintreepayments.api.interfaces.BraintreeErrorListener
-dontwarn com.braintreepayments.api.interfaces.BraintreePaymentResultListener
-dontwarn com.braintreepayments.api.interfaces.ConfigurationListener
-dontwarn com.braintreepayments.api.interfaces.PaymentMethodNonceCreatedListener
-dontwarn com.braintreepayments.api.models.CardNonce
-dontwarn com.braintreepayments.api.models.GooglePaymentCardNonce
-dontwarn com.braintreepayments.api.models.LocalPaymentResult
-dontwarn com.braintreepayments.api.models.PayPalAccountNonce
-dontwarn com.braintreepayments.api.models.PaymentMethodNonce
-dontwarn com.braintreepayments.api.models.VenmoAccountNonce
-dontwarn com.braintreepayments.api.models.VisaCheckoutNonce