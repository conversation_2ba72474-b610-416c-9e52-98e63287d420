<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-feature
        android:name="android.hardware.camera"
        android:required="false" />

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_MULTICAST_STATE" />
    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
    <!-- Android7.0以上安装APK的权限 -->
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />

    <queries>
        <intent>
            <action android:name="android.intent.action.SENDTO" />
            <data android:scheme="mailto" />
        </intent>

        <intent>
            <action android:name="android.intent.action.VIEW" />
            <data android:scheme="http" />
        </intent>

        <intent>
            <action android:name="android.intent.action.VIEW" />
            <data android:scheme="https" />
        </intent>

        <intent>
            <action android:name="android.intent.action.VIEW" />
            <data android:scheme="smsto" />
        </intent>

        <package android:name="com.tencent.mm" />
        <package android:name="com.tencent.mm.opensdk" />
        <package android:name="com.tencent.mobileqq" />
        <package android:name="com.qzone" />
        <package android:name="com.aquila.bible.test" />
        <package android:name="com.aquila.bible" />
        <package android:name="com.wedevote" />
        <package android:name="com.wedevote.test" />
    </queries>

    <application
        android:name=".base.APP"
        android:allowBackup="true"
        android:hardwareAccelerated="false"
        android:icon="@mipmap/ic_logo"
        android:label="@string/app_name"
        android:largeHeap="true"
        android:maxAspectRatio="2.4"
        android:networkSecurityConfig="@xml/network_security_config"
        android:requestLegacyExternalStorage="true"
        android:resizeableActivity="true"
        android:roundIcon="@mipmap/ic_logo_round"
        android:supportsRtl="true"
        android:theme="@style/AppTheme.light"
        tools:ignore="LockedOrientationActivity"
        tools:replace="android:supportsRtl">

        <meta-data
            android:name="android.max_aspect"
            android:value="2.4" />
        <!--适配华为（huawei）刘海屏-->
        <meta-data
            android:name="android.notch_support"
            android:value="true" />
        <!--适配小米（xiaomi）刘海屏-->
        <meta-data
            android:name="notch.config"
            android:value="portrait|landscape" />

        <meta-data
            android:name="com.wedevote.wdbook.MyAppGlideModule"
            android:value="AppGlideModule" />

        <meta-data
            android:name="io.sentry.dsn"
            android:value="https://<EMAIL>/10" />

        <!-- enable automatic breadcrumbs for user interactions (clicks, swipes, scrolls) -->
        <meta-data
            android:name="io.sentry.traces.user-interaction.enable"
            android:value="true" />
        <!-- enable screenshot for crashes -->
        <meta-data
            android:name="io.sentry.attach-screenshot"
            android:value="true" />
        <!-- enable view hierarchy for crashes -->
        <meta-data
            android:name="io.sentry.attach-view-hierarchy"
            android:value="true" />

        <!-- enable the performance API by setting a sample-rate, adjust in production env -->
        <meta-data
            android:name="io.sentry.traces.sample-rate"
            android:value="1.0" />

        <!-- 这个是为了解决Android7.0以上的升级自动安装的问题 -->
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileprovider"
            android:grantUriPermissions="true"
            tools:replace="name,authorities,grantUriPermissions">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths"
                tools:replace="name,resource" />
        </provider>

        <!--APP的首页-->
        <activity
            android:name=".ui.home.HomeMainActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />

        <!--Launcher的Activity-->
        <activity
            android:name=".base.SplashActivity"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>

        </activity>

        <!--Paypal支付的设置-->
        <activity
            android:name="com.braintreepayments.api.BraintreeBrowserSwitchActivity"
            android:exported="true"
            android:launchMode="singleTask">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="${applicationId}.braintree" />
            </intent-filter>
        </activity>

        <!--deepLink调试用-->
        <activity
            android:name=".base.TestDeeplinkActivity"
            android:screenOrientation="portrait" />

        <!--购物车购买成功后结果页-->
        <activity
            android:name=".ui.user.cart.CartBuySuccessActivity"
            android:screenOrientation="portrait" />

        <!--购物车列表页-->
        <activity
            android:name=".ui.user.cart.ShoppingCartActivity"
            android:screenOrientation="portrait" />

        <!--活动列表页-->
        <activity
            android:name=".ui.store.ProductActiveActivity"
            android:screenOrientation="portrait" />

        <!--订单确认页-->
        <activity
            android:name=".ui.store.OrderConfirmActivity"
            android:exported="true"
            android:hardwareAccelerated="true"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden|adjustPan">

            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="paypalpay"
                    android:scheme="${applicationId}" />
            </intent-filter>
        </activity>

        <!--优惠券详情页-->
        <activity
            android:name=".ui.user.coupon.CouponDetailActivity"
            android:screenOrientation="portrait" />

        <!--语言设置与选择的UI-->
        <activity
            android:name=".ui.user.LanguageSelectActivity"
            android:screenOrientation="portrait" />

        <!--优惠券首页的UI-->
        <activity
            android:name=".ui.user.CouponCenterActivity"
            android:screenOrientation="portrait" />

        <!--帮助中心的UI-->
        <activity
            android:name=".ui.user.feedback.HelpCenterActivity"
            android:screenOrientation="portrait" />

        <!--通知消息查看详情的布局-->
        <activity
            android:name=".ui.user.notification.NotificationDetailActivity"
            android:screenOrientation="portrait" />

        <!--通知类型的列表布局-->
        <activity
            android:name=".ui.user.notification.NotificationTypeListActivity"
            android:screenOrientation="portrait" />

        <!--消息设置的页面-->
        <activity
            android:name=".ui.user.notification.NotificationSettingActivity"
            android:screenOrientation="portrait" />

        <!--通知中心的UI-->
        <activity
            android:name=".ui.user.notification.NotificationCenterActivity"
            android:screenOrientation="portrait" />

        <!--帮助中心的文章详情-->
        <activity
            android:name=".ui.user.feedback.FaqArticleDetailActivity"
            android:screenOrientation="portrait" />

        <!--书籍下载专用的UI-->
        <activity
            android:name=".ui.user.BookDownloadActivity"
            android:screenOrientation="portrait"
            android:theme="@style/activity_transfer" />

        <!--意见反馈-->
        <activity
            android:name=".ui.user.feedback.EditFeedbackActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize" />

        <!--我的反馈列表-->
        <activity
            android:name=".ui.user.feedback.UserFeedbackListActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />

        <!--反馈详情的UI-->
        <activity
            android:name=".ui.user.feedback.FeedbackDetailActivity"
            android:screenOrientation="portrait" />

        <!--首页点击栏目的跳转界面-->
        <activity
            android:name=".ui.home.BookSectionListActivity"
            android:screenOrientation="portrait" />

        <!--设备管理的页面-->
        <activity
            android:name=".ui.account.DeviceManagerActivity"
            android:screenOrientation="portrait" />

        <!--成功注销账号后的UI-->
        <activity
            android:name=".ui.user.DeleteAccountResultActivity"
            android:screenOrientation="portrait" />

        <!--注销账号的UI-->
        <activity
            android:name=".ui.user.DeleteAccountActivity"
            android:screenOrientation="portrait" />

        <!--设备超出限制的UI-->
        <activity
            android:name=".ui.account.ShowDeviceDisableDialogActivity"
            android:theme="@style/activity_transfer" />

        <!--我的收藏列表页面-->
        <activity
            android:name=".ui.user.FavoriteBookListActivity"
            android:screenOrientation="portrait" />

        <!--作者详情列表页面-->
        <activity
            android:name=".ui.store.AuthorDetailActivity"
            android:screenOrientation="portrait" />

        <!--新的书籍商品列表页-->
        <activity
            android:name=".ui.store.NewBookListActivity"
            android:screenOrientation="portrait" />

        <!--搜索页面-->
        <activity
            android:name=".ui.store.search.SearchActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateVisible|adjustPan" />

        <!--书架搜索页面-->
        <activity
            android:name=".ui.shelf.search.ShelfSearchActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateVisible|adjustPan" />

        <!--书籍内搜索页面-->
        <activity
            android:name=".ui.read.search.BookSearchActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateVisible|adjustPan" />

        <!--当前书籍笔记的列表显示页面-->
        <activity
            android:name=".ui.user.BookNoteDetailActivity"
            android:screenOrientation="portrait" />

        <!--所有笔记的列表显示页面-->
        <activity
            android:name=".ui.user.BookNoteCountActivity"
            android:screenOrientation="portrait" />

        <!--  信用卡解绑的帮助页面  -->
        <activity
            android:name=".ui.store.UnbindCardHelperActivity"
            android:screenOrientation="portrait" />

        <!--编辑笔记页面-->
        <activity
            android:name=".ui.user.NoteEditActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan|stateHidden" />

        <!--首页推荐进入的列表页面-->
        <activity
            android:name=".ui.store.RecommendBookListActivity"
            android:screenOrientation="portrait" />
        <!--已购书籍的订单列表页面-->
        <activity
            android:name=".ui.user.PurchasedBookListActivity"
            android:screenOrientation="portrait" />

        <!--书籍详情点击目录跳转的页面-->
        <activity
            android:name=".ui.store.ContentsActivity"
            android:screenOrientation="portrait" />

        <!--书城的所有分类列表页面-->
        <activity
            android:name=".ui.store.BookCategoryListActivity"
            android:screenOrientation="portrait" />

        <!--订单详情的页面-->
        <activity
            android:name=".ui.account.OrderDetailActivity"
            android:screenOrientation="portrait" />

        <!--交易记录的页面-->
        <activity
            android:name=".ui.account.OrderListActivity"
            android:screenOrientation="portrait" />

        <!--通用的web页面-->
        <activity
            android:name=".ui.CommWebViewActivity"
            android:screenOrientation="portrait" />

        <!--跳转微读授权登录的页面-->
        <activity
            android:name=".ui.account.SSOLoginActivity"
            android:exported="true"
            android:launchMode="singleTask"
            android:theme="@style/dialogActivityTheme" />

        <!--书架文件夹列表的页面-->
        <activity
            android:name=".ui.shelf.FolderBookListActivity"
            android:screenOrientation="portrait" />

        <!--关于页面-->
        <activity
            android:name=".ui.AboutActivity"
            android:screenOrientation="portrait" />

        <!--阅读主界面-->
        <activity
            android:name=".ui.read.BookReadActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:hardwareAccelerated="true"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />
        <!--设置页面-->
        <activity
            android:name=".ui.SettingActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />

        <!--编辑资料页面-->
        <activity
            android:name=".ui.EditAccountActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />

        <!--编辑名字页面-->
        <activity
            android:name=".ui.EditNameActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden|adjustPan" />

        <!--安全验证页面-->
        <activity
            android:name=".ui.SecurityVerifyActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden|adjustPan" />

        <!--账户与安全页面-->
        <activity
            android:name=".ui.AccountSecurityActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />

        <!--设置密码页面-->
        <activity
            android:name=".ui.SetPasswordActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />

        <!--图片裁剪-->
        <activity
            android:name=".tools.util.crop.CropImageActivity"
            android:screenOrientation="portrait" />

        <!--书籍详情的页面-->
        <activity
            android:name=".ui.store.BookDetailActivity"
            android:exported="true"
            android:hardwareAccelerated="true"
            android:screenOrientation="portrait">
            <intent-filter>
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <!--书城的分类书籍列表页面-->
        <activity
            android:name=".ui.store.BookListActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />

        <!--图片全屏-->
        <activity
            android:name=".ui.read.ImageFullSizeActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden|adjustPan" />

        <!--用户注册界面-->
        <activity
            android:name=".ui.account.register.RegisterActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden|adjustPan" />

        <!--重置密码界面-->
        <activity
            android:name=".ui.account.ResetPasswordActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden|adjustPan" />

        <!--绑定手机或邮箱界面-->
        <activity
            android:name=".ui.account.BindPhoneOrEmailActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden|adjustPan" />

        <!--用户登录界面-->
        <activity
            android:name=".ui.account.LoginActivity"
            android:exported="true"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden|adjustPan">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="login"
                    android:scheme="@string/login_scheme" />
            </intent-filter>
        </activity>

        <!--国家代码选择-->
        <activity
            android:name=".ui.account.register.CountryCodeChooseActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"

            android:windowSoftInputMode="stateHidden|adjustPan" />

        <!--网络加速-->
        <activity
            android:name=".ui.InternetSpeedUpActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />

        <!--QR扫描-->
        <activity
            android:name=".tools.zxing.activity.CaptureActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />

        <!--Lab page 的页面（供 QA 测试用）-->
        <activity
            android:name=".tools.test.LabPageActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />

        <!--同步数据的服务-->
        <service
            android:name=".ui.service.SyncDataService"
            android:enabled="true" />

        <!--下载的后台服务-->
        <service
            android:name=".tools.download.DownloadService"
            android:enabled="true" />

        <!--APK 升级下载的服务-->
        <service android:name=".tools.upgrade.APKDownloadService" />
    </application>

</manifest>
