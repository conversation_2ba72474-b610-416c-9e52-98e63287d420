package p67.fox;

import java.net.InetSocketAddress;
import java.net.Proxy;

public class Client {
    static {
        System.loadLibrary("fox");
    }

    private static native boolean _isValidConfig(String opaqueConfig, String opaqueConfigOverride);

    private static native String _start(String opaqueConfig, String opaqueConfigOverride);

    private static native void _reconnect();

    private static native boolean _isConnected();

    private static native void _stop();

    private static Proxy proxy = null;
    private static String host = null;
    private static Integer port = 0;

    static String localAddress = null;

    public synchronized static Proxy start(String opaqueConfig) {
        return startWithOverride(opaqueConfig, null);
    }

    public synchronized static Proxy startWithOverride(
            String opaqueConfig,
            String opaqueConfigOverride) {
        if (proxy == null) {
            localAddress = _start(opaqueConfig, opaqueConfigOverride);
            if (localAddress == null) {
                throw new CftException("Unable to start server");
            }

            proxy = new Proxy(Proxy.Type.SOCKS, fromSocketAddr(localAddress));
        }

        return proxy;
    }

    public synchronized static boolean isValidConfig(
            String opaqueConfig,
            String opaqueConfigOverride) {
        return _isValidConfig(opaqueConfig, opaqueConfigOverride);
    }

    public synchronized static void reconnect() {
        _reconnect();
    }

    public synchronized static Proxy proxy() {
        return proxy;
    }

    public synchronized static String host() {
        return host;
    }

    public synchronized static Integer port() {
        return port;
    }

    public synchronized static boolean isConnected() {
        if (proxy != null) {
            return _isConnected();
        } else {
            return false;
        }
    }

    public synchronized static void stop() {
        if (proxy != null) {
            _stop();
            proxy = null;
            host = null;
            port = 0;
        }
    }

    private static InetSocketAddress fromSocketAddr(String s) {
        int idx = s.lastIndexOf(":");

        if (idx == -1) {
            throw new CftException("invalid SocketAddr");
        }
        host = s.substring(0, idx);
        port = Integer.parseInt(s.substring(idx + 1));
        return new InetSocketAddress(host, port);
    }
}
