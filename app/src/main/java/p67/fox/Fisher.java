package p67.fox;

public class Fisher {
    static {
        System.loadLibrary("fox");
    }

    /**
     * Downloads a resource into a file.
     * @param url URL to the resource to download.
     * @param path Path to the file to write content into.
     * @throws CftException upon failure.
     */
    public static void catchFish(String url, String path) {
        _catch(url, path);
    }

    private static native void _catch(String url, String path);
}
