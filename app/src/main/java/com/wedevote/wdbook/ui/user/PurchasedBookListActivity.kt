package com.wedevote.wdbook.ui.user

import android.os.Bundle
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.FragmentActivity
import com.aquila.lib.base.BaseRecycleAdapter
import com.aquila.lib.layout.SmartRefreshLayout
import com.aquila.lib.layout.api.RefreshLayout
import com.aquila.lib.layout.listener.OnLoadMoreListener
import com.aquila.lib.layout.listener.OnRefreshListener
import com.aquila.lib.tools.util.ToastUtil
import com.aquila.lib.widget.group.GroupImageTextLayout
import com.aquila.lib.widget.view.CustomRecyclerView
import com.wedevote.wdbook.R
import com.wedevote.wdbook.base.ExceptionHandler
import com.wedevote.wdbook.base.RootActivity
import com.wedevote.wdbook.base.SDKSingleton
import com.wedevote.wdbook.constants.DownloadStatus
import com.wedevote.wdbook.entity.PurchasedResourceEntity
import com.wedevote.wdbook.entity.store.BookFileDownloadEntity
import com.wedevote.wdbook.tools.download.DownloaderEngine
import com.wedevote.wdbook.tools.download.OnDownloadingListener
import com.wedevote.wdbook.tools.event.AutoDownloadEvent
import com.wedevote.wdbook.tools.event.OnBookItemDownloadFinish
import com.wedevote.wdbook.tools.event.OnLoginEvent
import com.wedevote.wdbook.tools.util.getFunctionInfo
import com.wedevote.wdbook.ui.home.microwidget.BookProductViewHolder
import com.wedevote.wdbook.ui.store.DownloadOption
import com.wedevote.wdbook.ui.widgets.CommTopTitleLayout
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

/***
 * @date 创建时间 2020/9/14 15:08
 * <AUTHOR> W.YuLong
 * @description 已购书籍列表
 */
class PurchasedBookListActivity : RootActivity(), OnRefreshListener, View.OnClickListener, OnLoadMoreListener {
    lateinit var topTitleLayout: CommTopTitleLayout
    lateinit var refreshLayout: SmartRefreshLayout
    lateinit var dataRecyclerView: CustomRecyclerView
    lateinit var emptyLayout: GroupImageTextLayout

    val limitSize = 10
    var position = 0

    lateinit var orderAdapter: PurchasedOrderAdapter

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_purchase_list_layout)
        topTitleLayout = findViewById(R.id.purchase_list_top_TitleLayout)
        refreshLayout = findViewById(R.id.purchase_list_SmartRefreshLayout)
        dataRecyclerView = findViewById(R.id.purchase_list_data_RecyclerView)
        emptyLayout = findViewById(R.id.purchase_list_empty_icon_Layout)

        refreshLayout.setOnRefreshListener(this).setOnLoadMoreListener(this)
        emptyLayout.setOnClickListener(this)
        orderAdapter = PurchasedOrderAdapter(this)
        dataRecyclerView.adapter = orderAdapter

        reloadData()
        onRefresh(refreshLayout)
        EventBus.getDefault().post(AutoDownloadEvent())
    }

    fun setCurrentUIShowStatus() {
        if (orderAdapter.dataList.isNullOrEmpty()) {
            emptyLayout.visibility = View.VISIBLE
            refreshLayout.visibility = View.GONE
        } else {
            emptyLayout.visibility = View.GONE
            refreshLayout.visibility = View.VISIBLE
        }
    }

    override fun onRefresh(layout: RefreshLayout?) {
        MainScope().launch(ExceptionHandler.coroutineExceptionHandler) {
            SDKSingleton.syncBl.syncPurchasedData()
            reloadData()
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onReceiveDownloadFinish(event: OnBookItemDownloadFinish) {
//        reloadData()
    }

    fun reloadData() {
        position = 0
        orderAdapter.clearDataList()
        refreshLayout.isEnableLoadMore = true
        onLoadMore(refreshLayout)
    }

    override fun onLoadMore(layout: RefreshLayout) {
        try {
            val dataList = SDKSingleton.dbWrapBl.getPurchasedResourceEntityList(position, limitSize)
            orderAdapter.addDataList(dataList)
            position = orderAdapter.itemCount
            if (dataList.isNullOrEmpty()) {
                refreshLayout.isEnableLoadMore = false
            }
            setCurrentUIShowStatus()

            refreshLayout.finishLoadMoreAndRefresh()
        } catch (e: Exception) {
            e.printStackTrace()
            ToastUtil.showToastLong(e.message)
            SDKSingleton.loggerBl.handleThrowable(e, getFunctionInfo())
        }
    }

    override fun onClick(v: View?) {
        when (v) {
            emptyLayout -> {
                onRefresh(refreshLayout)
            }
        }
    }
}

/***
 *@date 创建时间 2020/9/29 17:51
 *<AUTHOR> W.YuLong
 *@description
 */
class PurchasedOrderAdapter(val activity: FragmentActivity) : BaseRecycleAdapter<PurchasedResourceEntity, BookProductViewHolder>() {
    var downloadEngine = DownloaderEngine(activity)

    var onDownloadingListener = object : OnDownloadingListener {
        override fun onWait(entity: BookFileDownloadEntity, isAdded: Boolean) {
            var option = DownloadOption(
                if (isAdded) DownloadStatus.WAIT else DownloadStatus.CANCEL
            )
            notifyItemChanged(findDataPosition(entity), option)
        }

        override fun onBeginning(entity: BookFileDownloadEntity) {
            var option = DownloadOption(DownloadStatus.BEGIN)
            notifyItemChanged(findDataPosition(entity), option)
        }

        override fun onDownloadingProgress(entity: BookFileDownloadEntity, downloadSize: Long, totalSize: Long) {
            var option = DownloadOption(DownloadStatus.DOWNLOADING, downloadSize, totalSize)
            notifyItemChanged(findDataPosition(entity), option)
        }

        override fun onPause(entity: BookFileDownloadEntity) {
            var option = DownloadOption(DownloadStatus.PAUSE)
            notifyItemChanged(findDataPosition(entity), option)
        }

        override fun onError(entity: BookFileDownloadEntity, errorDesc: String) {
            var option = DownloadOption(DownloadStatus.ERROR)
            notifyItemChanged(findDataPosition(entity), option)
        }

        override fun onComplete(entity: BookFileDownloadEntity) {
            var option = DownloadOption(DownloadStatus.COMPLETE)
            EventBus.getDefault().post(OnBookItemDownloadFinish())
            notifyItemChanged(findDataPosition(entity), option)
        }
    }

    init {
        downloadEngine.setOnDownloadingListener(onDownloadingListener)
    }

    fun findDataPosition(entity: BookFileDownloadEntity): Int {
        var position = -1
        dataList?.let {
            for (i in it.indices) {
                if (it[i].resourceDownloadInfo.fileId == entity.fileId) {
                    position = i
                    break
                }
            }
        }
        return position
    }

    override fun onBindViewHolder(holder: BookProductViewHolder, position: Int, payloads: MutableList<Any>) {
        if (!payloads.isNullOrEmpty()) {
            if (payloads[0] is DownloadOption) {
                var option = payloads[0] as DownloadOption
                holder.updateButtonShow(option)
            }
        } else {
            onBindViewHolder(holder, position)
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BookProductViewHolder {
        return BookProductViewHolder(parent, BookProductViewHolder.TYPE_PRODUCT_ORDER, downloadEngine)
    }
}
