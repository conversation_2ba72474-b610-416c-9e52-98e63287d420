package com.wedevote.wdbook.ui.read.lib.css;

import android.graphics.Typeface;
import android.text.TextUtils;
import android.text.style.ForegroundColorSpan;
import android.text.style.RelativeSizeSpan;
import android.text.style.StrikethroughSpan;
import android.text.style.StyleSpan;
import android.text.style.UnderlineSpan;

import com.wedevote.wdbook.ui.read.lib.BookParameters;
import com.wedevote.wdbook.ui.read.lib.span.AlignSpan;
import com.wedevote.wdbook.ui.read.lib.span.BlockSpan;

import java.util.ArrayList;
import java.util.HashMap;

public class CssElement {
    private final ArrayList<CSSAttribute> attributes = new ArrayList<>();
    static private HashMap<String,Class<?>> nameMap;

    public static abstract class CSSAttribute {
        private String name;
        abstract boolean initParameters(ArrayList<String> params);
        void setName(String name){this.name = name;}
        String getName(){return name;}
    }

    public int getAttrSize(){
        return attributes.size();
    }

    public static abstract class CSSTextAttribute extends CSSAttribute {
        abstract Object getSpan();
    }

    public static abstract class CSSBlock extends CSSAttribute {

    }

    public static class CSSPadding extends CSSBlock{
        private HeightUnit left;
        private HeightUnit right;
        private HeightUnit top;
        private HeightUnit bottom;

        @Override
        public boolean initParameters(ArrayList<String> params){
            if(params.size() == 2){
                init(DataFormatParser.parseFloat(params.get(1)));
            }else if(params.size() == 3){
                init(DataFormatParser.parseFloat(params.get(1)),DataFormatParser.parseFloat(params.get(2)));
            }else if(params.size() == 4){
                init(DataFormatParser.parseFloat(params.get(1)),
                        DataFormatParser.parseFloat(params.get(2)),
                        DataFormatParser.parseFloat(params.get(3)));
            }else if(params.size() == 5){
                init(DataFormatParser.parseFloat(params.get(1)),
                        DataFormatParser.parseFloat(params.get(2)),
                        DataFormatParser.parseFloat(params.get(3)),
                        DataFormatParser.parseFloat(params.get(4)));
            }else{
                return false;
            }
            return true;
        }

        private void init(HeightUnit top,HeightUnit left){
            init(top,left,top,left);
        }

        private void init(HeightUnit top,HeightUnit left,HeightUnit bottom){
            init(top,left,bottom,left);
        }

        private void init(HeightUnit top,HeightUnit right,HeightUnit bottom,HeightUnit left){
            this.left = left;
            this.top = top;
            this.right = right;
            this.bottom = bottom;
        }

        private void init(HeightUnit padding){
            init(padding,padding,padding,padding);
        }

        public void setBlockSpanPadding(BlockSpan blockSpan){
            blockSpan.setPadding(left == null?0:left.px,
                    right == null?0:right.px,
                    top == null?0:top.px,
                    bottom == null?0:bottom.px);
        }
    }

    public enum BorderType{
        none,solid,dotted,dashed
    }

    public static class CSSBorder extends CSSBlock{
        @Override
        public boolean initParameters(ArrayList<String> params){
            if(params.size() > 1){
                width = DataFormatParser.parseFloat(params.get(1));
            }
            if(params.size() > 2){
                if(params.get(2).equals("solid")){
                    type = BorderType.solid;
                }else if(params.get(2).equals("dotted")){
                    type = BorderType.dotted;
                }else if(params.get(2).equals("dashed")){
                    type = BorderType.dashed;
                }
            }
            if(params.size() > 3){
                color = DataFormatParser.parseColor(params.get(3));
            }
            return true;
        }
        public HeightUnit width;
        public BorderType type = BorderType.none;
        public int color = 0;
    }

    public static class CSSColor extends CSSTextAttribute{
        private int color = 0;
        @Override
        public boolean initParameters(ArrayList<String> params){
            if(params.size() == 2){
                color = DataFormatParser.parseColor(params.get(1));
                return true;
            }else{
                return false;
            }
        }

        @Override
        public Object getSpan() {
            return new ForegroundColorSpan(color);
        }
    }

    public static class CSSBkColor extends CSSBlock{
        public int color = 0;
        @Override
        public boolean initParameters(ArrayList<String> params){
            if(params.size() == 2){
                color = DataFormatParser.parseColor(params.get(1));
                return true;
            }else{
                return false;
            }
        }
    }

    public static class CSSHeight extends CSSBlock {
        protected HeightUnit height;
        @Override
        public boolean initParameters(ArrayList<String> params){
            if(params.size() == 2){
                height = DataFormatParser.parseFloat(params.get(1));
                return true;
            }else{
                return false;
            }
        }
    }

    public static class CSSFontSize extends CSSTextAttribute{
        protected HeightUnit height;
        @Override
        public boolean initParameters(ArrayList<String> params){
            if(params.size() == 2){
                height = DataFormatParser.parseFloat(params.get(1));
                return true;
            }else{
                return false;
            }
        }

        public Object getSpan(){
            if(height != null && height.em > 0.0){
                return new RelativeSizeSpan(height.em);
            }else{
                return null;
            }
        }
    }

    public static class CSSDecoration extends CSSTextAttribute{
        enum DecorationType{
            none,underline,line_through
        }

        @Override
        public boolean initParameters(ArrayList<String> params){
            if(params.size() == 2){
                if(params.get(1).equals("underline")){
                    type = DecorationType.underline;
                }else if(params.get(1).equals("line-through")) {
                    type = DecorationType.line_through;
                }
                return true;
            }else{
                return false;
            }
        }

        public Object getSpan(){
            if(type == DecorationType.underline){
                return new UnderlineSpan();
            }else if(type == DecorationType.line_through){
                return new StrikethroughSpan();
            }else{
                return null;
            }
        }

        private DecorationType type = DecorationType.none;
    }

    public enum TextAlignType{
        left,right,center,justify
    }

    public static class CSSTextAlign extends CSSTextAttribute{
        @Override
        public boolean initParameters(ArrayList<String> params){
            if(params.size() == 2){
                if(params.get(1).equals("right")){
                    type = TextAlignType.right;
                }else if(params.get(1).equals("center")){
                    type = TextAlignType.center;
                }else if(params.get(1).equals("left")){
                    type = TextAlignType.left;
                }
                return true;
            }else{
                return false;
            }
        }

        @Override
        Object getSpan() {
            return new AlignSpan(type);
        }

        private TextAlignType type = TextAlignType.justify;
    }

    public static class CSSFontWeight extends CSSTextAttribute{
        enum FontWeightType{
            normal,bold
        }

        private int weight = 0;
        @Override
        public boolean initParameters(ArrayList<String> params){
            if(params.size() == 2){
                if(params.get(1).equals("bold") || params.get(1).equals("bolder")){
                    type = FontWeightType.bold;
                }else{
                    weight = safeParseInt(params.get(1),0);
                }
                return true;
            }else{
                return false;
            }
        }

        public Object getSpan(){
            if(type == FontWeightType.bold){
                return new StyleSpan(Typeface.BOLD);
            }else{
                return null;
            }
        }

        private FontWeightType type = FontWeightType.normal;
    }

    public static class CSSListStyle extends CSSAttribute{
        public static final int NONE = 0;
        public static final int DECIMAL = 1;
        public static final int DECIMAL_LEADING_ZERO = 2;
        public static final int LOWER_ROMAN = 3;
        public static final int UPPER_ROMAN = 4;
        public static final int LOWER_GREEK = 5;
        public static final int LOWER_LATIN = 6;
        public static final int UPPER_LATIN = 7;
        public static final int LOWER_ALPHA = 8;
        public static final int UPPER_ALPHA = 9;

        private int type = NONE;
        public int getStyleType(){
            return type;
        }

        @Override
        boolean initParameters(ArrayList<String> params){
            if(params.size() == 2){
                if(params.get(1).equals("decimal")){
                    type = DECIMAL;
                }else if(params.get(1).equals("decimal-leading-zero")){
                    type = DECIMAL_LEADING_ZERO;
                }else if(params.get(1).equals("lower-roman")){
                    type = LOWER_ROMAN;
                }else if(params.get(1).equals("upper-roman")){
                    type = UPPER_ROMAN;
                }else if(params.get(1).equals("lower-greek")){
                    type = LOWER_GREEK;
                }else if(params.get(1).equals("lower-latin")){
                    type = LOWER_LATIN;
                }else if(params.get(1).equals("upper-latin")){
                    type = UPPER_LATIN;
                }else if(params.get(1).equals("lower-alpha")){
                    type = LOWER_ALPHA;
                }else if(params.get(1).equals("upper-alpha")){
                    type = UPPER_ALPHA;
                }
                return true;
            }else{
                return false;
            }
        }
    }

    public CssElement(String text){
        initAttributeNames();
        String[] attrs = text.split(";");
        for(String a:attrs){
            addAttribute(a);
        }
    }

    public void merge(CssElement e){
        attributes.addAll(e.attributes);
    }

    private void initAttributeNames(){
        if(nameMap == null){
            nameMap = new HashMap<>();
            nameMap.put("margin",CSSPadding.class);
            nameMap.put("padding",CSSPadding.class);
            nameMap.put("border",CSSBorder.class);
            nameMap.put("border-left",CSSBorder.class);
            nameMap.put("color",CSSColor.class);
            nameMap.put("background-color",CSSBkColor.class);
            nameMap.put("height",CSSHeight.class);
            nameMap.put("text-align",CSSTextAlign.class);
            nameMap.put("text-decoration",CSSDecoration.class);
            nameMap.put("font-weight",CSSFontWeight.class);
            nameMap.put("font-size",CSSFontSize.class);
            nameMap.put("list-style-type",CSSListStyle.class);
        }
    }

    private void addAttribute(String attrString){
        ArrayList<String> attrs = new ArrayList<>();
        int length = attrString.length();
        int start = -1;
        for(int i=0;i<length;i++){
            char c = attrString.charAt(i);
            if(c != ' ' && c != '\n' && c != ':'){
                if(start < 0){
                    start = i;
                }
            }else if(start >= 0){
                attrs.add(attrString.substring(start,i));
                start = -1;
            }
        }

        if(start > 0){
            attrs.add(attrString.substring(start));
        }

        if(attrs.isEmpty()){
            return;
        }

        CSSAttribute attribute = findAttribute(attrs.get(0));
        if(attribute != null){
            if(attribute.initParameters(attrs)){
                attribute.setName(attrs.get(0));
                attributes.add(attribute);
            }
        }
    }

    private CSSAttribute findAttribute(String attributeName){
        Class<?> cls = nameMap.get(attributeName);
        if(cls == null){
            return null;
        }
        try{
            return (CSSAttribute)cls.newInstance();
        }catch (Exception e){
            e.printStackTrace();
            return null;
        }
    }

    public int getColor(){
        for(CSSAttribute a:attributes){
            if(a instanceof CSSColor){
                return ((CSSColor)a).color;
            }
        }
        return BookParameters.getTextNormalColor();
    }

    public Object getDecoration(){
        for(CSSAttribute a:attributes){
            if(a instanceof CSSDecoration){
                return ((CSSDecoration)a).getSpan();
            }
        }
        return null;
    }

    public int getListStyle(){
        for(CSSAttribute a:attributes){
            if(a instanceof CSSListStyle){
                return ((CSSListStyle)a).getStyleType();
            }
        }
        return CSSListStyle.NONE;
    }

    public ArrayList<Object> getTextSpans(){
        ArrayList<Object> list = new ArrayList<>();
        BlockSpan blockSpan = null;
        for(CSSAttribute a:attributes){
            if(a instanceof CSSTextAttribute){
                Object o = ((CSSTextAttribute)a).getSpan();
                if(o != null){
                    list.add(o);
                }
            }else if(a instanceof CSSBlock){
                if(blockSpan == null){
                    blockSpan = new BlockSpan();
                }
                if(a instanceof CSSBkColor){
                    blockSpan.setBackgroundColor(((CSSBkColor)a).color);
                }else if(a.name.equals("border-left")){
                    blockSpan.setBorder((CSSBorder) a,null,null,null);
                }else if(a.name.equals("border")){
                    blockSpan.setBorder((CSSBorder) a,(CSSBorder) a,(CSSBorder) a,(CSSBorder) a);
                }else if(a.name.equals("padding")){
                    ((CSSPadding) a).setBlockSpanPadding(blockSpan);
                }else if(a.name.equals("height")){
                    blockSpan.setHeight(((CSSHeight)a).height.px);
                }else if(a.name.equals("margin")){
                    blockSpan.setMargin(((CSSPadding)a).top.em,((CSSPadding)a).bottom.em);
                }
            }
        }
        if(blockSpan != null){
            list.add(blockSpan);
        }
        return list;
    }

    private static int safeParseInt(String str, int defaultNumber) {
        int result = defaultNumber;
        try {
            if (!TextUtils.isEmpty(str)) {
                result = Integer.parseInt(str);
            }
        } catch (NumberFormatException e) {
            e.printStackTrace();
        }
        return result;
    }
}
