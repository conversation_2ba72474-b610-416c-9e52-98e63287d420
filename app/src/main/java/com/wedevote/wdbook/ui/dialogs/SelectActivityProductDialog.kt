package com.wedevote.wdbook.ui.dialogs

import android.content.Context
import android.os.Bundle
import android.text.Html
import android.view.Gravity
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import com.aquila.lib.base.BaseRecycleAdapter
import com.aquila.lib.base.BaseViewHolder
import com.aquila.lib.base.OnViewClickListener
import com.wedevote.wdbook.tools.util.ImageLoadUtil
import com.aquila.lib.widget.view.CustomRecyclerView
import com.wedevote.wdbook.R
import com.wedevote.wdbook.entity.store.ProductDetailEntity
import com.wedevote.wdbook.tools.util.PictureUtil
import com.wedevote.wdbook.tools.util.UnitFormatUtil

/***
 * @date 创建时间 2022/6/21 17:38
 * <AUTHOR> W.YuLong
 * @description
 */
class SelectActivityProductDialog(context: Context, var activityId: Long, var onDeleteListener: OnViewClickListener?) :
    BaseDialog(context) {
    lateinit var dataRecyclerView: CustomRecyclerView
    lateinit var activityAdapter: SelectActivityAdapter

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.dialog_activity_select_product_layout)
        dataRecyclerView = findViewById(R.id.dialog_select_product_data_RecyclerView)
        activityAdapter = SelectActivityAdapter(activityId)
        activityAdapter.onDeleteListener = onDeleteListener
        dataRecyclerView.adapter = activityAdapter

        configDialog(Gravity.BOTTOM)
    }

    fun initDataList(list: ArrayList<ProductDetailEntity>?) {
        var detaillist = ArrayList<ProductDetailEntity>()
        if (list != null) {
            detaillist.addAll(list)
        }
        activityAdapter.dataList = detaillist
    }
}

/***
 *@date 创建时间 2022/6/22 09:44
 *<AUTHOR> W.YuLong
 *@description
 */
class SelectActivityAdapter(var activityId: Long) : BaseRecycleAdapter<ProductDetailEntity, SelectActivityProductViewHolder>() {
    var onDeleteListener: OnViewClickListener? = null

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): SelectActivityProductViewHolder {
        return SelectActivityProductViewHolder(parent, activityId)
    }

//    override fun getItemCount(): Int {
//        return 10
//    }

    override fun onBindViewHolder(holder: SelectActivityProductViewHolder, position: Int) {
        val entity = getDataFromPosition(position)
        holder.initUIData(entity)
        holder.deleteTextView.setOnClickListener {
            deleteItem(entity!!)
            onDeleteListener?.onClickAction(it, "delete", entity)
            notifyDataSetChanged()
        }
    }
}

/***
 *@date 创建时间 2022/6/21 23:29
 *<AUTHOR> W.YuLong
 *@description
 */
class SelectActivityProductViewHolder(parent: ViewGroup, var activityId: Long) :
    BaseViewHolder(parent, R.layout.item_dialog_activity_book_list_layout) {
    val coverImageView: ImageView = itemView.findViewById(R.id.dialog_activity_cover_ImageView)
    val nameTextView: TextView = itemView.findViewById(R.id.dialog_activity_book_name_TextView)
    val priceTextView: TextView = itemView.findViewById(R.id.dialog_activity_price_TextView)
    val deleteTextView: TextView = itemView.findViewById(R.id.dialog_activity_delete_TextView)

    override fun <T> initUIData(t: T) {
        t as ProductDetailEntity
        PictureUtil.loadImage(coverImageView, t.cover)
        nameTextView.text = t.title
        priceTextView.setText(Html.fromHtml(getActivityPriceStr(t)))
    }

    /*格式化获取活动的价格信息*/
    fun getActivityPriceStr(entity: ProductDetailEntity): String {
        if (!entity.activitiesList.isNullOrEmpty()) {
            for (item in entity.activitiesList!!) {
                if (item.productId == entity.productId) {
                    return UnitFormatUtil.formatPrice(entity.currency, item.amount)
                }
            }
        }
        return UnitFormatUtil.formatPrice(entity.currency, entity.price)
    }
}
