package com.wedevote.wdbook.ui.dialogs

import android.app.Dialog
import android.content.Context
import android.os.Bundle
import android.view.Gravity
import android.view.ViewGroup
import android.view.WindowManager
import android.widget.ImageView
import android.widget.TextView
import com.aquila.lib.base.BaseRecycleAdapter
import com.aquila.lib.base.BaseViewHolder
import com.aquila.lib.widget.view.CustomRecyclerView
import com.wedevote.wdbook.R
import com.wedevote.wdbook.entity.store.ProductDetailEntity
import com.wedevote.wdbook.tools.util.UnitFormatUtil
import com.wedevote.wdbook.tools.util.findString

/***
 * @date 创建时间 2020/9/24 20:43
 * <AUTHOR> W.YuLong
 * @description 打包购买商品中已购商品列表的对话框
 */
class PartPurchasedItemListDialog(context: Context) : Dialog(context) {
    lateinit var titleTextView: TextView
    lateinit var dismissImageView: ImageView
    lateinit var dataRecyclerView: CustomRecyclerView

    lateinit var purchaseAdapter: PurchasedItemAdapter
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.dialog_purchased_item_list_layout)
        initViewFromXML()
        configDialog()
        purchaseAdapter = PurchasedItemAdapter()
        dataRecyclerView.adapter = purchaseAdapter

        dismissImageView.setOnClickListener {
            dismiss()
        }
    }

    private fun initViewFromXML() {
        titleTextView = findViewById(R.id.purchase_item_title_TextView)
        dismissImageView = findViewById(R.id.purchase_item_close_ImageView)
        dataRecyclerView = findViewById(R.id.purchase_item_data_RecyclerView)
    }

    protected fun configDialog() {
        val wl = window!!.attributes
        wl.gravity = Gravity.BOTTOM // 设置重力
        wl.width = WindowManager.LayoutParams.MATCH_PARENT
        wl.height = WindowManager.LayoutParams.WRAP_CONTENT
        window!!.setWindowAnimations(com.aquila.lib.dialog.R.style.bottomDialogWindowAnim)
        window!!.attributes = wl
        window!!.setBackgroundDrawableResource(android.R.color.transparent)
    }

    fun initDataList(purchasedProductList: ArrayList<ProductDetailEntity>?) {
        purchaseAdapter.dataList = purchasedProductList
    }
}

/***
 *@date 创建时间 2020/9/24 21:24
 *<AUTHOR> W.YuLong
 *@description
 */
class PurchasedItemAdapter : BaseRecycleAdapter<ProductDetailEntity, PurchasedViewHolder>() {
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): PurchasedViewHolder {
        return PurchasedViewHolder(parent)
    }
}

/***
 *@date 创建时间 2020/9/24 21:24
 *<AUTHOR> W.YuLong
 *@description
 */
class PurchasedViewHolder(parent: ViewGroup) : BaseViewHolder(parent, R.layout.item_purchased_product_layout) {
    val titleTextView: TextView = itemView.findViewById(R.id.item_purchased_title_TextView)
    val priceTextView: TextView = itemView.findViewById(R.id.item_purchased_price_TextView)

    override fun <T> initUIData(t: T) {
        t as ProductDetailEntity

        titleTextView.text = findString(R.string.buy) + "《${t.title}》"
        priceTextView.text = "-${UnitFormatUtil.getCurrencySymbol(t.currency)}${t.price}"
    }
}
