package com.wedevote.wdbook.ui.read

import android.content.Context
import android.content.SharedPreferences
import com.wedevote.wdbook.constants.AnalyticsConstants
import com.wedevote.wdbook.tools.util.AnalyticsUtils
import com.wedevote.wdbook.tools.util.GsonUtil
import java.text.SimpleDateFormat
import java.util.*

// Data model for a reading session
private data class ReadingSessionData(
    val sessionId: String,
    val resourceId: String,
    val startTime: Long, // Unix timestamp in ms
    var endTime: Long? = null,
    var isCompleted: Boolean = false
) {
    fun isValidReading(): Boolean {
        val end = endTime ?: return false
        val duration = end - startTime
        return duration >= 300_000 // 5 minutes
    }
    fun getSessionDate(): String {
        val date = Date(startTime)
        val formatter = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
        return formatter.format(date)
    }
}

object ReadingSessionTracker {
    private const val PREFS_NAME = "reading_session_prefs"
    private const val CURRENT_SESSION_KEY = "current_reading_session"
    private var currentSession: ReadingSessionData? = null

    // Call this when reading starts
    fun startSession(context: Context, resourceId: String) {
        if (currentSession != null) endSession(context)
        if (resourceId.isEmpty()) return
        val session = ReadingSessionData(
            sessionId = UUID.randomUUID().toString(),
            resourceId = resourceId,
            startTime = System.currentTimeMillis()
        )
        currentSession = session
        saveCurrentSession(context)
    }

    // Call this when reading ends
    fun endSession(context: Context) {
        val session = currentSession ?: return
        session.endTime = System.currentTimeMillis()
        session.isCompleted = true
        clearCurrentSession(context)
        currentSession = null
        if (session.isValidReading()) {
            reportSession(session)
        }
    }

    // Call this in Application.onCreate() to handle abnormal termination
    fun handleAbnormalTermination(context: Context) {
        val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        val json = prefs.getString(CURRENT_SESSION_KEY, null) ?: return
        val session = try {
            GsonUtil.parseJsonToObject<ReadingSessionData>(json)
        } catch (e: Exception) {
            clearCurrentSession(context)
            return
        }
        if (session.endTime == null) {
            val now = System.currentTimeMillis()
            val duration = now - session.startTime
            if (duration >= 300_000) { // More than 5 minutes
                session.endTime = now
                session.isCompleted = true
                reportSession(session)
            } 
            clearCurrentSession(context)
        }
    }

    // Report session to AnalyticsUtils
    private fun reportSession(session: ReadingSessionData) {
        val endTime = session.endTime ?: return
        val durationSeconds = ((endTime - session.startTime) / 1000).toInt()
        val params = arrayOf(
            "resource_id", session.resourceId,
            "start_time", session.startTime.toString(),
            "end_time", endTime.toString(),
            "session_date", session.getSessionDate(),
            "reading_duration_seconds", durationSeconds.toString()
        )
        AnalyticsUtils.logEvent(AnalyticsConstants.LOG_V2_READING_SESSION_COMPLETE, *params)
    }

    // Persistence helpers
    private fun saveCurrentSession(context: Context) {
        val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        val json = GsonUtil.objectToJson(currentSession)
        prefs.edit().putString(CURRENT_SESSION_KEY, json).apply()
    }
    private fun clearCurrentSession(context: Context) {
        val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        prefs.edit().remove(CURRENT_SESSION_KEY).apply()
    }
} 