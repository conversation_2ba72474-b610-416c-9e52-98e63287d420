package com.wedevote.wdbook.ui.user

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.os.Message
import android.view.View
import android.widget.EditText
import android.widget.TextView
import androidx.core.widget.addTextChangedListener
import com.wedevote.wdbook.R
import com.wedevote.wdbook.base.ExceptionHandler
import com.wedevote.wdbook.base.RootActivity
import com.wedevote.wdbook.base.SDKSingleton
import com.wedevote.wdbook.constants.AnalyticsConstants
import com.wedevote.wdbook.entity.NoteEntity
import com.wedevote.wdbook.tools.event.OnSyncNoteFinish
import com.wedevote.wdbook.tools.util.*
import com.wedevote.wdbook.utils.JsonUtility
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus

/***
 * @date 创建时间 2021/9/6 11:18
 * <AUTHOR> W.<PERSON><PERSON>
 * @description 笔记编辑的页面
 */
class NoteEditActivity : RootActivity() {
    lateinit var finishTextView: TextView
    lateinit var flagColorView: View
    lateinit var bookTextTextView: TextView
    lateinit var warningTextView: TextView
    lateinit var descEditText: EditText

    val MAX_TEXT_LENGTH = 20000
    val MSG_SAVE = 0
    var isFirstTextChange = true
    val SAVE_NOTE_RATE = (10 * 1000).toLong()

    var lastText = ""
    lateinit var noteEntity: NoteEntity

    companion object {
        private var onNoteEditCallback: OnNoteEditCallback? = null

        fun gotoNoteEdit(context: Context, entity: NoteEntity, callback: OnNoteEditCallback) {
            val intent = Intent(context, NoteEditActivity::class.java)
            intent.putExtra(IntentConstants.EXTRA_NoteEntity, JsonUtility.encodeToString(entity))
            context.startActivity(intent)
            onNoteEditCallback = callback
            context.scanForActivity()?.let {
                it.overridePendingTransition(R.anim.anim_move_from_bottom, R.anim.anim_normal)
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_note_edit_layout)
        SoftHideKeyBoardUtil.assistActivity(this)
        initViewFromXML()
        intent.getStringExtra(IntentConstants.EXTRA_NoteEntity)?.let {
            noteEntity = JsonUtility.decodeFromString(it)
            initUI(noteEntity)
        }
        setViewListeners()
        AnalyticsUtils.logEvent(
            AnalyticsConstants.LOG_V1_BOOK_EDIT_NOTE, AnalyticsConstants.LOG_V1_PARAM_RESOURCE_ID, noteEntity.resourceId
        )
    }

    private fun initViewFromXML() {
        finishTextView = findViewById(R.id.activity_edit_note_finish_TextView)
        flagColorView = findViewById(R.id.activity_edit_note_mark_color_View)
        bookTextTextView = findViewById(R.id.activity_edit_note_book_text_TextView)
        descEditText = findViewById(R.id.activity_edit_note_desc_EditText)
        warningTextView = findViewById(R.id.activity_edit_note_warning_TextView)
    }

    private fun setViewListeners() {
        finishTextView.setOnClickListener {
            onBackPressed()
        }
        descEditText.addTextChangedListener {
            warningTextView.visibility = if (descEditText.text.length > MAX_TEXT_LENGTH) View.VISIBLE else View.GONE
            if (isFirstTextChange) {
                isFirstTextChange = false
                handler.sendEmptyMessageDelayed(MSG_SAVE, SAVE_NOTE_RATE)
            }
        }
    }

    fun initUI(entity: NoteEntity) {
        this.noteEntity = entity
        lastText = noteEntity.noteText

        flagColorView.setBackgroundColor(parseColor(noteEntity.highlightColorType))
        bookTextTextView.text = noteEntity.getDisplaySummery()
        descEditText.setText(noteEntity.noteText)
        descEditText.setSelection(0)
    }

    private fun saveCurrentNote() {
        if (lastText != descEditText.text.toString()) {
            val noteText = descEditText.text.toString()

            if ((noteText.isEmpty() || noteText.isBlank()) && noteEntity.noteText.isNullOrEmpty()) {
                return
            }
            noteEntity.noteText = noteText
            if (noteEntity.noteText!!.length > MAX_TEXT_LENGTH) {
                noteEntity.noteText = noteEntity.noteText!!.substring(0, MAX_TEXT_LENGTH)
            }
            SDKSingleton.dbWrapBl.saveNote(noteEntity)
        } else {
            if (!noteEntity.conflictRemoteId.isNullOrEmpty()) {
                noteEntity.conflictRemoteId = "" // KMM层来写
                SDKSingleton.userBl.solvedConflictData(
                    noteEntity.remoteId, noteEntity.dataId!!
                )
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        handler.removeMessages(MSG_SAVE)
        onNoteEditCallback = null
    }

    override fun onPause() {
        super.onPause()
        if (lastText != descEditText.text.toString() || !noteEntity.conflictRemoteId.isNullOrEmpty()) {
            saveCurrentNote()
            MainScope().launch(ExceptionHandler.getCoroutineExceptionHandler {
                onBackPressed()
            }) {
                SDKSingleton.syncBl.syncNoteData()
                EventBus.getDefault().post(OnSyncNoteFinish())
            }
        }
        onNoteEditCallback?.onNoteEditFinish(noteEntity)
    }

    override fun finish() {
        super.finish()
        overridePendingTransition(R.anim.anim_normal, R.anim.anim_move_to_bottom)
    }

    private val handler: Handler = object : Handler() {
        override fun handleMessage(msg: Message) {
            if (isFinishing) {
                saveCurrentNote()
                sendEmptyMessageDelayed(MSG_SAVE, SAVE_NOTE_RATE)
            }
        }
    }
}

interface OnNoteEditCallback {
    fun onNoteEditFinish(noteEntity: NoteEntity)
}
