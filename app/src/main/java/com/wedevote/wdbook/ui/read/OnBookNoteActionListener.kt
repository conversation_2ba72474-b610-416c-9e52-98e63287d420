package com.wedevote.wdbook.ui.read

import com.wedevote.wdbook.constants.HighlightColorType
import com.wedevote.wdbook.entity.NoteEntity
import com.wedevote.wdbook.entity.feedback.CorrectionParamsEntity

/***
 * @date 创建时间 2020/12/3 15:06
 * <AUTHOR> <PERSON><PERSON>
 * @description
 */
interface OnBookNoteActionListener {
    fun onHighlightAction(color: HighlightColorType, noteEntity: NoteEntity?): Boolean
    fun onNoteAdd(noteEntity: NoteEntity?)
    fun onNoteShow(noteEntity: NoteEntity?): Boolean
    fun onDeleteNote(noteEntity: NoteEntity?)
    fun onCancelSelect()

    fun initCorrectionEntity(text: String): CorrectionParamsEntity
}
