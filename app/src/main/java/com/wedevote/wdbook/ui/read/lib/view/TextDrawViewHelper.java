package com.wedevote.wdbook.ui.read.lib.view;

import android.text.Spanned;
import android.text.TextPaint;

public class TextDrawViewHelper {
    private final TextPaint paint;
    private float y = 0;

    public TextDrawViewHelper(TextPaint paint, float y) {
        this.paint = paint;
        this.y = y;
    }

    private CharSequence text;

    public void setText(CharSequence text) {
        this.text = text;
    }

    private TextPageList measureUnknownText(TextPageList textPageList) {
        int totalLength = text.length();
        for (int i = 0; i < totalLength; i++) {
            int j = i + 1;
            if (isContinueSymbol(text.charAt(i)) && !textPageList.isInTable()) {
                while (j < totalLength - 1 && isContinueSymbol(text.charAt(j))) {
                    j++;
                }
            }
            textPageList.addSpanned((Spanned) text.subSequence(i, j), i);
            i = j - 1;
        }
        textPageList.originalText = text.toString();
        return textPageList;
    }

    public TextPageList measureAll(float parentWidth, float parentHeight) {
        TextPageList wordPage = new TextPageList(paint, y, parentWidth, parentHeight);
        return measureUnknownText(wordPage);
    }

    private boolean isContinueSymbol(char c) {
        return (c >= '0' && c <= '9') ||
                (c >= 'a' && c <= 'z') ||
                (c >= 'A' && c <= 'Z') ||
                (c == '’') || (c == '-') ||
                (c >= 0x0590 && c <= 0x05ff) ||//support hebrew code
                (c >= 0xD800 && c <= 0xDfff);//support strange chinese character
    }
}
