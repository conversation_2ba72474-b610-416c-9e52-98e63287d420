package com.wedevote.wdbook.ui.store

import android.os.Bundle
import android.text.Html
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.appcompat.widget.Toolbar
import androidx.constraintlayout.widget.ConstraintLayout
import com.aquila.lib.layout.SmartRefreshLayout
import com.aquila.lib.layout.api.RefreshLayout
import com.aquila.lib.layout.listener.OnLoadMoreListener
import com.aquila.lib.layout.listener.OnRefreshListener
import com.aquila.lib.tools.util.ToastUtil
import com.aquila.lib.widget.group.GroupImageTextLayout
import com.aquila.lib.widget.view.CustomRecyclerView
import com.google.android.material.appbar.AppBarLayout
import com.google.android.material.appbar.CollapsingToolbarLayout
import com.wedevote.wdbook.R
import com.wedevote.wdbook.base.ExceptionHandler
import com.wedevote.wdbook.base.RootActivity
import com.wedevote.wdbook.base.SDKSingleton
import com.wedevote.wdbook.constants.OrderField
import com.wedevote.wdbook.entity.store.ProductFilterEntity
import com.wedevote.wdbook.entity.store.StoreCategoryEntity
import com.wedevote.wdbook.exception.SDKException
import com.wedevote.wdbook.tools.event.OnSuccessBuyEvent
import com.wedevote.wdbook.tools.interfaces.OnItemClickListener
import com.wedevote.wdbook.tools.util.APPUtil
import com.wedevote.wdbook.tools.util.HtmlUtil
import com.wedevote.wdbook.tools.util.IntentConstants
import com.wedevote.wdbook.tools.util.PictureUtil
import com.wedevote.wdbook.ui.home.microwidget.BookItemRecyclerAdapter
import com.wedevote.wdbook.ui.widgets.CommTopTitleLayout
import com.wedevote.wdbook.ui.widgets.ExpandTextViewTwo
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

/***
 *@date 创建时间 2023/9/6
 *<AUTHOR> John.Qian
 *@description  作者详情页面
 */
class AuthorDetailActivity : RootActivity(), OnRefreshListener, OnLoadMoreListener,
    View.OnClickListener {
    private var isFirst: Boolean = true
    lateinit var rootView: View
    lateinit var topTitleLayout: CommTopTitleLayout

    lateinit var refreshLayout: SmartRefreshLayout

    lateinit var appbarLayout: AppBarLayout
    lateinit var collapsingToolbarLayout: CollapsingToolbarLayout

    lateinit var collapsingToolBar: Toolbar
    lateinit var descContentLayout: GroupImageTextLayout

    lateinit var filterContainerLayout: ConstraintLayout
    lateinit var sortContainerLayout: ConstraintLayout
    lateinit var defaultSortTextView: TextView
    lateinit var soldSortTextView: TextView
    lateinit var priceSortTextView: TextView
    lateinit var authorNameTextView: TextView
    lateinit var numberTextView: TextView
    lateinit var languageLayout: GroupImageTextLayout
    lateinit var dataRecyclerView: CustomRecyclerView
    lateinit var emptyPromptTextView: TextView
    lateinit var summaryExpandTextViewTwo: ExpandTextViewTwo
    lateinit var avatarImageView: ImageView

    lateinit var itemDataAdapter: BookItemRecyclerAdapter

    lateinit var languagePopupWindow: SortPopupWindow
    lateinit var filterEntity: ProductFilterEntity

    var currentSortMode: OrderField = OrderField.CreateTime_DESC
    var authorName: String = ""
    var authorId: Long = -1
    var IsPublisher: Boolean = false

    lateinit var allEntity: StoreCategoryEntity

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        rootView = layoutInflater.inflate(R.layout.activity_author_detail_layout, null)
        setContentView(rootView)
        initViewFromXML()

        filterEntity = ProductFilterEntity()
        allEntity = StoreCategoryEntity().apply {
            categoryName = getString(R.string.all_books)
        }
        authorId = intent.getLongExtra(IntentConstants.EXTRA_AuthorId, -1)
//        authorName = intent.getStringExtra(IntentConstants.EXTRA_AuthorName) ?: ""
        IsPublisher = intent.getBooleanExtra(IntentConstants.EXTRA_IsPublisher, false)
        languagePopupWindow = SortPopupWindow(this)
        initAdapters()
        setViewListeners()
        setCurrentSortModeUI(OrderField.CreateTime_DESC)

        if (IsPublisher) {
            filterEntity.publisherId = authorId
        } else {
            filterEntity.editorId = authorId
        }
        filterEntity.language = ""
        initUI()
        getAuthorData()
        getDataFromServer()
    }

    private fun initUI() {
        if (IsPublisher) {
            avatarImageView.setImageResource(R.drawable.ic_default_publisher_avatar)
        } else {
            if (SDKSingleton.appBl.isCurrentThemeLight()) {
                avatarImageView.setImageResource(R.drawable.ic_default_author)
            } else {
                avatarImageView.setImageResource(R.drawable.ic_default_author_dark)
            }
        }
        numberTextView.text = String.format(getString(R.string.author_book_number), 0)
        topTitleLayout.setTitle(authorName)
    }

    private fun getAuthorData() {
        MainScope().launch(ExceptionHandler.coroutineExceptionHandler) {
            if (IsPublisher) {
                val authorData = SDKSingleton.storeBl.getPublisherDetails(authorId, APPUtil.getLanguage())
                PictureUtil.loadCircleImageWithDefault(avatarImageView, authorData?.picture, R.drawable.ic_default_publisher_avatar)
                summaryExpandTextViewTwo.currentText = Html.fromHtml(HtmlUtil.delHTMLTag(authorData?.description0 ?: ""))
                authorName = authorData?.name ?: ""
                authorNameTextView.text = authorName
                topTitleLayout.setTitle(authorName)
            } else {
                val authorData = SDKSingleton.storeBl.getEditorDetails(authorId, APPUtil.getLanguage())
                if (SDKSingleton.appBl.isCurrentThemeLight()) {
                    PictureUtil.loadCircleImageWithDefault(avatarImageView, authorData?.url, R.drawable.ic_default_author)
                } else {
                    PictureUtil.loadCircleImageWithDefault(avatarImageView, authorData?.url, R.drawable.ic_default_author_dark)
                }
                summaryExpandTextViewTwo.currentText = Html.fromHtml(HtmlUtil.delHTMLTag(authorData?.description0 ?: ""))
                authorName = authorData?.name ?: ""
                authorNameTextView.text = authorName
                topTitleLayout.setTitle(authorName)
            }
            if (summaryExpandTextViewTwo.currentText.isEmpty()) {
                summaryExpandTextViewTwo.visibility = View.GONE
            } else {
                summaryExpandTextViewTwo.visibility = View.VISIBLE
            }
        }
    }

    fun initViewFromXML() {
        topTitleLayout = findViewById(R.id.book_list_top_TitleLayout)

        appbarLayout = findViewById(R.id.book_list_AppBarLayout)

        collapsingToolbarLayout = findViewById(R.id.book_list_CollapsingToolbarLayout)

        collapsingToolBar = findViewById(R.id.book_list_collapsing_title_Toolbar)
        descContentLayout = findViewById(R.id.book_list_top_collapsing_content_Layout)

        filterContainerLayout = findViewById(R.id.book_list_filter_container_layout)
        emptyPromptTextView = findViewById(R.id.book_list_empty_prompt_TextView)

        sortContainerLayout = findViewById(R.id.book_list_sort_container_Layout)
        defaultSortTextView = findViewById(R.id.book_list_sort_default_TextView)
        soldSortTextView = findViewById(R.id.book_list_sort_sold_TextView)
        priceSortTextView = findViewById(R.id.book_list_sort_price_TextView)
        authorNameTextView = findViewById(R.id.author_name_TextView)
        numberTextView = findViewById(R.id.author_book_number_TextView)
        languageLayout = findViewById(R.id.book_list_language_layout)

        refreshLayout = findViewById(R.id.book_list_SmartRefreshLayout)
        dataRecyclerView = findViewById(R.id.book_list_data_RecyclerView)
        summaryExpandTextViewTwo = findViewById(R.id.author_detail_summary_ExpandTextViewTwo)
        avatarImageView = findViewById(R.id.author_avatar_ImageView)
    }

    private fun initAdapters() {
        itemDataAdapter = BookItemRecyclerAdapter()
        dataRecyclerView.adapter = itemDataAdapter
    }

    private fun setViewListeners() {
        refreshLayout.setOnRefreshListener(this).setOnLoadMoreListener(this)
        defaultSortTextView.setOnClickListener(this)
        priceSortTextView.setOnClickListener(this)
        soldSortTextView.setOnClickListener(this)
        languageLayout.setOnClickListener(this)
        descContentLayout.setOnClickListener(this)
        appbarLayout.addOnOffsetChangedListener(object : AppBarLayout.OnOffsetChangedListener {
            override fun onOffsetChanged(appBarLayout: AppBarLayout?, verticalOffset: Int) {
                var alpha: Float =
                    Math.abs(verticalOffset)
                        .toFloat() / (collapsingToolbarLayout.height - collapsingToolBar.height).toFloat()
                collapsingToolBar.alpha = alpha
                filterContainerLayout.alpha = 1 - alpha
                collapsingToolBar.visibility = if (alpha == 0f) View.GONE else View.VISIBLE
            }
        })

        languagePopupWindow.onItemClickListener = object : OnItemClickListener {
            override fun <T> onItemClick(obj: Any, tag: String, t: T) {
                t as LanguageEntity
                filterEntity.language = t.language
                languageLayout.setText(t.name)
                onRefresh(refreshLayout)
                languagePopupWindow.dismiss()
            }
        }

        summaryExpandTextViewTwo.clickListener = object : ExpandTextViewTwo.ClickListener {
            override fun onContentTextClick() {
            }
            override fun onSpecialTextClick(currentExpand: Boolean) {
                summaryExpandTextViewTwo.isExpand = !currentExpand
            }
        }
    }

    /*折叠的title*/
    fun initCollapsingTitle() {
        descContentLayout.setText("$sortText · ${languageLayout.text}")
    }

    var sortText: String = ""
    private fun setCurrentSortModeUI(sort: OrderField) {
        when (sort) {
            OrderField.CreateTime_DESC -> {
                defaultSortTextView.isSelected = true
                priceSortTextView.isSelected = false
                soldSortTextView.isSelected = false
                filterEntity.orderField = OrderField.CreateTime_DESC.value
                sortText = defaultSortTextView.text.toString()
            }
            OrderField.Price_ASC -> {
                defaultSortTextView.isSelected = false
                priceSortTextView.isSelected = true
                soldSortTextView.isSelected = false
                filterEntity.orderField = OrderField.Price_ASC.value
                sortText = priceSortTextView.text.toString()
            }
            OrderField.SaleCount_DESC -> {
                defaultSortTextView.isSelected = false
                priceSortTextView.isSelected = false
                soldSortTextView.isSelected = true
                filterEntity.orderField = OrderField.SaleCount_DESC.value
                sortText = soldSortTextView.text.toString()
            }
            else -> {}
        }
        filterEntity.orderField = sort.value
        currentSortMode = sort
        onRefresh(refreshLayout)
    }

    override fun onRefresh(refreshLayout: RefreshLayout) {
        itemDataAdapter.clearDataList()
        filterEntity.page = 1
        initCollapsingTitle()
        getAuthorData()
        getDataFromServer()
    }

    override fun onLoadMore(refreshLayout: RefreshLayout) {
        filterEntity.page++
        getDataFromServer()
    }

    private fun getDataFromServer() {
        MainScope().launch(ExceptionHandler.getCoroutineExceptionHandler {
            if (it is SDKException) {
                ToastUtil.showToastShort(R.string.get_list_data_failure)
            }
            numberTextView.text = String.format(getString(R.string.author_book_number), 0)
            refreshLayout.finishLoadMoreAndRefresh()
        }) {
            SDKSingleton.storeBl.getBookProductFilter(filterEntity.transferToMap()).also { t ->
                if (filterEntity.page == 1) {
                    itemDataAdapter.dataList = t.productList
                } else {
                    itemDataAdapter.addDataList(t.productList)
                }
                if (isFirst) {
                    numberTextView.text = String.format(getString(R.string.author_book_number), t.total)
                    isFirst = false
                }
                refreshLayout.isEnableLoadMore = t.currentPage < t.totalPage
                if (itemDataAdapter.itemCount == 0) {
                    emptyPromptTextView.visibility = View.VISIBLE
                    refreshLayout.visibility = View.GONE
                } else {
                    emptyPromptTextView.visibility = View.GONE
                    refreshLayout.visibility = View.VISIBLE
                }
            }
            refreshLayout.finishLoadMoreAndRefresh()
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onReceiveBuyResultEvent(event: OnSuccessBuyEvent) {
        itemDataAdapter.updateProductPurchaseStatus(event.productId)
    }

    override fun onClick(v: View?) {
        when (v) {
            descContentLayout -> {
                appbarLayout.setExpanded(true)
            }
            defaultSortTextView -> {
                setCurrentSortModeUI(OrderField.CreateTime_DESC)
            }
            soldSortTextView -> {
                setCurrentSortModeUI(OrderField.SaleCount_DESC)
            }
            priceSortTextView -> {
                setCurrentSortModeUI(OrderField.Price_ASC)
            }
            languageLayout -> {
                languagePopupWindow.showAsDropDown(languageLayout)
            }
        }
    }
}
