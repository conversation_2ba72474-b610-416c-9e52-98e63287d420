package com.wedevote.wdbook.ui.read.lib.data;


public final class FormatTextRange {
    int mLocation;
    int mLength;
    String mFormat;
    String mData;

    public FormatTextRange(int location, int length, String format, String data) {
        this.mLocation = location;
        this.mLength = length;
        this.mFormat = format;
        this.mData = data;
    }

    public FormatTextRange() {
        this.mLocation = 0;
        this.mLength = 0;
        this.mFormat = "";
        this.mData = "";
    }

    public int getLocation() {
        return mLocation;
    }

    public void setLocation(int input) {
        this.mLocation = input;
    }

    public int getLength() {
        return mLength;
    }

    public void setLength(int input) {
        this.mLength = input;
    }

    public String getFormat() {
        return mFormat;
    }

    public void setFormat(String input) {
        this.mFormat = input;
    }

    public String getData() {
        return mData;
    }

    public void setData(String input) {
        this.mData = input;
    }

    @Override
    public String toString() {
        return "FormatTextRange{" +
                "mLocation=" + mLocation +
                "," + "mLength=" + mLength +
                "," + "mFormat=" + mFormat +
                "," + "mData=" + mData +
                "}";
    }
}
