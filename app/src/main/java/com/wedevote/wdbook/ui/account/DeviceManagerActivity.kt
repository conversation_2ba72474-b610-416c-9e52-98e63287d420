package com.wedevote.wdbook.ui.account

import android.app.Activity
import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.TextView
import com.aquila.lib.base.BaseRecycleAdapter
import com.aquila.lib.base.BaseViewHolder
import com.aquila.lib.base.OnViewClickListener
import com.aquila.lib.tools.singleton.SPSingleton
import com.aquila.lib.widget.view.CustomRecyclerView
import com.wedevote.wdbook.R
import com.wedevote.wdbook.base.ExceptionHandler
import com.wedevote.wdbook.base.RootActivity
import com.wedevote.wdbook.base.SDKSingleton
import com.wedevote.wdbook.entity.user.UserDeviceEntity
import com.wedevote.wdbook.network.ApiErrorCode
import com.wedevote.wdbook.tools.event.LogoutEvent
import com.wedevote.wdbook.tools.util.AnalyticsUtils
import com.wedevote.wdbook.tools.util.SPKeyDefine
import com.wedevote.wdbook.tools.util.UnitFormatUtil
import com.wedevote.wdbook.tools.util.findString
import com.wedevote.wdbook.ui.home.HomeMainActivity
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus

/***
 * @date 创建时间 2022/2/28 16:08
 * <AUTHOR> W.YuLong
 * @description 设备管理的页面
 */
class DeviceManagerActivity : RootActivity(), View.OnClickListener {
    lateinit var logoutTextView: TextView
    lateinit var userNameTextView: TextView
    lateinit var timeTextView: TextView
    lateinit var localDeviceTextView: TextView
    lateinit var tipsTextView: TextView
    lateinit var enterButton: Button
    lateinit var dataRecyclerView: CustomRecyclerView
    lateinit var deviceAdapter: DeviceAdapter

    companion object {
        var onCallBack: OnLoginResultCallBack? = null

        fun gotoDeviceManager(activity: Activity, callBack: OnLoginResultCallBack? = null) {
            onCallBack = callBack
            activity.startActivity(
                Intent(activity, DeviceManagerActivity::class.java).apply {
                    flags = Intent.FLAG_ACTIVITY_NEW_TASK
                }
            )
            activity.overridePendingTransition(R.anim.anim_move_from_bottom, R.anim.anim_normal)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_device_manager_layout)
        initViewFromXML()

        deviceAdapter = DeviceAdapter()
        dataRecyclerView.adapter = deviceAdapter
        getDeviceDataFromServe()
        setViewListeners()
    }

    private fun setViewListeners() {
        logoutTextView.setOnClickListener(this)
        enterButton.setOnClickListener(this)
        deviceAdapter.onViewClickListener = object : OnViewClickListener {
            override fun <T> onClickAction(v: View, str: String, t: T?) {
                MainScope().launch(ExceptionHandler.coroutineExceptionHandler) {
                    SDKSingleton.userBl.deleteUserDevice((t as UserDeviceEntity).deviceId!!)
                    SDKSingleton.userBl.checkAndAddDevice(SDKSingleton.appBl.deviceId)
                    getDeviceDataFromServe()
                }
            }
        }
    }

    private fun initViewFromXML() {
        logoutTextView = findViewById(R.id.device_manager_logout_TextView)
        userNameTextView = findViewById(R.id.device_manager_name_TextView)
        timeTextView = findViewById(R.id.device_manager_time_TextView)
        localDeviceTextView = findViewById(R.id.device_manager_local_device_TextView)
        tipsTextView = findViewById(R.id.device_manager_tips_TextView)
        enterButton = findViewById(R.id.device_manager_goto_app_button)
        dataRecyclerView = findViewById(R.id.device_manager_list_RecyclerView)
    }

    override fun onBackPressed() {
        if (enterButton.isEnabled) {
            super.onBackPressed()
        } else {
            logoutTextView.performClick()
        }
    }

    fun getDeviceDataFromServe() {
        MainScope().launch(ExceptionHandler.coroutineExceptionHandler) {
            val list = SDKSingleton.userBl.getUserDeviceList()

            // 更新限制设备提示的bug
            SDKSingleton.appBl.getDeviceCount()?.let {
                tipsTextView.text = findString(R.string.device_count_tip).format(it.deviceLimit)
            }

            val dataList = ArrayList<UserDeviceEntity>()
            for (item in list!!) {
                if (item.deviceId != SDKSingleton.appBl.deviceId) {
                    dataList.add(item)
                }
            }
            deviceAdapter.dataList = dataList

            val result = SDKSingleton.userBl.checkAndAddDevice(SDKSingleton.appBl.deviceId)

            enterButton.isEnabled = result != ApiErrorCode.DeviceExceedLimit_1312

            timeTextView.text = UnitFormatUtil.formatDate_ymdhm(System.currentTimeMillis())
            userNameTextView.text = "${Build.MODEL}"
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        onCallBack = null
    }

    override fun onClick(v: View?) {
        when (v) {
            logoutTextView -> {
                MainScope().launch(ExceptionHandler.coroutineExceptionHandler) {
                    SDKSingleton.sessionBl.logout()
                    SPSingleton.get().removeKey(SPKeyDefine.SP_LoginUserId)
                    EventBus.getDefault().post(LogoutEvent())
                    HomeMainActivity.gotoHomeActivity(this@DeviceManagerActivity)
                    AnalyticsUtils.updateAnalyticsUserID()
                    onCallBack?.onLoginResult(false)
                    finish()
                }
            }
            enterButton -> {
                onCallBack?.onLoginResult(true)
                finish()
            }
        }
    }
}

/***
 *@date 创建时间 2022/2/28 16:34
 *<AUTHOR> W.YuLong
 *@description
 */
class DeviceAdapter : BaseRecycleAdapter<UserDeviceEntity, DeviceItemViewHolder>() {
    var onViewClickListener: OnViewClickListener? = null
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): DeviceItemViewHolder {
        return DeviceItemViewHolder(parent)
    }

    override fun onBindViewHolder(holder: DeviceItemViewHolder, position: Int) {
//        super.onBindViewHolder(holder, position)
        val data = dataList!![position]
        holder.initUIData(data)
        holder.unbindButton.setOnClickListener {
            onViewClickListener?.onClickAction(it, "delete", data)
        }
    }
}

/***
 *@date 创建时间 2022/2/28 16:30
 *<AUTHOR> W.YuLong
 *@description
 */
class DeviceItemViewHolder(parent: ViewGroup) : BaseViewHolder(parent, R.layout.holder_item_device_layout), View.OnClickListener {
    val deviceNameTextView: TextView = itemView.findViewById(R.id.item_device_name_TextView)
    val timeTextView: TextView = itemView.findViewById(R.id.item_device_time_TextView)
    val unbindButton: TextView = itemView.findViewById(R.id.item_device_unbind_Button)
    lateinit var deviceEntity: UserDeviceEntity

    override fun <T> initUIData(t: T) {
        deviceEntity = t as UserDeviceEntity
        deviceNameTextView.text = t.deviceModel
        timeTextView.text = UnitFormatUtil.formatDate_ymdhm(t.lastUpdateTime!!)
    }

    override fun onClick(v: View?) {
        when (v) {
            unbindButton -> {
                MainScope().launch(ExceptionHandler.coroutineExceptionHandler) {
                    SDKSingleton.userBl.deleteUserDevice(deviceEntity.deviceId!!)
                }
            }
        }
    }
}
