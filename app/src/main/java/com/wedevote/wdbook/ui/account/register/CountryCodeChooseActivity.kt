package com.wedevote.wdbook.ui.account.register

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import com.aquila.lib.base.BaseRecycleAdapter
import com.aquila.lib.base.BaseViewHolder
import com.aquila.lib.widget.view.CustomRecyclerView
import com.wedevote.wdbook.R
import com.wedevote.wdbook.base.RootActivity
import com.wedevote.wdbook.tools.util.IntentConstants
import java.util.*

/**
 * Created by theophilus on 4/22/19.
 */

/***
 *@date 重构时间 2019-11-04 16:34
 *<AUTHOR> <PERSON><PERSON>
 *@description 国家的选择
 */
class CountryCodeChooseActivity : RootActivity() {

    public override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_country_choose_layout)
        val recyclerView: CustomRecyclerView = findViewById(R.id.country_RecyclerView)
        recyclerView.adapter = CountryListAdapter(this)
    }

    private class CountryListAdapter(val activity: Activity) : BaseRecycleAdapter<CountryItem, BaseViewHolder>() {

        init {
            val displayList = ArrayList<CountryItem>()

            val country_items = activity.resources.getStringArray(R.array.country_name_and_code)
            displayList.add(CountryItem(activity.getString(R.string.common_use), ""))
            for (s in country_items) {
                if (s[0] == '#') {
                    addCountryItem(displayList, s)
                }
            }

            var c = 'A'
            while (c <= 'Z') {
                displayList.add(CountryItem("" + c, ""))
                var number = 0
                for (s in country_items) {
                    if (s[0] == c) {
                        addCountryItem(displayList, s)
                        number++
                    }
                }
                if (number == 0) {
                    displayList.removeAt(displayList.size - 1)
                }
                c++
            }

            dataList = displayList
        }

        private fun addCountryItem(displayList: ArrayList<CountryItem>, country: String) {
            val params = country.split(" ")
            if (params.size >= 4) {
                val last = params.size - 1
                var name = ""
                for (i in 1 until last - 1) {
                    name += params[i]
                    name += " "
                }
                displayList.add(CountryItem(name, "+" + params[last]))
            }
        }

        override fun getItemViewType(position: Int): Int {
            return getDataFromPosition(position)?.let {
                if (it.code.isNullOrEmpty()) {
                    0
                } else {
                    1
                }
            } ?: 0
        }

        override fun onCreateViewHolder(viewGroup: ViewGroup, type: Int): BaseViewHolder {
            return if (type == 0) CountryCodeTitleViewHolder(viewGroup) else CountryCodeItemViewHolder(viewGroup)
        }
    }

    /***
     *@date 创建时间 2019-11-04 16:28
     *<AUTHOR> W.YuLong
     *@description
     */
    class CountryCodeTitleViewHolder(parent: ViewGroup) : BaseViewHolder(parent, R.layout.downloaded_list_title_layout) {
        val nameTextView: TextView = itemView.findViewById(R.id.downloaded_item_title_TextView)
        override fun <T> initUIData(t: T) {
            t as CountryItem
            nameTextView.text = t.name
        }
    }

    /***
     *@date 创建时间 2019-11-04 16:28
     *<AUTHOR> W.YuLong
     *@description
     */
    class CountryCodeItemViewHolder(parent: ViewGroup) : BaseViewHolder(parent, R.layout.country_code_item_layout), View.OnClickListener {
        val nameTextView: TextView = itemView.findViewById(R.id.code_item_name_TextView)
        private val codeTextView: TextView = itemView.findViewById(R.id.code_item_number_TextView)

        lateinit var countryItem: CountryItem
        override fun <T> initUIData(t: T) {
            countryItem = t as CountryItem
            nameTextView.text = t.name
            codeTextView.text = t.code
            itemView.setOnClickListener(this)
        }

        override fun onClick(v: View) {
            val data = Intent()
            data.putExtra(IntentConstants.Extra_countryCode, countryItem.code)
            v.context.let {
                if (it is Activity) {
                    it.setResult(IntentConstants.INTENT_RESULT_COUNTRY_CODE, data)
                    it.onBackPressed()
                }
            }
        }
    }

    class CountryItem internal constructor(var name: String, var code: String)
}
