package com.wedevote.wdbook.ui.user.coupon

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import android.widget.Button
import android.widget.ImageView
import android.widget.TextView
import com.wedevote.wdbook.R
import com.wedevote.wdbook.base.RootActivity
import com.wedevote.wdbook.entity.coupon.CouponEntity
import com.wedevote.wdbook.entity.coupon.CouponStatus
import com.wedevote.wdbook.tools.util.IntentConstants
import com.wedevote.wdbook.tools.util.UnitFormatUtil
import com.wedevote.wdbook.tools.util.findString
import com.wedevote.wdbook.ui.home.HomeMainActivity
import com.wedevote.wdbook.ui.home.HomeTab
import com.wedevote.wdbook.ui.store.BookDetailActivity
import com.wedevote.wdbook.utils.JsonUtility

/***
 * @date 创建时间 2022/6/8 18:05
 * <AUTHOR> W<PERSON><PERSON>
 * @description
 */
class CouponDetailActivity : RootActivity(), View.OnClickListener {
    lateinit var couponAmountTextView: TextView
    lateinit var titleTextView: TextView
    lateinit var dashView: View
    lateinit var dateTextView: TextView
    lateinit var useButton: Button
    lateinit var statusImageView: ImageView
    
    companion object {
        fun gotoCouponDetail(context: Context, entity: CouponEntity?) {
            val intent = Intent(context, CouponDetailActivity::class.java)
            intent.putExtra(IntentConstants.EXTRA_CouponEntity, JsonUtility.encodeToString(entity))
            context.startActivity(intent)
        }
    }
    
    lateinit var couponEntity: CouponEntity
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_coupon_detail_layout)
        initViewFromXML()
        intent.getStringExtra(IntentConstants.EXTRA_CouponEntity)?.let {
            couponEntity = JsonUtility.decodeFromString(it)
            titleTextView.setText(couponEntity.couponName)
            dateTextView.text =
                "${UnitFormatUtil.formatDate_ymd(couponEntity.availableStartTime)} - ${UnitFormatUtil.formatDate_ymdhm(couponEntity.availableEndTime)}"
            couponAmountTextView.text = UnitFormatUtil.formatPrice("", couponEntity.couponAmount)
            initCouponStatus(couponEntity.status)
        }
        useButton.setOnClickListener(this)
    }
    
    private fun initViewFromXML() {
        couponAmountTextView = findViewById(R.id.item_coupon_money_TextView)
        titleTextView = findViewById(R.id.item_coupon_name_TextView)
        dashView = findViewById(R.id.item_coupon_dash_line_View)
        dateTextView = findViewById(R.id.item_coupon_active_date_TextView)
        useButton = findViewById(R.id.item_coupon_use_coupon_Button)
        statusImageView = findViewById(R.id.item_coupon_status_ImageView)
    }
    
    fun initCouponStatus(status: Int) {
        when (status) {
            CouponStatus.NOT_GET.status -> {//未领取
                useButton.visibility = View.VISIBLE
                useButton.text = findString(R.string.get_coupon)
                statusImageView.visibility = View.GONE
            }
            CouponStatus.CAN_USE.status -> {//可使用
                useButton.visibility = View.VISIBLE
                useButton.text = findString(R.string.use_coupon)
                statusImageView.visibility = View.GONE
            }
            CouponStatus.USED.status -> {//已使用
                useButton.visibility = View.GONE
                statusImageView.visibility = View.VISIBLE
                statusImageView.setImageResource(R.drawable.ic_coupon_used)
            }
            CouponStatus.EXPIRED.status -> { //已过期
                useButton.visibility = View.GONE
                statusImageView.visibility = View.VISIBLE
                statusImageView.setImageResource(R.drawable.ic_coupon_expired)
            }
        }
    }
    
    override fun onClick(v: View?) {
        if (v == useButton) {
            if (couponEntity.status == CouponStatus.CAN_USE.status) {
                if (couponEntity.scope == 0 && couponEntity.productIds != null && couponEntity.productIds!!.size == 1) {
                    BookDetailActivity.gotoBookDetail(this, couponEntity.productIds!![0])
                } else {
                    HomeMainActivity.gotoHomeActivity(this, HomeTab.STORE)
                }
    
            }
        }
    }
    
    
}