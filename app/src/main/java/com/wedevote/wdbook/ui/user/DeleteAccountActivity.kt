package com.wedevote.wdbook.ui.user

import android.app.Dialog
import android.content.Intent
import android.os.Bundle
import android.view.View
import android.widget.Button
import com.aquila.lib.dialog.CommProgressDialog
import com.aquila.lib.dialog.OnDialogViewClickListener
import com.wedevote.wdbook.R
import com.wedevote.wdbook.base.ExceptionHandler
import com.wedevote.wdbook.base.RootActivity
import com.wedevote.wdbook.base.SDKSingleton
import com.wedevote.wdbook.ui.dialogs.DeleteAccountDialog
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch

/***
 * @date 创建时间 2022/11/25 16:00
 * <AUTHOR> W.<PERSON>ong
 * @description
 */
class DeleteAccountActivity : RootActivity(), View.OnClickListener {
    lateinit var executeButton: Button
    lateinit var commProgressDialog: CommProgressDialog
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_delete_account_layout)
        executeButton = findViewById(R.id.delete_account_execute_Button)
        executeButton.setOnClickListener(this)
    
        commProgressDialog = CommProgressDialog.with(this).create()
    
    }
    
    override fun onResume() {
        super.onResume()
        MainScope().launch(ExceptionHandler.coroutineExceptionHandler) {
            var status = SDKSingleton.userBl.getUserAccountStatus()
            executeButton.isEnabled = status != 1
        }
    
    }
    
    override fun onClick(v: View?) {
        if (v == executeButton) {
            var dialog = DeleteAccountDialog(this)
            dialog.show()
            dialog.onViewClickListener = object : OnDialogViewClickListener {
                override fun onViewClick(dialog: Dialog, v: View, tag: Int) {
                    MainScope().launch(ExceptionHandler.coroutineExceptionHandler) {
                        commProgressDialog.show()
                        val result = SDKSingleton.userBl.deleteUserAccount()
                        val intent = Intent(this@DeleteAccountActivity, DeleteAccountResultActivity::class.java)
                        startActivity(intent)
                        dialog.dismiss()
                        commProgressDialog.dismiss()
                        finish()
                        executeButton.isEnabled = false
                    }
                }
            }
        }
    }
    
}