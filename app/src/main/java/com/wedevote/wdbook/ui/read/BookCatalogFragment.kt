package com.wedevote.wdbook.ui.read

import android.os.Bundle
import android.os.Handler
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.aquila.lib.base.BaseRecycleAdapter
import com.aquila.lib.base.BaseViewHolder
import com.aquila.lib.layout.util.DensityUtil
import com.aquila.lib.widget.view.CustomRecyclerView
import com.wedevote.wdbook.R
import com.wedevote.wdbook.base.APPConfig
import com.wedevote.wdbook.base.BaseRootFragment
import com.wedevote.wdbook.entity.BookCatalogEntity
import com.wedevote.wdbook.tools.util.AnimatorUtil
import com.wedevote.wdbook.ui.read.lib.EPubBook

/***
 * @date 创建时间 2020/5/27 14:40
 * <AUTHOR> <PERSON><PERSON>
 * @description 书籍的目录
 */
@Suppress("DEPRECATION")
class BookCatalogFragment(
    val currentPath: String,
    val contentsDialogData: ArrayList<BookCatalogEntity>
) : BaseRootFragment() {

    lateinit var dataRecycleView: CustomRecyclerView

    lateinit var textView: TextView
    lateinit var bookCatalogAdapter: BookCatalogAdapter
    var dialoglistener: BookContentsDialog.ContentsDialogListener? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        val v = inflater.inflate(R.layout.fragment_child_book_catalog_layout, container, false)
        dataRecycleView = v.findViewById(R.id.book_notes_catalog_RecyclerView)
        textView = v.findViewById(R.id.book_notes_catalog_TextView)
        textView.setPadding(0, 0, 0, 0)
        val recycledViewPool = dataRecycleView.recycledViewPool
        dataRecycleView.setRecycledViewPool(recycledViewPool)
        dataRecycleView.layoutManager?.isAutoMeasureEnabled = false

        dataRecycleView.setHasFixedSize(true)

        bookCatalogAdapter = BookCatalogAdapter(recycledViewPool)
        dataRecycleView.adapter = bookCatalogAdapter

        bookCatalogAdapter.dataList = createCatalogData(contentsDialogData)
        scrollToTitle()
        return v
    }

    private fun scrollToTitle() {
        Handler().postDelayed({
            var outIndex: Int = 0
            var insideIndex: Int = 0
            val bookCatalogEntities = bookCatalogAdapter.dataList as ArrayList<BookCatalogEntity>
            for (index in 0 until bookCatalogEntities.size) {
                if (bookCatalogEntities[index].expand) {
                    outIndex = index
                    val subCatalogList = bookCatalogEntities.get(/* index = */ index).subCatalogList
                    if (!subCatalogList.isNullOrEmpty()) {
                        for (i in 0 until subCatalogList.size) {
                            if (subCatalogList[i].path.startsWith(currentPath) && !includeCurrentPath(
                                    subCatalogList[i].subCatalogList
                                )
                            ) {
                                insideIndex = i
                                break
                            }
                        }
                    }
                    break
                }
            }
            dataRecycleView.scrollBy(
                0,
                DensityUtil.dp2px(((outIndex + insideIndex) * 50).toFloat())
            )
        }, 10)
    }

    fun setOnDialogListener(listener: BookContentsDialog.ContentsDialogListener?) {
        dialoglistener = listener
    }

    private fun includeCurrentPath(catalogList: ArrayList<BookCatalogEntity>?): Boolean {
        if (catalogList == null) {
            return false
        }
        for (catalog in catalogList) {
            if (catalog.path.startsWith(currentPath)) {
                return true
            } else if (includeCurrentPath(catalog.subCatalogList)) {
                return true
            }
        }
        return false
    }

    private fun createCatalogData(contentsDialogData: ArrayList<BookCatalogEntity>): ArrayList<BookCatalogEntity> {
        setContentsData(contentsDialogData)
        val list: ArrayList<BookCatalogEntity> = ArrayList<BookCatalogEntity>()
        list.addAll(contentsDialogData)
        insertSubCatalogList(list, 1)
        return list
    }

    private fun insertSubCatalogList(contentsDialogData: ArrayList<BookCatalogEntity>, level: Int) {
        for (i in contentsDialogData.indices) {
            val currentItem = contentsDialogData[i]
            val subCatalogList = currentItem.subCatalogList

            if (!subCatalogList.isNullOrEmpty() && currentItem.expand && currentItem.level == level) {
                // 将 subCatalogList 列表数据插入到当前元素后一位的位置
                contentsDialogData.addAll(i + 1, subCatalogList)
                // 递归调用，处理插入的 subCatalogList 列表数据
                insertSubCatalogList(contentsDialogData, level + 1)
            }
        }
    }

    private fun setContentsData(contentsDialogData: ArrayList<BookCatalogEntity>): Boolean {
        var isExpand = false

        for (t in contentsDialogData) {
            if (t.path.startsWith(currentPath)) {
                t.expand = true
                isExpand = true
            } else {
                t.expand = false
            }
            if (!t.subCatalogList.isNullOrEmpty()) {
                isExpand = setContentsData(t.subCatalogList!!)

                if (isExpand) {
                    t.expand = true
                }
            } else {
                if (t.path.startsWith(currentPath)) {
                    t.expand = true
                    isExpand = true
                } else {
                    t.expand = false
                }
            }

            if (isExpand) {
                return true
            }
        }

        return isExpand
    }

    /***
     *@date 创建时间 2020/5/27 15:08
     *<AUTHOR> W.YuLong
     *@description
     */
    inner class BookCatalogAdapter(var recycledViewPool: RecyclerView.RecycledViewPool) :
        BaseRecycleAdapter<BookCatalogEntity, BookCatalogViewHolder>() {
        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BookCatalogViewHolder {
            return BookCatalogViewHolder(parent, recycledViewPool)
        }
    }

    /***
     *@date 创建时间 2020/5/27 15:08
     *<AUTHOR> W.YuLong
     *@description 书籍的目录item
     */
    inner class BookCatalogViewHolder(
        parent: ViewGroup,
        var recycledViewPool: RecyclerView.RecycledViewPool
    ) :
        BaseViewHolder(parent, R.layout.holder_item_book_catalog_layout) {
        private val flagImageView: ImageView =
            itemView.findViewById(R.id.item_catalog_triangle_ImageView)
        private val nameTextView: TextView = itemView.findViewById(R.id.item_catalog_title_TextView)
        private val descTextView: TextView = itemView.findViewById(R.id.item_catalog_desc_TextView)

        var recyclerHeight: Int = 0
        lateinit var item: BookCatalogEntity

        override fun <T> initUIData(t: T) {
            dataRecycleView.setRecycledViewPool(recycledViewPool)
            dataRecycleView.layoutManager?.isAutoMeasureEnabled = false
            item = t as BookCatalogEntity
            if (item.subCatalogList.isNullOrEmpty()) {
                recyclerHeight = 0
                flagImageView.visibility = View.INVISIBLE
            } else {
                flagImageView.visibility = View.VISIBLE

                if (item.expand) {
                    AnimatorUtil.rotateAnimator(flagImageView, -90f, 0f, 0L)
                } else {
                    AnimatorUtil.rotateAnimator(flagImageView, 0f, -90f, 0L)
                }

                flagImageView.setOnClickListener {
                    if (!item.expand) {
                        AnimatorUtil.rotateAnimator(flagImageView, -90f, 0f, 150L)
                        expandAnimator()
                        item.expand = true
                    } else {
                        AnimatorUtil.rotateAnimator(flagImageView, 0f, -90f, 150L)
                        foldAnimator()
                        item.expand = false
                    }
                }
            }

            itemView.setPadding((item.level - 1) * DensityUtil.dp2px(15F), 0, 0, 0)
            nameTextView.text = t.name
            descTextView.text = EPubBook.getPageString(item.pathIndex)
            nameTextView.setOnClickListener {
                t.path.let { it1 -> dialoglistener?.onCallBack(it1) }
            }

            if (item.path.startsWith(currentPath) && !includeCurrentPath(item.subCatalogList)) {
                activity?.resources?.getColor(R.color.color_select_orange)?.let {
                    nameTextView.setTextColor(
                        it
                    )
                }
            } else if (!APPConfig.isCurrentThemeLight()) {
                activity?.resources?.getColor(R.color.color_dark_F0F0F2)?.let {
                    nameTextView.setTextColor(
                        it
                    )
                }
            } else {
                activity?.resources?.getColor(R.color.text_color_black_33)?.let {
                    nameTextView.setTextColor(
                        it
                    )
                }
            }
        }

        private fun includeCurrentPath(catalogList: ArrayList<BookCatalogEntity>?): Boolean {
            if (catalogList == null) {
                return false
            }
            for (catalog in catalogList) {
                if (catalog.path.startsWith(currentPath)) {
                    return true
                } else if (includeCurrentPath(catalog.subCatalogList)) {
                    return true
                }
            }
            return false
        }

        fun expandAnimator() {
            bookCatalogAdapter.dataList?.get(position)!!.expand = true
            val list: ArrayList<BookCatalogEntity> = ArrayList<BookCatalogEntity>()
            bookCatalogAdapter.dataList?.let { list.addAll(it) }
            if (!item.subCatalogList.isNullOrEmpty()) {
                for (data in item.subCatalogList!!) {
                    data.expand = false
                }
            }
            item.subCatalogList?.let { list.addAll(position + 1, it) }
            bookCatalogAdapter.dataList = list
            bookCatalogAdapter.notifyDataSetChanged()


//            dataRecyclerView.visibility = View.INVISIBLE
//            if (recyclerHeight == 0) {
//                recyclerHeight = dataRecyclerView.height
//            }
//            val anim = ValueAnimator.ofInt(0, recyclerHeight)
//            anim.addListener(object : Animator.AnimatorListener {
//                override fun onAnimationRepeat(animation: Animator) {
//                }
//
//                override fun onAnimationEnd(animation: Animator) {
//                    // 动画结束后需要将布局高度设置为自适应，不然下一级展开UI不会变化
//                    var layoutParams: ViewGroup.LayoutParams = dataRecyclerView.layoutParams
//                    layoutParams.height = ViewGroup.LayoutParams.WRAP_CONTENT
//                    dataRecyclerView.layoutParams = layoutParams
//                }
//
//                override fun onAnimationCancel(animation: Animator) {
//                }
//
//                override fun onAnimationStart(animation: Animator) {
//                    dataRecyclerView.visibility = View.VISIBLE
//                }
//            })
//            anim.addUpdateListener(object : ValueAnimator.AnimatorUpdateListener {
//                var value: Int = 0
//                override fun onAnimationUpdate(animation: ValueAnimator) {
//                    value = animation.animatedValue as Int
//                    var layoutParams: ViewGroup.LayoutParams = dataRecyclerView.layoutParams
//                    layoutParams.height = value
//                    dataRecyclerView.layoutParams = layoutParams
//                }
//            })
//            anim.duration = 250
//            anim.start()
        }

        fun foldAnimator() {
            bookCatalogAdapter.dataList?.get(position)!!.expand = false
            val list: ArrayList<BookCatalogEntity> = ArrayList<BookCatalogEntity>()
            bookCatalogAdapter.dataList?.let { list.addAll(it) }
            removeSubCatalogList(item, list)

            bookCatalogAdapter.dataList = list
            bookCatalogAdapter.notifyDataSetChanged()

//            val anim = ValueAnimator.ofInt(recyclerHeight, 0)
//            anim.addListener(object : Animator.AnimatorListener {
//                override fun onAnimationRepeat(animation: Animator) {
//                }
//
//                override fun onAnimationEnd(animation: Animator) {
//                    dataRecyclerView.visibility = View.GONE
//                }
//
//                override fun onAnimationCancel(animation: Animator) {
//                }
//
//                override fun onAnimationStart(animation: Animator) {
//                }
//            })
//            anim.addUpdateListener(object : ValueAnimator.AnimatorUpdateListener {
//                var value: Int = 0
//                override fun onAnimationUpdate(animation: ValueAnimator) {
//                    value = animation.animatedValue as Int
//                    val layoutParams: ViewGroup.LayoutParams = dataRecyclerView.layoutParams
//                    layoutParams.height = value
//                    dataRecyclerView.layoutParams = layoutParams
//                }
//            })
//            anim.duration = 250
//            anim.start()
        }

        private fun removeSubCatalogList(
            entity: BookCatalogEntity,
            contentsDialogData: ArrayList<BookCatalogEntity>
        ) {
            val subCatalogList = entity.subCatalogList

            if (!subCatalogList.isNullOrEmpty()) {
                contentsDialogData.removeAll(subCatalogList)

                for (subEntity in subCatalogList) {
                    removeSubCatalogList(subEntity, contentsDialogData)
                }
            }
        }
    }
}
