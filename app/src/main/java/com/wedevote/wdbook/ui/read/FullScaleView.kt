package com.wedevote.wdbook.ui.read

import android.app.Activity
import android.content.Context
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Paint
import android.util.AttributeSet
import android.view.GestureDetector
import android.view.GestureDetector.SimpleOnGestureListener
import android.view.MotionEvent
import android.view.ScaleGestureDetector
import android.view.ScaleGestureDetector.SimpleOnScaleGestureListener
import android.view.View

class FullScaleView(context: Context, attrs: AttributeSet? = null) : View(context, attrs) {
    private var scaleGestureDetector: ScaleGestureDetector? = null
    private var scaleFactor = 0f
    private var posX = 0f
    private var posY = 0f
    private var viewWidth = 0
    private var viewHeight = 0
    private var widthScale = 0f
    private var heightScale = 0f
    private val paint = Paint()
    private var bitmap: Bitmap? = null
    private var activity: Activity? = null
    private var imgWidth = 0f
    private var imgHeight = 0f
    private var gestureDetector: GestureDetector? = null
    private var prevX = 0f
    private var prevY = 0f

    init {
        scaleGestureDetector = ScaleGestureDetector(context, ImageSimpleScaleOnGestureDetector())
        gestureDetector = GestureDetector(context, ImageSimpleOnGestureDetector())
    }

    private fun initSize(width: Int, height: Int) {
        viewWidth = width
        viewHeight = height
        widthScale = viewWidth / imgWidth
        heightScale = viewHeight / imgHeight
        scaleFactor = Math.min(widthScale, heightScale)
        posX = viewWidth / 2 - imgWidth / 2
        posY = viewHeight / 2 - imgHeight / 2
    }

    private fun checkBounds() {
        if (scaleFactor > widthScale) {
            posX = Math.min(posX, (scaleFactor - 1) * (imgWidth / 2))
            posX = Math.max(posX, viewWidth - imgWidth - (scaleFactor - 1) * (imgWidth / 2))
        } else {
            posX = Math.max(posX, (scaleFactor - 1) * (imgWidth / 2))
            posX = Math.min(posX, viewWidth - imgWidth - (scaleFactor - 1) * (imgWidth / 2))
        }
        if (scaleFactor > heightScale) {
            posY = Math.min(posY, (scaleFactor - 1) * (imgHeight / 2))
            posY = Math.max(posY, viewHeight - imgHeight - (scaleFactor - 1) * (imgHeight / 2))
        } else {
            posY = Math.max(posY, (scaleFactor - 1) * (imgHeight / 2))
            posY = Math.min(posY, viewHeight - imgHeight - (scaleFactor - 1) * (imgHeight / 2))
        }
    }

    private fun isOutOfPicture(x: Float, y: Float): Boolean {
        val width = imgWidth / 2 * scaleFactor
        val height = imgHeight / 2 * scaleFactor
        val centerX = posX + imgWidth / 2
        val centerY = posY + imgHeight / 2
        return x < centerX - width || x > centerY + width || y < centerY - height || y > centerY + height
    }

    override fun onTouchEvent(event: MotionEvent): Boolean {
        scaleGestureDetector!!.onTouchEvent(event) // 双指缩放
        gestureDetector!!.onTouchEvent(event) // 单指移动
        when (event.actionMasked) {
            MotionEvent.ACTION_DOWN -> {
                prevX = event.x
                prevY = event.y
            }

            MotionEvent.ACTION_UP -> {
                val x = event.x
                val y = event.y
                if ((x - prevX) * (x - prevX) + (y - prevY) * (y - prevY) < 50) {
                    if (isOutOfPicture(x, y) && activity != null) {
                        activity!!.onBackPressed()
                    } else if (scaleFactor > 1.0f) {
                        initSize(viewWidth, viewHeight)
                        invalidate()
                    }
                }
            }
        }
        return true
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)

        if (bitmap != null) {
            canvas.save()
            checkBounds()
            canvas.scale(scaleFactor, scaleFactor, posX + imgWidth / 2, posY + imgHeight / 2)
            canvas.drawBitmap(bitmap!!, posX, posY, paint)
            canvas.restore()
        }
    }

    fun setImage(activity: Activity, bitmap: Bitmap, w: Int, h: Int) {
        this.bitmap = bitmap
        this.activity = activity
        imgWidth = bitmap.width.toFloat()
        imgHeight = bitmap.height.toFloat()
        initSize(w, h)
        invalidate()
    }

    internal inner class ImageSimpleOnGestureDetector : SimpleOnGestureListener() {
        override fun onScroll(
            e1: MotionEvent?,
            e2: MotionEvent,
            distanceX: Float,
            distanceY: Float
        ): Boolean {
            posX -= distanceX
            posY -= distanceY
            invalidate()
            return true
        }
    }

    internal inner class ImageSimpleScaleOnGestureDetector : SimpleOnScaleGestureListener() {
        override fun onScale(detector: ScaleGestureDetector): Boolean {
            scaleFactor *= detector.scaleFactor
            scaleFactor =
                if (scaleFactor < 0.75f) 0.75f else if (scaleFactor > 3f) 3f else scaleFactor
            invalidate()
            return true
        }
    }
}
