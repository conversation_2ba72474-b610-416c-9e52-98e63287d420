package com.wedevote.wdbook.ui.read.lib

import android.graphics.Typeface
import android.os.Handler
import android.os.Looper
import com.aquila.lib.tools.singleton.ThreadPoolSingleton
import com.wedevote.wdbook.base.SDKSingleton
import com.wedevote.wdbook.tools.define.TextFontType
import com.wedevote.wdbook.tools.util.getFunctionInfo
import kotlin.collections.ArrayList

/***
 * @date 重构时间 2020/10/28 10:23
 * <AUTHOR> W.YuLong
 * @description 计算书籍页码的Helper类
 */
class PageInfoCalculateHelper {
    private var totalPageCount = 0
    private var pageEndList = ArrayList<Int>()
    private var isInCalculating = false
    private var breakCalculate = false
    private var calculateCompleteListener: OnCalculateCompleteListener? = null

    fun setListener(listener: OnCalculateCompleteListener?) {
        calculateCompleteListener = listener
    }

    private fun doCalculateInThread(pageInfoIndex: Int, fileId: String, textSize: Float, textFont: Int) {
        isInCalculating = true
        breakCalculate = false
        totalPageCount = 0
        val pageInfoList = ArrayList<Int>()

        for (i in 0 until EPubBook.pathList.size) {
            val content: String = EPubBook.getText(i)
            val textPageCalculateHelper = BookTextControllerHelper()
            textPageCalculateHelper.setTextSize(textSize)
            if (textFont == TextFontType.SERIF.value) {
                textPageCalculateHelper.setTextFontType(EPubBook.typefaceSerif)
            } else {
                textPageCalculateHelper.setTextFontType(Typeface.DEFAULT)
            }
            textPageCalculateHelper.parseText(content, !EPubBook.isCopyrightPage(i), false)
            totalPageCount += textPageCalculateHelper.getChapterPageCount()
            pageInfoList.add(totalPageCount)

            if (breakCalculate) {
                return
            }
        }

        val sb = StringBuilder()
        sb.append(totalPageCount)
        for (pageEnd in pageInfoList) {
            sb.append('#')
            sb.append(pageEnd)
        }
        SDKSingleton.dbWrapBl.saveFilePageInfo(fileId, pageInfoIndex, sb.toString())

        Handler(Looper.getMainLooper()).post {
            isInCalculating = false
            // 等页码信息运算完成之后，再赋给全局
            pageEndList = pageInfoList
            EPubBook.setPageInfo(pageInfoIndex, pageEndList)
            calculateCompleteListener?.onCalculateComplete()
        }
    }

    var tempTotalCount = 0
    private fun pageInfoString2List(pageInfo: String): ArrayList<Int> {
        val infoArray = pageInfo.split("#")
        var pageInfoList = ArrayList<Int>()
        if (infoArray.size == EPubBook.pathList.size + 1) {
            tempTotalCount = infoArray[0].toInt()
            for (i in 1 until infoArray.size) {
                pageInfoList.add(infoArray[i].toInt())
            }
            // 等页码信息运算完成之后，再赋给全局
        }
        return pageInfoList
    }

    fun calculateBookPageInfo(fileId: String, textSize: Float, textFont: Int) {
        val pageInfoIndex = EPubBook.getCurrentPageInfoIndex()
        var pageInfo = SDKSingleton.dbWrapBl.getFilePageInfo(fileId, pageInfoIndex)
        var isSuccessInit = false

        if (!pageInfo.isNullOrEmpty()) {
            pageEndList = pageInfoString2List(pageInfo)
            totalPageCount = tempTotalCount
            if (pageEndList.isNotEmpty()) {
                isSuccessInit = true
            }
        }

        EPubBook.setPageInfo(pageInfoIndex, pageEndList)
        if (isSuccessInit) {
            return
        }

        ThreadPoolSingleton.executeTask {
            breakCalculate = true
            synchronized(this) {
                breakCalculate = false
                try {
                    doCalculateInThread(pageInfoIndex, fileId, textSize, textFont)
                } catch (e: Exception) {
                    e.printStackTrace()
                    SDKSingleton.loggerBl.handleThrowable(e, getFunctionInfo())
                }
            }
        }
    }

    fun interruptCalculation() {
        breakCalculate = true
    }

    fun getCurrentPageIndex(page: Int, indexInPage: Int): Int {
        return if (page == 0 || pageEndList.isNullOrEmpty()) {
            (1 + indexInPage)
        } else {
            (pageEndList[page - 1] + indexInPage + 1)
        }
    }

    fun getPercent(page: Int, indexInPage: Int): Int {
        val total = totalPageCount
        if (total == 0) {
            return 0
        }
        return if (isInCalculating) {
            if (EPubBook.pathList.size != 0) {
                (page + 1) * 100 / EPubBook.pathList.size
            } else {
                0
            }
        } else getCurrentPageIndex(page, indexInPage) * 100 / total
    }

    fun getPageString(page: Int, indexInPage: Int): String {
        return if (isInCalculating || pageEndList.isNullOrEmpty()) {
            if (EPubBook.pathList.size > EPubBook.bigBookSize) {
                ""
            } else {
                "页码计算中..."
            }
        } else {
            val total = totalPageCount
            "${getCurrentPageIndex(page, indexInPage)}/$total"
        }
    }
}

interface OnCalculateCompleteListener {
    fun onCalculateComplete()
}
