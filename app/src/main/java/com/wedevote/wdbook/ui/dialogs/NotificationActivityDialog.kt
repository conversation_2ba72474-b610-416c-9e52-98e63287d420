package com.wedevote.wdbook.ui.dialogs

import android.annotation.SuppressLint
import android.content.Context
import android.os.Build
import android.os.Bundle
import android.text.Html
import android.view.Gravity
import android.view.View
import android.webkit.WebResourceRequest
import android.webkit.WebSettings
import android.webkit.WebView
import android.webkit.WebViewClient
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.webkit.WebSettingsCompat
import androidx.webkit.WebViewFeature
import com.aquila.lib.tools.singleton.SPSingleton
import com.wedevote.wdbook.R
import com.wedevote.wdbook.base.SDKSingleton
import com.wedevote.wdbook.entity.notification.NotificationDetailEntity
import com.wedevote.wdbook.entity.notification.NotificationEntity
import com.wedevote.wdbook.tools.util.APPUtil
import com.wedevote.wdbook.tools.util.SPKeyDefine
import com.wedevote.wdbook.ui.user.notification.NotificationDetailActivity

/***
 * @date 创建时间 2022/6/24 15:34
 * <AUTHOR> W.YuLong
 * @description
 */
class NotificationActivityDialog(context: Context) : BaseDialog(context), View.OnClickListener {
    lateinit var titleTextView: TextView
    lateinit var subTitleTextView: TextView

    //    lateinit var contentTextView: TextView
    lateinit var closeTextView: TextView
    lateinit var viewTextView: TextView
    lateinit var webView: WebView

    var notificationEntity: NotificationEntity? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.dialog_notification_activity_layout)
        inifViewFromXML()
        closeTextView.setOnClickListener(this)
        viewTextView.setOnClickListener(this)
        configDialog(Gravity.CENTER)
        configWebViewSetting()

    }

    private fun inifViewFromXML() {
        titleTextView = findViewById(R.id.notification_activity_title_TextView)
        subTitleTextView = findViewById(R.id.notification_activity_sub_title_TextView)
//        contentTextView = findViewById(R.id.notification_activity_content_TextView)
        webView = findViewById(R.id.notification_activity_content_WebView)
        closeTextView = findViewById(R.id.notification_activity_close_TextView)
        viewTextView = findViewById(R.id.notification_activity_view_TextView)
    }

    fun configWebViewSetting() {
        //支持javascript
        webView.settings.setJavaScriptEnabled(true);
        // 设置可以支持缩放
        webView.settings.setSupportZoom(true);
        // 设置出现缩放工具
        webView.settings.setBuiltInZoomControls(true);
        //扩大比例的缩放
        webView.settings.setUseWideViewPort(true);
        webView.settings.setSupportZoom(false);
        //自适应屏幕
        webView.settings.setLayoutAlgorithm(WebSettings.LayoutAlgorithm.SINGLE_COLUMN);
        webView.settings.setLoadWithOverviewMode(true);

        if (WebViewFeature.isFeatureSupported(WebViewFeature.FORCE_DARK)) {
            val isDark = !SDKSingleton.appBl.isCurrentThemeLight()
            WebSettingsCompat.setForceDark(
                webView.settings,
                if (isDark) WebSettingsCompat.FORCE_DARK_ON else WebSettingsCompat.FORCE_DARK_OFF
            )
        }
        webView.webViewClient = webClient
    }

    val webClient = object : WebViewClient() {
        override fun shouldOverrideUrlLoading(
            view: WebView?,
            request: WebResourceRequest?
        ): Boolean {
            return true
        }
    }

    @SuppressLint("NewApi")
    fun initUIData(entity: NotificationEntity, notificationDetail: NotificationDetailEntity?) {
        notificationEntity = entity
        titleTextView.text = entity.title
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            subTitleTextView.text = Html.fromHtml(entity.subTitle, Html.FROM_HTML_OPTION_USE_CSS_COLORS)
        } else {
            subTitleTextView.text = Html.fromHtml(entity.subTitle)
        }
        if (notificationDetail != null) {
            val url = getNoEffectLink(notificationDetail.content)
            webView.loadDataWithBaseURL("", url, "text/html", "UTF-8", "")
            val params = webView.layoutParams
            params.height = ConstraintLayout.LayoutParams.WRAP_CONTENT
            webView.layoutParams = params
        }
        SPSingleton.get().putString(SPKeyDefine.SP_ClosedDialogMessageId, APPUtil.formatNotifyMsgId(entity))
    }

    private fun getNoEffectLink(url: String): String {
        var noEffectUrl = url
        val indexOne = url.indexOf("<a href=", 0)
        val indexTwo = url.indexOf("</a>", 0) + 4
        if (indexOne > 0 && indexTwo > indexOne) {
            val substringOne = url.substring(indexOne, indexTwo)
            val startIndex = substringOne.indexOf("\">", 0) + 2
            val endIndex = substringOne.indexOf("</a>", 0)
            if (startIndex > 0 && endIndex > startIndex) {
                val substringTwo = substringOne.substring(startIndex, endIndex)
                val url1 = noEffectUrl.replace(substringOne, substringTwo)
                return getNoEffectLink(url1)
            } else {
                return noEffectUrl
            }
        } else {
            return noEffectUrl
        }
    }

    override fun onClick(v: View?) {
        when (v) {
            closeTextView -> {
                dismiss()
            }
            viewTextView -> {
                if (notificationEntity != null) {
                    NotificationDetailActivity.gotoNotificationDetail(
                        context,
                        notificationEntity!!.id,
                        notificationEntity!!.type,
                        null
                    )
                }
                dismiss()
            }

        }
    }


}