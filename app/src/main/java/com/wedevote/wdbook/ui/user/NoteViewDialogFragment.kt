package com.wedevote.wdbook.ui.user

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.ObjectAnimator
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.ImageView
import android.widget.TextView
import androidx.fragment.app.DialogFragment
import com.aquila.lib.base.OnViewClickListener
import com.aquila.lib.dialog.CommAlertDialog
import com.aquila.lib.tools.util.ToastUtil
import com.aquila.lib.widget.view.DotView
import com.gyf.immersionbar.ktx.statusBarHeight
import com.wedevote.wdbook.R
import com.wedevote.wdbook.base.APPConfig
import com.wedevote.wdbook.base.ExceptionHandler
import com.wedevote.wdbook.base.SDKSingleton
import com.wedevote.wdbook.constants.DataStatus
import com.wedevote.wdbook.entity.NoteEntity
import com.wedevote.wdbook.tools.util.IntentConstants
import com.wedevote.wdbook.tools.util.UnitFormatUtil
import com.wedevote.wdbook.tools.util.parseColor
import com.wedevote.wdbook.ui.widgets.CommTopTitleLayout
import com.wedevote.wdbook.utils.JsonUtility
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch

/***
 * @date 创建时间 2021/8/18 17:00
 * <AUTHOR> W.YuLong
 * @description
 */
class NoteViewDialogFragment : DialogFragment(), View.OnClickListener {
    lateinit var dotView: DotView
    lateinit var dateTextView: TextView
    lateinit var quoteTextView: TextView
    lateinit var noteTextView: TextView
    lateinit var deleteImageView: ImageView
    lateinit var nextButton: Button
    lateinit var rootContainerLayout: View
    lateinit var titleLayout: CommTopTitleLayout

    var onNoteOperateListener: OnNoteOperateListener? = null
    var bookJumpListener: OnViewClickListener? = null

    lateinit var noteEntity: NoteEntity
    var bookNoteList: List<NoteEntity>? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL, R.style.Dialog_FullScreen)
        var noteEntityJson = arguments?.getString(IntentConstants.EXTRA_NoteEntity)
        noteEntityJson?.let {
            noteEntity = JsonUtility.decodeFromString(it)
            bookNoteList = SDKSingleton.dbWrapBl.getNoteEntityList(noteEntity.resourceId, 0, 100)
        }
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        rootContainerLayout = inflater.inflate(R.layout.dialog_view_note_detial_layout, container, false)
        rootContainerLayout.setPadding(0, statusBarHeight, 0, 0)
        initViewFromXML(rootContainerLayout)
        setViewListeners()
        initNoteDataUI(noteEntity)
        return rootContainerLayout
    }

    private fun setViewListeners() {
        deleteImageView.setOnClickListener(this)
        nextButton.setOnClickListener(this)
        quoteTextView.setOnClickListener(this)
        noteTextView.setOnClickListener(this)
        titleLayout.backImageView.setOnClickListener(this)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        rootContainerLayout.post {
            ObjectAnimator.ofFloat(rootContainerLayout, "translationY", rootContainerLayout.height.toFloat(), 0f).apply {
                duration = 300
                addListener(object : AnimatorListenerAdapter() {
                    override fun onAnimationStart(animation: Animator) {
                        super.onAnimationStart(animation)
                        rootContainerLayout.visibility = View.VISIBLE
                    }
                })
            }.start()
        }
    }

    private fun initViewFromXML(v: View) {
        titleLayout = v.findViewById(R.id.dialog_note_detail_title_layout)
        dotView = v.findViewById(R.id.dialog_note_detail_color_DotView)
        dateTextView = v.findViewById(R.id.dialog_note_detail_date_TextView)
        quoteTextView = v.findViewById(R.id.dialog_note_detail_quote_TextView)
        noteTextView = v.findViewById(R.id.dialog_note_detail_note_TextView)
        deleteImageView = v.findViewById(R.id.dialog_note_detail_delete_ImageView)
        nextButton = v.findViewById(R.id.dialog_note_detail_next_Button)
    }

    fun initNoteDataUI(noteEntity: NoteEntity) {
        dotView.setDotColor(parseColor(noteEntity.highlightColorType))
        dateTextView.text = UnitFormatUtil.formatDate_ymdhm(noteEntity.lastUpdateTime)
        quoteTextView.text = noteEntity.getDisplaySummery()
        noteTextView.text = noteEntity.noteText
    }

    override fun onClick(v: View?) {
        when (v) {
            titleLayout.backImageView -> {
                dismiss()
            }
            quoteTextView -> {
                bookJumpListener?.onClickAction(v, "", noteEntity)
            }
            deleteImageView -> {
                showDeleteDialog()
            }
            noteTextView -> {
                editNote(noteEntity)
            }
            nextButton -> {
                var nextEntity = findNextNote(noteEntity)
                if (nextEntity != null) {
                    noteEntity = nextEntity
                    initNoteDataUI(noteEntity)
                } else {
                    ToastUtil.showToastShort(R.string.the_last_note)
                }
            }
        }
    }

    var isOnPaused = false
    override fun onPause() {
        super.onPause()
        isOnPaused = true
    }

    override fun onResume() {
        super.onResume()
        if (isOnPaused) {
            var entity = SDKSingleton.dbWrapBl.getNoteEntityByDataId(noteEntity.dataId!!, dataStatus = DataStatus.NORMAL.value)
            if (entity != null) {
                noteEntity = entity
                bookNoteList = SDKSingleton.dbWrapBl.getNoteEntityList(noteEntity.resourceId, 0, 100)
                initNoteDataUI(noteEntity)
            } else {
                dismiss()
            }
        }

        isOnPaused = false
    }

    private fun editNote(entity: NoteEntity) {
        NoteEditActivity.gotoNoteEdit(
            requireActivity(), entity,
            object : OnNoteEditCallback {
                override fun onNoteEditFinish(callbackEntity: NoteEntity) {
                    onNoteOperateListener?.onNoteEdit()
                }
            }
        )
    }

    fun findNextNote(entity: NoteEntity): NoteEntity? {
        var result: NoteEntity? = null
        bookNoteList?.let {
            for (i in it.indices) {
                if (it[i].dataId == entity.dataId) {
                    if (i < it.size - 1) {
                        result = it[i + 1]
                        break
                    } else {
                        result = it[0]
                    }
                }
            }
        }
        return result
    }

    fun showDeleteDialog() {
        CommAlertDialog.with(requireActivity())
            .setTitle(R.string.delete_note).setMessage(R.string.sure_to_delete_note)
            .setStartText(R.string.label_cancel).setEndText(R.string.label_delete)
            .setRightColorRes(if (APPConfig.isCurrentThemeLight()) R.color.color_red_E33733 else R.color.color_red_A5343C)
            .setOnViewClickListener { d, v, tag ->
                if (tag == CommAlertDialog.TAG_CLICK_END) {
                    SDKSingleton.dbWrapBl.removeNote(noteEntity.dataId!!)
                    onNoteOperateListener?.onNoteEdit()
                    MainScope().launch(ExceptionHandler.coroutineExceptionHandler) {
                        SDKSingleton.syncBl.uploadNoteData()
                        <EMAIL>()
                        ToastUtil.showToastShort(R.string.has_deleted)
                    }
                }
            }.showDialog()
    }
}
