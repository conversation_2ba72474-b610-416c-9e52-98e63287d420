package com.wedevote.wdbook.ui.user

import android.os.Bundle
import android.view.View
import android.widget.Button
import com.aquila.lib.dialog.CommAlertDialog
import com.wedevote.wdbook.R
import com.wedevote.wdbook.base.ExceptionHandler
import com.wedevote.wdbook.base.RootActivity
import com.wedevote.wdbook.base.SDKSingleton
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch

/***
 * @date 创建时间 2022/11/25 16:00
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON>
 * @description
 */
class DeleteAccountResultActivity : RootActivity() {
    lateinit var executeButton: Button
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_delete_account_result_layout)
    }
    
}