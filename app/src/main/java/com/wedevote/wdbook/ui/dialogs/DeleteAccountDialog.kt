package com.wedevote.wdbook.ui.dialogs

import android.content.Context
import android.os.Bundle
import android.view.Gravity
import android.view.View
import android.widget.TextView
import com.aquila.lib.dialog.OnDialogViewClickListener
import com.wedevote.wdbook.R

class DeleteAccountDialog(context: Context) : BaseDialog(context), View.OnClickListener {
    lateinit var cancelTextView: TextView
    lateinit var deleteTextView: TextView
    var onViewClickListener: OnDialogViewClickListener? = null
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.dialog_delete_prompt_layout)
        cancelTextView = findViewById(R.id.delete_account_cancel_TextView)
        deleteTextView = findViewById(R.id.delete_account_request_TextView)
        
        configDialog(Gravity.CENTER)
        cancelTextView.setOnClickListener(this)
        deleteTextView.setOnClickListener(this)
    }
    
    override fun onClick(v: View?) {
        when (v) {
            cancelTextView -> {
                dismiss()
            }
            deleteTextView -> {
                onViewClickListener?.onViewClick(this, deleteTextView, 0)
            }
        }
    }
}