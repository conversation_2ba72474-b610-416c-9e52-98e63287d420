package com.wedevote.wdbook.ui.store

import android.view.ViewGroup
import android.widget.TextView
import com.aquila.lib.base.BaseRecycleAdapter
import com.aquila.lib.base.BaseViewHolder
import com.wedevote.wdbook.R
import com.wedevote.wdbook.entity.store.ProductDetailEntity

/***
 *@date 创建时间 2021/7/27 5:07 下午
 *<AUTHOR> <PERSON><PERSON>
 *@description
 */
class ReferenceRecyclerAdapter : BaseRecycleAdapter<ProductDetailEntity, ReferenceViewHolder>() {
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ReferenceViewHolder {
        return ReferenceViewHolder(parent)
    }
}

/***
 *@date 创建时间 2021/7/27 5:07 下午
 *<AUTHOR> <PERSON><PERSON>
 *@description
 */
class ReferenceViewHolder(parent: ViewGroup) :
    BaseViewHolder(parent, R.layout.item_book_detail_reference_layout) {
    val titleTextView: TextView = itemView.findViewById(R.id.reference_title_TextView)
    override fun <T> initUIData(t: T) {
        t as ProductDetailEntity
        titleTextView.text = t.title
        itemView.setOnClickListener { v ->
            BookDetailActivity.gotoBookDetail(v.context, t.productId)
        }
    }
}
