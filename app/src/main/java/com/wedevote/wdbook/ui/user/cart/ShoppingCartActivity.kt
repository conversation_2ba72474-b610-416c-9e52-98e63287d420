package com.wedevote.wdbook.ui.user.cart

import android.os.Bundle
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.CheckBox
import android.widget.CompoundButton
import android.widget.LinearLayout
import android.widget.TextView
import com.aquila.lib.layout.SmartRefreshLayout
import com.aquila.lib.layout.api.RefreshLayout
import com.aquila.lib.layout.listener.OnRefreshListener
import com.aquila.lib.widget.group.GroupImageTextLayout
import com.aquila.lib.widget.view.CustomRecyclerView
import com.wedevote.wdbook.R
import com.wedevote.wdbook.base.ExceptionHandler
import com.wedevote.wdbook.base.RootActivity
import com.wedevote.wdbook.base.SDKSingleton
import com.wedevote.wdbook.entity.store.CartPublisherEntity
import com.wedevote.wdbook.tools.util.FilePathUtil
import com.wedevote.wdbook.ui.user.OnDataChangeSelectListener
import com.wedevote.wdbook.ui.widgets.CommTopTitleLayout
import com.wedevote.wdbook.utils.JsonUtility
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch

/***
 * @date 创建时间 2022/9/19 22:05
 * <AUTHOR> W.YuLong
 * @description
 */
class ShoppingCartActivity : RootActivity(), View.OnClickListener, OnRefreshListener {
    lateinit var topTitleLayout: CommTopTitleLayout
    lateinit var refreshLayout: SmartRefreshLayout
    lateinit var dataRecyclerView: CustomRecyclerView
    lateinit var allCheckLayout: GroupImageTextLayout
    lateinit var totalPriceTextView: TextView
    lateinit var discountPriceTextView: TextView
    lateinit var discountDetailTextView: TextView
    lateinit var paymentButton: Button
    lateinit var emptyContainerLayout: LinearLayout
    lateinit var dataContainerLayout: ViewGroup
    lateinit var gobackButton: Button
    
    lateinit var cartAdapter: PublisherCartListAdapter
    
    lateinit var testJson: String
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_shopping_cart_layout)
        initViewFromXML()
        cartAdapter = PublisherCartListAdapter()
        dataRecyclerView.adapter = cartAdapter
        
        testJson = FilePathUtil.getTextFromAssets("cart.json")
        var dataList: ArrayList<CartPublisherEntity> = JsonUtility.decodeFromString(testJson)
        cartAdapter.dataList = dataList
        setViewListeners()
    }
    
    private fun setViewListeners() {
        allCheckLayout.setOnClickListener(this)
        paymentButton.setOnClickListener(this)
        discountDetailTextView.setOnClickListener(this)
        refreshLayout.setOnRefreshListener(this)
        cartAdapter.onPublisherDataSelectListener = object : OnDataChangeSelectListener {
            override fun isAllSelected(isAllSelected: Boolean) {
                allCheckLayout.setSelectStatus(isAllSelected)
            }
    
            override fun itemCount(count: Int) {
                if (count == 0) {
                    paymentButton.setText("结算")
                } else {
                    paymentButton.setText("结算($count)")
                }
            }
        }
    }
    
    private fun initViewFromXML() {
        topTitleLayout = findViewById(R.id.cart_top_title_layout)
        refreshLayout = findViewById(R.id.cart_item_list_RefreshLayout)
        dataRecyclerView = findViewById(R.id.cart_item_RecyclerView)
        allCheckLayout = findViewById(R.id.cart_select_all_check_Layout)
        totalPriceTextView = findViewById(R.id.cart_amount_price_TextView)
        discountPriceTextView = findViewById(R.id.cart_amount_has_discount_price_TextView)
        discountDetailTextView = findViewById(R.id.cart_amount_discount_detail_TextView)
        paymentButton = findViewById(R.id.cart_payment_Button)
        dataContainerLayout = findViewById(R.id.cart_data_container_layout)
        emptyContainerLayout = findViewById(R.id.cart_empty_container_layout)
        gobackButton = findViewById(R.id.cart_go_back_Button)
    }
    
    override fun onRefresh(layout: RefreshLayout?) {
        
        refreshLayout.finishLoadMoreAndRefresh()
    }
    
    override fun onClick(v: View?) {
        when (v) {
            discountDetailTextView -> {
            
            }
            paymentButton -> {
                cartAdapter.getAllSelectProduceList()
            }
            allCheckLayout -> {
                allCheckLayout.setSelectStatus(!allCheckLayout.getSelectStatus())
                cartAdapter.setAllDataSelectStatus(allCheckLayout.getSelectStatus())
            }
        }
    }
    
    
    fun getDataFromServer() {
        MainScope().launch(ExceptionHandler.coroutineExceptionHandler) {
            val cartList = SDKSingleton.userBl.getShoppingCartList(0)
            if (cartList.isNullOrEmpty()) {
                emptyContainerLayout.visibility = View.VISIBLE
                dataContainerLayout.visibility = View.GONE
            } else {
                emptyContainerLayout.visibility = View.GONE
                dataContainerLayout.visibility = View.VISIBLE
                cartAdapter.dataList = cartList?.toMutableList()
            }
        }
    }
    
    override fun onDestroy() {
        super.onDestroy()
        PublisherCartListAdapter.openProductId = 0
    }
    
}