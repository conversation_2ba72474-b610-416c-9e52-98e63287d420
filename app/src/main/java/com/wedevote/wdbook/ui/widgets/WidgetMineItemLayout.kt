package com.wedevote.wdbook.ui.widgets

import android.content.Context
import android.util.AttributeSet
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.wedevote.wdbook.R

/***
 * @date 创建时间 2020/4/30 16:29
 * <AUTHOR> <PERSON><PERSON>
 * @description
 */
class WidgetMineItemLayout(context: Context, attr: AttributeSet? = null) : ConstraintLayout(context, attr) {
    val imgImageView: ImageView
    val arrowImageView: ImageView
    val titleTextView: TextView
    val dataTextView: TextView
    
    init {
        View.inflate(context, R.layout.widget_mine_item_layout, this)
        imgImageView = findViewById(R.id.widget_mine_image_ImageView)
        arrowImageView = findViewById(R.id.widget_mine_right_ImageView)
        titleTextView = findViewById(R.id.widget_mine_title_TextView)
        dataTextView = findViewById(R.id.widget_mine_data_TextView)
        
        val a = context.obtainStyledAttributes(attr, R.styleable.WidgetMineItemLayout)
        var title = a.getString(R.styleable.WidgetMineItemLayout_attr_title_text)
        var data = a.getString(R.styleable.WidgetMineItemLayout_attr_data_text)
        var image = a.getDrawable(R.styleable.WidgetMineItemLayout_attr_image_src)
        var showImage = a.getBoolean(R.styleable.WidgetMineItemLayout_attr_show_image, true)
        var tint = a.getColorStateList(R.styleable.WidgetMineItemLayout_attr_image_tint)
        var dataTextColor = a.getColorStateList(R.styleable.WidgetMineItemLayout_attr_data_text_color)
        var arrowTintColorStateList = a.getColorStateList(R.styleable.WidgetMineItemLayout_attr_arrow_image_tint)
        a.recycle()
        
        if (title != null) {
            setTitle(title)
        }
        
        if (data != null) {
            setDataText(data)
        }
        if (image != null) {
            imgImageView.setImageDrawable(image)
        }
        
        if (tint != null) {
            imgImageView.imageTintList = tint
        }
        
        if (arrowTintColorStateList != null) {
            arrowImageView.imageTintList = arrowTintColorStateList
        }
        
        if (dataTextColor != null) {
            dataTextView.setTextColor(dataTextColor)
        }
        
        imgImageView.visibility = if (showImage) View.VISIBLE else View.GONE
    }
    
    fun setTitle(text: String) {
        titleTextView.text = text
    }
    
    fun setDataText(text: String) {
        dataTextView.text = text
    }
}
