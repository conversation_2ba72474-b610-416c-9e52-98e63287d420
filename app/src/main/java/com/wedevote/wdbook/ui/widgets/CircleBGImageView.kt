package com.wedevote.wdbook.ui.widgets

import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.util.AttributeSet
import androidx.appcompat.widget.AppCompatImageView
import com.wedevote.wdbook.R

/***
 * @date 创建时间 2020/11/16 18:34
 * <AUTHOR> <PERSON><PERSON>
 * @description
 */
class CircleBGImageView(context: Context, attrs: AttributeSet? = null) : AppCompatImageView(context, attrs) {

    val paint = Paint(Paint.ANTI_ALIAS_FLAG)
    var bgColor: Int = Color.TRANSPARENT
    var stokeColor: Int = Color.TRANSPARENT
    var stokeSize: Float = 0f

    init {
        attrs?.let {
            var a = context.obtainStyledAttributes(attrs, R.styleable.CircleBGImageView)
            bgColor = a.getColor(R.styleable.CircleBGImageView_attr_circle_bg_color, -1)
            stokeColor = a.getColor(R.styleable.CircleBGImageView_attr_stoke_color, -1)
            stokeSize = a.getDimension(R.styleable.CircleBGImageView_attr_stoke_size, 0f)
            isSelected = a.getBoolean(R.styleable.CircleBGImageView_attr_selected, false)
            a.recycle()
        }
    }

    fun initBgColor(color: Int) {
        bgColor = color
        invalidate()
    }

    fun initStokeColor(color: Int) {
        stokeColor = color
        invalidate()
    }

    var cx: Float = 0f
    var cy: Float = 0f
    var radius: Float = 0f
    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        cx = (w - paddingLeft - paddingRight) / 2f
        cy = (h - paddingTop - paddingBottom) / 2f
        radius = Math.min(cx, cy)
    }

    override fun onDraw(canvas: Canvas) {
        if (bgColor != -1) {
            paint.color = bgColor
            paint.style = Paint.Style.FILL
            canvas.drawCircle(cx + paddingLeft, cy + paddingTop, radius, paint)
        }

        if (isSelected && stokeSize > 0f) {
            paint.style = Paint.Style.STROKE
            paint.strokeWidth = stokeSize
            paint.color = stokeColor
            canvas.drawCircle(cx + paddingLeft, cy + paddingTop, radius, paint)
        }
        super.onDraw(canvas)
    }
}
