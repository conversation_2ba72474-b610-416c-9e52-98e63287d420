package com.wedevote.wdbook.ui.user.notification

import android.content.Intent
import android.os.Bundle
import android.view.View
import android.view.View.INVISIBLE
import android.view.View.OnClickListener
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.aquila.lib.dialog.CommAlertDialog
import com.aquila.lib.layout.SmartRefreshLayout
import com.aquila.lib.layout.api.RefreshLayout
import com.aquila.lib.layout.listener.OnLoadMoreListener
import com.aquila.lib.layout.listener.OnRefreshListener
import com.aquila.lib.tools.singleton.SPSingleton
import com.aquila.lib.tools.util.ToastUtil
import com.aquila.lib.widget.group.GroupImageTextLayout
import com.aquila.lib.widget.view.CustomRecyclerView
import com.wedevote.wdbook.R
import com.wedevote.wdbook.base.ExceptionHandler
import com.wedevote.wdbook.base.RootActivity
import com.wedevote.wdbook.base.SDKSingleton
import com.wedevote.wdbook.entity.notification.NotificationStatusEntity
import com.wedevote.wdbook.entity.notification.NotificationType
import com.wedevote.wdbook.tools.util.APPUtil
import com.wedevote.wdbook.tools.util.SPKeyDefine
import com.wedevote.wdbook.tools.util.findString
import com.wedevote.wdbook.ui.widgets.CommTopTitleLayout
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus

/***
 * @date 创建时间 2022/5/16 15:51
 * <AUTHOR> W.YuLong
 * @description
 */
class NotificationCenterActivity : RootActivity(), OnClickListener, OnLoadMoreListener, OnRefreshListener {
    lateinit var topTitleLayout: CommTopTitleLayout
    lateinit var clearUnreadImageView: ImageView
    lateinit var platformLayout: GroupImageTextLayout
    lateinit var couponLayout: GroupImageTextLayout
    lateinit var todoLayout: GroupImageTextLayout
    lateinit var typeContainerLayout: ConstraintLayout
    lateinit var loadingContainerLayout: LinearLayout
    
    lateinit var emptyLayout: ViewGroup
    
    lateinit var dataRefreshLayout: SmartRefreshLayout
    lateinit var dataRecyclerView: CustomRecyclerView
    lateinit var messageAdapter: NotificationMessageAdapter
    
    lateinit var platformCountTextView: TextView
    lateinit var couponCountTextView: TextView
    lateinit var todoCountTextView: TextView
    
    var offset = 0L
    var limit = 10L
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_notification_center_layout)
        initViewFromXML()
        initFragments()
        setViewListeners()
        onRefresh(dataRefreshLayout)
    }
    
    fun initFragments() {
        messageAdapter = NotificationMessageAdapter()
        dataRecyclerView.adapter = messageAdapter
        
        dataRefreshLayout.setOnRefreshListener(this).setOnLoadMoreListener(this)
        
    }
    
    override fun onResume() {
        super.onResume()
        initUnreadCountUI()
    }
    
    private fun initViewFromXML() {
        dataRefreshLayout = findViewById(R.id.fragment_notification_RefreshLayout)
        dataRecyclerView = findViewById(R.id.fragment_notification_data_RecyclerView)
    
        topTitleLayout = findViewById(R.id.notification_center_top_title_layout)
        loadingContainerLayout = findViewById(R.id.fragment_notification_loading_container_Layout)
        clearUnreadImageView = findViewById(R.id.notification_center_clear_ImageView)
        platformLayout = findViewById(R.id.notification_center_type_platform_Layout)
        couponLayout = findViewById(R.id.notification_center_type_coupon_Layout)
        todoLayout = findViewById(R.id.notification_center_type_todo_Layout)
        platformCountTextView = findViewById(R.id.notification_center_platform_count_TextView)
        couponCountTextView = findViewById(R.id.notification_center_coupon_count_TextView)
        todoCountTextView = findViewById(R.id.notification_center_todo_count_TextView)
        typeContainerLayout = findViewById(R.id.notification_center_type_container_layout)
        emptyLayout = findViewById(R.id.fragment_notification_empty_container_Layout)
    }
    
    private fun setViewListeners() {
        clearUnreadImageView.setOnClickListener(this)
        platformLayout.setOnClickListener(this)
        couponLayout.setOnClickListener(this)
        todoLayout.setOnClickListener(this)
    }
    
    
    override fun onRefresh(layout: RefreshLayout?) {
        MainScope().launch(ExceptionHandler.coroutineExceptionHandler) {
            if (messageAdapter.itemCount == 0) {
                loadingContainerLayout.visibility = View.VISIBLE
            }
            APPUtil.syncNotification()
            offset = 0
            messageAdapter.clearDataList()
            onLoadMore(layout!!)
            loadingContainerLayout.visibility = View.GONE
    
            initUnreadCountUI()
        }
    }
    
    override fun onLoadMore(layout: RefreshLayout) {
        initMessageDataUI()
        dataRefreshLayout.finishLoadMoreAndRefresh()
    }
    
    private fun initMessageDataUI() {
        val dataList = SDKSingleton.userBl.loadALlNotificationListFromDB(offset, limit)
        if (!dataList.isNullOrEmpty()) {
            messageAdapter.addDataList(dataList)
            offset += dataList!!.size
            dataRefreshLayout.isEnableLoadMore = dataList.size >= limit
        } else {
            dataRefreshLayout.isEnableLoadMore = false
        }
    
        if (messageAdapter.itemCount == 0) {
            emptyLayout.visibility = View.VISIBLE
            dataRecyclerView.visibility = View.GONE
        } else {
            emptyLayout.visibility = View.GONE
            dataRecyclerView.visibility = View.VISIBLE
        }
    }
    
    private fun initUnreadCountUI() {
        var platformCount = SDKSingleton.userBl.getNotificationReadStatusCountWithType(0, NotificationType.PLATFORM.type)
        platformCountTextView.text = platformCount.toString()
        platformCountTextView.visibility = if (platformCount > 0) View.VISIBLE else View.GONE
        var couponCount = SDKSingleton.userBl.getNotificationReadStatusCountWithType(0, NotificationType.COUPON.type)
        couponCountTextView.text = couponCount.toString()
        couponCountTextView.visibility = if (couponCount > 0) View.VISIBLE else View.GONE
    }
    
    fun showMakeNotificationReadDialog() {
        CommAlertDialog.with(this)
            .setMessage(R.string.confirm_set_all_notification_read)
            .setStartText(R.string.label_cancel)
            .setEndText(R.string.label_OK)
            .setAllButtonColorRes(R.color.color_blue_2E7BE4)
            .setOnViewClickListener { d, V, tag ->
                if (tag == CommAlertDialog.TAG_CLICK_END) {
                    MainScope().launch(ExceptionHandler.coroutineExceptionHandler) {
                        SDKSingleton.userBl.makeNotificationRead()
                        makeLoadedMessageRead()
                        ToastUtil.showToastShort(R.string.all_message_has_set_read)
                        onRefresh(dataRefreshLayout)
//                        EventBus.getDefault().post(OnNotificationReadEvent())
                    }
                }
                
            }.create().show()
    }
    
    fun makeLoadedMessageRead() {
        if (!messageAdapter.dataList.isNullOrEmpty()) {
            for (msg in messageAdapter.dataList!!) {
                if (msg.read == 0) {
                    SDKSingleton.userBl.makeLocalNotificationReadById(msg.id)
                }
            }
        }
    }
    
    fun checkMessageUnread(): Boolean {
        var result = false
        if (!messageAdapter.dataList.isNullOrEmpty()) {
            for (msg in messageAdapter.dataList!!) {
                if (msg.read == 0) {
                    result = true
                    break
                }
    
            }
        } else {
            result = true
        }
        return result
    }
    
    override fun onClick(v: View?) {
        when (v) {
            clearUnreadImageView -> {
                MainScope().launch(ExceptionHandler.coroutineExceptionHandler) {
                    val notificationStatusList = SDKSingleton.userBl.getNotificationStatus()
                    var unreadCount = 0
                    if (!notificationStatusList.isNullOrEmpty()) {
                        for (entity in notificationStatusList) {
                            unreadCount += entity.unreadCount
                        }
                    }
                    if (unreadCount > 0) {
                        showMakeNotificationReadDialog()
                    } else {
                        //Dialog版本
                        // CommAlertDialog.with(this)
                        //     .setMessage(R.string.you_have_no_unread_message)
                        //     .setEndText(R.string.got_it)
                        //     .setAllButtonColorRes(R.color.color_blue_2E7BE4)
                        //     .create().show()
                        ToastUtil.showToastShort(R.string.you_have_no_unread_message)
                    }
                }
            }
            platformLayout -> {
                NotificationTypeListActivity.gotoTypeListActivity(
                    this,
                    findString(R.string.notification_type_platform),
                    NotificationType.PLATFORM.type
                )
            }
            couponLayout -> {
                NotificationTypeListActivity.gotoTypeListActivity(
                    this,
                    findString(R.string.notification_type_recommend),
                    NotificationType.COUPON.type
                )
            }
            todoLayout -> {
                NotificationTypeListActivity.gotoTypeListActivity(
                    this,
                    findString(R.string.notification_type_todolist),
                    NotificationType.TODOLIST.type
                )
            }
        }
    }
    
    
}