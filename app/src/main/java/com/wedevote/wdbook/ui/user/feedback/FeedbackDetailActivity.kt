package com.wedevote.wdbook.ui.user.feedback

import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.View
import android.view.View.OnClickListener
import android.view.ViewGroup
import android.widget.Button
import android.widget.EditText
import android.widget.ImageView
import android.widget.TextView
import androidx.core.content.ContextCompat
import com.aquila.lib.base.BaseRecycleAdapter
import com.aquila.lib.base.BaseViewHolder
import com.aquila.lib.dialog.CommProgressDialog
import com.aquila.lib.widget.view.CustomRecyclerView
import com.wedevote.wdbook.R
import com.wedevote.wdbook.base.APPConfig
import com.wedevote.wdbook.base.ExceptionHandler
import com.wedevote.wdbook.base.RootActivity
import com.wedevote.wdbook.base.SDKSingleton
import com.wedevote.wdbook.entity.SystemParamEntity
import com.wedevote.wdbook.entity.UserInfoEntity
import com.wedevote.wdbook.entity.feedback.FeedbackDetailEntity
import com.wedevote.wdbook.entity.feedback.FeedbackSaveEntity
import com.wedevote.wdbook.network.SystemParamDefine
import com.wedevote.wdbook.tools.event.OnFeedbackReadEvent
import com.wedevote.wdbook.tools.util.IntentConstants
import com.wedevote.wdbook.tools.util.PictureUtil
import com.wedevote.wdbook.tools.util.UnitFormatUtil
import com.wedevote.wdbook.tools.util.dp2px
import com.wedevote.wdbook.tools.util.findString
import com.wedevote.wdbook.ui.widgets.CommTopTitleLayout
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus

/***
 * @date 创建时间 2022/4/12 17:45
 * <AUTHOR> W.YuLong
 * @description
 */
class FeedbackDetailActivity : RootActivity(), OnClickListener {
    lateinit var titleLayout: CommTopTitleLayout
    lateinit var dataRecyclerView: CustomRecyclerView
    lateinit var contentEditText: EditText
    lateinit var sendButton: Button
    lateinit var detailAdapter: FeedbackDetailAdapter

    companion object {
        fun gotoFeedbackDetailActivity(context: Context, feedbackId: Long) {
            val intent = Intent(context, FeedbackDetailActivity::class.java)
            intent.putExtra(IntentConstants.EXTRA_feedbackId, feedbackId)
            context.startActivity(intent)
        }
    }

    var feedbackId: Long = 0
    lateinit var mainScope: CoroutineScope
    lateinit var progressDialog: CommProgressDialog

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_feedback_detail_layout)
        initViewFromXML()
        progressDialog =
            CommProgressDialog.with(this).setTitle(findString(R.string.in_sending)).create()
        feedbackId = intent.getLongExtra(IntentConstants.EXTRA_feedbackId, 0L)

        sendButton.isEnabled = !contentEditText.text.trim().isNullOrEmpty()
        contentEditText.isEnabled = false
        sendButton.setOnClickListener(this)
        contentEditText.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
            }

            override fun afterTextChanged(s: Editable?) {
                sendButton.isEnabled = !s?.trim().isNullOrEmpty()
            }
        })

        mainScope = MainScope()
        detailAdapter = FeedbackDetailAdapter()
        dataRecyclerView.adapter = detailAdapter

        getDataFromServer()
    }

    private fun initViewFromXML() {
        titleLayout = findViewById(R.id.feedback_detail_top_title_Layout)
        dataRecyclerView = findViewById(R.id.feedback_detail_data_RecyclerView)
        contentEditText = findViewById(R.id.feedback_detail_content_EditText)
        sendButton = findViewById(R.id.feedback_detail_send_Button)
    }

    fun getDataFromServer() {
        SDKSingleton.appBl.getSystemParam(SystemParamDefine.OPERATION_AVATAR) {
            mainScope.launch(ExceptionHandler.coroutineExceptionHandler) {
                if (it != null) {
                    detailAdapter.serviceEntity = it
                }
                detailAdapter.userInfoEntity = SDKSingleton.userBl.getUserInfoEntity()
                loadFeedbackDetailList()
                contentEditText.isEnabled = true
                EventBus.getDefault().post(OnFeedbackReadEvent(feedbackId))
            }
        }
    }

    private suspend fun loadFeedbackDetailList() {
        val dataList = SDKSingleton.userBl.getFeedbackDetailList(feedbackId)
        detailAdapter.dataList = dataList?.toMutableList()
    }

    override fun onClick(v: View?) {
        if (v == sendButton) {
            mainScope.launch(
                ExceptionHandler.getCoroutineExceptionHandler {
                    progressDialog.dismiss()
                }
            ) {
                progressDialog.show()
                val entity = FeedbackSaveEntity().also {
                    it.feedbackId = feedbackId
                    it.messageText = contentEditText.text.toString()
                }
                SDKSingleton.userBl.saveFeedback(entity)
                contentEditText.setText("")
                loadFeedbackDetailList()
                progressDialog.dismiss()
            }
        }
    }
}

/***
 *@date 创建时间 2022/4/29 17:14
 *<AUTHOR> W.YuLong
 *@description
 */
class FeedbackDetailAdapter() :
    BaseRecycleAdapter<FeedbackDetailEntity, FeedbackDetailItemHolder>() {
    var userInfoEntity: UserInfoEntity? = null
    var serviceEntity: SystemParamEntity? = null

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): FeedbackDetailItemHolder {
        return FeedbackDetailItemHolder(parent, userInfoEntity, serviceEntity)
    }

    override fun onBindViewHolder(holder: FeedbackDetailItemHolder, position: Int) {
        super.onBindViewHolder(holder, position)
        holder.adjustFirstUI(position == 0)
    }
}

/***
 *@date 创建时间 2022/4/29 17:13
 *<AUTHOR> W.YuLong
 *@description
 */
class FeedbackDetailItemHolder(
    parent: ViewGroup,
    var userInfoEntity: UserInfoEntity?,
    var serviceEntity: SystemParamEntity?
) :
    BaseViewHolder(parent, R.layout.holder_item_feedback_detail_layout) {
    val avatarImageView: ImageView =
        itemView.findViewById(R.id.item_feedback_detail_avatar_ImageView)
    val timeTextView: TextView = itemView.findViewById(R.id.item_feedback_detail_time_TextView)
    val nameTextView: TextView = itemView.findViewById(R.id.item_feedback_detail_name_TextView)
    val contentTextView: TextView =
        itemView.findViewById(R.id.item_feedback_detail_content_TextView)

    override fun <T> initUIData(t: T) {
        t as FeedbackDetailEntity
        if (t.messageType == 1) {
            PictureUtil.loadCircleImageWithDefault(
                avatarImageView,
                userInfoEntity?.avatarPath,
                R.drawable.ic_default_avatar
            )
            if (APPConfig.isCurrentThemeLight()) {
                nameTextView.setTextColor(Color.BLACK)
            } else {
                nameTextView.setTextColor(Color.parseColor("#A0FFFFFF"))
            }
        } else {
            PictureUtil.loadCircleImage(avatarImageView, R.drawable.app_logo)

            nameTextView.setTextColor(
                ContextCompat.getColorStateList(
                    itemView.context,
                    R.color.color_orange_FF8A00
                )
            )
            nameTextView.setText(R.string.wd_system_service)
        }
        timeTextView.setText(UnitFormatUtil.formatDate_ymdhm(t.createTime))
        contentTextView.setText(t.messageText)
    }

    fun adjustFirstUI(isFirstItem: Boolean) {
        if (isFirstItem) {
            contentTextView.setPadding(0, dp2px(8), 0, 0)
            contentTextView.paint.isFakeBoldText = true
        } else {
            contentTextView.paint.isFakeBoldText = false
            contentTextView.setPadding(dp2px(55), 0, 0, 0)
        }
    }
}
