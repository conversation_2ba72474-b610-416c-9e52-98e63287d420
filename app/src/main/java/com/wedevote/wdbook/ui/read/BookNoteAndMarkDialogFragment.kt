package com.wedevote.wdbook.ui.read

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.ObjectAnimator
import android.os.Bundle
import android.os.Handler
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.fragment.app.DialogFragment
import com.wedevote.wdbook.R
import com.wedevote.wdbook.tools.event.DialogDismissEvent
import com.wedevote.wdbook.tools.util.IntentConstants
import org.greenrobot.eventbus.EventBus

/***
 * @date 创建时间 2020/5/27 11:17
 * <AUTHOR> W.YuLong
 * @description 书籍目录的页面
 */
class BookNoteAndMarkDialogFragment : DialogFragment(), View.OnClickListener {
    private lateinit var maskView: View
    private lateinit var closeImageView: ImageView
    private lateinit var markTextView: TextView
    private lateinit var noteTextView: TextView
    private lateinit var tabContainerLayout: LinearLayout
    private lateinit var rootContainerLayout: LinearLayout

    private lateinit var notesFragment: BookNotesFragment
    private lateinit var bookMarkFragment: BookMarkFragment
    var resourceId: String = ""
    var showNoteOnly = false
    lateinit var listener: OnJumpActionListener

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL, R.style.Dialog_FullScreen)
        resourceId = arguments?.getString(IntentConstants.EXTRA_ResourceId)!!
        showNoteOnly = (arguments?.getInt(IntentConstants.EXTRA_PathIndex, -1)!! >= 0)
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        val rootView = inflater.inflate(R.layout.activity_book_note_mark_layout, container, false)
        initViewFromXML(rootView)
        setViewListeners()

        if (showNoteOnly) {
            tabContainerLayout.visibility = View.GONE
        }

        val resourceId = arguments?.getString(IntentConstants.EXTRA_ResourceId)!!
        notesFragment = BookNotesFragment(requireActivity(), requireArguments())
        notesFragment.setOnJumpListener(listener)
        rootContainerLayout.addView(notesFragment.rootView)

        bookMarkFragment = BookMarkFragment(requireActivity(), resourceId)
        bookMarkFragment.setOnJumpListener(listener)
        rootContainerLayout.addView(bookMarkFragment.rootView)

        setCurrentTab(0)
        return rootView
    }

    private fun initViewFromXML(rootView: View) {
        maskView = rootView.findViewById(R.id.book_note_mask_View)
        closeImageView = rootView.findViewById(R.id.book_note_close_ImageView)
        markTextView = rootView.findViewById(R.id.book_note_bookmark_TextView)
        noteTextView = rootView.findViewById(R.id.book_note_bookNote_TextView)
        tabContainerLayout = rootView.findViewById(R.id.book_note_tab_container_layout)
        rootContainerLayout = rootView.findViewById(R.id.book_note_root_container_layout)
        noteTextView.isSelected = true
    }

    private fun setViewListeners() {
        maskView.setOnClickListener(this)
        closeImageView.setOnClickListener(this)
        noteTextView.setOnClickListener(this)
        markTextView.setOnClickListener(this)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        rootContainerLayout.post {
            ObjectAnimator.ofFloat(rootContainerLayout, "translationY", rootContainerLayout.height.toFloat(), 0f).apply {
                duration = 300
                addListener(object : AnimatorListenerAdapter() {
                    override fun onAnimationStart(animation: Animator) {
                        super.onAnimationStart(animation)
                        rootContainerLayout.visibility = View.VISIBLE
                    }
                })
            }.start()

            ObjectAnimator.ofFloat(maskView, "alpha", 0.2f, 1f).apply {
                addListener(object : AnimatorListenerAdapter() {
                    override fun onAnimationStart(animation: Animator) {
                        super.onAnimationStart(animation)
                        maskView.visibility = View.VISIBLE
                    }
                })
                duration = 300
            }.start()
        }
    }

    override fun onClick(v: View) {
        when (v) {
            maskView, closeImageView -> dismiss()
            noteTextView -> setCurrentTab(0)
            markTextView -> setCurrentTab(1)
        }
    }

    private fun setCurrentTab(tab: Int) {
        if (tab == 0) {
            noteTextView.isSelected = true
            markTextView.isSelected = false
            notesFragment.rootView.visibility = View.VISIBLE
            bookMarkFragment.rootView.visibility = View.GONE
        } else {
            noteTextView.isSelected = false
            markTextView.isSelected = true
            notesFragment.rootView.visibility = View.GONE
            bookMarkFragment.rootView.visibility = View.VISIBLE
        }
    }

    override fun dismiss() {
        val alphaAnimator = ObjectAnimator.ofFloat(maskView, "alpha", 1f, 0f)
        alphaAnimator.duration = 300
        alphaAnimator.start()
        val moveAnimator = ObjectAnimator.ofFloat(
            rootContainerLayout, "translationY", 0f,
            rootContainerLayout.height.toFloat()
        )
        moveAnimator.setDuration(300).addListener(object : AnimatorListenerAdapter() {
            override fun onAnimationEnd(animation: Animator) {
            }
        })
        moveAnimator.start()

        Handler().postDelayed(
            {
                super.dismiss()
                EventBus.getDefault().post(DialogDismissEvent())
            },
            300
        )
    }

    fun setOnJumpListener(l: OnJumpActionListener) {
        listener = l
    }
}
