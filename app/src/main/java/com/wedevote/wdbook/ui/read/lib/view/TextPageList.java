package com.wedevote.wdbook.ui.read.lib.view;

import android.text.Spanned;
import android.text.TextPaint;
import android.text.style.RelativeSizeSpan;
import android.text.style.StyleSpan;

import com.wedevote.wdbook.ui.read.lib.TextHelper;
import com.wedevote.wdbook.ui.read.lib.span.AlignSpan;
import com.wedevote.wdbook.ui.read.lib.span.BlockSpan;
import com.wedevote.wdbook.ui.read.lib.span.SpecialElementSpan;
import com.wedevote.wdbook.ui.read.lib.span.TableDataSpan;
import com.wedevote.wdbook.ui.read.lib.span.WDImageSpan;
import com.wedevote.wdbook.ui.read.lib.span.WDMarginSpan;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

/**
 * Created by <PERSON> on 14/10/31.
 */
public class TextPageList extends LinkedList<TextLineList> {
    private static final long serialVersionUID = 318371282148654041L;
    private float y = 0;
    private final TextPaint paint;
    private TextLineList currentLine;
    private TextLineList currentPreviousLine;
    private float maxLineWidth;

    private BlockSpan blockSpan;

    private StyleSpan styleSpan;
    private RelativeSizeSpan relativeSizeSpan;
    private AlignSpan alignSpan;
    private WDImageSpan imageSpan;
    private TableDataSpan tableDataSpan;
    private WDMarginSpan marginSpan;
    private LinkedList<Spanned>[] tableWordLists = null;
    private ArrayList<Integer> linePosition;
    private int tableLine = 0;
    private int tableRow = 0;
    private boolean checkNewLine = true;
    private boolean isPreviousWordCannotLineEnd = false;
    private final TextHelper textHelper;
    private final float contentWidth;
    private final float pageHeight;
    public String originalText;
    private int lineInBlock = 0;
    private int prevBlockId = -1;

    public TextPageList(TextPaint paint, float y, float contentWidth, float pageHeight) {
        this.paint = paint;
        this.y = y;
        this.contentWidth = contentWidth;
        this.pageHeight = pageHeight;
        this.textHelper = new TextHelper(paint);
    }

    private void addNewTextLine() {
        if (this.size() > 0) {
            currentPreviousLine = this.getLast();
        }
        currentLine = new TextLineList(paint, textHelper, y, relativeSizeSpan, alignSpan);
        maxLineWidth = contentWidth;
        if (imageSpan != null) {
            currentLine.setDrawImage(imageSpan, maxLineWidth, pageHeight);
        } else if (blockSpan != null) {
            currentLine.setBlockSpan(blockSpan);
            maxLineWidth = contentWidth - blockSpan.getPaddingRight();
        } else if (tableDataSpan != null) {
            currentLine.setTableSpan(tableDataSpan);
        }

        add(currentLine);
        // Apply margin
        float margin = 0;
        if (blockSpan != null) {
            margin += blockSpan.getPaddingLeft();
        }
        if(marginSpan != null){
            lineInBlock++;
            margin += marginSpan.getLeadingMargin(lineInBlock);
        }
        if (tableDataSpan != null) {
            margin = 5;
            if (currentPreviousLine != null) {
                TableDataSpan prevTableSpan = currentPreviousLine.getTableSpan();
                if (prevTableSpan != null) {
                    if (tableDataSpan.getLine() == prevTableSpan.getLine()) {
                        currentLine.deleteStartTableLine();
                        currentPreviousLine.deleteEndTableLine();
                    }
                }
            }
        }

        currentLine.setMargin(margin);
    }

    public boolean isInTable() {
        return tableDataSpan != null;
    }

    private boolean addTableWord(Spanned spanned,int row){
        initSpans(spanned);
        if (currentLine == null) {
            addNewTextLine();
        }

        int maxRow = tableWordLists.length-1;
        if (row != tableRow) {
            tableRow = row;
            currentLine.setLineWidth(maxLineWidth * (row) / (maxRow + 1) + 5);
        }

        float rowEnd = maxLineWidth * (row + 1) / (maxRow + 1);
        char c = spanned.charAt(0);

        float wordWidth = getSpecialElementWidth(spanned);
        if(wordWidth < 0){
            wordWidth = textHelper.getWordWidth(spanned, relativeSizeSpan, styleSpan);
        }

        float possibleWidth = currentLine.getLineWidth() + wordWidth;
        if (possibleWidth > rowEnd || c == '\n') {
            if(row < maxRow){
                currentLine.setLineWidth(rowEnd);//跳到下一格
            }
            return false;
        }
        currentLine.addSpanned(spanned, -1, wordWidth);
        return true;
    }

    private void addSpannedFromTableWordList() {
        if (tableWordLists != null) {
            boolean addedWord = true;
            while (addedWord) {
                addedWord = false;
                tableRow = 0;
                currentLine = null;
                for(int i=0;i<tableWordLists.length;i++){
                    while(!tableWordLists[i].isEmpty()){
                        Spanned word = tableWordLists[i].get(0);
                        if(addTableWord(word,i)){
                            tableWordLists[i].remove(0);
                            addedWord = true;
                        }else{
                            if(word.charAt(0) == '\n'){
                                tableWordLists[i].remove(0);
                                addedWord = true;
                            }
                            break;
                        }
                    }
                }
            }
            currentLine = null;
            addNewTextLine();
        }
    }

    private void addLongWord(Spanned spanned, int location, float wordWidth) {
        float remainWidth = maxLineWidth - currentLine.getLineWidth();
        if (remainWidth - wordWidth < 0) {
            handleExceedLineMaxWidth();
        }
        currentLine.addSpanned(spanned, location, wordWidth);
    }

    private void initSpans(Spanned spanned) {
        relativeSizeSpan = null;
        imageSpan = null;
        tableDataSpan = null;
        blockSpan = null;
        alignSpan = null;
        styleSpan = null;
        marginSpan = null;
        Object[] spans = spanned.getSpans(0, 1, Object.class);
        if (spans.length > 0) {
            for (Object span : spans) {
                if (span instanceof BlockSpan) {
                    blockSpan = (BlockSpan) span;
                } else if (span instanceof RelativeSizeSpan) {
                    relativeSizeSpan = (RelativeSizeSpan) span;
                } else if (span instanceof WDImageSpan) {
                    imageSpan = (WDImageSpan) span;
                } else if (span instanceof TableDataSpan) {
                    tableDataSpan = (TableDataSpan) span;
                } else if (span instanceof AlignSpan) {
                    alignSpan = (AlignSpan) span;
                } else if (span instanceof StyleSpan) {
                    styleSpan = (StyleSpan) span;
                } else if(span instanceof WDMarginSpan && marginSpan == null){
                    marginSpan = (WDMarginSpan)span;
                    if(marginSpan.getId() != prevBlockId){
                        lineInBlock = 0;
                        prevBlockId = marginSpan.getId();
                    }
                }
            }
        }
    }

    private float getSpecialElementWidth(Spanned word){
        SpecialElementSpan[] spans = word.getSpans(0, word.length(), SpecialElementSpan.class);
        if(spans.length != 0) {
            return spans[0].getElementWidth(paint);
        }
        return -1;
    }

    public void addSpanned(Spanned spanned, int location) {
        if (checkNewLine) {
            checkNewLine = false;
            initSpans(spanned);
        }

        if (currentLine == null) {
            addNewTextLine();
        }

        if (tableDataSpan != null) {
            if (tableDataSpan.getLine() != tableLine) {
                TableDataSpan oldSpan = tableDataSpan;
                addSpannedFromTableWordList();
                tableDataSpan = oldSpan;
                tableRow = 0;
                tableLine = tableDataSpan.getLine();
                tableWordLists = null;
            }
            if (tableWordLists == null) {
                tableWordLists = new LinkedList[tableDataSpan.getMaxRow()+1];
                for(int i=0;i<tableWordLists.length;i++){
                    tableWordLists[i] = new LinkedList<>();
                }
            }
            for (int i = 0; i < spanned.length(); i++) {
                tableWordLists[tableDataSpan.getRowIndex()].add((Spanned) spanned.subSequence(i, i + 1));
            }
            checkNewLine = true;
            return;
        } else {
            addSpannedFromTableWordList();
            tableDataSpan = null;
            tableWordLists = null;//表格结束，清空表格
        }

        float wordWidth = getSpecialElementWidth(spanned);
        if(wordWidth < 0){
            wordWidth = textHelper.getWordWidth(spanned, relativeSizeSpan, styleSpan);
        }

        float possibleWidth = currentLine.getLineWidth() + wordWidth;

        if (spanned.length() > 1) {
            addLongWord(spanned, location, wordWidth);
            return;
        }

        char c = spanned.charAt(0);
        if (c == '\n') {
            checkNewLine = true;
            currentLine.addBreakLine(maxLineWidth);
            if (blockSpan != null) {
                currentLine.setBlockSpan(blockSpan);
            }
            currentLine = null;
            return;
        }

        if (possibleWidth > maxLineWidth) {
            handleExceedLineMaxWidth();
        }

        if (currentLine.isEmpty() && tableDataSpan == null) {
            // Handle First Letter of the first line
            if (textHelper.isSymbolCannotLineBegin(c)) {
                if (possibleWidth > maxLineWidth) {
                    // Handle symbol at first
                    int previousLastCount = currentPreviousLine.size();
                    int moveDownCount = 0;
                    Word w;
                    do {
                        moveDownCount++;
                        w = currentPreviousLine.get(previousLastCount - moveDownCount);
                    } while (!textHelper.canFirst(w) && moveDownCount < previousLastCount);

                    List<Word> moveDownWordList = new ArrayList<>();
                    for (int index = 0; index < moveDownCount; index++) {
                        Word word = currentPreviousLine.removeWordAtIndex(previousLastCount - 1 - index);
                        moveDownWordList.add(0, word);
                    }

                    for (Word moveDownWord : moveDownWordList) {
                        currentLine.addSpanned(moveDownWord.getWord(), moveDownWord.getLocation(), moveDownWord.getWordWidth());
                    }
                    currentPreviousLine.adjustWordPosition(maxLineWidth);
                    currentLine.addSpanned(spanned, location, wordWidth);
                    return;
                }
            }
        }
        isPreviousWordCannotLineEnd = textHelper.isSymbolCannotLineEnd(c);
        currentLine.addSpanned(spanned, location, wordWidth);
    }

    private void handleExceedLineMaxWidth() {
        List<Word> wordList = new ArrayList<>();
        if (isPreviousWordCannotLineEnd) {
            Word word;
            while (true) {
                if (currentLine.isEmpty()) {
                    break;
                }
                word = currentLine.getLast();
                if (word.getWord().length() > 1) {
                    break;
                }
                if (textHelper.isSymbolCannotLineEnd(word.getWord().charAt(0))) {
                    currentLine.removeLastWord();
                    wordList.add(0, word);
                    continue;
                }
                break;
            }
        }

        currentLine.adjustWordPosition(maxLineWidth);
        addNewTextLine();
        for (Word tmpWord : wordList) {
            currentLine.addSpanned(tmpWord.getWord(), tmpWord.getLocation(), tmpWord.getWordWidth());
        }
        checkNewLine = true;
    }

    public void computeLinePosition(float lineHeight) {
        linePosition = new ArrayList<>();
        int position = 0;
        TextLineList prev = null;
        for (TextLineList l : this) {
            l.checkPrevLineBlock(prev);
            prev = l;
        }

        for (TextLineList l : this) {
            position += (int) l.getLineHeight(lineHeight);
            linePosition.add(position);
        }
    }

    public float getLinePosition(int i) {
        if (i < 0 || linePosition == null) {
            return 0;
        } else {
            return linePosition.get(i);
        }
    }

    public float findTouchLineBottom() {
        for (Integer i : linePosition) {
            if (i > y) {
                return i;
            }
        }
        return -1;
    }

    public int findLine(float y) {
        int index = 0;
        for (Integer i : linePosition) {
            if (i > y) {
                return index;
            }
            index++;
        }
        return -1;
    }

    public Word findWord(int line, float x) {
        for (Word w : get(line)) {
            if (x > w.getX() && x < w.getX() + w.getWordWidth()) {
                return w;
            }
        }
        return null;
    }
}
