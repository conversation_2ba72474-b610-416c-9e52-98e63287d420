package com.wedevote.wdbook.ui.read

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.ObjectAnimator
import android.annotation.SuppressLint
import android.content.DialogInterface
import android.os.Bundle
import android.os.Handler
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.activity.OnBackPressedCallback
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.FragmentPagerAdapter
import androidx.viewpager.widget.ViewPager
import com.aquila.lib.tools.interfaceimpl.OnPageChangeListenerImpl
import com.wedevote.wdbook.R
import com.wedevote.wdbook.base.APPConfig
import com.wedevote.wdbook.entity.BookCatalogEntity
import com.wedevote.wdbook.ui.read.lib.EPubBook


/***
 * @date 创建时间 2020/5/27 11:17
 * <AUTHOR> W<PERSON>
 * @description 书籍目录的页面
 */
class BookContentsDialog(val currentPath: String, val contentsDialogData: ArrayList<BookCatalogEntity>) : DialogFragment(), View.OnClickListener {
    private lateinit var maskView: View
    private lateinit var closeImageView: ImageView
    private lateinit var backImageView: ImageView
    private lateinit var bookContentsTextView: TextView
    private lateinit var bibleContentsTextView: TextView
    private lateinit var titleTextView: TextView
    private lateinit var tabContainerLayout: LinearLayout
    private lateinit var viewPager: ViewPager
    private lateinit var rootContainerLayout: LinearLayout
    lateinit var pageAdapter: ContentsFragmentPagerAdapter
    lateinit var bookFragment: BookCatalogFragment
    val bibleFragment = BibleCatalogFragment()
    var listener: ContentsDialogListener? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL, R.style.Dialog_FullScreen)
        if (APPConfig.isCurrentThemeLight()) {
            context?.setTheme(R.style.book_contents_theme_light)
        } else {
            context?.setTheme(R.style.book_contents_theme_dark)
        }
    }

    private var mOnClickListener: DialogInterface.OnDismissListener? = null

    fun setOnDismissListener(listener: DialogInterface.OnDismissListener?) {
        mOnClickListener = listener
    }

    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)
        if (mOnClickListener != null) {
            mOnClickListener!!.onDismiss(dialog)
        }
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        val rootView = inflater.inflate(R.layout.activity_book_catalog_layout, container, false)
        initViewFromXML(rootView)
        return rootView
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setViewListeners()

        resetExpend(contentsDialogData)
        bookFragment = BookCatalogFragment(currentPath, contentsDialogData)
        bookFragment.setOnDialogListener(listener)

        bibleFragment.onBibleTabChangeCallback = onBibleTabChangeCallback
        pageAdapter = ContentsFragmentPagerAdapter(childFragmentManager)
        viewPager.adapter = pageAdapter
        rootContainerLayout.post {
            ObjectAnimator.ofFloat(rootContainerLayout, "translationY", rootContainerLayout.height.toFloat(), 0f)
                .apply {
                    duration = 300
                    addListener(object : AnimatorListenerAdapter() {
                        override fun onAnimationStart(animation: Animator) {
                            super.onAnimationStart(animation)
                            rootContainerLayout.visibility = View.VISIBLE
                        }
                    })
                }.start()

            ObjectAnimator.ofFloat(maskView, "alpha", 0.2f, 1f).apply {
                addListener(object : AnimatorListenerAdapter() {
                    override fun onAnimationStart(animation: Animator) {
                        super.onAnimationStart(animation)
                        maskView.visibility = View.VISIBLE
                    }
                })
                duration = 300
            }.start()
        }

        if (!EPubBook.hasReference()) {
            tabContainerLayout.visibility = View.GONE
            titleTextView.visibility = View.VISIBLE
        }
        requireActivity().onBackPressedDispatcher.addCallback(
            this,
            object : OnBackPressedCallback(true) {
                override fun handleOnBackPressed() {
                    if (viewPager.currentItem != 1 || !bibleFragment.doOnBackPress()) {
                        dismiss()
                    }
                }
            }
        )
    }

    private fun resetExpend(contentsDialogData: java.util.ArrayList<BookCatalogEntity>) {
        for (item in contentsDialogData) {
            item.expand = false
            if (!item.subCatalogList.isNullOrEmpty()) {
                resetExpend(item.subCatalogList!!)
            }
        }
    }

    private fun setViewListeners() {
        closeImageView.setOnClickListener(this)
        backImageView.setOnClickListener(this)
        bookContentsTextView.setOnClickListener(this)
        bibleContentsTextView.setOnClickListener(this)

        viewPager.addOnPageChangeListener(object : OnPageChangeListenerImpl() {
            override fun onPageSelected(position: Int) {
                setCurrentTab(position)
            }
        })
    }

    private fun initViewFromXML(rootView: View) {
        maskView = rootView.findViewById(R.id.catalog_tool_mask_View)
        closeImageView = rootView.findViewById(R.id.catalog_tool_close_ImageView)
        backImageView = rootView.findViewById(R.id.catalog_tool_back_ImageView)
        bookContentsTextView = rootView.findViewById(R.id.catalog_tool_book_contents_TextView)
        bibleContentsTextView = rootView.findViewById(R.id.catalog_tool_bible_contents_TextView)
        titleTextView = rootView.findViewById(R.id.catalog_tool_bible_title_TextView)
        tabContainerLayout = rootView.findViewById(R.id.catalog_tool_tab_container_layout)
        viewPager = rootView.findViewById(R.id.catalog_tool_ViewPager)
        rootContainerLayout = rootView.findViewById(R.id.catalog_tool_root_container_layout)
        bookContentsTextView.isSelected = true
        if (!EPubBook.hasReference())
            bookContentsTextView.setText(R.string.catalogue)
    }

    private val onBibleTabChangeCallback = object : OnBibleTabChangeCallback {
        override fun onCurrentStepSelected(currentTitle: String?) {
            if (!currentTitle.isNullOrEmpty()) {
                titleTextView.text = currentTitle
            }
            updateCurrentBibleStepTitleUI(!currentTitle.isNullOrEmpty())
        }
    }

    override fun onClick(v: View) {
        when (v) {
            backImageView -> requireActivity().onBackPressed()
            closeImageView -> dismiss()
            bookContentsTextView -> setCurrentTab(0)
            bibleContentsTextView -> setCurrentTab(1)
        }
    }

    fun setCurrentTab(tab: Int) {
        if (tab == 0) {
            titleTextView.visibility = View.GONE
            tabContainerLayout.visibility = View.VISIBLE
            bookContentsTextView.isSelected = true
            bibleContentsTextView.isSelected = false
        } else {
            bookContentsTextView.isSelected = false
            bibleContentsTextView.isSelected = true
            updateCurrentBibleStepTitleUI(bibleFragment.currentStep != 0)
        }
        viewPager.setCurrentItem(tab, true)
    }

    fun updateCurrentBibleStepTitleUI(isNotBookStep: Boolean) {
        if (isNotBookStep) {
            titleTextView.visibility = View.VISIBLE
            tabContainerLayout.visibility = View.GONE
        } else {
            titleTextView.visibility = View.GONE
            tabContainerLayout.visibility = View.VISIBLE
        }
    }

    override fun dismiss() {
        val alphaAnimator = ObjectAnimator.ofFloat(maskView, "alpha", 1f, 0f)
        alphaAnimator.duration = 300
        alphaAnimator.start()
        val moveAnimator = ObjectAnimator.ofFloat(
            rootContainerLayout,
            "translationY",
            0f,
            rootContainerLayout.height.toFloat()
        )
        moveAnimator.setDuration(300).addListener(object : AnimatorListenerAdapter() {
            override fun onAnimationEnd(animation: Animator) {
            }
        })
        moveAnimator.start()
        Handler().postDelayed(Runnable { super.dismiss() }, 300)
    }

    /***
     *@date 创建时间 2020/5/27 17:28
     *<AUTHOR> W.YuLong
     *@description
     */
    @SuppressLint("WrongConstant")
    inner class ContentsFragmentPagerAdapter(fm: FragmentManager) : FragmentPagerAdapter(fm) {
        override fun getItem(position: Int): Fragment {
            return when (position) {
                0 -> bookFragment
                else -> bibleFragment
            }
        }

        override fun getCount(): Int {
            return if (EPubBook.hasReference()) {
                2
            } else {
                1
            }
        }
    }

    fun setOnDialogListener(listener: ContentsDialogListener?) {
        this.listener = listener
        bibleFragment.setOnDialogListener(listener)
    }

    interface ContentsDialogListener {
        fun onCallBack(link: String)
    }
}
