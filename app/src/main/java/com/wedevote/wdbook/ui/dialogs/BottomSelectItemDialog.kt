package com.wedevote.wdbook.ui.dialogs

import android.content.Context
import android.os.Bundle
import android.view.Gravity
import android.view.View
import android.widget.TextView
import com.wedevote.wdbook.R


/***
 *@date 创建时间 2023/5/15
 *<AUTHOR> <PERSON>.<PERSON>
 *@description
 */
class BottomSelectItemDialog(context: Context) : BaseDialog(context), View.OnClickListener {
    lateinit var firstTextView: TextView
    lateinit var secondTextView: TextView
    lateinit var cancelTextView: TextView
    private var onClickListener: View.OnClickListener? = null
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.my_customer_dialog_layout)
        findView()
        cancelTextView.setOnClickListener(this)
        firstTextView.setOnClickListener(this)
        secondTextView.setOnClickListener(this)
        configDialog(Gravity.BOTTOM)
    }

    private fun findView() {
        firstTextView = findViewById(R.id.customer_dialog_title_TextView)
        secondTextView = findViewById(R.id.customer_dialog_ok_TextView)
        cancelTextView = findViewById(R.id.customer_dialog_cancel_TextView)
    }

    fun setItemsText(firstText: String?, secondText: String?, thirdText: String?) {
        firstTextView.text = firstText
        secondTextView.text = secondText
        cancelTextView.text = thirdText
    }

    fun setOnClickListener(onClickListener: View.OnClickListener?) {
        this.onClickListener = onClickListener
    }

    override fun onClick(v: View) {
        if (v !== cancelTextView) {
            if (onClickListener != null) {
                onClickListener!!.onClick(v)
            }
        }
        dismiss()
    }
}