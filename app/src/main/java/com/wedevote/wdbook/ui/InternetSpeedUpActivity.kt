package com.wedevote.wdbook.ui

import android.Manifest
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Bundle
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import com.aquila.lib.base.BaseRecycleAdapter
import com.aquila.lib.base.BaseViewHolder
import com.aquila.lib.dialog.CommAlertDialog
import com.aquila.lib.tools.singleton.SPSingleton
import com.aquila.lib.tools.util.ToastUtil
import com.aquila.lib.widget.view.CustomRecyclerView
import com.wedevote.wdbook.R
import com.wedevote.wdbook.base.APPConfig
import com.wedevote.wdbook.base.RootActivity
import com.wedevote.wdbook.base.SDKSingleton
import com.wedevote.wdbook.entity.apiServer.SpeedUpServerEntity
import com.wedevote.wdbook.tools.util.CftUtils
import com.wedevote.wdbook.tools.util.IntentConstants
import com.wedevote.wdbook.tools.util.QRCodeUtil
import com.wedevote.wdbook.tools.util.SPKeyDefine
import com.wedevote.wdbook.tools.zxing.activity.CaptureActivity
import java.text.SimpleDateFormat
import java.util.*

class InternetSpeedUpActivity : RootActivity() {
    private lateinit var emptyLayout: LinearLayout
    private lateinit var serverRecyclerView: CustomRecyclerView
    private val serverAdapter = ServerAdapter()
    private var currentServer = SPSingleton.get().getString(SPKeyDefine.SP_SpeedUpServer)

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_internet_speed_up_layout)
        initViewFromXML()
        serverRecyclerView.adapter = serverAdapter
        CftUtils.setListener(object : CftUtils.ConnectListener {
            override fun onConnectUpdate() {
                runOnUiThread {
                    serverAdapter.notifyDataSetChanged()
                }
            }
        })
        refreshUI()
    }

    private fun initViewFromXML() {
        emptyLayout = findViewById(R.id.speed_up_empty_layout)
        serverRecyclerView = findViewById(R.id.speed_up_list)
        val emptyTextView: TextView = findViewById(R.id.speed_up_empty_TextView)
        emptyTextView.text = getString(R.string.message_add_speed_up_service_tip)
    }

    private fun refreshUI() {
        serverAdapter.loadData()
        if (serverRecyclerView.adapter!!.itemCount == 1) {
            emptyLayout.visibility = View.VISIBLE
        } else {
            emptyLayout.visibility = View.GONE
        }
    }

    private fun showDeleteDialog(key: String) {
        CommAlertDialog.with(this).setTitle("")
            .setMessage(getString(R.string.message_internet_service_remove_tip))
            .setStartText(getString(R.string.label_cancel))
            .setEndText(getString(R.string.label_delete))
            .setOnViewClickListener { _, _, tag ->
                if (tag == CommAlertDialog.TAG_CLICK_END) {
                    val isCurrent = if (key == currentServer) {
                        if (serverAdapter.itemCount == 2) {
                            currentServer = ""
                            SPSingleton.get().putString(SPKeyDefine.SP_SpeedUpServer, "")
                            CftUtils.updateCode()
                        }
                        true
                    } else {
                        false
                    }
                    SDKSingleton.apiServerBl.deleteSpeedUpServer(key)
                    refreshUI()
                    if (isCurrent && serverAdapter.itemCount > 1) {
                        val newKey = serverAdapter.dataList?.get(0)?.key
                        if (newKey != null) {
                            activeServer(newKey)
                        }
                    }
                }
            }.showDialog()
    }

    private fun addServer(content: String): Boolean {
        val codeStart = content.indexOf(":")
        if (codeStart > 0) {
            val title = content.substring(0, codeStart)
            val key = content.substring(codeStart + 1)
            return if (CftUtils.isValidConfig(key)) {
                SDKSingleton.apiServerBl.addSpeedUpServer(title, key)
                refreshUI()
                if (serverAdapter.itemCount == 2) {
                    activeServer(key)
                }
                true
            } else {
                false
            }
        } else {
            return false
        }
    }

    private fun activeServer(key: String) {
        currentServer = key
        SPSingleton.get().putString(SPKeyDefine.SP_SpeedUpServer, currentServer)
        serverAdapter.notifyDataSetChanged()
        CftUtils.updateCode()
    }

    private fun startQRScan() {
        if (checkSelfPermission(Manifest.permission.CAMERA) != PackageManager.PERMISSION_GRANTED) {
            requestPermissions(arrayOf(Manifest.permission.CAMERA), 0)
            return
        }
        val intent = Intent()
        intent.setClass(this, CaptureActivity::class.java)
        startActivityForResult(intent, 0)
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        if (requestCode == 0) {
            if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                startQRScan()
            }
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        if (data != null) {
            val content = data.getStringExtra(IntentConstants.EXTRA_qrCode)
            if (content != null) {
                if (!addServer(content)) {
                    ToastUtil.showToastShort(getString(R.string.label_service_status_invalid))
                }
            }
        } else {
            super.onActivityResult(requestCode, resultCode, null)
        }
    }

    override fun onDestroy() {
        CftUtils.setListener(null)
        super.onDestroy()
    }

    inner class ServerAdapter : BaseRecycleAdapter<SpeedUpServerEntity, ServerViewHolder>() {
        fun loadData() {
            val serverList = SDKSingleton.apiServerBl.getSpeedUpServerList().toMutableList()
            // 添加这个空白的 SpeedUpServerEntity ，是为了显示“添加二维码”的按钮，见 ServerViewHolder
            serverList.add(SpeedUpServerEntity(0L, "", "", 0L, 0L))
            dataList = serverList
        }

        override fun onCreateViewHolder(p0: ViewGroup, p1: Int): ServerViewHolder {
            return ServerViewHolder(p0)
        }

        override fun onBindViewHolder(holder: ServerViewHolder, position: Int) {
            val server = dataList!![position]
            holder.initUIData(server, position)
        }
    }

    inner class ServerViewHolder(parent: ViewGroup) :
        BaseViewHolder(parent, R.layout.holder_sever_item_layout) {
        private var timeTextView: TextView = itemView.findViewById(R.id.server_item_time_TextView)
        private var titleTextView: TextView = itemView.findViewById(R.id.server_item_title_TextView)
        private var statusLayout: View = itemView.findViewById(R.id.server_item_status_layout)
        private var statusTextView: TextView =
            itemView.findViewById(R.id.server_item_status_TextView)
        private var statusImageView: ImageView =
            itemView.findViewById(R.id.server_item_status_ImageView)
        private var chooseTextView: TextView =
            itemView.findViewById(R.id.server_item_choose_TextView)
        private var qrImageView: ImageView = itemView.findViewById(R.id.server_item_qr_ImageView)
        private var closeImageView: ImageView =
            itemView.findViewById(R.id.server_item_close_ImageView)
        private var mainLayout: View = itemView.findViewById(R.id.server_item_main_layout)

        override fun <T> initUIData(t: T, position: Int) {
            t as SpeedUpServerEntity
            if (t.key.isEmpty()) {
                mainLayout.visibility = View.GONE
                chooseTextView.text = getString(R.string.button_title_internet_add_service)
                itemView.background = getDrawable(R.color.transparent)
                if (APPConfig.isCurrentThemeLight()) {
                    chooseTextView.setTextColor(getColor(R.color.primary_light_color))
                    chooseTextView.setBackgroundResource(R.drawable.shape_gray_color_corner_56)
                } else {
                    chooseTextView.setTextColor(getColor(R.color.primary_dark_color))
                    chooseTextView.setBackgroundResource(R.drawable.shape_gray_color_corner_56)
                }
            } else {
                mainLayout.visibility = View.VISIBLE
                val qrImg = QRCodeUtil.createQRCode("${t.title}:${t.key}", 1024)
                qrImageView.setImageBitmap(qrImg)

                if (t.key == currentServer) {
                    itemView.background = getDrawable(R.drawable.shape_white_corner_red_edge)
                    statusLayout.visibility = View.VISIBLE
                    when (CftUtils.status) {
                        CftUtils.CFTStatus.CONNECTING -> {
                            statusTextView.text =
                                getString(R.string.label_service_status_connecting)
                            statusTextView.setTextColor(getColor(R.color.text_color_gray_99))
                            statusImageView.setImageResource(R.drawable.ic_rotate_loading_green)
                        }

                        CftUtils.CFTStatus.FAILED -> {
                            statusTextView.text = getString(R.string.label_service_status_invalid)
                            statusTextView.setTextColor(getColor(R.color.color_red_FF342A))
                            statusImageView.setImageResource(R.drawable.ic_clear_round_red)
                        }

                        CftUtils.CFTStatus.CONNECTED -> {
                            statusTextView.text = getString(R.string.label_service_status_active)
                            statusTextView.setTextColor(getColor(R.color.green_4DCC33))
                            statusImageView.setImageResource(R.drawable.ic_finished_green)
                        }

                        else -> {
                            statusLayout.visibility = View.GONE
                        }
                    }
                    chooseTextView.text = getString(R.string.button_already_use_the_service)
                    chooseTextView.setTextColor(getColor(R.color.text_color_gray_99))
                    chooseTextView.setBackgroundResource(R.drawable.shape_gray_04_corner)
                } else {
                    if (APPConfig.isCurrentThemeLight()) {
                        itemView.background = getDrawable(R.drawable.shape_white_color_conner)
                        chooseTextView.setTextColor(getColor(R.color.primary_light_color))
                        chooseTextView.setBackgroundResource(R.drawable.shape_gray_color_corner_56)
                    } else {
                        itemView.background = getDrawable(R.drawable.shape_white_color_conner_night)
                        chooseTextView.setTextColor(getColor(R.color.primary_dark_color))
                        chooseTextView.setBackgroundResource(R.drawable.shape_gray_color_corner_56)
                    }
                    statusLayout.visibility = View.GONE
                    chooseTextView.text = getString(R.string.button_title_use_this_service)
                }

                titleTextView.text = t.title
                timeTextView.text =
                    getString(R.string.label_speedup_server_add_time).format(
                        SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(
                            t.createTime * 1000
                        )
                    )

                closeImageView.setOnClickListener {
                    showDeleteDialog(t.key)
                }
            }
            chooseTextView.setOnClickListener {
                if (t.key.isEmpty()) {
                    startQRScan()
                } else if (t.key != currentServer) {
                    activeServer(t.key)
                }
            }
        }
    }
}