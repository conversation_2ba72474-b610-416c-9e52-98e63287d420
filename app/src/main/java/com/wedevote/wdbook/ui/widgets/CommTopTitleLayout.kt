package com.wedevote.wdbook.ui.widgets

import android.app.Activity
import android.content.Context
import android.util.AttributeSet
import android.util.TypedValue
import android.view.View
import android.view.inputmethod.InputMethodManager
import android.widget.ImageView
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.annotation.StringRes
import androidx.appcompat.app.AppCompatActivity
import com.wedevote.wdbook.R

/***
 * @date 创建时间 2019-09-28 16:34
 * <AUTHOR> W.<PERSON>Long
 * @description 通用的标题栏
 */
class CommTopTitleLayout @JvmOverloads constructor(context: Context, attrs: AttributeSet? = null) :
    RelativeLayout(context, attrs) {

    val backImageView: ImageView
    val titleTextView: TextView
    val leftTitleTextView: TextView

    init {
        View.inflate(context, R.layout.widget_comm_top_title_layout, this)
        backImageView = findViewById(R.id.top_title_back_ImageView)
        titleTextView = findViewById(R.id.top_title_content_TextView)
        leftTitleTextView = findViewById(R.id.top_title_left_TextView)
        initAttrs(context, attrs)

        backImageView.setOnClickListener {
            if (context is Activity) {
                if (null != context.currentFocus) {
                    val mInputMethodManager: InputMethodManager =
                        (context.getSystemService(AppCompatActivity.INPUT_METHOD_SERVICE) as InputMethodManager)!!
                    mInputMethodManager.hideSoftInputFromWindow(
                        context.currentFocus!!.windowToken,
                        0
                    )
                }

                context.onBackPressed()
            }
        }
    }

    private fun initAttrs(context: Context, attrs: AttributeSet?) {
        attrs?.let {

            val a = context.obtainStyledAttributes(attrs, R.styleable.CommTopTitleLayout)
            val title = a.getString(R.styleable.CommTopTitleLayout_attr_title_text)
            val textSize = a.getDimension(R.styleable.CommTopTitleLayout_attr_title_text_size, -1f)
            val colorStateList = a.getColorStateList(R.styleable.CommTopTitleLayout_attr_title_text_color)
            val isHideBack = a.getBoolean(R.styleable.CommTopTitleLayout_attr_hide_back, false)
            val maxTitleWidth = a.getDimensionPixelSize(R.styleable.CommTopTitleLayout_attr_max_title_width, -1)
            var backImageDrawable = a.getDrawable(R.styleable.CommTopTitleLayout_attr_back_src)
            var isBold = a.getBoolean(R.styleable.CommTopTitleLayout_attr_is_text_bold, false)
            a.recycle()

            setTitle(title)
            if (textSize != -1f) {
                titleTextView.setTextSize(TypedValue.COMPLEX_UNIT_PX, textSize)
            }

            if (isBold) {
                var paint = titleTextView.paint
                paint.isFakeBoldText = true
            }

            if (maxTitleWidth > 0) {
                titleTextView.maxWidth = maxTitleWidth
            }

            if (colorStateList != null) {
                titleTextView.setTextColor(colorStateList)
            }

            if (backImageDrawable != null) {
                backImageView.setImageDrawable(backImageDrawable)
            }

            backImageView.visibility = if (isHideBack) View.GONE else View.VISIBLE
        }
    }

    fun setTitleRes(@StringRes title: Int) {
        titleTextView.setText(title)
    }

    fun setTitle(title: String?) {
        titleTextView.text = title
    }

    fun setLeftTitle(title: String?) {
        leftTitleTextView.visibility = if (title.isNullOrEmpty()) View.GONE else View.VISIBLE
        leftTitleTextView.text = title
    }
}
