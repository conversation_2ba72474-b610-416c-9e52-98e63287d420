package com.wedevote.wdbook.ui.user

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.ObjectAnimator
import android.graphics.Color
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.ProgressBar
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.fragment.app.DialogFragment
import com.aquila.lib.log.KLog
import com.wedevote.wdbook.tools.util.ImageLoadUtil
import com.aquila.lib.tools.util.ToastUtil
import com.aquila.lib.widget.view.AdaptiveImageView
import com.wedevote.wdbook.R
import com.wedevote.wdbook.base.SDKSingleton
import com.wedevote.wdbook.entity.BookNoteCountEntity
import com.wedevote.wdbook.entity.NoteEntity
import com.wedevote.wdbook.entity.store.BookFileDownloadEntity
import com.wedevote.wdbook.tools.download.DownloaderEngine
import com.wedevote.wdbook.tools.download.OnDownloadingListener
import com.wedevote.wdbook.tools.util.IntentConstants
import com.wedevote.wdbook.tools.util.PictureUtil
import com.wedevote.wdbook.ui.read.BookReadActivity
import com.wedevote.wdbook.utils.JsonUtility

/***
 * @date 创建时间 2021/8/6 10:58
 * <AUTHOR> W.YuLong
 * @description
 */
class NoteBookDownloadDialogFragment : DialogFragment(), View.OnClickListener {
    lateinit var closeImageView: ImageView
    lateinit var coverImageView: AdaptiveImageView
    lateinit var nameTextView: TextView
    lateinit var percentTextView: TextView
    lateinit var promptTextView: TextView
    lateinit var progressBar: ProgressBar
    lateinit var failureTextView: TextView
    lateinit var containerLayout: ConstraintLayout

    lateinit var noteEntity: NoteEntity
    lateinit var noteCountEntity: BookNoteCountEntity
    lateinit var downloadEngine: DownloaderEngine
    var downloadFileId: String? = ""

    var isNotActive = false
    val onDownloadListener = object : OnDownloadingListener {
        var percent: Int = 0

        override fun onBeginning(entity: BookFileDownloadEntity) {
            progressBar.visibility = View.VISIBLE
            percentTextView.visibility = View.VISIBLE
            promptTextView.text = "下载完成后，自动跳转至划线位置"
        }

        override fun onDownloadingProgress(entity: BookFileDownloadEntity, downloadSize: Long, totalSize: Long) {
            if (entity.fileId == downloadFileId) {
                KLog.d("${entity.fileId}, downloadSize = $downloadSize, totalSize = $totalSize")
                percent = (downloadSize / totalSize).toInt()
                progressBar.progress = percent
                percentTextView.text = "$percent" + "%"
            }
        }

        override fun onComplete(entity: BookFileDownloadEntity) {
            if (isNotActive) {
                return
            }
            coverImageView.setMaskColor(Color.TRANSPARENT)
            progressBar.visibility = View.GONE
            percentTextView.visibility = View.GONE
            ToastUtil.showToastLong("下载完成")
            val info = SDKSingleton.dbWrapBl.getResourceDownloadInfo(noteEntity.resourceId)
            info?.let {
                BookReadActivity.gotoBookReadActivity(
                    requireContext(),
                    info.getActualFilePath(),
                    info.fileId,
                    info.resourceId,
                    JsonUtility.encodeToString(noteEntity),
                )
                dismiss()
            }
        }

        override fun onError(entity: BookFileDownloadEntity, errorDesc: String) {
            progressBar.visibility = View.GONE
            percentTextView.visibility = View.GONE
            failureTextView.visibility = View.VISIBLE
            promptTextView.text = getString(R.string.download_failure_prompt)
        }

        override fun onCancel(entity: BookFileDownloadEntity) {
            dismiss()
            ToastUtil.showToastShort(R.string.cancel_download_toast)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL, R.style.Dialog_FullScreen)
        downloadEngine = DownloaderEngine(requireActivity())
        downloadEngine.setOnDownloadingListener(onDownloadListener)
    }

    override fun onResume() {
        super.onResume()
        isNotActive = false
    }

    override fun onPause() {
        super.onPause()
        isNotActive = true
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        val v = inflater.inflate(R.layout.dialog_note_book_downloading_layout, container, false)
        initViewFromXML(v)
        setViewListeners()
        noteEntity = JsonUtility.decodeFromString(arguments?.getString(IntentConstants.EXTRA_NoteEntity) ?: "")
        noteCountEntity = JsonUtility.decodeFromString(arguments?.getString(IntentConstants.EXTRA_NoteCountEntity) ?: "")
        PictureUtil.loadImage(coverImageView, noteCountEntity.cover)
        nameTextView.text = noteCountEntity.name
        val info = SDKSingleton.dbWrapBl.getResourceDownloadInfo(noteEntity.resourceId)
        downloadFileId = info?.fileId
        downloadEngine.readyDownloadOnlyByFileId(downloadFileId!!)
        return v
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        containerLayout.post {
            ObjectAnimator.ofFloat(containerLayout, "alpha", 0.2f, 1f).apply {
                addListener(object : AnimatorListenerAdapter() {
                    override fun onAnimationStart(animation: Animator) {
                        super.onAnimationStart(animation)
                        containerLayout.visibility = View.VISIBLE
                    }
                })
                duration = 300
            }.start()
        }
    }

    private fun setViewListeners() {
        closeImageView.setOnClickListener(this)
        coverImageView.setOnClickListener(this)
        nameTextView.setOnClickListener(this)
    }

    private fun initViewFromXML(v: View) {
        containerLayout = v.findViewById(R.id.note_book_download_container_layout)
        closeImageView = v.findViewById(R.id.note_book_download_close_ImageView)
        coverImageView = v.findViewById(R.id.note_book_download_cover_ImageView)
        nameTextView = v.findViewById(R.id.note_book_download_name_TextView)
        promptTextView = v.findViewById(R.id.note_book_download_prompt_TextView)
        progressBar = v.findViewById(R.id.note_book_download_ProgressBar)
        percentTextView = v.findViewById(R.id.note_book_download_percent_TextView)
        failureTextView = v.findViewById(R.id.note_book_download_failure_TextView)
    }

    override fun onClick(v: View?) {
        when (v) {
            closeImageView -> {
                dismiss()
            }
            coverImageView, nameTextView -> {
                downloadEngine.readyDownloadOnlyByFileId(downloadFileId!!)
            }
        }
    }
}
