package com.wedevote.wdbook.ui.store

import android.text.TextUtils
import android.view.ViewGroup
import com.aquila.lib.base.BaseRecycleAdapter
import com.wedevote.wdbook.constants.DownloadStatus
import com.wedevote.wdbook.entity.store.BookFileDownloadEntity
import com.wedevote.wdbook.entity.store.ProductDetailEntity
import com.wedevote.wdbook.tools.download.DownloaderEngine
import com.wedevote.wdbook.tools.download.OnDownloadingListener
import com.wedevote.wdbook.ui.home.microwidget.BookProductViewHolder

/***
 *@date 创建时间 2020/8/20 18:55
 *<AUTHOR> W.<PERSON>ong
 *@description
 */
class PackageBookItemAdapter(var downloadEngine: DownloaderEngine) : BaseRecycleAdapter<ProductDetailEntity, BookProductViewHolder>() {

    var onDownloadingListener = object : OnDownloadingListener {
        override fun onWait(entity: BookFileDownloadEntity, isAdded: Boolean) {
            var option = DownloadOption(if (isAdded) DownloadStatus.WAIT else DownloadStatus.CANCEL)
            notifyItemChanged(findDataPosition(entity), option)
        }

        override fun onBeginning(entity: BookFileDownloadEntity) {
            var option = DownloadOption(DownloadStatus.BEGIN)
            notifyItemChanged(findDataPosition(entity), option)
        }

        override fun onDownloadingProgress(entity: BookFileDownloadEntity, downloadSize: Long, totalSize: Long) {
            var option = DownloadOption(DownloadStatus.DOWNLOADING, downloadSize, totalSize)
            notifyItemChanged(findDataPosition(entity), option)
        }

        override fun onPause(entity: BookFileDownloadEntity) {
            var option = DownloadOption(DownloadStatus.PAUSE)
            notifyItemChanged(findDataPosition(entity), option)
        }

        override fun onError(entity: BookFileDownloadEntity, errorDesc: String) {
            var option = DownloadOption(DownloadStatus.ERROR)
            notifyItemChanged(findDataPosition(entity), option)
        }

        override fun onComplete(entity: BookFileDownloadEntity) {
            var option = DownloadOption(DownloadStatus.COMPLETE)
            notifyItemChanged(findDataPosition(entity), option)
        }
    }

    init {
        downloadEngine.setOnDownloadingListener(onDownloadingListener)
    }

    fun findDataPosition(entity: BookFileDownloadEntity): Int {
        var position = -1
        dataList?.let {
            var isInList = false
            for (i in it.indices) {
                if (it[i].fileList != null) {
                    for (file in it[i].fileList) {
                        if (TextUtils.equals(entity.fileId, file.fileId)) {
                            position = i
                            isInList = true
                            break
                        }
                    }
                    if (isInList) {
                        break
                    }
                }
            }
        }
        return position
    }

    override fun onBindViewHolder(holder: BookProductViewHolder, position: Int, payloads: MutableList<Any>) {
        if (!payloads.isNullOrEmpty()) {
            if (payloads[0] is DownloadOption) {
                var option = payloads[0] as DownloadOption
                holder.updateButtonShow(option)
            }
        } else {
            onBindViewHolder(holder, position)
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BookProductViewHolder {
        return BookProductViewHolder(parent, viewType, downloadEngine)
    }

    override fun getItemViewType(position: Int): Int {
        return BookProductViewHolder.TYPE_PRODUCT_DETAIL
    }
}
