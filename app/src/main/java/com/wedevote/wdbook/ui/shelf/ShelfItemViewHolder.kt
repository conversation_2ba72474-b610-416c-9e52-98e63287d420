package com.wedevote.wdbook.ui.shelf

import android.os.Handler
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.ProgressBar
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.aquila.lib.base.BaseViewHolder
import com.aquila.lib.log.KLog
import com.aquila.lib.tools.singleton.ThreadPoolSingleton
import com.wedevote.wdbook.tools.util.ImageLoadUtil
import com.bumptech.glide.load.model.GlideUrl
import com.wedevote.wdbook.R
import com.wedevote.wdbook.base.ExceptionHandler
import com.wedevote.wdbook.base.SDKSingleton
import com.wedevote.wdbook.constants.DownloadStatus
import com.wedevote.wdbook.entity.shelf.HomeShelfItemCombineEntity
import com.wedevote.wdbook.entity.shelf.ShelfArchiveItemEntity
import com.wedevote.wdbook.entity.shelf.ShelfBookItemEntity
import com.wedevote.wdbook.entity.shelf.ShelfDataType
import com.wedevote.wdbook.tools.download.DownloaderEngine
import com.wedevote.wdbook.tools.util.DataPathUtil
import com.wedevote.wdbook.tools.util.FilePathUtil
import com.wedevote.wdbook.tools.util.MD5EncryptUtil
import com.wedevote.wdbook.tools.util.NetWorkUtils
import com.wedevote.wdbook.tools.util.PictureUtil
import com.wedevote.wdbook.tools.util.dp2px
import com.wedevote.wdbook.tools.util.findString
import com.wedevote.wdbook.tools.util.getPictureRemotePath
import com.wedevote.wdbook.ui.read.BookReadActivity
import com.wedevote.wdbook.ui.service.SyncDataService
import com.wedevote.wdbook.ui.store.DownloadOption
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import java.io.File

/***
 *@date 创建时间 2020/6/3 18:17
 *<AUTHOR> W.YuLong
 *@description
 */
class ShelfItemViewHolder(parent: ViewGroup, var downloadEngine: DownloaderEngine) :
    BaseViewHolder(parent, R.layout.holder_book_shelf_item_layout) {
    private val readPercentTextView: TextView =
        itemView.findViewById(R.id.shelf_book_read_percent_TextView)
    private val percentTextView: TextView =
        itemView.findViewById(R.id.note_book_download_percent_TextView)
    private val downloadContainerLayout: ConstraintLayout =
        itemView.findViewById(R.id.shelf_download_info_container_layout)

    val coverImageView: ImageView = itemView.findViewById(R.id.shelf_book_cover_ImageView)
    val maskView: View = itemView.findViewById(R.id.shelf_mask_View)
    val nameTextView: TextView = itemView.findViewById(R.id.shelf_book_name_TextView)
    val progressBar: ProgressBar = itemView.findViewById(R.id.note_book_download_ProgressBar)
    val cloudImageView: ImageView = itemView.findViewById(R.id.shelf_book_status_ImageView)
    val selectImageView: ImageView = itemView.findViewById(R.id.shelf_book_check_ImageView)
    var shelfItemCombineEntity: HomeShelfItemCombineEntity? = null
    var isEditMode: Boolean = false
    var tryDownloadTimes: Int = 3

    override fun <T> initUIData(t: T) {
        shelfItemCombineEntity = t as HomeShelfItemCombineEntity
        when (shelfItemCombineEntity!!.dataType) {
            ShelfDataType.RESOURCE -> initBookUI(shelfItemCombineEntity!!.bookItemEntity!!)
            ShelfDataType.ARCHIVE -> initArchiveUI(shelfItemCombineEntity!!.archiveEntity!!)
        }
    }

    /*显示书籍的UI*/
    private fun initBookUI(shelfItemEntity: ShelfBookItemEntity) {
        nameTextView.text = shelfItemEntity.resourceName
        coverImageView.scaleType = ImageView.ScaleType.FIT_XY
        coverImageView.setPadding(0, 0, 0, 0)
        initDownloadStatusUI(shelfItemEntity)
        var url = shelfItemEntity.cover
        if (url.isNullOrEmpty()) {
            url = SDKSingleton.userBl.getUserResource(shelfItemEntity.resourceId)?.cover ?: ""
        }
        PictureUtil.loadImage(coverImageView, shelfItemEntity.cover)
    }

    /*显示书架显示的UI*/
    private fun initArchiveUI(archiveEntity: ShelfArchiveItemEntity) {
        nameTextView.text = archiveEntity.archiveName
        cloudImageView.visibility = View.GONE
        readPercentTextView.visibility = View.GONE
        val urlList = SDKSingleton.dbWrapBl.getShelfArchiveUrlList(archiveEntity.clientArchiveId)
        if (!urlList.isNullOrEmpty()) {
            var pictureFolderPath = DataPathUtil.getPictureCachePath(archiveEntity.clientArchiveId)
            val pictureName: String = MD5EncryptUtil.md5String(PictureUtil.appendUrlToStr(urlList))
            if (File(pictureFolderPath, SDKSingleton.appBl.isCurrentThemeLight().toString() + pictureName).exists()) {
                PictureUtil.loadImageWithRemotePath(coverImageView, pictureFolderPath + SDKSingleton.appBl.isCurrentThemeLight().toString() + pictureName, R.drawable.ic_folder_black)
            } else {
                coverImageView.post {
                    val width = coverImageView.width
                    val height = coverImageView.height
                    val builder = PictureUtil.Builder(width, height).apply {
                        paddingSize = dp2px(5)
                        roundSize = dp2px(4f)
                        itemMargin = dp2px(3)
                    }

                    ThreadPoolSingleton.executeTask {
                        val realUrlList = ArrayList<GlideUrl?>()
                        for (url in urlList) {
                            realUrlList.add(getPictureRemotePath(url))
                        }
                        val bitmap = PictureUtil.formatCellUrlBitmap(realUrlList, builder)
                        if (bitmap != null) {
                            coverImageView.post {
                                FilePathUtil.deleteFolderFile(File(pictureFolderPath), false)
                                ImageLoadUtil.writeBitmapToFile(bitmap, pictureFolderPath + SDKSingleton.appBl.isCurrentThemeLight().toString() + pictureName)
                                ImageLoadUtil.loadBitMapImage(coverImageView, bitmap)
                            }
                        } else {
                            setDefaultFolderIcon()
                        }
                    }
                }
            }
        } else {
            setDefaultFolderIcon()
        }
    }

    private fun setDefaultFolderIcon() {
        coverImageView.scaleType = ImageView.ScaleType.CENTER
        coverImageView.setBackgroundResource(R.color.color_F5F5F5)
        coverImageView.setImageResource(R.drawable.ic_folder_black)
    }

    private fun initDownloadStatusUI(entity: ShelfBookItemEntity) {
        readPercentTextView.visibility = View.GONE
        val resourceDownloadInfo = entity.resourceDownloadInfo
        when (resourceDownloadInfo.downloadStatus) {
            DownloadStatus.COMPLETE,
            DownloadStatus.UPDATE,
            -> {
                downloadContainerLayout.visibility = View.GONE
                maskView.visibility = View.GONE
                if (!File(resourceDownloadInfo.getActualFilePath()).exists()) {
                    cloudImageView.setImageResource(R.drawable.ic_shelf_book_cloud)
                    cloudImageView.visibility = View.VISIBLE
                } else {
                    cloudImageView.visibility = View.GONE
                    if (entity.progress >= 0) {
                        readPercentTextView.text = "${entity.progress}%"
                        readPercentTextView.visibility = View.VISIBLE
                    }
                }
            }
            DownloadStatus.WAIT -> {
                if (removeArchiveMaskView()) return
                downloadContainerLayout.visibility = View.VISIBLE
                maskView.visibility = View.VISIBLE
                percentTextView.setText(R.string.wait_download)
            }
            DownloadStatus.PAUSE,
            DownloadStatus.BEGIN,
            DownloadStatus.DOWNLOADING,
            -> {
                // 下载临时文件存在
                if (!resourceDownloadInfo.getTempFilePath().isNullOrEmpty()) {
                    var tmpFileSize: Long = 0
                    val tmpFile = File(resourceDownloadInfo.getTempFilePath())
                    if (tmpFile.exists()) {
                        tmpFileSize = tmpFile.length()
                    }
                    if (shelfItemCombineEntity?.dataType != ShelfDataType.ARCHIVE) {
                        downloadContainerLayout.visibility = View.VISIBLE
                    }
                    var progress: Int = 0
                    if (resourceDownloadInfo.fileSize > 0) {
                        progress = (tmpFileSize.toFloat() / resourceDownloadInfo.fileSize * 100).toInt()
                    }
                    if (progress >= 99) {
                        progress = 99
                    }
                    if (progress > progressBar.progress) {
                        progressBar.progress = progress
                        if (downloadEngine.isInDownloadingTaskQueue(resourceDownloadInfo.fileId)) {
                            percentTextView.text = "$progress%"
                        } else {
                            percentTextView.text = "暂停中 $progress%"
                        }
                    }
                } else { // 下载临时文件不存在
                    cloudImageView.setImageResource(R.drawable.ic_shelf_book_cloud)
                    cloudImageView.visibility = View.VISIBLE
                }
            }
            DownloadStatus.ERROR -> {
                downloadContainerLayout.visibility = View.VISIBLE
                percentTextView.setText(R.string.download_error)
                if (tryDownloadTimes < 1) {
                    return
                }
                try {
                    Handler().postDelayed({
                        clickDownloadAction(entity, false)
                    },5000)
                    tryDownloadTimes -= 1
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
            else -> { // 其他情况
                downloadContainerLayout.visibility = View.GONE
                cloudImageView.visibility = View.VISIBLE
                cloudImageView.setImageResource(R.drawable.ic_shelf_book_cloud)
            }
        }
    }

    private fun removeArchiveMaskView(): Boolean {
        if (shelfItemCombineEntity!!.dataType == ShelfDataType.ARCHIVE) {
            downloadContainerLayout.visibility = View.GONE
            setMaskViewShow(false)
            return true
        }
        return false
    }

    fun doItemClick() {
        shelfItemCombineEntity?.let {
            when (it.dataType) {
                ShelfDataType.RESOURCE -> {
                    val downloadInfo = it.bookItemEntity!!.resourceDownloadInfo
                    if ((downloadInfo.downloadStatus == DownloadStatus.COMPLETE || downloadInfo.downloadStatus == DownloadStatus.UPDATE) &&
                        File(downloadInfo.getActualFilePath()).exists()
                    ) {
                        it.bookItemEntity!!.lastVisitTime = System.currentTimeMillis()
                        BookReadActivity.gotoBookReadActivity(
                            itemView.context,
                            downloadInfo.getActualFilePath(),
                            downloadInfo.fileId,
                            it.bookItemEntity!!.resourceId
                        )
                    } else {
                        if (!NetWorkUtils.isNetworkAvailable()) {
                            NetWorkUtils.showTipDialog(downloadEngine.activity, downloadEngine.activity.getString(R.string.no_network_connect))
                            return
                        }
                        clickDownloadAction(it.bookItemEntity!!, true)
                    }
                }
                ShelfDataType.ARCHIVE -> {
                    FolderBookListActivity.gotoFolderBookListActivity(itemView.context, it.archiveEntity!!)
                }
            }
        }
    }

    /*执行下载动作*/
    private fun clickDownloadAction(bookItemEntity: ShelfBookItemEntity, isShowErrorTip: Boolean) {
        var fileId = bookItemEntity.resourceDownloadInfo.fileId
        if (fileId.isNullOrEmpty()) {
//            if (SyncDataService.isInSyncProgress) {
//                ToastUtil.showToastShort(R.string.syncing_data_toast)
//                return
//            }
            //                val resInfo = SDKSingleton.dbWrapBl.getResourceDownloadInfo(bookItemEntity.resourceId)
            MainScope().launch(ExceptionHandler.coroutineExceptionHandler) {
                var resInfo =
                    SDKSingleton.storeBl.getResourceFileEntity(bookItemEntity.resourceId)
                if (resInfo != null && !resInfo.fileId.isNullOrEmpty()) {
                    fileId = resInfo.fileId
                    downloadEngine.readyDownloadOnlyByFileId(fileId!!)
                } else {
                    //TODO 执行更新resourceId的fileInfo数据

                    if (isShowErrorTip) {
                        NetWorkUtils.showTipDialog(itemView.context, itemView.context.getString(R.string.no_network_connect))
//                        ToastUtil.showToastShort(R.string.data_exception_prompt)
                    }
                    return@launch
                }
            }

        } else {
            downloadEngine.readyDownloadOnlyByFileId(fileId!!)
        }
    }

    fun setMaskViewShow(isShow: Boolean) {
        if (isShow) {
            if (shelfItemCombineEntity!!.dataType == ShelfDataType.RESOURCE) {
                maskView.visibility = View.VISIBLE
            }
        } else {
            if (downloadContainerLayout.visibility != View.VISIBLE && !isEditMode) {
                maskView.visibility = View.GONE
            }
        }
    }

    /*更新下载进度的UI*/
    fun updateDownloadStatus(option: DownloadOption) {

        if (removeArchiveMaskView()) return

        when (option.downloadStatus) {
            DownloadStatus.WAIT -> {
                downloadContainerLayout.visibility = View.VISIBLE
                setMaskViewShow(true)
                cloudImageView.visibility = View.GONE
                percentTextView.setText(R.string.wait_download)
            }
            DownloadStatus.CANCEL -> {
                initDownloadStatusUI(shelfItemCombineEntity!!.bookItemEntity!!)
            }
            DownloadStatus.QUEUE, DownloadStatus.BEGIN -> {
                downloadContainerLayout.visibility = View.VISIBLE
                setMaskViewShow(true)
                cloudImageView.visibility = View.GONE
            }
            DownloadStatus.DOWNLOADING -> {
                downloadContainerLayout.visibility = View.VISIBLE
                setMaskViewShow(true)
                var progress = option.getProgress().toInt()
                if (progress >= 99) {
                    progress = 99
                }
                progressBar.progress = progress
                percentTextView.text = "${progressBar.progress}%"
                if (cloudImageView.visibility != View.GONE) {
                    cloudImageView.visibility = View.GONE
                }
            }
            DownloadStatus.PAUSE -> {
                if (cloudImageView.visibility != View.GONE) {
                    cloudImageView.visibility = View.GONE
                }
                percentTextView.text = findString(R.string.pause_in_percent) + " ${progressBar.progress}%"
            }
            DownloadStatus.COMPLETE -> {
                cloudImageView.visibility = View.GONE
                downloadContainerLayout.visibility = View.GONE
                setMaskViewShow(false)
            }
            DownloadStatus.ERROR -> {
                downloadContainerLayout.visibility = View.GONE
                setMaskViewShow(false)
                cloudImageView.visibility = View.VISIBLE
            }
            else -> {
                KLog.e("Unhandled download status ${option.downloadStatus}")
            }
        }
    }

    fun setItemSelectedState(isSelected: Boolean) {
        selectImageView.isSelected = isSelected
    }

    private fun isBookTypeHolder(): Boolean {
        return shelfItemCombineEntity!!.dataType == ShelfDataType.RESOURCE
    }

    fun updateEditModeUI(isEditMode: Boolean) {
        this.isEditMode = isEditMode
        if (isEditMode) {
            if (isBookTypeHolder()) {
                selectImageView.visibility = View.VISIBLE
                itemView.alpha = 1f
            } else {
                itemView.alpha = 0.3f
                selectImageView.visibility = View.GONE
            }
        } else {
            selectImageView.visibility = View.GONE
            itemView.alpha = 1f
        }
        setMaskViewShow(isEditMode)
    }
}
