package com.wedevote.wdbook.ui.read.widgets

import android.app.Activity
import android.content.Context
import android.util.AttributeSet
import android.view.View
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.SeekBar
import com.aquila.lib.base.OnViewClickListener
import com.aquila.lib.tools.interfaceimpl.OnSeekBarListenerImpl
import com.aquila.lib.tools.singleton.SPSingleton
import com.wedevote.wdbook.R
import com.wedevote.wdbook.base.APPConfig
import com.wedevote.wdbook.base.SDKSingleton
import com.wedevote.wdbook.tools.util.APPUtil
import com.wedevote.wdbook.tools.util.SPKeyDefine
import com.wedevote.wdbook.ui.read.lib.css.CSSParser

/***
 * @date 创建时间 2020/6/16 15:45
 * <AUTHOR> W<PERSON>
 * @description 亮度设置的封装布局
 */
class OptionBrightnessLayout @JvmOverloads constructor(context: Context, attr: AttributeSet? = null) :
    LinearLayout(context, attr),
    View.OnClickListener {
    val themeNightImageView: ImageView
    val themeWhiteImageView: ImageView
    val upImageView: ImageView
    val downImageView: ImageView
    val seekBar: SeekBar

    var onViewClickListener: OnViewClickListener? = null

    init {
        View.inflate(context, R.layout.option_read_bright_layout, this)
        themeNightImageView = findViewById(R.id.brightness_theme_night_ImageView)
        themeWhiteImageView = findViewById(R.id.brightness_theme_white_ImageView)
        upImageView = findViewById(R.id.brightness_up_ImageView)
        downImageView = findViewById(R.id.brightness_down_ImageView)
        seekBar = findViewById(R.id.brightness_set_SeekBar)
    }

    fun initUI() {
        val progress = SPSingleton.get().getInt(SPKeyDefine.SP_Brightness, -1)
        if (progress >= 0) { // 没有调过亮度值的情况下默认跟随系统
            seekBar.progress = progress
            APPUtil.setScreenBrightness(context as Activity, seekBar.progress.toFloat())
        }
        setViewListeners()
        if (APPConfig.isCurrentThemeLight()) {
            themeWhiteImageView.isSelected = true
            themeNightImageView.isSelected = false
        } else {
            themeWhiteImageView.isSelected = false
            themeNightImageView.isSelected = true
        }
    }

    private val onSeekBarChangeListener = object : OnSeekBarListenerImpl() {
        override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
            if (fromUser) {
                updateBrightness(progress)
            }
        }

        override fun onStopTrackingTouch(seekBar: SeekBar?) {
        }
    }

    private fun updateBrightness(brightness: Int) {
        APPUtil.setScreenBrightness(context as Activity, brightness.toFloat())
        SPSingleton.get().putInt(SPKeyDefine.SP_Brightness, brightness)
    }

    fun setViewListeners() {
        upImageView.setOnClickListener(this)
        downImageView.setOnClickListener(this)
        themeWhiteImageView.setOnClickListener(this)
        themeNightImageView.setOnClickListener(this)
        seekBar.setOnSeekBarChangeListener(onSeekBarChangeListener)
    }

    override fun onClick(v: View?) {
        when (v) {
            upImageView -> {
                seekBar.progress = seekBar.progress + 1
                updateBrightness(seekBar.progress)
            }
            downImageView -> {
                seekBar.progress = seekBar.progress - 1
                updateBrightness(seekBar.progress)
            }
            themeWhiteImageView -> {
                if (!APPConfig.isCurrentThemeLight()) {
                    CSSParser.releaseInstance()
                    SDKSingleton.appBl.toggleCurrentTheme()
                    onViewClickListener?.onClickAction(v, OptionClickTag.TAG_THEME_WHITE, "")
                }
            }
            themeNightImageView -> {
                if (APPConfig.isCurrentThemeLight()) {
                    CSSParser.releaseInstance()
                    SDKSingleton.appBl.toggleCurrentTheme()
                    onViewClickListener?.onClickAction(v, OptionClickTag.TAG_THEME_BLACK, "")
                }
            }
        }
    }
}
