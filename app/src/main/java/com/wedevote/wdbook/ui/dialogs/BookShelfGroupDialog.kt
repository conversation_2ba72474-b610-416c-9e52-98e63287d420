package com.wedevote.wdbook.ui.dialogs

import android.app.Dialog
import android.content.Context
import android.os.Bundle
import android.view.Gravity
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.widget.Button
import android.widget.ImageView
import android.widget.TextView
import com.aquila.lib.base.BaseRecycleAdapter
import com.aquila.lib.base.BaseViewHolder
import com.aquila.lib.base.OnViewClickListener
import com.aquila.lib.tools.util.ScreenUtil
import com.aquila.lib.widget.group.GroupImageTextLayout
import com.aquila.lib.widget.view.CustomRecyclerView
import com.wedevote.wdbook.R
import com.wedevote.wdbook.base.SDKSingleton
import com.wedevote.wdbook.entity.shelf.ShelfArchiveItemEntity

/***
 * @date 创建时间 2020/6/9 15:37
 * <AUTHOR> <PERSON><PERSON>
 * @description 书籍目录分类的对话框
 */
class BookShelfGroupDialog(context: Context, var isInGroup: Boolean = false, var onViewClickListener: OnViewClickListener? = null) :
    Dialog(context),
    View.OnClickListener {
    lateinit var titleTextView: TextView
    lateinit var cancelButton: Button
    lateinit var dataRecyclerView: CustomRecyclerView
    lateinit var addNewLayout: GroupImageTextLayout
    lateinit var removeCurrentLayout: GroupImageTextLayout
    lateinit var bookAdapter: BookGroupAdapter

    var index = -1

    companion object {
        const val TAG_REMOVE_CURRENT_FOLDER = "REMOVE_CURRENT_FOLDER"
        const val TAG_CREATE_FOLDER = "CREATE_FOLDER"
        const val TAG_SELECT_FOLDER = "SELECT_FOLDER"
        const val TAG_CANCEL = "CANCEL"
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.dialog_view_book_group_layout)

        titleTextView = findViewById(R.id.book_group_title_TextView)
        cancelButton = findViewById(R.id.book_group_cancel_Button)
        dataRecyclerView = findViewById(R.id.book_group_data_RecyclerView)
        addNewLayout = findViewById(R.id.book_group_create_new_Layout)
        removeCurrentLayout = findViewById(R.id.book_group_move_Layout)

        bookAdapter = BookGroupAdapter(onViewClickListener)
        dataRecyclerView.adapter = bookAdapter

        configDialog()
        initUIData()
        removeCurrentLayout.visibility = if (isInGroup) View.VISIBLE else View.GONE
        cancelButton.setOnClickListener(this)
        addNewLayout.setOnClickListener(this)
        removeCurrentLayout.setOnClickListener(this)
    }

    fun initUIData() {
        val dataList = arrayListOf<ShelfArchiveItemEntity>()
        SDKSingleton.dbWrapBl.getArchiveDataList()?.let {
            dataList.addAll(it)
        }
        bookAdapter.dataList = dataList
    }

    fun initAttachedArchiveId(clientArchiveId: String) {
        bookAdapter.dataList?.let {
            for (i in it.indices) {
                if (it[i].clientArchiveId == clientArchiveId) {
                    index = i
                    bookAdapter.selectPosition = index
                    break
                }
            }
        }
    }

    private fun configDialog() {
        val wl = window!!.attributes
        wl.gravity = Gravity.CENTER // 设置重力
        wl.width = (ScreenUtil.getScreenWidth() * 0.8f).toInt()
        wl.height = WindowManager.LayoutParams.WRAP_CONTENT
//        window!!.setWindowAnimations(R.style.centerDialogWindowAnim)
        window!!.attributes = wl
        window!!.setBackgroundDrawableResource(android.R.color.transparent)
    }

    override fun onClick(v: View?) {
        when (v) {
            cancelButton -> {
                dismiss()
                onViewClickListener?.onClickAction(v, TAG_CANCEL, "")
            }
            addNewLayout -> {
                val createBookGroupDialog = CreateBookGroupDialog(
                    v.context,
                    object : OnCreateBookGroupCallBack {
                        override fun onCallBack(dialog: Dialog, name: String) {
                            onViewClickListener?.onClickAction(v, TAG_CREATE_FOLDER, name)
                            dialog.dismiss()
                        }
                    }
                )
                createBookGroupDialog.show()
                dismiss()
            }
            removeCurrentLayout -> {
                onViewClickListener?.onClickAction(v, TAG_REMOVE_CURRENT_FOLDER, "")
            }
        }
    }

    /***
     *@date 创建时间 2020/6/11 15:07
     *<AUTHOR> W.YuLong
     *@description
     */
    class BookGroupAdapter(var onViewClickListener: OnViewClickListener? = null) :
        BaseRecycleAdapter<ShelfArchiveItemEntity, BookGroupItemViewHolder>() {

        var selectPosition = -1
            set(value) {
                field = value
                notifyDataSetChanged()
            }

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BookGroupItemViewHolder {
            return BookGroupItemViewHolder(parent)
        }

        override fun onBindViewHolder(holder: BookGroupItemViewHolder, position: Int) {
            super.onBindViewHolder(holder, position)
            holder.initCheckUI(position == selectPosition)
            holder.itemView.setOnClickListener { v ->
                if (selectPosition != position) {
                    onViewClickListener?.onClickAction(v, TAG_SELECT_FOLDER, dataList!![position])
                }
            }
        }
    }

    /***
     *@date 创建时间 2020/6/11 15:07
     *<AUTHOR> W.YuLong
     *@description
     */
    class BookGroupItemViewHolder(parent: ViewGroup) : BaseViewHolder(parent, R.layout.item_book_group_layout) {
        val iconImageView: ImageView = itemView.findViewById(R.id.item_book_group_icon_ImageView)
        val nameTextView: TextView = itemView.findViewById(R.id.item_book_group_name_TextView)
        val checkImageView: ImageView = itemView.findViewById(R.id.item_book_group_check_ImageView)

        override fun <T> initUIData(t: T) {
            t as ShelfArchiveItemEntity
            nameTextView.text = t.archiveName
        }

        fun initCheckUI(isChecked: Boolean) {
            checkImageView.visibility = if (isChecked) View.VISIBLE else View.GONE
        }
    }
}
