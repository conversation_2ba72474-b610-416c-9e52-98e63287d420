package com.wedevote.wdbook.ui.read.lib.data

class TocItem {
    var title: String
    var path: String
    var tag: String
    var level: Int

    constructor(title: String, path: String, tag: String, level: Int) {
        this.title = title.trim()
        this.path = path
        this.tag = tag
        this.level = level
    }

    constructor() {
        title = ""
        path = ""
        tag = ""
        level = 0
    }

    val fullPath: String
        get() = if (tag.isEmpty()) {
            path
        } else {
            path + "#" + tag
        }

    override fun toString(): String {
        return "TocItem{" +
            "mTitle=" + title +
            "," + "mPath=" + path +
            "," + "mTag=" + tag +
            "," + "mLevel=" + level +
            "}"
    }
}
