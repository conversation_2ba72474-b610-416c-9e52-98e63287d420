package com.wedevote.wdbook.ui.read.lib.span;

import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.util.Base64;

import com.wedevote.wdbook.tools.util.ImageLoadUtil;

import java.io.File;

import p67.fox.Client;


/**
 * Created by theophilus on 12/12/16.
 */

public class WDImageSpan {
    private Bitmap bitmap;
    private final String imagePath;
    private boolean scaled = false;

    public WDImageSpan(String imagePath){
        this.imagePath = imagePath;
        bitmap = loadBitmap();
    }

    private Bitmap getBitmap(byte[] imageData) {
        BitmapFactory.Options opts = new BitmapFactory.Options();
        opts.inPreferredConfig = Bitmap.Config.RGB_565;//to reduce the memory of the bitmap...to avoid the exception "out of memory"
        try{
            return BitmapFactory.decodeByteArray(imageData, 0, imageData.length,opts);
        }catch (Exception e){
            opts.inSampleSize = 2;
            opts.inPurgeable = true;
            return BitmapFactory.decodeByteArray(imageData, 0, imageData.length,opts);
        }
    }

    private Bitmap loadBitmap(){
        if(imagePath.length() < 512){
            if(!imagePath.startsWith("http") && !(new File(imagePath)).exists()){
                return null;
            }
            return ImageLoadUtil.getBitmapUrlAsync(imagePath, Client.proxy());
        }else{
            byte[]imageData = Base64.decode(imagePath,Base64.DEFAULT);
            return getBitmap(imageData);
        }
    }

    public Bitmap getOriginalBitmap(){
        if(scaled){
            return loadBitmap();
        }else{
            return bitmap;
        }
    }

    public Bitmap getBitmap(){
        return bitmap;
    }

    public void scaleBitmap(float maxWidth,float maxHeight){
        if(bitmap == null || (bitmap.getWidth() < maxWidth+1 && bitmap.getHeight() < maxHeight)){
            return;
        }

        float scaleW = maxWidth/bitmap.getWidth();
        float scaleH = maxHeight/bitmap.getHeight();
        float scale = Math.min(scaleW,scaleH);

        int w = (int)(bitmap.getWidth()*scale);
        int h = (int)(bitmap.getHeight()*scale);

        Bitmap newBitmap = Bitmap.createScaledBitmap(bitmap,w,h,false);
        if(newBitmap != null){
            bitmap.recycle();
            bitmap = newBitmap;
        }
        scaled = true;
    }
}
