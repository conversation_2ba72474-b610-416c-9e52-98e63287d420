package com.wedevote.wdbook.ui.store

import android.os.Bundle
import android.widget.TextView
import com.wedevote.wdbook.R
import com.wedevote.wdbook.base.RootActivity
import com.wedevote.wdbook.tools.util.IntentConstants

/***
 * @date 创建时间 2020/9/1 16:09
 * <AUTHOR> <PERSON><PERSON>
 * @description 书籍详情点击目录的页面
 */
class ContentsActivity : RootActivity() {
    lateinit var detailTextView: TextView

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_book_contents_layout)
        detailTextView = findViewById(R.id.book_contents_details_TextView)

        var contentsStr = intent.getStringExtra(IntentConstants.EXTRA_BookContents)
        detailTextView.text = contentsStr
    }
}
