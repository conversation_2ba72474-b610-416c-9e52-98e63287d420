package com.wedevote.wdbook.ui.user.notification

import android.os.Bundle
import com.aquila.lib.tools.singleton.SPSingleton
import com.wedevote.wdbook.R
import com.wedevote.wdbook.base.RootActivity
import com.wedevote.wdbook.tools.util.SPKeyDefine
import com.wedevote.wdbook.ui.widgets.CommTopTitleLayout
import com.wedevote.wdbook.ui.widgets.SlideSwitch

/***
 * @date 创建时间 2022/5/23 12:01
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON>
 * @description
 */
class NotificationSettingActivity : RootActivity() {
    lateinit var topTitleLayout: CommTopTitleLayout
    lateinit var messageSlideSwitch: SlideSwitch

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_notification_setting_layout)
        messageSlideSwitch = findViewById(R.id.notification_setting_receive_SlideSwitch)
        messageSlideSwitch.state = SPSingleton.get().getBoolean(SPKeyDefine.SP_ReceiveCouponNotification, true)
        messageSlideSwitch.setSlideListener { slideSwitch, isOpen ->
            SPSingleton.get().putBoolean(SPKeyDefine.SP_ReceiveCouponNotification, isOpen)
        }
    }

}