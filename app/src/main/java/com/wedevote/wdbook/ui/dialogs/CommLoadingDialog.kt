package com.wedevote.wdbook.ui.dialogs

import android.content.Context
import android.graphics.drawable.AnimationDrawable
import android.os.Bundle
import android.widget.ImageView
import android.widget.TextView
import com.wedevote.wdbook.R

class CommLoadingDialog(context: Context) : BaseDialog(context) {
    /*** 上面动画View */
    private lateinit var spinnerImageView: ImageView

    /*** 下面文字View */
    private lateinit var messageTextView: TextView

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.dialog_loading_layout)
        spinnerImageView = findViewById(R.id.loading_progress_ImageView)
        messageTextView = findViewById(R.id.loading_content_TextView)
        window!!.setBackgroundDrawableResource(android.R.color.transparent)
    }

    /*** 当窗口焦点改变时调用 */
    override fun onWindowFocusChanged(hasFocus: Boolean) {
        val spinner = spinnerImageView.background as AnimationDrawable?
        spinner?.start()
    }

    fun setDialogText(text: CharSequence?) {
        messageTextView.text = text
    }
}