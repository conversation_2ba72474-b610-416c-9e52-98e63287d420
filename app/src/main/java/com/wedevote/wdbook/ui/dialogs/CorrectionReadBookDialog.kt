package com.wedevote.wdbook.ui.dialogs

import android.app.Dialog
import android.content.Context
import android.os.Bundle
import android.view.Gravity
import android.view.View
import android.view.View.OnClickListener
import android.view.ViewGroup
import android.view.WindowManager
import android.widget.Button
import android.widget.ImageView
import android.widget.TextView
import com.aquila.lib.base.BaseRecycleAdapter
import com.aquila.lib.base.BaseViewHolder
import com.aquila.lib.log.KLog
import com.aquila.lib.tools.util.ToastUtil
import com.aquila.lib.widget.view.CustomRecyclerView
import com.wedevote.wdbook.R
import com.wedevote.wdbook.base.ExceptionHandler
import com.wedevote.wdbook.base.SDKSingleton
import com.wedevote.wdbook.constants.EntranceType
import com.wedevote.wdbook.entity.feedback.CorrectionParamsEntity
import com.wedevote.wdbook.entity.feedback.FeedbackSaveEntity
import com.wedevote.wdbook.entity.feedback.FeedbackTagEntity
import com.wedevote.wdbook.utils.JsonUtility
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch

/***
 * @date 创建时间 2022/5/10 18:47
 * <AUTHOR> W.YuLong
 * @description
 */
class CorrectionReadBookDialog(context: Context) : Dialog(context), OnClickListener {
    lateinit var dataRecyclerView: CustomRecyclerView
    lateinit var cancelButton: Button
    lateinit var submitButton: Button
    
    lateinit var dataAdapter: CorrectionTypeAdapter
    
    var correctionParamsEntity: CorrectionParamsEntity? = null
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.dialog_correction_layout)
        dataRecyclerView = findViewById(R.id.correction_data_RecyclerView)
        cancelButton = findViewById(R.id.correction_cancel_Button)
        submitButton = findViewById(R.id.correction_submit_Button)
        cancelButton.setOnClickListener(this)
        submitButton.setOnClickListener(this)
    
        dataAdapter = CorrectionTypeAdapter()
        dataRecyclerView.adapter = dataAdapter
    
        configDialog()
    }
    
    fun setDataList(dataList: List<FeedbackTagEntity>?) {
        dataAdapter.dataList = dataList?.toMutableList()
    }
    
    protected fun configDialog() {
        val wl = window!!.attributes
        wl.gravity = Gravity.CENTER // 设置重力
        wl.width = WindowManager.LayoutParams.MATCH_PARENT
        wl.height = WindowManager.LayoutParams.WRAP_CONTENT
        window!!.setWindowAnimations(com.aquila.lib.dialog.R.style.centerDialogWindowAnim)
        window!!.attributes = wl
        window!!.setBackgroundDrawableResource(android.R.color.transparent)
    }
    
    override fun onClick(v: View?) {
        when (v) {
            cancelButton -> {
                dismiss()
            }
            submitButton -> {
                if (!dataAdapter.selectList.isNullOrEmpty()) {
                    MainScope().launch(ExceptionHandler.coroutineExceptionHandler) {
                        val entity = FeedbackSaveEntity().also {
                            it.entrance = EntranceType.FROM_CORRECTION.type
                            for (data in dataAdapter.selectList) {
                                it.tagIdList.add(data.tagId)
                                it.readTypeList.add(data.tagName)
                            }
    
                            if (correctionParamsEntity != null) {
                                correctionParamsEntity!!.tagIdList = it.tagIdList
                                correctionParamsEntity!!.readTypeList = it.readTypeList
                                it.messageText = JsonUtility.encodeToString(correctionParamsEntity)
                            }
                        }
                        KLog.e("参数json: ${JsonUtility.encodeToString(entity)}")
                        SDKSingleton.userBl.saveFeedback(entity)
                        ToastUtil.showToastShort(R.string.thannks_for_your_feedback)
                        dismiss()
                    }
                } else {
                    dismiss()
                }
            }
        }
    }
}

/***
 *@date 创建时间 2022/5/10 21:28
 *<AUTHOR> W.YuLong
 *@description
 */
class CorrectionTypeAdapter : BaseRecycleAdapter<FeedbackTagEntity, CorrectionViewHolder>() {
    val selectList = ArrayList<FeedbackTagEntity>()
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): CorrectionViewHolder {
        return CorrectionViewHolder(parent)
    }
    
    override fun onBindViewHolder(holder: CorrectionViewHolder, position: Int) {
        val dataEntity = getDataFromPosition(position)!!
        holder.initUIData(dataEntity)
        holder.setChecked(selectList.contains(dataEntity))
        holder.itemView.setOnClickListener {
            if (selectList.contains(dataEntity)) {
                selectList.remove(dataEntity)
            } else {
                selectList.add(dataEntity)
            }
            notifyDataSetChanged()
        }
    }
    
    
}

class CorrectionViewHolder(parent: ViewGroup) : BaseViewHolder(parent, R.layout.item_correction_dialog_layout) {
    val typeNameTextView: TextView = itemView.findViewById(R.id.item_correction_type_Name_TextView)
    val checkImageVew: ImageView = itemView.findViewById(R.id.item_correction_check_ImageView)
    
    override fun <T> initUIData(t: T) {
        t as FeedbackTagEntity
        typeNameTextView.setText(t.tagName)
    }
    
    fun setChecked(isChecked: Boolean) {
        checkImageVew.isSelected = isChecked
        
    }
    
}

