package com.wedevote.wdbook.ui.user

import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.os.Bundle
import android.text.Spannable
import android.text.SpannableString
import android.text.style.ForegroundColorSpan
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.ImageView
import android.widget.TextView
import androidx.core.widget.NestedScrollView
import com.aquila.lib.base.BaseRecycleAdapter
import com.aquila.lib.base.BaseViewHolder
import com.aquila.lib.base.OnViewClickListener
import com.aquila.lib.dialog.CommAlertDialog
import com.aquila.lib.layout.SmartRefreshLayout
import com.aquila.lib.layout.api.RefreshLayout
import com.aquila.lib.layout.listener.OnLoadMoreListener
import com.aquila.lib.layout.listener.OnRefreshListener
import com.wedevote.wdbook.tools.util.ImageLoadUtil
import com.aquila.lib.widget.view.AdaptiveImageView
import com.aquila.lib.widget.view.CustomRecyclerView
import com.aquila.lib.widget.view.DotImageView
import com.chauthai.swipereveallayout.SwipeRevealLayout
import com.wedevote.wdbook.R
import com.wedevote.wdbook.base.APPConfig
import com.wedevote.wdbook.base.ExceptionHandler
import com.wedevote.wdbook.base.RootActivity
import com.wedevote.wdbook.base.SDKSingleton
import com.wedevote.wdbook.entity.BookNoteCountEntity
import com.wedevote.wdbook.entity.NoteEntity
import com.wedevote.wdbook.entity.resource.ResourceDownloadInfo
import com.wedevote.wdbook.tools.event.OnNoteMergedEvent
import com.wedevote.wdbook.tools.event.OnSyncNoteFinish
import com.wedevote.wdbook.tools.util.IntentConstants
import com.wedevote.wdbook.tools.util.PictureUtil
import com.wedevote.wdbook.tools.util.UnitFormatUtil
import com.wedevote.wdbook.tools.util.findString
import com.wedevote.wdbook.tools.util.parseColor
import com.wedevote.wdbook.tools.util.toJsonStr
import com.wedevote.wdbook.ui.read.BookReadActivity
import com.wedevote.wdbook.ui.read.NoteRecycleBinDialogFragment
import com.wedevote.wdbook.utils.JsonUtility
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import java.io.File
import kotlin.math.max

/***
 * @date 创建时间 2021/7/27 16:21
 * <AUTHOR> W.YuLong
 * @description
 */
class BookNoteDetailActivity : RootActivity(), View.OnClickListener, OnLoadMoreListener, OnRefreshListener {
    lateinit var coverImageView: AdaptiveImageView
    lateinit var nameTextView: TextView
    lateinit var authorTextView: TextView
    lateinit var countTextView: TextView
    lateinit var recyclerBinButton: Button
    lateinit var dataRecyclerView: CustomRecyclerView
    lateinit var refreshLayout: SmartRefreshLayout
    lateinit var scrollView: NestedScrollView

    lateinit var noteCountEntity: BookNoteCountEntity

    lateinit var adapter: BookNoteDetailAdapter
    private var offset: Int = 0
    private val limit = 20

    var noteEntity: NoteEntity? = null

    companion object {
        fun gotoBookNoteDetailActivity(context: Context, entity: BookNoteCountEntity, step: Int = 0) {
            val intent = Intent(context, BookNoteDetailActivity::class.java)
            intent.putExtra("BookNoteCountEntity", entity.toJsonStr())
            intent.putExtra("step", step)
            context.startActivity(intent)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_book_note_detail_layout)
        initViewFromXML()
        recyclerBinButton.setOnClickListener(this)
        refreshLayout.setOnRefreshListener(this).setOnLoadMoreListener(this)
        intent.getStringExtra("BookNoteCountEntity")?.let {
            noteCountEntity = JsonUtility.decodeFromString(it)
        }
        adapter = BookNoteDetailAdapter(
            this,
            noteCountEntity,
            object : OnNoteOperateListener {
                override fun onNoteDelete(view: View, entity: NoteEntity) {
                    adapter.deleteItem(entity)
                    initBookInfoUI()
                }

                override fun onNoteEdit() {
                    var count = adapter.itemCount
                    adapter.clearDataList()
                    loadNoteData(0, count)
                }
            },
        )
        dataRecyclerView.adapter = adapter

        scrollView.post {
            var step = intent.getIntExtra("step", 0)
            if (step == 1) {
                recyclerBinButton.performClick()
            }

            scrollView.scrollTo(0, 0)
        }
    }

    private fun initViewFromXML() {
        scrollView = findViewById(R.id.book_note_detail_NestedScrollView)
        coverImageView = findViewById(R.id.book_note_detail_cover_ImageView)
        nameTextView = findViewById(R.id.book_note_detail_name_TextView)
        authorTextView = findViewById(R.id.book_note_detail_author_TextView)
        countTextView = findViewById(R.id.book_note_detail_note_count_TextView)
        recyclerBinButton = findViewById(R.id.book_note_detail_note_recyclerBin_Button)
        dataRecyclerView = findViewById(R.id.book_note_detail_data_RecyclerView)
        refreshLayout = findViewById(R.id.book_note_detail_RefreshLayout)
    }

    fun initBookInfoUI() {
        val entity = SDKSingleton.dbWrapBl.getBookNoteCountEntity(noteCountEntity.resourceId)
        if (entity == null) {
            noteCountEntity.count = 0
        } else {
            noteCountEntity.count = entity.count
        }
        nameTextView.text = noteCountEntity.name
        authorTextView.text = SDKSingleton.userBl.formatAuthorName(noteCountEntity.authorEntityList)
        countTextView.text = findString(R.string.format_text_count_note).format(noteCountEntity.count)
        PictureUtil.loadImage(coverImageView, noteCountEntity.cover)
    }

    override fun onRefresh(refreshLayout: RefreshLayout) {
        MainScope().launch(ExceptionHandler.coroutineExceptionHandler) {
            SDKSingleton.syncBl.syncNoteData()
            EventBus.getDefault().post(OnSyncNoteFinish())
            offset = 0
            adapter.clearDataList()
            onLoadMore(refreshLayout)
            refreshLayout.isEnableLoadMore = true
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onSyncNoteData(noteMergedEvent: OnSyncNoteFinish?) {
        onReloadLocalData(null)
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onReloadLocalData(noteMergedEvent: OnNoteMergedEvent?) {
        var count = max(adapter.itemCount, limit)
        adapter.clearDataList()
        loadNoteData(0, count)
    }

    override fun onResume() {
        super.onResume()
        onReloadLocalData(null)
    }

    override fun onClick(v: View?) {
        if (v == recyclerBinButton) {
            val dialog = NoteRecycleBinDialogFragment()
            dialog.arguments = Bundle().apply {
                putString(IntentConstants.EXTRA_ResourceId, noteCountEntity.resourceId)
            }
            supportFragmentManager.let { it1 -> dialog.show(it1, "NoteRecycleBinDialog") }
            dialog.onDataChangeListener = object : OnViewClickListener {
                override fun <T> onClickAction(v: View, str: String, t: T?) {
                    if (str.equals("Recover")) {
                        onReloadLocalData(null)
                    }
                }
            }
        }
    }

    override fun onLoadMore(layout: RefreshLayout) {
        loadNoteData(offset, limit)
        refreshLayout.finishLoadMoreAndRefresh()
    }

    fun loadNoteData(pos: Int, limit: Int) {
        val noteList = SDKSingleton.dbWrapBl.getNoteEntityList(noteCountEntity.resourceId, pos, limit)
        adapter.addDataList(noteList)
        offset = adapter.itemCount
        refreshLayout.isEnableLoadMore = noteList.size >= limit
        initBookInfoUI()
    }
}

/***
 *@date 创建时间 2021/8/4 17:50
 *<AUTHOR> W.YuLong
 *@description
 */
class BookNoteDetailAdapter(val activity: RootActivity, val noteCountEntity: BookNoteCountEntity, val onNoteOperateListener: OnNoteOperateListener) :
    BaseRecycleAdapter<NoteEntity, BookNoteDetailViewHolder>() {
    var itemOpenPosition = -1
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BookNoteDetailViewHolder {
        return BookNoteDetailViewHolder(activity, noteCountEntity, parent, onNoteOperateListener)
    }

    override fun deleteItem(t: NoteEntity) {
        itemOpenPosition = -1
        super.deleteItem(t)
        notifyDataSetChanged()
    }

    override fun addDataList(list: List<NoteEntity>?) {
        itemOpenPosition = -1
        super.addDataList(list)
    }

    override fun onBindViewHolder(holder: BookNoteDetailViewHolder, position: Int) {
        super.onBindViewHolder(holder, position)
        var swipeLayout = holder.itemView as SwipeRevealLayout
        if (itemOpenPosition == position) {
            swipeLayout.open(false)
        } else {
            swipeLayout.close(true)
        }

        swipeLayout.setSwipeListener(object : SwipeRevealLayout.SwipeListener {
            override fun onClosed(view: SwipeRevealLayout?) {
            }

            override fun onOpened(view: SwipeRevealLayout?) {
                itemOpenPosition = position
                notifyDataSetChanged()
            }

            override fun onSlide(view: SwipeRevealLayout?, slideOffset: Float) {
            }
        })
    }
}

/***
 *@date 创建时间 2021/7/27 16:32
 *<AUTHOR> W.YuLong
 *@description
 */
class BookNoteDetailViewHolder(
    val activity: RootActivity,
    val noteCountEntity: BookNoteCountEntity,
    parent: ViewGroup,
    val onNoteOperateListener: OnNoteOperateListener,
) :
    BaseViewHolder(parent, R.layout.holder_item_detail_book_note_layout), View.OnClickListener {
    val dotImageView: DotImageView = itemView.findViewById(R.id.item_note_detail_dot_ImageView)
    val dateTextView: TextView = itemView.findViewById(R.id.item_note_detail_date_TextView)
    val quoteTextView: TextView = itemView.findViewById(R.id.item_note_detail_summary_TextView)
    val noteTextView: TextView = itemView.findViewById(R.id.item_note_detail_content_TextView)
    val deleteTextView: TextView = itemView.findViewById(R.id.item_note_detail_delete_Button)

    val deleteImageView: ImageView = itemView.findViewById(R.id.item_note_detail_delete_ImageView)
    val editImageView: ImageView = itemView.findViewById(R.id.item_note_detail_edit_ImageView)
    val readImageView: ImageView = itemView.findViewById(R.id.item_note_detail_read_ImageView)
    lateinit var noteEntity: NoteEntity

    init {
        deleteImageView.setOnClickListener(this)
        deleteTextView.setOnClickListener(this)
        quoteTextView.setOnClickListener(this)
        noteTextView.setOnClickListener(this)
        editImageView.setOnClickListener(this)
        readImageView.setOnClickListener(this)
    }

    override fun <T> initUIData(t: T) {
        noteEntity = t as NoteEntity
        dateTextView.text = UnitFormatUtil.formatDate_ymdhm(t.createTime)
        if (!t.noteText.isNullOrEmpty()) {
            quoteTextView.visibility = View.VISIBLE
            quoteTextView.text = findString(R.string.quote) + "   | " + t.getDisplaySummery()
            if (!t.conflictRemoteId.isNullOrEmpty()) {
                val conflictTitle = findString(R.string.conflict_note)
                val spannableStr = SpannableString("$conflictTitle${t.noteText}")
                spannableStr.setSpan(
                    ForegroundColorSpan(Color.parseColor("#E53935")),
                    0,
                    conflictTitle.length,
                    Spannable.SPAN_EXCLUSIVE_EXCLUSIVE,
                )
                noteTextView.text = spannableStr
            } else {
                noteTextView.text = t.noteText
            }
        } else {
            quoteTextView.visibility = View.GONE
            noteTextView.text = t.summary
        }
        dotImageView.setDotColor(parseColor(SDKSingleton.appBl.getHighlightColor(t.highlightColorType)))
    }

    override fun onClick(v: View?) {
        when (v) {
            deleteImageView, deleteTextView -> {
                showDeleteDialog()
            }
            readImageView -> {
                gotoBookRead(noteEntity)
            }
            editImageView -> {
                editNote(noteEntity)
            }
            noteTextView, quoteTextView -> {
                showNoteViewDialog()
            }
        }
    }

    private fun showNoteViewDialog() {
        val noteViewDialogFragment = NoteViewDialogFragment()
        noteViewDialogFragment.arguments = Bundle().apply {
            putString(IntentConstants.EXTRA_NoteEntity, JsonUtility.encodeToString(noteEntity))
        }
        noteViewDialogFragment.onNoteOperateListener = onNoteOperateListener
        noteViewDialogFragment.bookJumpListener = object : OnViewClickListener {
            override fun <T> onClickAction(v: View, str: String, t: T?) {
                gotoBookRead(t as NoteEntity)
            }
        }
        activity.supportFragmentManager.let { it1 -> noteViewDialogFragment.show(it1, "NoteViewDialogFragment") }
    }

    private fun gotoBookRead(entity: NoteEntity) {
        val info = SDKSingleton.dbWrapBl.getResourceDownloadInfo(entity.resourceId)
        if (info?.getActualFilePath().isNullOrEmpty() || !File(info?.getActualFilePath()).exists()) {
            showNeedDownloadDialog()
        } else {
            info?.let {
                BookReadActivity.gotoBookReadActivity(
                    activity,
                    info.getActualFilePath(),
                    info.fileId,
                    info.resourceId,
                    JsonUtility.encodeToString(entity),
                )
            }
        }
    }

    fun showNeedDownloadDialog() {
        CommAlertDialog.with(itemView.context)
            .setTitle(R.string.warm_prompt_title).setMessage(R.string.please_download_before_read)
            .setStartText(R.string.label_cancel).setEndText(R.string.download_this_book)
            .setOnViewClickListener { _, _, tag ->
                if (tag == CommAlertDialog.TAG_CLICK_END) {
//                    showBookDownloadingDialog()
                    BookDownloadActivity.gotoDownloadBook(
                        activity,
                        noteEntity.resourceId,
                        object : OnBookDownloadCallback {
                            override fun onDownloadComplete(info: ResourceDownloadInfo) {
                                BookReadActivity.gotoBookReadActivity(
                                    activity,
                                    info.getActualFilePath(),
                                    info.fileId,
                                    info.resourceId,
                                    JsonUtility.encodeToString(noteEntity),
                                )
                            }
                        },
                    )
                }
            }.showDialog()
    }

    /*显示正在下载的对话框*/
    private fun showBookDownloadingDialog() {
        val dialog = NoteBookDownloadDialogFragment()
        dialog.arguments = Bundle().apply {
            putString(IntentConstants.EXTRA_NoteEntity, noteEntity.toJsonStr())
            putString(IntentConstants.EXTRA_NoteCountEntity, noteCountEntity.toJsonStr())
        }
        activity.supportFragmentManager.let { it1 -> dialog.show(it1, "NoteBookDownloadDialogFragment") }
    }

    fun showDeleteDialog() {
        CommAlertDialog.with(itemView.context)
            .setTitle(R.string.delete_note).setMessage(R.string.sure_to_delete_note)
            .setStartText(R.string.label_cancel).setEndText(R.string.label_delete)
            .setRightColorRes(if (APPConfig.isCurrentThemeLight()) R.color.color_red_E33733 else R.color.color_red_A5343C)
            .setOnViewClickListener { _, _, tag ->
                if (tag == CommAlertDialog.TAG_CLICK_END) {
                    SDKSingleton.dbWrapBl.removeNote(noteEntity.dataId!!)
                    onNoteOperateListener.onNoteDelete(itemView, noteEntity)
                    MainScope().launch(ExceptionHandler.coroutineExceptionHandler) {
                        SDKSingleton.syncBl.syncNoteData()
                        EventBus.getDefault().post(OnSyncNoteFinish())
                    }
                }
            }.showDialog()
    }

    private fun editNote(noteEntity: NoteEntity) {
        NoteEditActivity.gotoNoteEdit(
            itemView.context,
            noteEntity,
            object : OnNoteEditCallback {
                override fun onNoteEditFinish(noteEntity: NoteEntity) {
                    onNoteOperateListener.onNoteEdit()
                }
            },
        )
    }
}
