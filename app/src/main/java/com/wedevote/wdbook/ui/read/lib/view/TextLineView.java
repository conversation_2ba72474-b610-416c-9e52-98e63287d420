package com.wedevote.wdbook.ui.read.lib.view;

import android.content.Context;
import android.graphics.Canvas;
import android.util.AttributeSet;
import android.view.View;

public class TextLineView extends View {
    private TextLineList line;

    public TextLineView(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);

    }

    public TextLineView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public TextLineView(Context context) {
        this(context, null, 0);
    }

    public void setText(TextLineList line) {
        this.line = line;
    }


    @Override
    protected void onDraw(Canvas canvas) {
        if(line.needDrawText()){
            float topMargin = line.getTopMargin();
            for (Word word : line) {
                word.draw(canvas, topMargin, getHeight());
            }
        }
    }
}
