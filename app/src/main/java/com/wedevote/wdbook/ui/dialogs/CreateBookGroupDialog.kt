package com.wedevote.wdbook.ui.dialogs

import android.app.Dialog
import android.content.Context
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.Gravity
import android.view.View
import android.view.WindowManager
import android.widget.Button
import android.widget.EditText
import android.widget.ImageView
import android.widget.TextView
import com.aquila.lib.tools.util.ScreenUtil
import com.aquila.lib.tools.util.ToastUtil
import com.wedevote.wdbook.R

/***
 * @date 创建时间 2020/6/9 15:37
 * <AUTHOR> <PERSON><PERSON>
 * @description 新建书籍目录分类的对话框
 */
class CreateBookGroupDialog(
    context: Context,
    var onCreateBookGroupCallBack: OnCreateBookGroupCallBack? = null
) : Dialog(context), View.OnClickListener {
    private lateinit var cancelButton: Button
    private lateinit var okButton: Button
    private lateinit var inputEditText: EditText
    private lateinit var clearInputImageView: ImageView

    lateinit var titleTextView: TextView
    val maxTextLength = 20

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.dialog_create_book_group_layout)

        titleTextView = findViewById(R.id.create_book_group_title_TextView)
        cancelButton = findViewById(R.id.create_book_group_cancel_Button)
        okButton = findViewById(R.id.create_book_group_ok_Button)
        inputEditText = findViewById(R.id.create_book_group_input_EditText)
        clearInputImageView = findViewById(R.id.create_book_group_clear_input_ImageView)
        configDialog()
        setViewListeners()
        inputEditText.setText("")
    }

    private fun setViewListeners() {
        cancelButton.setOnClickListener(this)
        okButton.setOnClickListener(this)
        clearInputImageView.setOnClickListener(this)
        inputEditText.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
            }

            override fun afterTextChanged(s: Editable) {
                if (s.isNullOrEmpty()) {
                    clearInputImageView.visibility = View.GONE
                    okButton.alpha = 0.3f
                } else {
                    if (s.length > maxTextLength) {
                        ToastUtil.showToastShort(R.string.input_text_max_prompt)
                        inputEditText.setText(s.subSequence(0, maxTextLength))
                        inputEditText.setSelection(maxTextLength)
                    }
                    okButton.alpha = 1f
                    clearInputImageView.visibility = View.VISIBLE
                }
            }
        })
    }

    fun setTitleText(title: String) {
        titleTextView.text = title
    }

    fun setContentText(text: CharSequence?) {
        inputEditText.setText(text)
        text?.let {
            inputEditText.setSelection(Math.min(it.length, maxTextLength))
        }
    }

    private fun configDialog() {
        val wl = window!!.attributes
        wl.gravity = Gravity.CENTER // 设置重力
        wl.width = (ScreenUtil.getScreenWidth() * 0.8f).toInt()
        wl.height = WindowManager.LayoutParams.WRAP_CONTENT
//        window!!.setWindowAnimations(R.style.centerDialogWindowAnim)
        window!!.attributes = wl
        window!!.setBackgroundDrawableResource(android.R.color.transparent)
    }

    override fun onClick(v: View?) {
        when (v) {
            cancelButton -> {
                dismiss()
            }
            clearInputImageView -> {
                inputEditText.setText("")
            }
            okButton -> {
                if (inputEditText.text.trim().isNullOrEmpty()) {
                    ToastUtil.showToastShort(R.string.dialog_create_book_group_tip)
                } else {
                    var name = inputEditText.text.toString()
                    if (name.length > 20) {
                        name = name.substring(0, 20)
                    }
                    onCreateBookGroupCallBack?.onCallBack(this, name)
                    dismiss()
                }
            }
        }
    }
}

interface OnCreateBookGroupCallBack {
    fun onCallBack(dialog: Dialog, name: String)
}
