package com.wedevote.wdbook.ui.account

import android.app.Activity
import android.content.Intent
import com.aquila.lib.dialog.CommProgressDialog
import com.aquila.lib.dialog.CommProgressDialog.Companion.with
import com.aquila.lib.log.KLog
import com.aquila.lib.tools.singleton.SPSingleton
import com.aquila.lib.tools.util.ToastUtil
import com.wdbible.app.wedevotebible.tools.security.EncodingUtils
import com.wedevote.wdbook.R
import com.wedevote.wdbook.base.ExceptionHandler
import com.wedevote.wdbook.base.SDKSingleton
import com.wedevote.wdbook.constants.AnalyticsConstants
import com.wedevote.wdbook.constants.Constants
import com.wedevote.wdbook.exception.ApiException
import com.wedevote.wdbook.exception.ErrorInfo
import com.wedevote.wdbook.tools.event.OnLoginEvent
import com.wedevote.wdbook.tools.util.AnalyticsUtils
import com.wedevote.wdbook.tools.util.SPKeyDefine
import com.wedevote.wdbook.tools.util.toJsonStr
import com.wedevote.wdbook.ui.dialogs.RegisterTipDialog
import com.wedevote.wdbook.ui.service.SyncDataService
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus
import java.net.SocketException

/***
 *@date 创建时间 2023/4/19
 *<AUTHOR> John.Qian
 *@description
 */
class LoginManager(private val activity: Activity) {
    private val processDialog: CommProgressDialog?

    init {
        processDialog = with(activity).setCancelable(false).setTouchOutsideCancel(false)
            .setTitle(activity.getString(R.string.sso_login_syncing_data)).create()
    }

    fun closeDialog() {
        if (processDialog != null && processDialog.isShowing) {
            processDialog.dismiss()
        }
    }

    fun setDialogText(text: String?) {
        if (processDialog != null && processDialog.isShowing) {
            processDialog.setTitle(text)
        }
    }

    fun exitActivityAfterLogin() {
//        String planInstanceId = activity.getIntent().getStringExtra(IntentConstants.EXTRA_PlanId);
//        if (planInstanceId != null) {
//            DeepLinkUtils.getPlanFromServer(ThemeUtils.getMainActivity(), planInstanceId);
//        }
//        UserAccountManager.avatarBitmap = null;
//        SPSingleton.get().putString(SPKeyDefine.SP_AvatarPath, "");
//
//        EventBus.getDefault().post(new LoginEvent());
//        activity.setResult(IntentConstants.INTENT_RESULT_CODE_LOGIN);
        activity.onBackPressed()
    }

    fun checkSyncFile() {
        closeDialog()
        exitActivityAfterLogin()
    }

    fun startLogin(
        activity: Activity,
        userName: String,
        password: String,
        callBack: (isSuccess: Boolean) -> Unit
    ) {
        MainScope().launch(ExceptionHandler.coroutineExceptionHandler) {
            try {
                if (processDialog != null && !processDialog.isShowing) {
                    processDialog.show()
                }
                val sign = Constants.APP_KEY_ANDROID + "\n" + userName + "\n" + password
                val encryptSign = EncodingUtils.rsaEncryptByPublicKey(sign, Constants.PUBLICK_KEY)
                val authToken =
                    SDKSingleton.sessionBl.login(encryptSign)?.toJsonStr()
                if (authToken.isNullOrEmpty()) {
                    return@launch
                }
                SDKSingleton.sessionBl.setUserTokenString(authToken)
                SPSingleton.get()
                    .putString(SPKeyDefine.SP_LoginUserId, SDKSingleton.sessionBl.userId)
                KLog.d("SDKSingleton.sessionBl.userId = ${SDKSingleton.sessionBl.userId}, authToken = $authToken")

                try {
                    val i = SDKSingleton.userBl.checkAndAddDevice(SDKSingleton.appBl.deviceId)
                    if (i == 1312) {
                        checkAndGotoDeviceManageActivity(activity, callBack)
                    } else {
                        afterLogin(activity, callBack)
                    }
                    processDialog!!.dismiss()
                } catch (e: Exception) {
                    afterLogin(activity, callBack)
                    processDialog!!.dismiss()
                }
            } catch (exception: Throwable) {
                processDialog?.dismiss()
                when (exception) {
                    is ApiException -> {
                        when (exception.code) {
                            ErrorInfo.UserNotExists.code, ErrorInfo.UserNotExists2.code, ErrorInfo.PasswordNotMatch.code -> {
                                val tipDialog = RegisterTipDialog(activity)
                                tipDialog.show()
                                tipDialog.setTitleText(activity.getString(R.string.login_failed))
                                tipDialog.setContentText(activity.getString(R.string.account_or_password_not_correct))
                            }
                            else -> {
                                ToastUtil.showToastShort(exception.message)
                            }
                        }
                    }
                    is SocketException -> {
                        ToastUtil.showToastShort(activity.getString(R.string.no_network_connect))
                    }
                    else -> {
                        ToastUtil.showToastShort(activity.getString(R.string.login_failed))
                    }
                }
            }
        }
    }

    private fun afterLogin(activity: Activity, callBack: (isSuccess: Boolean) -> Unit) {
        EventBus.getDefault().post(OnLoginEvent())
        AnalyticsUtils.updateAnalyticsUserID()
        AnalyticsUtils.logEvent(
            AnalyticsConstants.LOG_V1_USER_LOGIN_SUCCESS,
            AnalyticsConstants.LOG_V1_PARAM_LOGIN_TYPE,
            "3"
        )
        callBack.invoke(true)
        val intent = Intent(activity, SyncDataService::class.java)
        activity.startService(intent)
        activity.finish()
    }

    fun checkAndGotoDeviceManageActivity(
        activity: Activity,
        callBack: (isSuccess: Boolean) -> Unit
    ) {
        DeviceManagerActivity.gotoDeviceManager(
            activity,
            object : OnLoginResultCallBack {
                override fun onLoginResult(isSuccess: Boolean) {
                    if (isSuccess) {
                        afterLogin(activity, callBack)
                    }
                }
            }
        )
    }

    private fun checkAfterLogin(code: Int) {
        isLoggedIn = code
        activity.runOnUiThread {
            if (code == Constants.HTTP_RESULT_SUCCESS) {
//                    setDialogText(WDLocalizedString.INSTANCE.getToastLoginAndSyncdata());
//                    StaticVars.loginSessionExpiredNotified = false;
//                    StatisticUtils.loginAccount();
//                    StaticVars.firstLogIn = true;
//                    WDBibleWebSocketSDK.INSTANCE.reconnect();
//                    KmmPlatHelper.syncData();
            } else {
//                    OptionUtility.showServerError(activity, code);
                closeDialog()
            }
        }
    }

    companion object {
        var isLoggedIn = 0
    }
}