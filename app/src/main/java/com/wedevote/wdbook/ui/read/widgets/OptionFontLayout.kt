package com.wedevote.wdbook.ui.read.widgets

import android.content.Context
import android.util.AttributeSet
import android.view.View
import android.widget.LinearLayout
import android.widget.SeekBar
import android.widget.TextView
import com.aquila.lib.base.OnViewClickListener
import com.aquila.lib.tools.interfaceimpl.OnSeekBarListenerImpl
import com.aquila.lib.tools.singleton.SPSingleton
import com.wedevote.wdbook.R
import com.wedevote.wdbook.tools.define.TextFontType
import com.wedevote.wdbook.tools.util.SPKeyDefine
import com.wedevote.wdbook.ui.read.lib.BookParameters

/***
 * @date 创建时间 2020/6/16 15:56
 * <AUTHOR> W.YuLong
 * @description
 */
class OptionFontLayout @JvmOverloads constructor(
    context: Context,
    attributeSet: AttributeSet? = null
) :
    LinearLayout(context, attributeSet),
    View.OnClickListener {
    val fontPlusTextView: TextView
    val fontMinusTextView: TextView
    val systemFontTextView: TextView
    val serifFontTextView: TextView
    val seekBar: SeekBar

    var onViewClickListener: OnViewClickListener? = null

    init {
        View.inflate(context, R.layout.option_read_font_size_layout, this)
        fontPlusTextView = findViewById(R.id.option_text_size_plus_TextView)
        fontMinusTextView = findViewById(R.id.option_text_size_minus_TextView)
        systemFontTextView = findViewById(R.id.read_system_font_TextView)
        serifFontTextView = findViewById(R.id.read_serif_font_TextView)
        seekBar = findViewById(R.id.option_text_size_SeekBar)
        setViewListeners()

        val textLevel = SPSingleton.get().getInt(SPKeyDefine.SP_BookTextLevel, 5)
        seekBar.progress = textLevel

        val textFont = SPSingleton.get().getInt(SPKeyDefine.SP_BookTextFont, TextFontType.SERIF.value)
        if (textFont == TextFontType.SERIF.value) {
            setSerifFontSelected()
        } else {
            setSystemFontSelected()
        }
    }

    private fun setViewListeners() {
        fontMinusTextView.setOnClickListener(this)
        fontPlusTextView.setOnClickListener(this)
        systemFontTextView.setOnClickListener(this)
        serifFontTextView.setOnClickListener(this)
        seekBar.setOnSeekBarChangeListener(object : OnSeekBarListenerImpl() {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                SPSingleton.get().putInt(SPKeyDefine.SP_BookTextLevel, progress)
                BookParameters.textLevel = progress
                onViewClickListener?.onClickAction(seekBar!!, OptionClickTag.TAG_FONT_SIZE, progress)
            }
        })
    }

    override fun onClick(v: View?) {
        when (v) {
            fontPlusTextView -> {
                seekBar.progress = seekBar.progress + 1
            }
            fontMinusTextView -> {
                seekBar.progress = seekBar.progress - 1
            }
            systemFontTextView -> {
                setSystemFontSelected()
                SPSingleton.get().putInt(SPKeyDefine.SP_BookTextFont, TextFontType.DEFAULT.value)
                onViewClickListener?.onClickAction(v, OptionClickTag.TAG_FONT_STYLE, TextFontType.DEFAULT.value)
            }
            serifFontTextView -> {
                setSerifFontSelected()
                SPSingleton.get().putInt(SPKeyDefine.SP_BookTextFont, TextFontType.SERIF.value)
                onViewClickListener?.onClickAction(v, OptionClickTag.TAG_FONT_STYLE, TextFontType.SERIF.value)
            }
        }
    }

    private fun setSerifFontSelected() {
        systemFontTextView.isSelected = false
        serifFontTextView.isSelected = true
    }

    private fun setSystemFontSelected() {
        systemFontTextView.isSelected = true
        serifFontTextView.isSelected = false
    }
}
