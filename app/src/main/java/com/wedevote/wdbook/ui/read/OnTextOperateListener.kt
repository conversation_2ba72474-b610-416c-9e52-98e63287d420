package com.wedevote.wdbook.ui.read

import android.graphics.Bitmap

/***
 * @date 创建时间 2020/11/30 16:54
 * <AUTHOR> <PERSON><PERSON><PERSON>
 * @description 文本选择后的操作接口
 */
interface OnTextOperateListener {

    fun onCopy(text: String)

    fun onShare(text: String)

    fun onBitmapClick(bitmap: Bitmap)

    fun onLayoutIsShowing(isShowing: Boolean)

    fun onFlipPage(position: Int, isNext: Boolean): Boolean

    fun onSelectedChanged(line: Int, wordIndex: Int)

    fun onSelectedStopMoving()

    fun onTextClick(position: Int, isLongCLick: Boolean)

    fun onReloadBookData()

    fun onNoteFold(position: Int)

    fun onInnerJump(link: String)
}
