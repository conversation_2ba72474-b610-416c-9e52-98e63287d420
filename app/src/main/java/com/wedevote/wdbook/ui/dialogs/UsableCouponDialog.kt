package com.wedevote.wdbook.ui.dialogs

import android.content.Context
import android.os.Bundle
import android.view.Gravity
import android.view.View
import android.widget.Button
import android.widget.TextView
import com.aquila.lib.base.OnViewClickListener
import com.aquila.lib.widget.view.CustomRecyclerView
import com.wedevote.wdbook.R
import com.wedevote.wdbook.entity.coupon.CouponEntity
import com.wedevote.wdbook.ui.user.coupon.CouponItemAdapter
import com.wedevote.wdbook.ui.user.coupon.CouponItemType

/***
 * @date 创建时间 2022/6/7 18:02
 * <AUTHOR> <PERSON><PERSON><PERSON>
 * @description 可用优惠券的对话框
 */
class UsableCouponDialog(context: Context) : BaseDialog(context), View.OnClickListener {
    lateinit var titleTextView: TextView
    lateinit var dataRecyclerView: CustomRecyclerView
    lateinit var okButton: Button
    
    lateinit var couponItemAdapter: CouponItemAdapter
    
    var onViewClickListener: OnViewClickListener? = null
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.dialog_get_coupon_layout)
        titleTextView = findViewById(R.id.get_coupon_title_TextView)
        dataRecyclerView = findViewById(R.id.get_coupon_data_RecyclerView)
        okButton = findViewById(R.id.get_coupon_ok_Button)
        okButton.visibility = View.VISIBLE
        couponItemAdapter = CouponItemAdapter(CouponItemType.USE_COUPON)
        dataRecyclerView.adapter = couponItemAdapter
        
        couponItemAdapter.onViewClickListener = onViewClickListener
        
        okButton.setOnClickListener(this)
        configDialog(Gravity.BOTTOM)
    }
    
    
    fun initDataList(couponList: List<CouponEntity>, currentEntity: CouponEntity?) {
        couponItemAdapter.dataList = couponList?.toMutableList()
        couponItemAdapter.findCurrentSelectPosition(currentEntity)
        dataRecyclerView.smoothScrollToPosition(if (couponItemAdapter.selectItemPosition < 0) 0 else couponItemAdapter.selectItemPosition)
    }
    
    override fun onClick(v: View?) {
        if (v == okButton) {
            onViewClickListener?.onClickAction(v, "", couponItemAdapter.getSelectItem())
            dismiss()
        }
    }
    
}