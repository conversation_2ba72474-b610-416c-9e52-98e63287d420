package com.wedevote.wdbook.ui.widgets

import android.annotation.TargetApi
import android.content.Context
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.drawable.Drawable
import android.os.Build
import android.text.SpannableString
import android.text.Spanned
import android.text.style.ImageSpan
import android.util.AttributeSet
import android.view.View
import android.view.animation.AlphaAnimation
import android.view.animation.Animation
import android.view.animation.Transformation
import android.widget.TextView
import androidx.appcompat.widget.AppCompatTextView
import com.wedevote.wdbook.R
import com.wedevote.wdbook.tools.util.dp2px

/***
 * @date 创建时间 2020/10/12 16:51
 * <AUTHOR> W.YuLong
 * @description
 */
class ExpandTextView @JvmOverloads constructor(context: Context, attrs: AttributeSet? = null) :
    AppCompatTextView(context, attrs),
    View.OnClickListener {
    private var maxCollapsedLines = 8 // 最大显示行数
    private val animationDuration: Int
    private val animAlphaStart: Float
    private var expandDrawable: Drawable? // 展开前显示图片

    private var collapseDrawable: Drawable? // 展开后图片
    private var collapsedHeight = 0
    private var textHeightWithMaxLines = 0
    private var collapsed = true // Show short version as default.标示现在所处的折叠状态
    private var animating = false
    private var drawableSize = 0
    private var arrowAlign = ALIGN_RIGHT_BOTTOM
    private var arrowPosition = POSITION_RIGHT
    var needCollapse = true // 标示是否需要折叠已显示末尾的图标
        set(value) {
            field = value
            invalidate()
        }

    /**
     * 箭头图标和文字的距离
     */
    private var arrowDrawablePadding = 0

    /* Listener for callback */
    private var mListener: OnExpandStateChangeListener? = null
    private var isDrawablePaddingResolved = false

    companion object {
        private const val MAX_COLLAPSED_LINES = 8 // The default number of lines 默认显示行数为8行
        private const val DEFAULT_ANIM_DURATION = 300 // The default animation duration 默认动画时长为300ms
        private const val DEFAULT_ANIM_ALPHA_START = 0.7f // The default alpha value when the animation starts

        /**
         * 表示箭头对齐方式,靠左/上,右/下,还是居中
         */
        private const val ALIGN_RIGHT_BOTTOM = 0
        private const val ALIGN_LEFT_TOP = 1
        private const val ALIGN_CENTER = 2

        /**
         * 表示箭头显示位置,在文字右边还是在文字下边
         */
        private const val POSITION_RIGHT = 0
        private const val POSITION_BELOW = 1
    }

    var drawableWidth: Int = 0
    var drawableHeight: Int = 0

    init {
        val typedArray = context.obtainStyledAttributes(attrs, R.styleable.ExpandTextView)
        maxCollapsedLines = typedArray.getInt(R.styleable.ExpandTextView_maxCollapsedLines, MAX_COLLAPSED_LINES)
        animationDuration = typedArray.getInt(R.styleable.ExpandTextView_animDuration, DEFAULT_ANIM_DURATION)
        animAlphaStart = typedArray.getFloat(R.styleable.ExpandTextView_animAlphaStart, DEFAULT_ANIM_ALPHA_START)
        expandDrawable = typedArray.getDrawable(R.styleable.ExpandTextView_expandDrawable)
        collapseDrawable = typedArray.getDrawable(R.styleable.ExpandTextView_collapseDrawable)
        arrowAlign = typedArray.getInteger(R.styleable.ExpandTextView_arrowAlign, ALIGN_RIGHT_BOTTOM)
        arrowPosition = typedArray.getInteger(R.styleable.ExpandTextView_arrowPosition, POSITION_RIGHT)
        arrowDrawablePadding = typedArray.getDimension(R.styleable.ExpandTextView_arrowPadding, dp2px(2f)).toInt()
        collapsed = typedArray.getBoolean(R.styleable.ExpandTextView_collapsed, false)
        typedArray.recycle()
        if (expandDrawable == null) {
            expandDrawable = getDrawable(getContext(), R.drawable.ic_arrow_down_gray)
        }
        if (collapseDrawable == null) {
            collapseDrawable = getDrawable(getContext(), R.drawable.ic_arrow_down_gray)
        }

        drawableWidth = (expandDrawable!!.intrinsicWidth * 1.2).toInt()
        drawableHeight = (expandDrawable!!.intrinsicHeight * 1.2).toInt()

        isClickable = true
        setOnClickListener(this)
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        if (visibility == GONE || animating) {
            super.onMeasure(widthMeasureSpec, heightMeasureSpec)
            return
        }

        // 重置高度重新测量
        layoutParams.height = -2 // 设置为wrap_content，重新measure
        maxLines = Int.MAX_VALUE
        // 测量TextView总高度
        super.onMeasure(widthMeasureSpec, heightMeasureSpec)
        if (lineCount <= maxCollapsedLines) {
            needCollapse = false
            return
        }
        needCollapse = true
        textHeightWithMaxLines = getRealTextViewHeight(this)
        if (collapsed) {
            maxLines = maxCollapsedLines
        }
        drawableSize = drawableWidth
        if (!isDrawablePaddingResolved) {
            if (arrowPosition == POSITION_RIGHT) {
                setPadding(paddingLeft, paddingTop, paddingRight + drawableSize + arrowDrawablePadding, paddingBottom)
            } else {
                setPadding(paddingLeft, paddingTop, paddingRight, paddingBottom + drawableHeight + arrowDrawablePadding)
            }
            isDrawablePaddingResolved = true
        }

        // 设置完成后重新测量
        super.onMeasure(widthMeasureSpec, heightMeasureSpec)
        if (collapsed) {
            collapsedHeight = measuredHeight
        }
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        if (needCollapse) {
            val left: Int
            val top: Int
            if (arrowPosition == POSITION_RIGHT) {
                left = width - totalPaddingRight + arrowDrawablePadding
                top = when (arrowAlign) {
                    ALIGN_LEFT_TOP -> totalPaddingTop
                    ALIGN_CENTER -> (height - drawableHeight) / 2
                    ALIGN_RIGHT_BOTTOM -> height - totalPaddingBottom - drawableHeight
                    else -> height - totalPaddingBottom - drawableHeight
                }
            } else {
                top = height - totalPaddingBottom + arrowDrawablePadding
                left = when (arrowAlign) {
                    ALIGN_LEFT_TOP -> totalPaddingLeft
                    ALIGN_CENTER -> (width - drawableWidth) / 2
                    ALIGN_RIGHT_BOTTOM -> width - totalPaddingRight - drawableWidth
                    else -> width - totalPaddingRight - drawableWidth
                }
            }
            canvas.translate(left.toFloat(), top.toFloat())
            if (collapsed) {
                expandDrawable!!.setBounds(0, 0, drawableWidth, drawableHeight)
                expandDrawable!!.draw(canvas)
            } else {
                collapseDrawable!!.setBounds(0, 0, drawableWidth, drawableHeight)
                collapseDrawable!!.draw(canvas)
            }
        }
    }

    override fun setText(text: CharSequence, type: BufferType) {
//        setCollapsed(true)
        super.setText(text, type)
    }

    override fun onClick(v: View) {
        if (!needCollapse) {
            return // 行数不足,不响应点击事件
        }
        collapsed = !collapsed
        val collapseBM = Bitmap.createBitmap(drawableWidth, drawableHeight, Bitmap.Config.ARGB_8888)
        val cv2 = Canvas(collapseBM)
        collapseDrawable!!.setBounds(0, 0, drawableWidth, drawableHeight)
        collapseDrawable!!.draw(cv2)
        val isExpand = ImageSpan(expandDrawable!!)
        val isCollapse = ImageSpan(context, collapseBM)
        val spannableString = SpannableString("icon")
        spannableString.setSpan(if (collapsed) isExpand else isCollapse, 0, 4, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)

        // mark that the animation is in progress
        animating = true
        val animation: Animation
        animation = if (collapsed) {
            ExpandCollapseAnimation(this, height, collapsedHeight)
        } else {
            ExpandCollapseAnimation(this, height, textHeightWithMaxLines)
        }
        animation.setFillAfter(true)
        animation.setAnimationListener(object : Animation.AnimationListener {
            override fun onAnimationStart(animation: Animation) {
                if (mListener != null) {
                    mListener!!.onChangeStateStart(!collapsed)
                }
                applyAlphaAnimation(this@ExpandTextView, animAlphaStart)
            }

            override fun onAnimationEnd(animation: Animation) {
                // clear animation here to avoid repeated applyTransformation() calls
                clearAnimation()
                // clear the animation flag
                animating = false

                // notify the listener
                if (mListener != null) {
                    mListener!!.onExpandStateChanged(this@ExpandTextView, !collapsed)
                }
            }

            override fun onAnimationRepeat(animation: Animation) {}
        })
        clearAnimation()
        startAnimation(animation)
    }

    /***
     *@date 创建时间 2020/10/12 16:55
     *<AUTHOR> W.YuLong
     *@description
     */
    private inner class ExpandCollapseAnimation(private val mTargetView: View, private val mStartHeight: Int, private val mEndHeight: Int) :
        Animation() {
        override fun applyTransformation(interpolatedTime: Float, t: Transformation) {
            val newHeight = ((mEndHeight - mStartHeight) * interpolatedTime + mStartHeight).toInt()
            mTargetView.layoutParams.height = newHeight
            maxHeight = newHeight
            if (java.lang.Float.compare(animAlphaStart, 1.0f) != 0) {
                applyAlphaAnimation(this@ExpandTextView, animAlphaStart + interpolatedTime * (1.0f - animAlphaStart))
            }
        }

        override fun willChangeBounds(): Boolean {
            return true
        }

        init {
            duration = animationDuration.toLong()
        }
    }

    private fun getDrawable(context: Context, drawableResId: Int): Drawable {
        val resources = context.resources
        return if (isPostLolipop) {
            resources.getDrawable(drawableResId, context.theme)
        } else {
            resources.getDrawable(drawableResId)
        }
    }

    @TargetApi(Build.VERSION_CODES.HONEYCOMB)
    private fun applyAlphaAnimation(view: View, alpha: Float) {
        if (isPostHoneycomb) {
            view.alpha = alpha
        } else {
            val alphaAnimation = AlphaAnimation(alpha, alpha)
            // make it instant
            alphaAnimation.duration = 0
            alphaAnimation.fillAfter = true
            view.startAnimation(alphaAnimation)
        }
    }

    private val isPostHoneycomb: Boolean get() = Build.VERSION.SDK_INT >= Build.VERSION_CODES.HONEYCOMB
    private val isPostLolipop: Boolean get() = Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP

    private fun getRealTextViewHeight(textView: TextView): Int {
        val textHeight = textView.layout.getLineTop(textView.lineCount)
        val padding = textView.compoundPaddingTop + textView.compoundPaddingBottom
        return textHeight + padding
    }

    fun setCollapsed(isCollapsed: Boolean) {
        collapsed = isCollapsed
    }

    /***
     *@date 创建时间 2020/10/12 16:56
     *<AUTHOR> W.YuLong
     *@description
     */
    interface OnExpandStateChangeListener {
        fun onChangeStateStart(willExpand: Boolean)

        /**
         * Called when the expand/collapse animation has been finished
         *
         * @param textView   - TextView being expanded/collapsed
         * @param isExpanded - true if the TextView has been expanded
         */
        fun onExpandStateChanged(textView: TextView?, isExpanded: Boolean)
    }

    fun setOnExpandStateChangeListener(listener: OnExpandStateChangeListener?) {
        mListener = listener
    }
}
