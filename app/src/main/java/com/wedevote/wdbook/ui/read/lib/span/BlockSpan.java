package com.wedevote.wdbook.ui.read.lib.span;

import android.graphics.Canvas;
import android.graphics.Paint;

import com.wedevote.wdbook.ui.read.lib.TextHelper;
import com.wedevote.wdbook.ui.read.lib.css.CssElement;

public class BlockSpan {
    private float paddingLeft, paddingRight, paddingTop, paddingBottom;//unit:px
    private float marginTop = 0, marginBottom = 0;//unit:em

    private CssElement.CSSBorder borderLeft, borderRight, borderTop, borderBottom;
    private int backgroundColor = -1;
    private float height = 0;
    private float originalHeight = 0;

    public BlockSpan() {

    }

    public void setBackgroundColor(int color) {
        backgroundColor = color;
    }

    public void setHeight(float height) {
        this.height = height;
    }

    public float getPaddingLeft() {
        return paddingLeft;
    }

    public float getPaddingRight() {
        return paddingRight;
    }

    public float getBorderHeight(float original, boolean first, boolean last) {
        float borderHeight = Math.max(height, 1.0f);
        float marginTopPx = 0;
        float marginBottomPx = 0;

        if (first) {
            if (borderTop != null) {
                borderHeight += borderTop.width.px;
            }
            marginTopPx = original * marginTop / 1.2f;
            borderHeight += marginTopPx;
        }
        if (last) {
            if (borderBottom != null) {
                borderHeight += borderBottom.width.px;
            }
            marginBottomPx = original * marginBottom / 1.2f;
            borderHeight += marginBottomPx;
        }
        originalHeight = original;
        return borderHeight;
    }

    public void setPadding(float l, float r, float t, float b) {
        paddingLeft = l;
        paddingRight = r;
        paddingTop = t;
        paddingBottom = b;
    }

    public void setMargin(float t, float b) {
        marginTop = t;
        marginBottom = b;
    }

    public float getTopMargin() {
        return originalHeight * marginTop / 1.2f;
    }

    public void setBorder(CssElement.CSSBorder b) {
        borderLeft = b;
        borderRight = b;
        borderTop = b;
        borderBottom = b;
    }

    public void setBorder(CssElement.CSSBorder l, CssElement.CSSBorder r, CssElement.CSSBorder t, CssElement.CSSBorder b) {
        if (l != null) {
            borderLeft = l;
        }
        if (r != null) {
            borderRight = r;
        }
        if (t != null) {
            borderTop = t;
        }
        if (b != null) {
            borderBottom = b;
        }
    }

    private Paint getPaint(Paint paint, CssElement.CSSBorder border) {
        Paint workPaint;
        if (border.type == CssElement.BorderType.dashed) {
            workPaint = TextHelper.getDashedUnderLinePaint(paint, false);
        } else {
            workPaint = paint;
        }
        workPaint.setStrokeWidth(border.width.px);
        workPaint.setColor(border.color);
        return workPaint;
    }

    public void drawBlock(Canvas canvas, Paint paint, int width, float height, float startX, float offsetY, boolean firstLine, boolean lastLine) {
        int oldColor = paint.getColor();
        float oldWidth = paint.getStrokeWidth();

        float marginTopPx = offsetY;
        float marginBottomPx = -offsetY;

        if (firstLine) {
            marginTopPx += originalHeight * marginTop / 1.2f;
        }

        if (lastLine) {
            marginBottomPx += originalHeight * marginBottom / 1.2f;
        }

        if (backgroundColor != -1) {
            paint.setColor(backgroundColor);
            canvas.drawRect(startX, marginTopPx, width, height - marginBottomPx, paint);
        }

        float lineWidth;
        if (borderLeft != null) {
            lineWidth = borderLeft.width.px;
            canvas.drawLine(lineWidth / 2 + startX, marginTopPx, lineWidth / 2 + startX, height - marginBottomPx, getPaint(paint, borderLeft));
        }

        if (borderRight != null) {
            lineWidth = borderRight.width.px;
            canvas.drawLine(width - lineWidth / 2, marginTopPx, width - lineWidth / 2, height - marginBottomPx, getPaint(paint, borderRight));
        }

        if (borderTop != null && firstLine) {
            lineWidth = borderTop.width.px;
            canvas.drawLine(startX, lineWidth / 2 + marginTopPx, width, lineWidth / 2 + marginTopPx, getPaint(paint, borderTop));
        }

        if (borderBottom != null && lastLine) {
            lineWidth = borderBottom.width.px;
            canvas.drawLine(startX, height - lineWidth / 2 - marginBottomPx, width, height - lineWidth / 2 - marginBottomPx, getPaint(paint, borderBottom));
        }

        paint.setColor(oldColor);
        paint.setStrokeWidth(oldWidth);
    }
}
