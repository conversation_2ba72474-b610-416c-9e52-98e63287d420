package com.wedevote.wdbook.ui

import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.View
import android.view.inputmethod.InputMethodManager
import android.widget.EditText
import android.widget.ImageView
import android.widget.TextView
import com.aquila.lib.tools.util.ToastUtil
import com.wedevote.wdbook.R
import com.wedevote.wdbook.base.ExceptionHandler
import com.wedevote.wdbook.base.RootActivity
import com.wedevote.wdbook.base.SDKSingleton
import com.wedevote.wdbook.entity.UserInfoEntity
import com.wedevote.wdbook.exception.ApiException
import com.wedevote.wdbook.tools.event.OnChangeAccountSuccess
import com.wedevote.wdbook.tools.util.APPUtil
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus

/***
 *@date 创建时间 2023/5/8
 *<AUTHOR> <PERSON><PERSON>
 *@description  个人中心-编辑名字页面
 */
class EditNameActivity : RootActivity(), View.OnClickListener {

    private lateinit var editNameEditText: EditText
    private lateinit var confirmTextView: TextView
    private lateinit var clearImageView: ImageView

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_edit_name_layout)
        initViewFromXML()
        setViewListeners()
        MainScope().launch(ExceptionHandler.coroutineExceptionHandler) {
            SDKSingleton.userBl.getUserInfoEntity(true)?.let {
                initUI(it)
            }
        }
    }

    private fun initUI(userInfoEntity: UserInfoEntity) {
        editNameEditText.requestFocus()
        (getSystemService(INPUT_METHOD_SERVICE) as InputMethodManager).showSoftInput(editNameEditText, InputMethodManager.SHOW_IMPLICIT)
        editNameEditText.setText(userInfoEntity.nickname)
        editNameEditText.setSelection(editNameEditText.text.length)
    }

    private fun setViewListeners() {
        confirmTextView.setOnClickListener(this)
        editNameEditText.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(charSequence: CharSequence, i: Int, i1: Int, i2: Int) {}
            override fun onTextChanged(charSequence: CharSequence, i: Int, i1: Int, i2: Int) {}
            override fun afterTextChanged(editable: Editable) {
                confirmTextView.isEnabled = editNameEditText.text.toString().trim().isNotEmpty()
                if (editNameEditText!!.text.isNotEmpty()) {
                    clearImageView.visibility = View.VISIBLE
                } else {
                    clearImageView.visibility = View.GONE
                }
            }
        })

        editNameEditText!!.setOnFocusChangeListener(object : View.OnFocusChangeListener {
            override fun onFocusChange(p0: View?, p1: Boolean) {
                if (!p1) {
                    clearImageView.visibility = View.GONE
                } else if (editNameEditText!!.text.isNotEmpty()) {
                    clearImageView.visibility = View.VISIBLE
                }
            }
        })

        editNameEditText!!.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(charSequence: CharSequence, i: Int, i1: Int, i2: Int) {}
            override fun onTextChanged(charSequence: CharSequence, i: Int, i1: Int, i2: Int) {
                if (charSequence.length > 30) {
                    ToastUtil.showToastShort(R.string.max_length_30)
                    var text = charSequence.toString().substring(0,30)
                    editNameEditText!!.setText(text)
                    editNameEditText!!.setSelection(text.length)
                } else if (charSequence.length < 30) {
                    editNameEditText!!.isSelected = false
                }
            }
            override fun afterTextChanged(editable: Editable) {
            }
        })
    }

    private fun initViewFromXML() {
        editNameEditText = findViewById(R.id.edit_name_EditText)
        confirmTextView = findViewById(R.id.edit_name_confirm_TextView)
        clearImageView = findViewById(R.id.clear_text_ImageView)
        clearImageView.setOnClickListener{
            editNameEditText.setText("")
        }
    }

    override fun onClick(v: View?) {
        when (v) {
            confirmTextView -> {
                val name = editNameEditText.text.toString().trim()
                if (name.isNullOrEmpty()) {
                    ToastUtil.showToastShort(R.string.name_can_not_empty)
                    return
                }
                MainScope().launch {
                    try {
                        APPUtil.showLoadingDialog(this@EditNameActivity)
                        SDKSingleton.userBl.updatNickname(name)
                        APPUtil.dismissLoadingDialog(this@EditNameActivity)
                        EventBus.getDefault().post(OnChangeAccountSuccess())
                        finish()
                    } catch (exception: Throwable) {
                        APPUtil.dismissLoadingDialog(this@EditNameActivity)
                        if (exception is ApiException) {
                            ToastUtil.showToastShort(exception.message)
                        } else {
                            ExceptionHandler.handleException(exception)
                        }
                    }
                }
            }
        }
    }

}
