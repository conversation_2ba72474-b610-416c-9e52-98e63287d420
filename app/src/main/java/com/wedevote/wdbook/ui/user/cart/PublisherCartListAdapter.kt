package com.wedevote.wdbook.ui.user.cart

import android.view.ViewGroup
import android.widget.ImageView
import com.aquila.lib.base.BaseRecycleAdapter
import com.aquila.lib.base.BaseViewHolder
import com.aquila.lib.log.KLog
import com.aquila.lib.widget.group.GroupImageTextLayout
import com.aquila.lib.widget.view.CustomRecyclerView
import com.wedevote.wdbook.R
import com.wedevote.wdbook.entity.store.CartProductEntity
import com.wedevote.wdbook.entity.store.CartPublisherEntity
import com.wedevote.wdbook.ui.user.OnDataChangeSelectListener
import com.wedevote.wdbook.utils.JsonUtility

class PublisherCartListAdapter : BaseRecycleAdapter<CartPublisherEntity, PublisherCartViewHolder>(), OnDataRefreshLoadListener {
    
    var onPublisherDataSelectListener: OnDataChangeSelectListener? = null
    var selectPublisherList = ArrayList<CartPublisherEntity>()
    var allSelectPublishMap: HashMap<Long, PublisherCartViewHolder> = HashMap()
    
    companion object {
        var openProductId: Long = 0
    
        var centerSelectList = ArrayList<CartProductEntity>()
    }
    
    
    private var onCartPublisherAllSelectListener: OnCartPublisherAllSelectListener = object : OnCartPublisherAllSelectListener {
        override fun onPublisherItemSelect(selected: Boolean, entity: CartPublisherEntity?) {
            if (selected) {
                if (entity != null && !selectPublisherList.contains(entity)) {
                    selectPublisherList.add(entity)
                }
            } else {
                if (entity != null && selectPublisherList.contains(entity)) {
                    selectPublisherList.remove(entity)
                }
            }
            onPublisherDataSelectListener?.isAllSelected(selectPublisherList.size == itemCount && itemCount > 0)
            onPublisherDataSelectListener?.itemCount(getSelectItemCount())
        }
    }
    
    fun getSelectItemCount(): Int {
        var count = 0
        //如果是全选的，直接计算
        if (selectPublisherList.size == itemCount && itemCount > 0) {
            for (item in dataList!!) {
                count += item.productList.size
            }
        } else {
            for (holder in allSelectPublishMap.values) {
                count += holder.dataAdapter.selectItemList.size
            }
        }
        return count
    }
    
    override fun onRefreshDataLoad() {
        notifyDataSetChanged()
    }
    
    override fun onDataDelete(entity: CartProductEntity) {
        for (item in dataList!!) {
            if (item.publisherId == entity.publisherId) {
                item.productList.remove(entity)
            }
        }
        notifyDataSetChanged()
    }
    
    fun setAllDataSelectStatus(isAllCheck: Boolean) {
        selectPublisherList.clear()
        centerSelectList.clear()
        
        //清空每个出版社下面选中的数据
        for (holder in allSelectPublishMap.values) {
            if (holder != null) {
                holder.dataAdapter.unSelectAll()
            }
        }
        if (isAllCheck && dataList != null) {
            for (entity in dataList!!) {
                selectPublisherList.add(entity)
                centerSelectList.addAll(entity.productList)
            }
        }
        notifyDataSetChanged()
    }
    
    fun getAllSelectProduceList() {
        var allSelectList: ArrayList<CartProductEntity> = ArrayList()
        for (data in allSelectPublishMap.values) {
            if (!data.dataAdapter.selectItemList.isNullOrEmpty()) {
                for (item in data.dataAdapter.selectItemList) {
                    allSelectList.add(item)
                }
            }
        }
        KLog.d("选取的为: count = ${allSelectList.size}, json = ${JsonUtility.encodeToString(allSelectList)}")
    }
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): PublisherCartViewHolder {
        return PublisherCartViewHolder(parent, onCartPublisherAllSelectListener, this)
    }
    
    override fun onBindViewHolder(holder: PublisherCartViewHolder, position: Int) {
        val data = getDataFromPosition(position)
        holder.initUIData(data)
        holder.setItemSelectStatus(selectPublisherList.contains(data))
        if (selectPublisherList.contains(data)) {
            holder.initSelectItemList(selectPublisherList[position].productList)
        }
        if (allSelectPublishMap[data!!.publisherId] == null) {
            allSelectPublishMap[data!!.publisherId] = holder
        }
    }
}


/***
 * @date 创建时间 2022/9/21 13:52
 * <AUTHOR> W.YuLong
 * @description 出版社的itemHolder
 */
class PublisherCartViewHolder(
    parent: ViewGroup,
    onCartPublisherAllSelectListener: OnCartPublisherAllSelectListener,
    onDataRefreshLoadListener: OnDataRefreshLoadListener
) :
    BaseViewHolder(parent, R.layout.holder_cart_book_item_layout) {
    
    val selectImageView: ImageView = itemView.findViewById(R.id.cart_publisher_check_flag_ImageView)
    val nameTextView: GroupImageTextLayout = itemView.findViewById(R.id.cart_publisher_name_Layout)
    val dataRecyclerView: CustomRecyclerView = itemView.findViewById(R.id.cart_publisher_data_RecyclerView)
    
    var cartPublisherEntity: CartPublisherEntity? = null
    
    
    val dataAdapter: BookCartListAdapter = BookCartListAdapter(object : OnDataChangeSelectListener {
        override fun isAllSelected(isAllSelected: Boolean) {
            setItemSelectStatus(isAllSelected)
            onCartPublisherAllSelectListener.onPublisherItemSelect(isAllSelected, cartPublisherEntity)
        }
        
    })
    
    init {
        dataAdapter.onDataRefreshLoadListener = onDataRefreshLoadListener
        
        selectImageView.setOnClickListener {
            if (selectImageView.isSelected) {
                dataAdapter.unSelectAll()
            } else {
                dataAdapter.doSelectAll()
            }
        }
        dataRecyclerView.adapter = dataAdapter
    }
    
    override fun <T> initUIData(t: T) {
        cartPublisherEntity = t as CartPublisherEntity
        dataAdapter.dataList = t.productList
        nameTextView.text = t.publisherName
    }
    
    fun initSelectItemList(list: ArrayList<CartProductEntity>?) {
        if (!list.isNullOrEmpty()) {
            dataAdapter.doSelectAll()
        }
    }
    
    fun setItemSelectStatus(isSelected: Boolean) {
        selectImageView.isSelected = isSelected
    }
    
}