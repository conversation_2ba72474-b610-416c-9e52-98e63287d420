package com.wedevote.wdbook.ui.account

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.Window
import com.aquila.lib.dialog.CommAlertDialog
import com.wedevote.wdbook.R
import com.wedevote.wdbook.base.RootActivity
import com.wedevote.wdbook.ui.home.HomeMainActivity
import com.wedevote.wdbook.ui.home.HomeTab

/***
 * @date 创建时间 2022/3/14 14:02
 * <AUTHOR> W.<PERSON>
 * @description
 */
class ShowDeviceDisableDialogActivity : RootActivity() {

    companion object {
        var isActivityShowing = false
        fun showDeviceFullDialog(context: Context) {
            if (!isActivityShowing) {
                val intent = Intent(context, ShowDeviceDisableDialogActivity::class.java)
                intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
                context.startActivity(intent)
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        this.requestWindowFeature(Window.FEATURE_NO_TITLE)
        isActivityShowing = true
        this.window.setBackgroundDrawableResource(R.color.transparent)
        setContentView(R.layout.activity_transfer_layout)
        showDeviceFullDialog()
    }

    fun showDeviceFullDialog() {
        CommAlertDialog.with(this)
            .setMessage(R.string.device_disable_dialog_text)
            .setMessageMinHeight(120)
            .setMiddleColor(R.color.color_blue_0644A0)
            .setMiddleText(R.string.label_OK)
            .setTouchOutsideCancel(false)
            .setCancelAble(false)
            .setOnViewClickListener { _, _, i ->
                if (i == CommAlertDialog.TAG_CLICK_MIDDLE) {
                    HomeMainActivity.gotoHomeActivity(this, HomeTab.STORE)
                    onBackPressed()
                }
            }.create().show()
    }

    override fun finish() {
        super.finish()
        isActivityShowing = false
    }

    override fun exitPendingAnim() {
        overridePendingTransition(com.aquila.lib.base.R.anim.base_anim_normal, com.aquila.lib.base.R.anim.base_anim_normal)
    }

    override fun enterPendingAnim() {
        overridePendingTransition(com.aquila.lib.base.R.anim.base_anim_normal, com.aquila.lib.base.R.anim.base_anim_normal)
    }
}
