package com.wedevote.wdbook.ui.user.coupon

import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.ImageView
import android.widget.TextView
import com.aquila.lib.base.BaseViewHolder
import com.wedevote.wdbook.R
import com.wedevote.wdbook.entity.coupon.CouponEntity
import com.wedevote.wdbook.entity.coupon.CouponStatus
import com.wedevote.wdbook.tools.util.APPUtil
import com.wedevote.wdbook.tools.util.UnitFormatUtil
import com.wedevote.wdbook.tools.util.findString

/***
 *@date 创建时间 2022/5/31 14:44
 *<AUTHOR> <PERSON><PERSON><PERSON>
 *@description
 */
open class BaseCouponViewHolder(parent: ViewGroup) : BaseViewHolder(parent, R.layout.holder_coupon_item_layout) {
    val couponAmountTextView: TextView = itemView.findViewById(R.id.item_coupon_money_TextView)
    val titleTextView: TextView = itemView.findViewById(R.id.item_coupon_name_TextView)

    //    val dashView: View = itemView.findViewById(R.id.item_coupon_dash_line_View)
    val dateTextView: TextView = itemView.findViewById(R.id.item_coupon_active_date_TextView)
    val useButton: Button = itemView.findViewById(R.id.item_coupon_use_coupon_Button)
    val statusImageView: ImageView = itemView.findViewById(R.id.item_coupon_status_ImageView)
    val checkImageView: ImageView = itemView.findViewById(R.id.item_coupon_radio_check_ImageView)



    fun selectResourceByLanguage(vararg resIds: Int): Int {
        val idx = when {
            resIds.size >= 2 && (APPUtil.currentLocale == APPUtil.LOCALE_TW || APPUtil.currentLocale == APPUtil.LOCALE_HK) -> 1
            else -> 0
        }
        return resIds.getOrElse(idx) { resIds.first() }
    }

    override fun <T> initUIData(t: T) {
        t as CouponEntity
        couponAmountTextView.text = UnitFormatUtil.formatPrice("", t.couponAmount)
        titleTextView.text = t.couponName
        dateTextView.text = "${UnitFormatUtil.formatDate_ymd(t.availableStartTime)} - ${UnitFormatUtil.formatDate_ymd(t.availableEndTime)}"
    }

    open fun initCouponStatus(status: Int) {
        when (status) {
            CouponStatus.NOT_GET.status -> {//未领取
                checkImageView.visibility = View.GONE
                useButton.visibility = View.VISIBLE
                useButton.text = findString(R.string.get_coupon)
                statusImageView.visibility = View.GONE
            }
            CouponStatus.CAN_USE.status -> {//可使用
                useButton.visibility = View.VISIBLE
                useButton.text = findString(R.string.use_coupon)
                statusImageView.visibility = View.GONE
            }
            CouponStatus.USED.status, CouponStatus.SOLD_OUT.status -> {//已使用,已冻结
                useButton.visibility = View.GONE
                statusImageView.visibility = View.VISIBLE
                statusImageView.setImageResource(R.drawable.ic_coupon_used)
            }
            CouponStatus.EXPIRED.status -> { //已过期
                useButton.visibility = View.GONE
                statusImageView.visibility = View.VISIBLE
                statusImageView.setImageResource(selectResourceByLanguage(R.drawable.ic_coupon_expired, R.drawable.ic_coupon_expired_cht))
            }
        }
    }
}

/***
 *@date 创建时间 2022/5/31 14:44
 *<AUTHOR> W.YuLong
 *@description
 */
class CouponItemViewHolder(parent: ViewGroup) : BaseCouponViewHolder(parent) {

    init {
        initCouponStatus(CouponStatus.CAN_USE.status)
    }

    override fun <T> initUIData(t: T) {
        super.initUIData(t)
        t as CouponEntity
//           0:未领取 1:未使用 2:已使用 3:已过期
        initCouponStatus(t.status)
    }
}

/***
 *@date 创建时间 2022/6/9 22:59
 *<AUTHOR> W.YuLong
 *@description
 */
class UseCouponViewHolder(parent: ViewGroup) : BaseCouponViewHolder(parent) {
    init {
        checkImageView.visibility = View.VISIBLE
        statusImageView.visibility = View.GONE
        useButton.visibility = View.GONE
    }

    override fun onItemSelected(isSelected: Boolean) {
        checkImageView.isSelected = isSelected
    }
}

/***
 *@date 创建时间 2022/5/31 14:44
 *<AUTHOR> W.YuLong
 *@description
 */
class GetCouponViewHolder(parent: ViewGroup) : BaseCouponViewHolder(parent) {

    init {
        checkImageView.visibility = View.GONE
        useButton.text = findString(R.string.get_coupon)
        statusImageView.setImageResource(selectResourceByLanguage(R.drawable.ic_coupon_has_get, R.drawable.ic_coupon_has_get_cht))
    }

    override fun <T> initUIData(t: T) {
        t as CouponEntity
        couponAmountTextView.text = UnitFormatUtil.formatPrice("", t.couponAmount)
        titleTextView.text = t.couponName
        dateTextView.text = "${UnitFormatUtil.formatDate_ymd(t.availableStartTime)} - ${UnitFormatUtil.formatDate_ymd(t.availableEndTime)}"
        initCouponStatus(t.status)
    }

    override fun initCouponStatus(status: Int) {
        super.initCouponStatus(status)
        //这里重写已领取的标记
        if (status == CouponStatus.CAN_USE.status) {
            useButton.visibility = View.GONE
            statusImageView.visibility = View.VISIBLE
            statusImageView.setImageResource(selectResourceByLanguage(R.drawable.ic_coupon_has_get, R.drawable.ic_coupon_has_get_cht))
        }
    }

}