package com.wedevote.wdbook.ui.dialogs

import android.app.Dialog
import android.content.Context
import android.os.Bundle
import android.widget.ProgressBar
import android.widget.TextView
import androidx.annotation.StringRes
import com.wedevote.wdbook.R

/***
 * @date 创建时间 2020/5/8 17:37
 * <AUTHOR> <PERSON><PERSON>
 * @description
 */
class LoadingProgressDialog(context: Context) : Dialog(context, R.style.DialogStyle) {

    private lateinit var progressBar: ProgressBar
    lateinit var titleTextView: TextView

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.dialog_loading_progress_layout)
        progressBar = findViewById(R.id.dialog_loading_ProgressBar)
        titleTextView = findViewById(R.id.dialog_loading_desc_TextView)
    }

    fun setTitleText(str: CharSequence) {
        titleTextView.text = str
    }

    fun setTitleText(@StringRes str: Int) {
        titleTextView.setText(str)
    }
}
