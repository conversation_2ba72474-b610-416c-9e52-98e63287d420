package com.wedevote.wdbook.ui.account

import android.content.Context
import android.content.Intent
import android.content.pm.ActivityInfo
import android.graphics.Color
import android.os.Build
import android.os.Bundle
import android.view.View
import android.view.ViewGroup
import android.view.Window
import android.view.WindowManager
import android.widget.Button
import com.aquila.lib.dialog.CommProgressDialog
import com.aquila.lib.log.KLog
import com.aquila.lib.tools.singleton.SPSingleton
import com.aquila.lib.tools.util.ToastUtil
import com.wedevote.wdbook.R
import com.wedevote.wdbook.base.ExceptionHandler
import com.wedevote.wdbook.base.RootActivity
import com.wedevote.wdbook.base.SDKSingleton
import com.wedevote.wdbook.constants.AnalyticsConstants
import com.wedevote.wdbook.constants.AnalyticsConstants.LOG_V1_PARAM_LOGIN_TYPE
import com.wedevote.wdbook.tools.event.OnLoginEvent
import com.wedevote.wdbook.tools.util.APPUtil
import com.wedevote.wdbook.tools.util.APPUtil.isAppInstalled
import com.wedevote.wdbook.tools.util.AnalyticsUtils
import com.wedevote.wdbook.tools.util.AnalyticsUtils.logEvent
import com.wedevote.wdbook.tools.util.IntentUtils
import com.wedevote.wdbook.tools.util.NetWorkUtils
import com.wedevote.wdbook.tools.util.SPKeyDefine
import com.wedevote.wdbook.tools.util.findString
import com.wedevote.wdbook.tools.util.scanForActivity
import com.wedevote.wdbook.ui.CommWebViewActivity
import com.wedevote.wdbook.ui.account.register.RegisterActivity
import com.wedevote.wdbook.ui.service.SyncDataService
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

/***
 * @date 创建时间 2020/7/22 13:36
 * <AUTHOR> W.YuLong
 * @description
 */
class SSOLoginActivity : RootActivity(), View.OnClickListener {
    private lateinit var ssoLoginButton: View
    private lateinit var rootView: View
    private lateinit var registerButton: Button
    private lateinit var loginButton: Button
    private var loadingDialog: CommProgressDialog? = null
    private var isLoginSuccess = false

    companion object {
        private var loginType: Int = 0
        private var executeNewIntentTime: Long = 0
        var onCallBack: OnLoginResultCallBack? = null
        var isActivityForeground = false
        fun checkAndGotoLogin(
            context: Context,
            needNewTask: Boolean = false,
            callBack: OnLoginResultCallBack? = null
        ) {
            if (isActivityForeground) {
                return
            }

            if (SDKSingleton.sessionBl.isLogin()) {
                callBack?.onLoginResult(true)
                return
            }
            onCallBack = callBack
            context.startActivity(
                Intent(context, SSOLoginActivity::class.java).apply {
                    if (needNewTask) {
                        flags = Intent.FLAG_ACTIVITY_NEW_TASK
                    }
                }
            )
            context.scanForActivity()
                ?.overridePendingTransition(R.anim.anim_move_from_bottom, R.anim.anim_normal)
        }
    }

    override fun interceptSetStatusBar(): Boolean {
        return APPUtil.isAboveAndroid15()
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
//        window.decorView.systemUiVisibility = View.SYSTEM_UI_FLAG_FULLSCREEN
        actionBar?.hide()
        window.run {
            setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT)
            setBackgroundDrawable(resources.getDrawable(R.drawable.shape_transparent))
        }

        // 5.x开始需要把颜色设置透明，否则导航栏会呈现系统默认的浅灰色
        val window: Window = window
        val decorView: View = window.decorView
        // 两个 flag 要结合使用，表示让应用的主体内容占用系统状态栏的空间
        val option = View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN or View.SYSTEM_UI_FLAG_LAYOUT_STABLE
        decorView.systemUiVisibility = option
        window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
        window.statusBarColor = Color.TRANSPARENT
        // 导航栏颜色也可以正常设置
        // window.setNavigationBarColor(Color.TRANSPARENT);
        setContentView(R.layout.activity_sso_login_layout)

        if (Build.VERSION.SDK_INT != Build.VERSION_CODES.O) {
            setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_PORTRAIT)
        }

        ssoLoginButton = findViewById(R.id.sso_login_LinearLayout)
        registerButton = findViewById(R.id.sso_register_Button)
        loginButton = findViewById(R.id.book_login_Button)
        rootView = findViewById(R.id.sso_root_RelativeLayout)
        if (isAppInstalled(this, SDKSingleton.appBl.androidWDBiblePackage)
            || isAppInstalled(this, SDKSingleton.appBl.androidWDBibleHDPackage)
        ) {
            ssoLoginButton.visibility = View.VISIBLE
        } else {
            ssoLoginButton.visibility = View.GONE
        }
        setViewListeners()
        isActivityForeground = true
    }

    override fun onResume() {
        super.onResume()
//        window.decorView.systemUiVisibility = View.SYSTEM_UI_FLAG_FULLSCREEN
        actionBar?.hide()
    }

    private fun setViewListeners() {
        ssoLoginButton.setOnClickListener(this)
        registerButton.setOnClickListener(this)
        loginButton.setOnClickListener(this)
        rootView.setOnClickListener(this)
    }

    override fun finish() {
        super.finish()
        overridePendingTransition(
            R.anim.anim_normal,
            com.aquila.lib.dialog.R.anim.translate_dialog_move_down
        )
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        if (System.currentTimeMillis() - executeNewIntentTime < 1000) {
            return
        }
        executeNewIntentTime = System.currentTimeMillis()

        val authToken = intent.getStringExtra("UserTokenBeanJson")
        KLog.d(authToken)
        if (!authToken.isNullOrEmpty()) {
            SDKSingleton.sessionBl.setUserTokenString(authToken)
            SPSingleton.get().putString(SPKeyDefine.SP_LoginUserId, SDKSingleton.sessionBl.userId)
            KLog.d("SDKSingleton.sessionBl.userId = ${SDKSingleton.sessionBl.userId}, authToken = $authToken")

            MainScope().launch {
                if (loadingDialog == null) {
                    loadingDialog = CommProgressDialog.with(this@SSOLoginActivity).create()
                }
                loadingDialog!!.show()
                loadingDialog!!.setTitleText(findString(R.string.label_dialog_loading))
                try {
                    val i = SDKSingleton.userBl.checkAndAddDevice(SDKSingleton.appBl.deviceId)
                    if (i == 1312) {
                        checkAndGotoDeviceManageActivity()
                    } else {
                        afterLogin()
                    }
                    loadingDialog!!.dismiss()
                } catch (e: Exception) {
                    afterLogin()
                    loadingDialog!!.dismiss()
                    ExceptionHandler.handleException(e)
                }
            }
        } else {
            ToastUtil.showToastShort(R.string.sso_login_unauthorized)
            KLog.e("授权登录失败")
            return
        }
    }

    private fun checkAndGotoDeviceManageActivity() {
        DeviceManagerActivity.gotoDeviceManager(
            this,
            object : OnLoginResultCallBack {
                override fun onLoginResult(isSuccess: Boolean) {
                    if (isSuccess) {
                        afterLogin()
                    }
                }
            }
        )
    }

    private fun afterLogin() {
        isLoginSuccess = true
        EventBus.getDefault().post(OnLoginEvent())
        AnalyticsUtils.updateAnalyticsUserID()
        logEvent(
            AnalyticsConstants.LOG_V1_USER_LOGIN_SUCCESS,
            LOG_V1_PARAM_LOGIN_TYPE,
            loginType.toString()
        )
        val intent = Intent(this@SSOLoginActivity, SyncDataService::class.java)
        startService(intent)
        finish()
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun doOnLoginEvent(event: OnLoginEvent) {
        isLoginSuccess = true
        finish()
    }

    override fun onClick(v: View?) {
        when (v) {
            loginButton -> {
                startActivity(Intent(this, LoginActivity::class.java))
            }

            registerButton -> {
                startActivity(Intent(this, RegisterActivity::class.java))
            }

            rootView -> {
                onBackPressed()
            }

            ssoLoginButton -> {
                if (!NetWorkUtils.isNetworkAvailable()) {
                    NetWorkUtils.showTipDialog(this, getString(R.string.no_network_connect))
                    return
                }
                logEvent(AnalyticsConstants.LOG_V1_USER_LOGIN)

                val wdBiblePackage = SDKSingleton.appBl.androidWDBiblePackage
                val wdBibleHDPackage = SDKSingleton.appBl.androidWDBibleHDPackage
                val isBiblePackageInstalled = isAppInstalled(this, wdBiblePackage)
                val isBibleHDPackageInstalled = isAppInstalled(this, wdBibleHDPackage)
                val thisClassName = "com.wedevote.wdbook.ui.account.SSOLoginActivity"
                val bibleIntent =
                    IntentUtils.createBibleInvocationIntent(wdBiblePackage, thisClassName)
                val bibleHDIntent =
                    IntentUtils.createBibleInvocationIntent(wdBibleHDPackage, thisClassName)

                if (isBiblePackageInstalled && isBibleHDPackageInstalled) {
                    // 比较极端的情况，用户同时安装了 Bible 和 Bible HD 这两个 App
                    val chooserIntent =
                        Intent.createChooser(bibleIntent, findString(R.string.choose_application))
                    chooserIntent.putExtra(Intent.EXTRA_INITIAL_INTENTS, arrayOf(bibleHDIntent))
                    try {
                        loginType = 1
                        startActivity(chooserIntent)
                    } catch (e: Exception) {
                        e.printStackTrace()
                        gotoWebLogin()
                    }
                } else if (isBiblePackageInstalled) {
                    // 只安装了 Bible App
                    try {
                        loginType = 1
                        startActivity(bibleIntent)
                    } catch (e: Exception) {
                        e.printStackTrace()
                        gotoWebLogin()
                    }
                } else if (isBibleHDPackageInstalled) {
                    // 只安装了 Bible HD App
                    try {
                        loginType = 1
                        startActivity(bibleHDIntent)
                    } catch (e: Exception) {
                        e.printStackTrace()
                        gotoWebLogin()
                    }
                } else {
                    gotoWebLogin()
                }
            }
        }
    }

    override fun onDestroy() {
        onCallBack?.onLoginResult(isLoginSuccess)
        super.onDestroy()
        onCallBack = null
        isActivityForeground = false
    }

    private fun gotoWebLogin() {
        loginType = 2
        CommWebViewActivity.gotoWebView(
            this,
            SDKSingleton.sessionBl.getAuthorizeUrl(),
            true,
            findString(R.string.sso_login_authorize_loging)
        )
    }

}

interface OnLoginResultCallBack {
    fun onLoginResult(isSuccess: Boolean)
}
