package com.wedevote.wdbook.ui.read

import android.annotation.SuppressLint
import android.app.Dialog
import android.content.Context
import android.content.Intent
import android.content.res.Configuration
import android.content.pm.ActivityInfo
import android.os.Build
import android.view.OrientationEventListener
import android.os.Bundle
import android.os.Handler
import android.os.Message
import android.os.PowerManager
import android.os.PowerManager.WakeLock
import android.view.KeyEvent
import android.view.View
import com.aquila.lib.base.OnViewClickListener
import com.aquila.lib.dialog.CommAlertDialog
import com.aquila.lib.dialog.CommProgressDialog
import com.aquila.lib.dialog.OnDialogViewClickListener
import com.aquila.lib.tools.singleton.SPSingleton
import com.aquila.lib.tools.util.ScreenUtil
import com.aquila.lib.tools.util.ToastUtil
import com.wedevote.wdbook.R
import com.wedevote.wdbook.base.APPConfig
import com.wedevote.wdbook.base.ExceptionHandler
import com.wedevote.wdbook.base.RootActivity
import com.wedevote.wdbook.base.SDKSingleton
import com.wedevote.wdbook.constants.AnalyticsConstants
import com.wedevote.wdbook.constants.AnalyticsConstants.LOG_V1_PARAM_RESOURCE_ID
import com.wedevote.wdbook.constants.DownloadStatus
import com.wedevote.wdbook.entity.BookCatalogEntity
import com.wedevote.wdbook.entity.NoteEntity
import com.wedevote.wdbook.entity.resource.ResourceDownloadInfo
import com.wedevote.wdbook.entity.store.BookFileDownloadEntity
import com.wedevote.wdbook.tools.download.DownloaderEngine
import com.wedevote.wdbook.tools.download.OnDownloadingListener
import com.wedevote.wdbook.tools.event.*
import com.wedevote.wdbook.tools.util.*
import com.wedevote.wdbook.tools.util.AnalyticsUtils.logEvent
import com.wedevote.wdbook.ui.home.microwidget.DeepLinkUtils
import com.wedevote.wdbook.ui.read.lib.EPubBook
import com.wedevote.wdbook.ui.read.lib.data.TocItem
import com.wedevote.wdbook.ui.read.search.BookSearchActivity
import com.wedevote.wdbook.ui.read.widgets.BookReadToolLayout
import com.wedevote.wdbook.ui.read.widgets.BookSearchResultLayout
import com.wedevote.wdbook.ui.read.widgets.OptionClickTag
import com.wedevote.wdbook.ui.read.widgets.OptionLinkJumpBackLayout
import com.wedevote.wdbook.ui.read.widgets.ReadProcess
import com.wedevote.wdbook.ui.service.SyncDataService
import com.wedevote.wdbook.utils.JsonUtility.decodeFromString
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import java.io.File
import org.json.JSONObject
import com.wedevote.wdbook.tools.event.OnBookSearchNavigateEvent
import com.wedevote.wdbook.ui.account.OnLoginResultCallBack
import com.wedevote.wdbook.ui.account.SSOLoginActivity
import com.wedevote.wdbook.ui.read.search.BookSearchManager
import com.wedevote.wdbook.ui.read.ReadingSessionTracker

class BookReadActivity : RootActivity() {
    private lateinit var toolLayout: BookReadToolLayout
    private lateinit var viewPager: BookReadViewPager
    private lateinit var linkBackLayout: OptionLinkJumpBackLayout
    lateinit var searchResultLayout: BookSearchResultLayout
    private lateinit var pageAdapter: BookPageAdapter
    private var mWakeLock: WakeLock? = null
    private var resourceId: String = ""
    private var bookFileId: String = ""
    private var bookKey: String = ""
    private lateinit var maskView: View
    private var initialProgressDialog: CommProgressDialog? = null

    private val handler: Handler = object : Handler() {
        override fun handleMessage(msg: Message) {
            if (toolLayout.commTitleLayout.visibility == View.VISIBLE) {
                toolLayout.hideToolsLayout()
                maskView.visibility = View.GONE
                toolLayout.setStatusBarShowState(this@BookReadActivity, searchResultLayout.isVisible())
            }
        }
    }

    companion object {
        var isOperateBookData = false

        fun gotoBookReadActivity(
            context: Context,
            filePath: String?,
            fileId: String?,
            resourceId: String?,
            noteEntityJson: String? = null,
            fromSearch: Boolean = false
        ) {
            gotoBookReadActivity(context, filePath, fileId, resourceId, noteEntityJson, "", fromSearch)
        }


        fun gotoBookReadActivity(
            context: Context,
            filePath: String?,
            fileId: String?,
            resourceId: String?,
            noteEntityJson: String? = null,
            jumpStep: String = "",
            fromSearch: Boolean = false,
            keywords: String = ""
        ) {
            if (APPConfig.isFastClick()) return
            MainScope().launch(ExceptionHandler.coroutineExceptionHandler) {
                SDKSingleton.userBl.getEncryptionKey(fileId!!).also { encryptionKey ->
                    if (!encryptionKey.isNullOrEmpty()) {
                        // 到阅读页面之前更新阅读本书的时间
                        resourceId?.let {
                            SDKSingleton.dbWrapBl.updateBookLastVisitTime(it)
                        }
                        val intent = Intent(context, BookReadActivity::class.java)
                        intent.putExtra(IntentConstants.EXTRA_BookFilePath, filePath)
                        intent.putExtra(IntentConstants.EXTRA_fileId, fileId)
                        intent.putExtra(IntentConstants.EXTRA_BookKey, encryptionKey)
                        intent.putExtra(IntentConstants.EXTRA_ResourceId, resourceId)
                        if (!noteEntityJson.isNullOrEmpty()) {
                            intent.putExtra(IntentConstants.EXTRA_NoteEntity, noteEntityJson)
                        }
                        intent.putExtra(IntentConstants.EXTRA_jumpStep, jumpStep)
                        intent.putExtra(IntentConstants.EXTRA_FromSearch, fromSearch)
                        if (keywords.isNotEmpty()) {
                            intent.putExtra(IntentConstants.EXTRA_Keywords, keywords)
                        }
                        context.startActivity(intent)
                    } else {
                        context.startService(Intent(context, SyncDataService::class.java))
                        showErrorDialog(context) {}
                    }
                }
            }
        }

        private fun showErrorDialog(context: Context, clickListener: (Int) -> Unit) {
            CommAlertDialog.with(context).setMessage(R.string.tip_open_failed)
                .setStartText(findString(R.string.label_OK))
                .setOnViewClickListener { _, _, t ->
                    clickListener(t)
                }.setCancelAble(false).setTouchOutsideCancel(false)
                .setAllButtonColorRes(R.color.text_color_blue_007AFF)
                .create().show()
        }
    }

    fun initUI() {
        viewPager.adapter = pageAdapter
        pageAdapter.onViewClickListener = object : OnViewClickListener {
            override fun <T> onClickAction(v: View, str: String, t: T?) {
                if (str == OptionClickTag.TAG_INNER_JUMP) {
                    linkBackLayout.visibility = View.VISIBLE
                    toolLayout.saveHistory()
                    linkBackLayout.lastReadProgress = toolLayout.getLastHistory()
                    linkBackLayout.setBackTitle(findString(R.string.back_to_page).format(pageAdapter.getCurrentPageInBook() - 1))
                    saveAndJump(t as String)
                    return
                }
                if (toolLayout.commTitleLayout.visibility == View.VISIBLE) {
                    toolLayout.hideToolsLayout()
                    maskView.visibility = View.GONE
                    toolLayout.setStatusBarShowState(this@BookReadActivity,
                        searchResultLayout.isVisible()
                    )
                } else {
                    if (ParamsDefine.TAG_HideToolBar.equals(str)) {
                        return
                    }
                    toolLayout.setStatusBarShowState(this@BookReadActivity, true)
                    toolLayout.showToolsLayout()
                    maskView.visibility = View.VISIBLE
                }
                handler.removeMessages(0)
            }
        }
        pageAdapter.setBookReadToolLayout(toolLayout)
        pageAdapter.onUserNavigationListener = {
            rotation.reset(resources.configuration.orientation)
        }

        // 将内置的圣经书籍数据库拷贝到指定的目录
        if (!File(DataPathUtil.getBibleDBPath()).exists()) {
            !FilePathUtil.copyRawFileToPath(DataPathUtil.getBibleDBPath(), R.raw.bible)
        }
        toolLayout.onViewClickListener = onViewClickListener
        linkBackLayout.onViewClickListener = onViewClickListener

        handler.sendEmptyMessageDelayed(0, 3000)
        resourceId = intent.getStringExtra(IntentConstants.EXTRA_ResourceId) ?: ""
        bookKey = intent.getStringExtra(IntentConstants.EXTRA_BookKey) ?: ""

        // Start reading session tracking
        ReadingSessionTracker.startSession(this, resourceId)

        openBook()
        checkBookFileNeedUpdate(resourceId)

        /*这个是处理deeplink的书签跳转*/
        var step = intent.getStringExtra(IntentConstants.EXTRA_jumpStep)
        if (step.equals(DeepLinkUtils.openBookMark)) {
            toolLayout.post() {
                toolLayout.bookNoteImageView.performClick()
            }
        }
        logEvent(
            AnalyticsConstants.LOG_V1_BOOK_OPEN, LOG_V1_PARAM_RESOURCE_ID, resourceId
        )
    }

    @SuppressLint("InvalidWakeLockTag")
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_book_read_layout)
        toolLayout = findViewById(R.id.book_tool_layout)
        searchResultLayout = findViewById(R.id.book_search_result_layout)
        viewPager = findViewById(R.id.book_content_view)
        linkBackLayout = findViewById(R.id.book_link_option_layout)
        maskView = findViewById(R.id.book_mask_View)
        toolLayout.setStatusBarShowState(this, true)

        if (ScreenUtil.isTablet(this)) {
            requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_FULL_SENSOR
            rotation.lastKnownOrientation = resources.configuration.orientation
            handleOrientationChange()
        }

        initResearchListener()
        updateNeedTopPadding(false)
        if (SPSingleton.get().getBoolean(SPKeyDefine.SP_IsLockScreen, false)) {
            val pm = getSystemService(POWER_SERVICE) as PowerManager
            mWakeLock = pm.newWakeLock(PowerManager.SCREEN_BRIGHT_WAKE_LOCK, "WDBook")
            mWakeLock?.acquire()
        }
        pageAdapter = BookPageAdapter(this, viewPager)
        setSwipeBackEnable(false)
        initialProgressDialog =
            CommProgressDialog.with(this).setCancelable(false).setTouchOutsideCancel(false)
                .setTitle(findString(R.string.loading)).create()
        if (!isFinishing && !isDestroyed) {
            initialProgressDialog?.show()
        }

        bookFileId = intent.getStringExtra(IntentConstants.EXTRA_fileId) ?: ""
        Thread {
            val filePath = intent.getStringExtra(IntentConstants.EXTRA_BookFilePath) ?: ""
            bookKey = intent.getStringExtra(IntentConstants.EXTRA_BookKey) ?: ""
            val ret = pageAdapter.preLoadBook(filePath, bookKey, bookFileId)
            if (ret) {
                if (filePath.contains(EPubBook.preload_book_name) && EPubBook.contentsDialogData_temp.size > 0) {
                    contentsDialogData.clear()
                    contentsDialogData.addAll(EPubBook.contentsDialogData_temp)
                } else {
                    index = 0
                    contentsDialogData = createSubList(1, true, EPubBook.tocItemList)
                    if (filePath.contains(EPubBook.preload_book_name) && EPubBook.contentsDialogData_temp.size == 0) {
                        EPubBook.contentsDialogData_temp.addAll(contentsDialogData)
                    }
                }
            }
            runOnUiThread {
                if (ret) {
                    initUI()
                } else {
                    showErrorDialog(this) {
                        startService(Intent(this, SyncDataService::class.java))
                        onBackPressed()
                    }
                }
                if (!isFinishing && !isDestroyed) {
                    initialProgressDialog?.dismiss()
                }
            }
        }.start()
    }

    private fun initResearchListener() {
        searchResultLayout.onSearchResultListener =
            object : BookSearchResultLayout.OnSearchResultListener {
                override fun onPreviousClick() {
                    onBookSearchNavigate(OnBookSearchNavigateEvent(false))
                    updateNavigationButtonState(false)
                }

                override fun onResultClick() {
                    showBookSearchPage(false)
                }

                override fun onNextClick() {
                    onBookSearchNavigate(OnBookSearchNavigateEvent(true))
                    updateNavigationButtonState(true)
                }

                override fun onCloseClick() {
                    BookSearchActivity.clearSavedData()
                    pageAdapter.clearSearchHighlight()
                }
            }
    }

    /**
     * 更新导航按钮状态
     */
    private fun updateNavigationButtonState(isNext: Boolean) {
        // 暂时禁用按钮，等待响应
        if (isNext) {
            searchResultLayout.setNextButtonEnabled(false)
        } else {
            searchResultLayout.setPreviousButtonEnabled(false)
        }
    }

    override fun onDestroy() {
        // End reading session tracking
        ReadingSessionTracker.endSession(this)
        pageAdapter.pageInfoCalculateHelper.interruptCalculation()
        EPubBook.isOpenBook = false

        initialProgressDialog?.let {
            if (it.isShowing) {
                try { it.dismiss() } catch (_: Exception) {}
            }
        }
        initialProgressDialog = null

        orientationListener?.disable()
        orientationListener = null
        isHandlingRotation = false

        super.onDestroy()
    }

    private var contentsDialogData: ArrayList<BookCatalogEntity> = ArrayList()
    private var catalogId = 0L
    var index = 0
    private fun createSubList(
        level: Int,
        checkExpand: Boolean,
        tocItem: ArrayList<TocItem>
    ): ArrayList<BookCatalogEntity> {
        val initialCapacity = if (tocItem.size > index) tocItem.size - index else 0
        val list = ArrayList<BookCatalogEntity>(initialCapacity)
        var lastItem = BookCatalogEntity()

        while (index < tocItem.size) {
            val t = tocItem[index]
            if (t.level == level) {
                lastItem = BookCatalogEntity().apply {
                    name = t.title
                    this.level = level
                    desc = (catalogId + 1).toString()
                    path = t.fullPath
                    pathIndex = if (EPubBook.pathList.isEmpty()) 0 else EPubBook.getPathIndex(t.path)
                    expand = false
                }
                list.add(lastItem)
            } else if (t.level > level) {
                lastItem.subCatalogList = createSubList(
                    level + 1,
                    checkExpand,
                    tocItem
                )
                if (!lastItem.subCatalogList.isNullOrEmpty()) {
                    if (checkExpand && lastItem.subCatalogList!!.any { it.expand }) {
                        lastItem.expand = true
                    }
                }
            } else {
                index--
                return list
            }
            index++
        }
        return list
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onSyncNoteData(noteMergedEvent: OnSyncNoteFinish?) {
        try {
            pageAdapter.notifyDataSetChanged()
        } catch (e: Exception) {
        }
        pageAdapter.initBookmarkState()
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onChangeBookDataEvent(event: BookDataChangeEvent) {
        try {
            pageAdapter.notifyDataSetChanged()
        } catch (e: Exception) {
        }
        pageAdapter.initBookmarkState()
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onExitDialog(event: DialogDismissEvent) {
        if (toolLayout.commTitleLayout.visibility == View.GONE) {
            toolLayout.setStatusBarShowState(this, false)
        }
    }

    private fun reportReadDuration() {
        pageAdapter.savePageReadDuration()
        val duration = SPSingleton.get().getLong(resourceId, 0L)
        if (duration >= 3) {
            val isSucceed = logEvent(
                AnalyticsConstants.LOG_V1_BOOK_READ_DURATION,
                LOG_V1_PARAM_RESOURCE_ID,
                resourceId,
                AnalyticsConstants.LOG_V1_PARAM_DURATION,
                duration.toString()
            )
            if (isSucceed) {
                SPSingleton.get().removeKey(resourceId)
            }
        } else {
            SPSingleton.get().removeKey(resourceId)
        }
    }

    override fun interceptSetStatusBar(): Boolean {
        return Build.VERSION.SDK_INT <= Build.VERSION_CODES.UPSIDE_DOWN_CAKE
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        // 获取手机当前音量值
        if (keyCode == KeyEvent.KEYCODE_VOLUME_DOWN) {// 音量减小
            pageAdapter.goToNextPage()
            return true
        } else if (keyCode == KeyEvent.KEYCODE_VOLUME_UP) {// 音量增大
            pageAdapter.goToPrevPage()
            return true
        }
        return super.onKeyDown(keyCode, event)
    }

    private fun saveAndJump(link: String) {
        pageAdapter.gotoAddress(link, -1)
        toolLayout.saveHistory()
        handler.sendEmptyMessageDelayed(0, 1000)
    }

    private fun hidSearchResultLayout() {
        if (searchResultLayout.isVisible()) {
            BookSearchActivity.clearSavedData()
            pageAdapter.clearSearchHighlight()
            searchResultLayout.hideMenu()
        }
    }

    private var isDialogShowing: Boolean = false

    private fun gotoBookContentsActivity() {
        if (isDialogShowing) return
        if (contentsDialogData.isEmpty()) {
            return
        }
        APPUtil.dismissLoadingDialog(this)
        isDialogShowing = true
        val dialog = BookContentsDialog(pageAdapter.getCurrentPath(), contentsDialogData)
        dialog.setOnDialogListener(object : BookContentsDialog.ContentsDialogListener {
            override fun onCallBack(link: String) {
                if (!link.isNullOrEmpty()) {
                    saveAndJump(link)
                    hidSearchResultLayout()
                    dialog.dismiss()
                }
            }
        })
        dialog.setOnDismissListener {
            isDialogShowing = false
        }
        dialog.show(supportFragmentManager, "ContentsDialog")
    }

    private fun showBookNoteDialog() {
        val dialog = BookNoteAndMarkDialogFragment()
        dialog.arguments = Bundle().apply {
            putString(IntentConstants.EXTRA_fileId, bookFileId)
            putString(IntentConstants.EXTRA_ResourceId, resourceId)
        }
        dialog.setOnJumpListener(object : OnJumpActionListener {
            override fun doJump(page: Int, offset: Int) {
                pageAdapter.gotoPage(page, offset)
                dialog.dismiss()
            }
        })
        dialog.show(supportFragmentManager, "BookNoteAndMarkDialog")
    }

    private fun openBook() {
        val jsonNote = intent.getStringExtra(IntentConstants.EXTRA_NoteEntity)
        pageAdapter.loadBook(bookFileId, resourceId, jsonNote)

        val fromSearch = intent.getBooleanExtra(IntentConstants.EXTRA_FromSearch, false)
        if (fromSearch && jsonNote != null) {
            // 从搜索结果跳转过来
            toolLayout.commTitleLayout.visibility = View.GONE
            toolLayout.optionContainerLayout.visibility = View.GONE
            toolLayout.bottomContainerLayout.visibility = View.GONE
            maskView.visibility = View.GONE
            val noteEntity = decodeFromString(jsonNote) as NoteEntity
            updateNavigationButtonsFromNoteEntity(noteEntity)
            searchResultLayout.showMenu()

            val keywords = intent.getStringExtra(IntentConstants.EXTRA_Keywords) ?: ""
            if (keywords.isNotEmpty()) {
                searchResultLayout.setSearchKeywords(keywords)
            }

            if (noteEntity.fileName.isNotEmpty()) {
                searchResultLayout.setChapterName(noteEntity.fileName)
            }
        }
    }

    private fun checkBookFileNeedUpdate(resourceId: String) {
        if (SDKSingleton.sessionBl.isLogin()) {
            doCheckUpdate(resourceId)
        } else {
            SSOLoginActivity.checkAndGotoLogin(
                this,
                callBack = object : OnLoginResultCallBack {
                    override fun onLoginResult(isSuccess: Boolean) {
                        if (isSuccess) {
                            doCheckUpdate(resourceId)
                        }
                    }
                },
            )
        }
    }

    private fun doCheckUpdate(resourceId: String) {
        val downloadInfo = SDKSingleton.dbWrapBl.getResourceDownloadInfo(resourceId)
        if (downloadInfo != null && downloadInfo.downloadStatus == DownloadStatus.UPDATE) {
            showBookFileUpdateDialog(downloadInfo, false)
        }
    }

    private fun showBookFileUpdateDialog(downloadInfo: ResourceDownloadInfo, isRetry: Boolean) {
        val downloadEngine = DownloaderEngine(this@BookReadActivity)
        CommAlertDialog.with(this).apply {
            if (isRetry) {
                setMessage(R.string.fail_update_book_file)
                setStartText(R.string.label_cancel)
                setEndText(R.string.retry)
            } else {
                setTitle(R.string.warm_prompt_title)
                setMessage(R.string.current_book_has_updated)
                setStartText(R.string.label_cancel).setEndText(R.string.update)
            }
        }
            .setClickButtonDismiss(true)
            .setAllButtonColorRes(if (APPConfig.isCurrentThemeLight()) R.color.text_color_blue_007AFF else R.color.color_blue_0357C3)
            .setOnViewClickListener(object : OnDialogViewClickListener {
                override fun onViewClick(dialog: Dialog, v: View, tag: Int) {
                    if (tag == CommAlertDialog.TAG_CLICK_END) {
                        downloadEngine.setOnDownloadingListener(object : OnDownloadingListener {
                            override fun onBeginning(entity: BookFileDownloadEntity) {
                                showDownloadingProgressDialog()
                            }

                            override fun onError(
                                entity: BookFileDownloadEntity,
                                errorDesc: String
                            ) {
                                dialog.dismiss()
                                dismissProgressDialog()
                                showBookFileUpdateDialog(downloadInfo, true)
                            }

                            override fun onComplete(entity: BookFileDownloadEntity) {
                                dismissProgressDialog()
                                if (SDKSingleton.sessionBl.isLogin()) {
                                    SDKSingleton.dbWrapBl.saveBookXmlData(entity.fileId, "")
                                    SDKSingleton.dbWrapBl.getFileDownloadDataEntity(entity.fileId)
                                        ?.let {
                                            reloadDataAndOpenBook(it.getActualFilePath(), entity.fileId)
                                        }
                                }
                            }
                        })
                        downloadEngine.readyDownloadOnlyByFileId(downloadInfo.fileId!!)
                    }
                }
            }).create().show()
    }

    private var downloadProgressDialog: CommProgressDialog? = null

    private fun showDownloadingProgressDialog() {
        if (downloadProgressDialog == null) {
            downloadProgressDialog =
                CommProgressDialog.with(this).setTitle(findString(R.string.in_updating))
                    .setTouchOutsideCancel(false).create()
        }
        if (!downloadProgressDialog!!.isShowing) {
            downloadProgressDialog!!.show()
        }
    }
    private fun reloadDataAndOpenBook(filePath: String, fileId: String) {
        val progressDialog =
            CommProgressDialog.with(this).setCancelable(false).setTouchOutsideCancel(false)
                .setTitle(findString(R.string.loading)).create()
        progressDialog.show()
        Thread {
            EPubBook.contentsDialogData_temp.clear()
            index = 0
            pageAdapter.preLoadBook(filePath, bookKey, fileId)
            contentsDialogData = createSubList(1, true, EPubBook.tocItemList)
            runOnUiThread {
                openBook()
                progressDialog.dismiss()
                ToastUtil.showToastShort(R.string.download_finish_and_reload)
            }
        }.start()
    }

    private fun dismissProgressDialog() {
        if (downloadProgressDialog != null && downloadProgressDialog!!.isShowing) {
            downloadProgressDialog!!.dismiss()
            downloadProgressDialog = null
        }
    }

    private val onViewClickListener: OnViewClickListener = object : OnViewClickListener {
        override fun <T> onClickAction(v: View, tag: String, t: T?) {
            when (tag) {
                OptionClickTag.TAG_CLICK_TOOL_TITLE -> gotoBookContentsActivity()
                OptionClickTag.TAG_FONT_SIZE -> pageAdapter.setTextSize((t as Int))
                OptionClickTag.TAG_FONT_STYLE -> pageAdapter.setTextFontType((t as Int))
                OptionClickTag.TAG_THEME_BLACK, OptionClickTag.TAG_THEME_WHITE -> {
                    pageAdapter.savePosition()
                    EventBus.getDefault().post(RestartAllActivityEvent())
                }
                OptionClickTag.TAG_SCOPE_PROGRESS -> {
                    val process = t as Int
                    toolLayout.setJumpTitle(pageAdapter.getTitle(process))
                    pageAdapter.setJumpProgress(process, 0)
                }
                OptionClickTag.TAG_SCOPE_REVOKE, OptionClickTag.TAG_SCOPE_FINISH -> {
                    val progress = t as ReadProcess
                    pageAdapter.gotoPage(progress.process, progress.offset)
                    if (tag == OptionClickTag.TAG_SCOPE_REVOKE) {
                        hidSearchResultLayout()
                    }
                }
                OptionClickTag.TAG_CONTENT -> gotoBookContentsActivity()
                OptionClickTag.TAG_BOOK_NOTE -> showBookNoteDialog()
                OptionClickTag.TAG_BOOK_SEARCH -> {
                    showBookSearchPage(false)
                }
                OptionClickTag.TAG_BOOK_MARK -> pageAdapter.addOrRemoveBookmark()
                // 点击工具栏任何地方之后不需要工具栏自动消失
                OptionClickTag.TAG_CLICK_TOOL_LAYOUT -> handler.removeMessages(0)
            }
        }
    }

    private fun showBookSearchPage(clearSavedData: Boolean = true) {
        if (clearSavedData) {
            BookSearchActivity.clearSavedData()
            EventBus.getDefault().post(OnBookSearchCallFinishEvent())
        }

        val intent = Intent(this, BookSearchActivity::class.java)
        intent.addFlags(Intent.FLAG_ACTIVITY_REORDER_TO_FRONT)
        intent.putExtra(IntentConstants.EXTRA_ResourceId, resourceId)
        startActivity(intent)
        overridePendingTransition(R.anim.anim_move_from_bottom, 0)
    }

    override fun onPause() {
        super.onPause()
        if (null != mWakeLock && mWakeLock!!.isHeld) {
            mWakeLock!!.release()
        }
        pageAdapter.savePosition()
        EventBus.getDefault().post(OnBookReadEvent())
        if (isOperateBookData) {
            val intent = Intent(this, SyncDataService::class.java)
            startService(intent)
            isOperateBookData = false
        }
        reportReadDuration()
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun OnBookSearchJumpClickEvent(event: OnBookSearchJumpClickEvent) {
        if (event.noteEntity != null) {
            toolLayout.hideToolsLayoutWithoutAnim()
            toolLayout.setStatusBarShowState(this@BookReadActivity,
                true
            )
            linkBackLayout.visibility = View.GONE
            handleSearchHighlight(
                event.noteEntity,
                event.resourceId,
                event.filePath,
                event.fileId,
                event.keywords
            )
        }
    }

    /**
     * 处理搜索导航事件
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onBookSearchNavigate(event: OnBookSearchNavigateEvent) {
        // 暂时禁用按钮，等待响应
        if (event.isNext) {
            searchResultLayout.setNextButtonEnabled(false)
        } else {
            searchResultLayout.setPreviousButtonEnabled(false)
        }

        // 使用BookSearchManager处理搜索导航
        BookSearchManager.getInstance().handleSearchNavigate(event, this)
    }

    /**
     * 处理搜索结果导航响应事件
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onBookSearchNavigateResult(event: OnBookSearchNavigateResultEvent) {
        // 恢复导航按钮状态
        searchResultLayout.setPreviousButtonEnabled(true)
        searchResultLayout.setNextButtonEnabled(true)

        if (event.success && event.noteEntity != null) {
            val keywords = intent.getStringExtra(IntentConstants.EXTRA_Keywords) ?: ""
            handleSearchHighlight(
                event.noteEntity,
                event.resourceId,
                event.filePath,
                event.fileId,
                keywords
            )
        }
    }

    /**
     * 根据笔记实体中的导航状态信息更新导航按钮状态
     */
    private fun updateNavigationButtonsFromNoteEntity(noteEntity: NoteEntity) {
        try {
            val extraData = noteEntity.extraData
            if (!extraData.isNullOrEmpty()) {
                val jsonObject = JSONObject(extraData)
                val hasPrevious = jsonObject.optBoolean("hasPrevious", true)
                val hasNext = jsonObject.optBoolean("hasNext", true)

                searchResultLayout.setPreviousButtonEnabled(hasPrevious)
                searchResultLayout.setNextButtonEnabled(hasNext)
            }
        } catch (e: Exception) {
            searchResultLayout.setPreviousButtonEnabled(true)
            searchResultLayout.setNextButtonEnabled(true)
        }
    }

    /**
     * 处理搜索清理事件
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onBookSearchClear(event: OnBookSearchClearEvent) {
        BookSearchActivity.clearSavedData()
        pageAdapter.clearSearchHighlight()
        searchResultLayout.hideMenu()
    }

    override fun onBackPressed() {
        EventBus.getDefault().post(OnBookSearchCallFinishEvent())
        super.onBackPressed()
    }

    override fun finish() {
        BookSearchActivity.clearSavedData()
        super.finish()
    }

    private fun handleSearchHighlight(noteEntity: NoteEntity, resourceId: String, filePath: String,
                                      fileId: String?, keywords: String = "") {
        // 导航成功，跳转到对应页面并显示高亮
        if (this.resourceId == resourceId) {
            pageAdapter.clearSearchHighlight()

            val noteEntityJson = noteEntity.toJsonStr()
            intent.putExtra(IntentConstants.EXTRA_NoteEntity, noteEntityJson)
            intent.putExtra(IntentConstants.EXTRA_FromSearch, true) // 标记为搜索跳转

            val pagePath = noteEntity.pagePath
            val pathIndex = EPubBook.getPathIndex(pagePath)

            if (pathIndex >= 0) {
                if (!pageAdapter.tryLocateHighlightContent(noteEntity, pathIndex)) {
                    val offset = noteEntity.wordStartOffset
                    pageAdapter.gotoPage(pathIndex, offset, true)
                }

                pageAdapter.reloadCurrentPage()
                searchResultLayout.showMenu()
                updateNavigationButtonsFromNoteEntity(noteEntity)

                // 设置章节名称
                if (noteEntity.fileName.isNotEmpty()) {
                    searchResultLayout.setChapterName(noteEntity.fileName)
                }

                // 获取搜索关键词并设置
                if (keywords.isNotEmpty()) {
                    searchResultLayout.setSearchKeywords(keywords)
                    intent.putExtra(IntentConstants.EXTRA_Keywords, keywords)
                }
            }
        } else {
            gotoBookReadActivity(
                this,
                filePath,
                fileId,
                resourceId,
                noteEntity.toJsonStr(),
                "",
                true,
                keywords
            )
        }
    }

    private var orientationListener: OrientationEventListener? = null
    private var isHandlingRotation = false
    private var anchorPathIndexBeforeRotate = -1
    private var anchorOffsetBeforeRotate = -1

    // 记录平板旋转会话状态
    private data class RotationSessionState(
        var active: Boolean = false,
        var startOrientation: Int = Configuration.ORIENTATION_UNDEFINED,
        var initialPathIndex: Int = -1,
        var initialOffset: Int = -1,
        var lastKnownOrientation: Int = Configuration.ORIENTATION_UNDEFINED
    ) {
        fun reset(currentOrientation: Int) {
            active = false
            startOrientation = Configuration.ORIENTATION_UNDEFINED
            initialPathIndex = -1
            initialOffset = -1
            lastKnownOrientation = currentOrientation
        }
    }
    private val rotation = RotationSessionState()

    private fun handleOrientationChange() {
        if (isHandlingRotation || !ScreenUtil.isTablet(this@BookReadActivity)) return
        isHandlingRotation = true
        Handler().postDelayed({
            try {
                val oldOrientation = rotation.lastKnownOrientation
                val currentOrientation = resources.configuration.orientation
                anchorPathIndexBeforeRotate = pageAdapter.getCurrentAnchorPathIndex()
                anchorOffsetBeforeRotate = pageAdapter.getCurrentPageFirstWordOffset()

                if (!rotation.active && oldOrientation != Configuration.ORIENTATION_UNDEFINED && currentOrientation != oldOrientation) {
                    rotation.active = true
                    rotation.startOrientation = oldOrientation
                    rotation.initialPathIndex = anchorPathIndexBeforeRotate
                    rotation.initialOffset = anchorOffsetBeforeRotate
                }

                viewPager.post {
                    pageAdapter.suppressNavigationCallback = true
                    if (pageAdapter.count > 0) {
                        pageAdapter.setTextFontType(EPubBook.textFont)
                    }

                    // 为了避免底部截断，按实际高度精细分页
                    viewPager.post {
                        val isLandscape = resources.configuration.orientation == Configuration.ORIENTATION_LANDSCAPE
                        val extraToolbar = dp2px(120)
                        val screenH = if (isLandscape) ScreenUtil.getScreenWidth() else ScreenUtil.getScreenHeight()
                        val statusHeight = APPUtil.getStatusBarHeight(this)
                        val newHeight = screenH - statusHeight - extraToolbar
                        val newOrientation = resources.configuration.orientation
                        val shouldRestoreToInitial = rotation.active && newOrientation == rotation.startOrientation

                        pageAdapter.suppressNavigationCallback = true
                        try {
                            pageAdapter.reflowForNewHeight(newHeight.toFloat())

                            if (pageAdapter.count > 0) {
                                if (shouldRestoreToInitial && rotation.initialPathIndex >= 0 && rotation.initialOffset >= 0) {
                                    pageAdapter.gotoPage(rotation.initialPathIndex, rotation.initialOffset, true, false, false)
                                    rotation.active = false
                                    rotation.initialPathIndex = -1
                                    rotation.initialOffset = -1
                                } else if (anchorPathIndexBeforeRotate >= 0 && anchorOffsetBeforeRotate >= 0) {
                                    pageAdapter.gotoPage(anchorPathIndexBeforeRotate, anchorOffsetBeforeRotate, true, false, false)
                                }
                            }
                        } finally {
                            pageAdapter.suppressNavigationCallback = false
                        }

                        rotation.lastKnownOrientation = newOrientation
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
            } finally {
                isHandlingRotation = false
            }
        }, 300)
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        handleOrientationChange()
    }

}
