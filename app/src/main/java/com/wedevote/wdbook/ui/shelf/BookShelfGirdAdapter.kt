package com.wedevote.wdbook.ui.shelf

import android.view.ViewGroup
import com.aquila.lib.base.BaseListAdapter
import com.aquila.lib.base.BaseViewHolder
import com.wedevote.wdbook.R

/***
 * @date 创建时间 2020/4/14 16:03
 * <AUTHOR> <PERSON><PERSON>
 * @description 书架的网格布局的adapter
 */
class BookShelfGirdAdapter : BaseListAdapter<Any, ShelfBookItemViewHolder>() {

    override fun onBindHolder(holder: ShelfBookItemViewHolder, position: Int) {
    }

    override fun onCreateViewHolder(parent: ViewGroup, type: Int): ShelfBookItemViewHolder {
        return ShelfBookItemViewHolder(parent)
    }

    override fun getCount(): Int {
        return 34
    }
}

class ShelfBookItemViewHolder(parent: ViewGroup) : BaseViewHolder(parent, R.layout.holder_book_shelf_item_layout)
