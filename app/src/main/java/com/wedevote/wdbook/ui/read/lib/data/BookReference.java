package com.wedevote.wdbook.ui.read.lib.data;

import java.util.ArrayList;

public class BookReference {
    public String bookId;
    public ArrayList<ChapterInfo> chapters = new ArrayList<>();
    private String lastPath = "";

    public class ChapterInfo{
        int chapterId;
        public ArrayList<String> verses = new ArrayList<>();
        public ArrayList<String> paths = new ArrayList<>();
        int getChapterId(){
            return chapterId;
        }

        @Override
        public String toString(){
            return String.valueOf(chapterId);
        }
    }

    private void addVerse(ChapterInfo info,int start,int end,String path){
        for(int i = info.verses.size()+1;i<start;i++){//前面没有关联的节需要关联到附近
            info.verses.add(String.valueOf(i));
            if(lastPath.isEmpty()){
                info.paths.add(path);
            }else{
                info.paths.add(lastPath);
            }
        }

        for(int i = start;i<=end;i++){
            info.verses.add(String.valueOf(i));
            info.paths.add(path);
        }
        lastPath = path;
    }

    public void addChapter(int chapter,int startVerse,int endVerse,String path){
        try{
            for(ChapterInfo info:chapters){
                if(chapter == info.getChapterId()){
                    addVerse(info,startVerse,endVerse,path);
                    return;
                }
            }

            ChapterInfo newChapterInfo = new ChapterInfo();
            newChapterInfo.chapterId = chapter;
            addVerse(newChapterInfo,startVerse,endVerse,path);
            chapters.add(newChapterInfo);
        }catch (Exception e){
            e.printStackTrace();
        }
    }
}
