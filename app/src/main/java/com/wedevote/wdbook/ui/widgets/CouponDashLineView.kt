package com.wedevote.wdbook.ui.widgets

import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.DashPathEffect
import android.graphics.Paint
import android.graphics.Paint.Style.FILL
import android.graphics.Paint.Style.STROKE
import android.util.AttributeSet
import android.widget.FrameLayout
import com.wedevote.wdbook.R

/***
 * @date 创建时间 2022/5/30 15:22
 * <AUTHOR> <PERSON><PERSON>
 * @description
 */
class CouponDashLineView(context: Context, attr: AttributeSet? = null) :
    FrameLayout(context, attr) {
    private var dashColor: Int = Color.TRANSPARENT
    private var dashWidth = 0f
    private var dashSize = 0f
    private var dashSpace = 0f
    private var connerRadius = 0f
    private var connerColor = Color.TRANSPARENT

    private val dashPaint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val connerPaint = Paint(Paint.ANTI_ALIAS_FLAG)

    private var viewWidth: Int = 0
    private var viewHeight: Int = 0

    init {
        attr?.let {
            val a = context.obtainStyledAttributes(it, R.styleable.CouponDashLineView)
            dashColor =
                a.getColor(R.styleable.CouponDashLineView_attr_dash_color, Color.TRANSPARENT)
            connerColor =
                a.getColor(R.styleable.CouponDashLineView_attr_conner_color, Color.TRANSPARENT)
            dashWidth = a.getDimension(R.styleable.CouponDashLineView_attr_dash_width, 0f)
            dashSize = a.getDimension(R.styleable.CouponDashLineView_attr_dash_size, 0f)
            dashSpace = a.getDimension(R.styleable.CouponDashLineView_attr_dash_space, 0f)
            connerRadius = a.getDimension(R.styleable.CouponDashLineView_attr_conner_radius, 0f)
            a.recycle()
        }

        dashPaint.color = dashColor
        dashPaint.style = STROKE
        dashPaint.strokeWidth = dashWidth
        dashPaint.pathEffect = DashPathEffect(floatArrayOf(dashSize, dashSpace), 0f)

        connerPaint.color = connerColor
        connerPaint.style = FILL

        setWillNotDraw(false)
    }


    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        viewWidth = w
        viewHeight = h
    }


    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        viewWidth = width
        viewHeight = height
        canvas.drawArc(
            viewWidth / 2 - connerRadius, -connerRadius, viewWidth / 2 + connerRadius, connerRadius,
            0f, 180f, true, connerPaint
        )

        canvas.drawArc(
            viewWidth / 2 - connerRadius,
            viewHeight.toFloat() - connerRadius,
            viewWidth / 2 + connerRadius,
            viewHeight.toFloat() + connerRadius,
            180f,
            360f,
            true,
            connerPaint
        )

        val dashCellHeight = dashSize + dashSpace
        val marginSize = ((viewHeight - connerRadius * 2 + dashSpace) % dashCellHeight)
        canvas.drawLine(
            viewWidth / 2f,
            connerRadius + marginSize,
            viewWidth / 2f,
            viewHeight - connerRadius - marginSize,
            dashPaint
        )
    }

}