package com.wedevote.wdbook.ui.user.notification

import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import com.aquila.lib.base.BaseRecycleAdapter
import com.aquila.lib.base.BaseViewHolder
import com.wedevote.wdbook.tools.util.ImageLoadUtil
import com.aquila.lib.widget.group.GroupImageTextLayout
import com.aquila.lib.widget.view.DotView
import com.wedevote.wdbook.R
import com.wedevote.wdbook.constants.NotificationReadStatus
import com.wedevote.wdbook.entity.notification.NotificationEntity
import com.wedevote.wdbook.tools.util.PictureUtil
import com.wedevote.wdbook.tools.util.UnitFormatUtil
import com.wedevote.wdbook.tools.util.dp2px
import com.wedevote.wdbook.tools.util.getPictureRemotePath
import java.util.*

/***
 * @date 创建时间 2022/5/17 11:24
 * <AUTHOR> <PERSON><PERSON>
 * @description
 */
class NotificationMessageAdapter : BaseRecycleAdapter<NotificationEntity, NotificationMessageViewHolder>() {
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): NotificationMessageViewHolder {
        return NotificationMessageViewHolder(parent)
    }

    override fun onBindViewHolder(holder: NotificationMessageViewHolder, position: Int) {
        super.onBindViewHolder(holder, position)
    }
}

/***
 *@date 创建时间 2022/5/17 11:25
 *<AUTHOR> W.YuLong
 *@description
 */
class NotificationMessageViewHolder(parent: ViewGroup) : BaseViewHolder(parent, R.layout.holder_notification_center_message_layout) {
    val timeTextView: TextView = itemView.findViewById(R.id.notification_message_time_TextView)
    val titleTextView: TextView = itemView.findViewById(R.id.notification_message_title_TextView)
    val contentTextView: TextView = itemView.findViewById(R.id.notification_message_content_TextView)
    val pictureImageView: ImageView = itemView.findViewById(R.id.notification_message_picture_ImageView)
    val viewDetailLayout: GroupImageTextLayout = itemView.findViewById(R.id.notification_message_view_detail_Layout)
    val flagDotView: DotView = itemView.findViewById(R.id.notification_message_dot_View)

    fun formatNotificationTime(time: Long): String {
        return UnitFormatUtil.sdfNotificationTime.format(Date(time))
    }

    override fun <T> initUIData(t: T) {
        t as NotificationEntity
        timeTextView.text = formatNotificationTime(t.lastUpdateTime)
        titleTextView.setText(t.title)
        if (t.cover.isNullOrEmpty()) {
            contentTextView.setText(t.subTitle)
            pictureImageView.visibility = View.GONE
            contentTextView.visibility = View.VISIBLE
            viewDetailLayout.setText(R.string.look_detail)
        } else {
            pictureImageView.visibility = View.VISIBLE
            PictureUtil.loadRoundImage(pictureImageView, getPictureRemotePath(t.cover), dp2px(5))
            contentTextView.visibility = View.GONE
            viewDetailLayout.setText(t.subTitle)
        }
        flagDotView.visibility = if (t.read == NotificationReadStatus.UNREAD.status) View.VISIBLE else View.GONE

        itemView.setOnClickListener {
            NotificationDetailActivity.gotoNotificationDetail(
                itemView.context,
                t.id,
                t.type,
                object : OnNotificationReadCallback {
                    override fun onReadCallback(notificationId: Long) {
                        t.read = NotificationReadStatus.READ.status
                        flagDotView.visibility = View.GONE
                    }
                },
            )
        }
    }
}
