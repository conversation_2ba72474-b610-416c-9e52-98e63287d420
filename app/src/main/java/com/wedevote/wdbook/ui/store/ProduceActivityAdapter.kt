package com.wedevote.wdbook.ui.store

import android.annotation.SuppressLint
import android.text.Html
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import com.aquila.lib.base.BaseRecycleAdapter
import com.aquila.lib.base.BaseViewHolder
import com.wedevote.wdbook.tools.util.ImageLoadUtil
import com.aquila.lib.tools.util.ToastUtil
import com.aquila.lib.widget.view.AdaptiveImageView
import com.wedevote.wdbook.R
import com.wedevote.wdbook.base.SDKSingleton
import com.wedevote.wdbook.entity.store.ProductDetailEntity
import com.wedevote.wdbook.tools.util.APPUtil
import com.wedevote.wdbook.tools.util.PictureUtil
import com.wedevote.wdbook.tools.util.UnitFormatUtil
import com.wedevote.wdbook.ui.user.OnDataChangeSelectListener

/***
 * @date 创建时间 2022/6/20 17:34
 * <AUTHOR> <PERSON><PERSON>
 * @description
 */
class ProductActivityAdapter(var activityId: Long) : BaseRecycleAdapter<ProductDetailEntity, ActivityViewHolder>() {
    var selectList = ArrayList<ProductDetailEntity>()
    var onItemSelectListener: OnItemSelectListener? = null
    var onAllDataSelectListener: OnDataChangeSelectListener? = null
    var unPurchaseCount = 0
    var mIsExpired: Boolean = false
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ActivityViewHolder {
        return ActivityViewHolder(parent, activityId, mIsExpired)
    }

    fun isAllSelected(): Boolean {
        unPurchaseCount = 0
        if (!dataList.isNullOrEmpty()) {
            for (item in dataList!!) {
                if (item.purchased != 1) {
                    unPurchaseCount++
                }
            }
        }
        return selectList.size == unPurchaseCount && unPurchaseCount > 0
    }

    fun removeSelectItem(entity: ProductDetailEntity) {
        selectList.forEach {
            if (it.productId == entity.productId) {
                onItemSelectListener?.onItemSelect(it)
                onAllDataSelectListener?.isAllSelected(isAllSelected())
                notifyDataSetChanged()
                return
            }
        }
    }

    fun doUnSelectAll() {
        selectList.clear()
        onAllDataSelectListener?.isAllSelected(isAllSelected())
        notifyDataSetChanged()
    }

    fun doSelectAll() {
        try {
            selectList.clear()
            for (item in dataList!!) {
                if (item.purchased != 1) {
                    selectList.add(item)
                }
            }
            onAllDataSelectListener?.isAllSelected(isAllSelected())
            notifyDataSetChanged()
        } catch (e: Exception) {
        }
    }

    fun doSelectAction(entity: ProductDetailEntity) {
        if (selectList.contains(entity)) {
            selectList.remove(entity)
        } else {
            selectList.add(entity)
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun onBindViewHolder(holder: ActivityViewHolder, position: Int) {
//        super.onBindViewHolder(holder, position)
        val entity = getDataFromPosition(position) as ProductDetailEntity
        holder.initUIData(entity)
        holder.onItemSelected(selectList.contains(entity))
        // 未购买的状态可以点击
        holder.setItemPurchaseStatus(entity.purchased != 1)
        holder.selectImageView.setOnClickListener {
            if (!holder.selectImageView.isEnabled) {
                ToastUtil.showToastShort(R.string.the_book_you_have_brought)
                return@setOnClickListener
            }

            if (!ProductActiveActivity.isClickable) {
                return@setOnClickListener
            }

            onItemSelectListener?.onItemSelect(entity)
            onAllDataSelectListener?.isAllSelected(isAllSelected())
            notifyDataSetChanged()
        }
        holder.itemView.setOnClickListener {
            BookDetailActivity.gotoBookDetail(it.context, entity.productId)
        }
    }

    fun setExpired(isExpired: Boolean) {
        mIsExpired = isExpired
        notifyDataSetChanged()
    }
}

/***
 *@date 创建时间 2022/6/21 13:21
 *<AUTHOR> W.YuLong
 *@description
 */
interface OnItemSelectListener {
    fun onItemSelect(entity: ProductDetailEntity)
}

/***
 *@date 创建时间 2022/6/20 17:53
 *<AUTHOR> W.YuLong
 *@description
 */
class ActivityViewHolder(parent: ViewGroup, var activityId: Long, isExpired: Boolean) : BaseViewHolder(parent, R.layout.item_activity_book_layout) {
    val selectImageView: ImageView = itemView.findViewById(R.id.item_activity_check_ImageView)
    val coverImageView: AdaptiveImageView = itemView.findViewById(R.id.item_activity_cover_ImageView)
    val discountTextView: TextView = itemView.findViewById(R.id.item_activity_discount_TextView)
    val bookNameTextView: TextView = itemView.findViewById(R.id.item_activity_book_name_TextView)
    val authorTextView: TextView = itemView.findViewById(R.id.item_activity_author_TextView)
    val activePriceTextView: TextView = itemView.findViewById(R.id.item_activity_price_value_TextView)
    val activePriceLabelTextView: TextView = itemView.findViewById(R.id.item_activity_price_label_TextView)
    val originalPriceTextView: TextView = itemView.findViewById(R.id.item_activity_original_price_TextView)
    var mIsExpired: Boolean = isExpired
    override fun <T> initUIData(t: T) {
        t as ProductDetailEntity
        bookNameTextView.setText(t.title)
        PictureUtil.loadImage(coverImageView, t.cover)
        originalPriceTextView.setText(APPUtil.setOriginalPrice(t.currency, t.originalPrice))
        activePriceTextView.setText(Html.fromHtml(getActivityPriceStr(t)))
        authorTextView.setText(SDKSingleton.userBl.formatAuthorName(t.authorList))

        if (mIsExpired) {
            activePriceLabelTextView.setText(R.string.current_price)
        } else {
            activePriceLabelTextView.setText(R.string.title_activity_price)
        }
        var discountText = getDiscountText(t)
        if (discountText.isNullOrEmpty()) {
            discountTextView.visibility = View.GONE
        } else {
            discountTextView.setText(discountText)
            discountTextView.visibility = View.VISIBLE
        }
    }

    private fun getDiscountText(entity: ProductDetailEntity): String {
        var resultText: String = ""
        if (!entity.activitiesList.isNullOrEmpty()) {
            for (item in entity.activitiesList!!) {
                if (item.activityId == activityId) {
                    resultText = UnitFormatUtil.formatDiscountText(item.discount, "%.1f")
                    break
                }
            }
        } else {
            resultText = UnitFormatUtil.formatDiscountText(entity.discount, "%.1f")
        }
        return resultText
    }

    /*格式化获取活动的价格信息*/
    fun getActivityPriceStr(entity: ProductDetailEntity): String {
        if (!entity.activitiesList.isNullOrEmpty()) {
            for (item in entity.activitiesList!!) {
                if (item.productId == entity.productId) {
                    return APPUtil.formatPrice(entity.currency, item.amount, item.amountCNY)
                }
            }
        }
        return APPUtil.formatPrice(entity.currency, entity.price, entity.priceCNY)
    }

    fun setItemPurchaseStatus(isPurchase: Boolean) {
        selectImageView.isEnabled = isPurchase
    }

    override fun onItemSelected(isSelected: Boolean) {
        selectImageView.isSelected = isSelected
    }
}
