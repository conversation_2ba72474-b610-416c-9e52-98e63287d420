package com.wedevote.wdbook.ui.user.coupon

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import com.aquila.lib.base.BaseFragment
import com.aquila.lib.layout.SmartRefreshLayout
import com.aquila.lib.tools.util.ToastUtil
import com.aquila.lib.widget.view.CustomRecyclerView
import com.wedevote.wdbook.R
import com.wedevote.wdbook.base.ExceptionHandler
import com.wedevote.wdbook.base.SDKSingleton
import com.wedevote.wdbook.entity.coupon.CouponEntity
import com.wedevote.wdbook.tools.util.FilePathUtil
import com.wedevote.wdbook.tools.util.findString
import com.wedevote.wdbook.utils.JsonUtility
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch

/***
 *@date 创建时间 2022/5/31 11:18
 *<AUTHOR> <PERSON><PERSON>
 *@description
 */
class CouponPageFragment(var tabType: Int) : BaseFragment() {
    
    lateinit var refreshLayout: SmartRefreshLayout
    lateinit var dataRecyclerView: CustomRecyclerView
    lateinit var couponItemAdapter: CouponItemAdapter
    lateinit var noDataTextView: TextView
    
    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        var v = inflater.inflate(R.layout.fragment_coupon_data_layout, container, false)
        refreshLayout = v.findViewById(R.id.fragment_data_refreshLayout)
        dataRecyclerView = v.findViewById(R.id.fragment_data_RecyclerView)
        noDataTextView = v.findViewById(R.id.fragment_no_data_TextView)
        
        refreshLayout.isEnableLoadMore = false
        
        couponItemAdapter = CouponItemAdapter(CouponItemType.COUPON_LIST)
        dataRecyclerView.adapter = couponItemAdapter
        
        getDataFromServer()
        return v
    }
    
    
    fun getDataFromServer() {
        MainScope().launch(ExceptionHandler.getCoroutineExceptionHandler {
            setEmptyView()
        }) {
            // status 0:全部 1:未使用 2:已使用 3:已过期
            val couponList = SDKSingleton.userBl.getCouponList(tabType + 1, couponItemAdapter.getMinCouponId())
            couponItemAdapter.dataList = couponList?.toMutableList()
            if (couponItemAdapter.itemCount <= 0) {
                noDataTextView.visibility = View.VISIBLE
                dataRecyclerView.visibility = View.GONE

                setEmptyView()
            } else {
                noDataTextView.visibility = View.GONE
                dataRecyclerView.visibility = View.VISIBLE
            }
        }
    }

    private fun setEmptyView() {
        var typeName = when (tabType) {
            0 -> {
                findString(R.string.coupon_usable)
            }
            1 -> {
                findString(R.string.coupon_has_used)
            }
            else -> {
                findString(R.string.coupon_expired)
            }
        }
        noDataTextView.setText(getString(R.string.no_coupon_prompt_with_params).format(typeName))
    }

    private fun initTestData() {
        val json = FilePathUtil.getTextFromAssets("coupon_active.json")
        var list: ArrayList<CouponEntity> = JsonUtility.decodeFromString(json)
        var dataList = ArrayList<CouponEntity>()
        for (item in list) {
            if (item.status == tabType + 1) {
                dataList.add(item)
            }
        }
        couponItemAdapter.dataList = dataList?.toMutableList()
    }
}


