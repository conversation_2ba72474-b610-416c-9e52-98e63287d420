package com.wedevote.wdbook.ui.store

import android.content.Context
import android.content.Intent
import android.graphics.Paint
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.text.Html
import android.text.TextUtils
import android.text.method.LinkMovementMethod
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.widget.Button
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import com.aquila.lib.base.OnViewClickListener
import com.aquila.lib.dialog.CommAlertDialog
import com.aquila.lib.dialog.CommProgressDialog
import com.aquila.lib.layout.SmartRefreshLayout
import com.aquila.lib.layout.api.RefreshLayout
import com.aquila.lib.layout.listener.OnRefreshListener
import com.aquila.lib.layout.util.DensityUtil
import com.aquila.lib.log.KLog
import com.aquila.lib.tools.util.ToastUtil
import com.aquila.lib.widget.view.CustomRecyclerView
import com.stripe.android.CustomerSession
import com.wedevote.wdbook.R
import com.wedevote.wdbook.base.APPConfig
import com.wedevote.wdbook.base.ExceptionHandler
import com.wedevote.wdbook.base.RootActivity
import com.wedevote.wdbook.base.SDKSingleton
import com.wedevote.wdbook.constants.DownloadStatus
import com.wedevote.wdbook.constants.AnalyticsConstants
import com.wedevote.wdbook.constants.AnalyticsConstants.LOG_V1_PARAM_ORDER_ID
import com.wedevote.wdbook.constants.AnalyticsConstants.LOG_V1_PARAM_PRODUCT_ID
import com.wedevote.wdbook.entity.DownloadDataEntity
import com.wedevote.wdbook.entity.shelf.ShelfBookEntity
import com.wedevote.wdbook.entity.store.AttributeValueEntity
import com.wedevote.wdbook.entity.store.BookFileDownloadEntity
import com.wedevote.wdbook.entity.store.ProductDetailEntity
import com.wedevote.wdbook.tools.define.PurchaseStatus
import com.wedevote.wdbook.tools.define.PutawayStatus
import com.wedevote.wdbook.tools.download.DownloaderEngine
import com.wedevote.wdbook.tools.download.OnDownloadingListener
import com.wedevote.wdbook.tools.event.OnBookItemDownloadFinish
import com.wedevote.wdbook.tools.event.OnFavoriteEvent
import com.wedevote.wdbook.tools.event.OnLoginEvent
import com.wedevote.wdbook.tools.event.OnRefreshAfterPay
import com.wedevote.wdbook.tools.event.OnSuccessBuyEvent
import com.wedevote.wdbook.tools.interfaces.OnItemClickListener
import com.wedevote.wdbook.tools.payment.OnOrderPayCallback
import com.wedevote.wdbook.tools.payment.OrderPaymentHelper
import com.wedevote.wdbook.tools.util.APPUtil
import com.wedevote.wdbook.tools.util.DataPathUtil
import com.wedevote.wdbook.tools.util.AnalyticsUtils.logEvent
import com.wedevote.wdbook.tools.util.GsonUtil
import com.wedevote.wdbook.tools.util.IntentConstants
import com.wedevote.wdbook.tools.util.NetWorkUtils
import com.wedevote.wdbook.tools.util.PictureUtil
import com.wedevote.wdbook.tools.util.UnitFormatUtil
import com.wedevote.wdbook.tools.util.dp2px
import com.wedevote.wdbook.tools.util.findString
import com.wedevote.wdbook.tools.util.getFunctionInfo
import com.wedevote.wdbook.ui.account.SSOLoginActivity
import com.wedevote.wdbook.ui.dialogs.AuthorChoiceDialog
import com.wedevote.wdbook.ui.dialogs.GetCouponDialog
import com.wedevote.wdbook.ui.dialogs.PayResultDialog
import com.wedevote.wdbook.ui.read.BookReadActivity
import com.wedevote.wdbook.ui.service.SyncDataService
import com.wedevote.wdbook.ui.widgets.CommTopTitleLayout
import com.wedevote.wdbook.ui.widgets.ExpandTextView
import com.wedevote.wdbook.ui.widgets.ProgressButton
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import java.io.File


/***
 * @date 创建时间 2020/5/13 10:49
 * <AUTHOR> W.YuLong
 * @description
 */
class BookDetailActivity : RootActivity(), View.OnClickListener, OnRefreshListener {
    private lateinit var topTitleLayout: CommTopTitleLayout
    private lateinit var refreshLayout: SmartRefreshLayout
    private lateinit var bookCoverImageView: ImageView
    private lateinit var purchaseFlagImageView: ImageView
    private lateinit var bookNameTextView: TextView

    private lateinit var authorTextView: TextView
    private lateinit var ectTextView: TextView
    private lateinit var priceTextView: TextView
    private lateinit var priceBeforeTextView: TextView
    private lateinit var statusTextView: TextView
    private lateinit var catalogTextView: TextView
    private lateinit var tipTextView: TextView
    private lateinit var summaryLinearLayout: LinearLayout
    private lateinit var descTextView: ExpandTextView
    private lateinit var buyButton: ProgressButton
    private lateinit var buyContainerLayout: LinearLayout
    private lateinit var propertyRecyclerView: CustomRecyclerView
    private lateinit var referenceRecyclerView: CustomRecyclerView
    private lateinit var notExistContainerLayout: LinearLayout
    private lateinit var errorInfoTextView: TextView
    private lateinit var disablePayTextView: TextView
    private lateinit var goBackButton: Button
    private lateinit var favoriteImageView: ImageView

    private lateinit var activityContainerLayout: LinearLayout
    private lateinit var activityNameTextView: TextView
    private lateinit var viewActivityLayout: ViewGroup

    private lateinit var couponContainerLayout: LinearLayout
    private lateinit var couponRecyclerView: CustomRecyclerView
    private lateinit var bookDetailCouponItemAdapter: BookDetailCouponItemAdapter

    private lateinit var detailEntity: ProductDetailEntity

    /*是否是套装的商品详情*/
    private var isPackageProduct = false

    private var productId: Long = 0

    private lateinit var downloadEngine: DownloaderEngine

    private var productItemEntity: ProductDetailEntity? = null
    private lateinit var orderPaymentHelper: OrderPaymentHelper
    private var downloadDataEntity: DownloadDataEntity? = null
    var isRefresh: Boolean = true
    var tryDownloadTimes: Int = 3


    companion object {
        fun gotoBookDetail(context: Context, productId: Long) {
            val intent = Intent(context, BookDetailActivity::class.java)
            intent.putExtra(IntentConstants.EXTRA_ProductId, productId)
            context.startActivity(intent)
        }
    }

    lateinit var loadingDialog: CommProgressDialog

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_store_book_detail_layout)
        initViewFromXML()
        setLayoutByTheme()
        bookDetailCouponItemAdapter = BookDetailCouponItemAdapter()
        couponRecyclerView.adapter = bookDetailCouponItemAdapter
        orderPaymentHelper = OrderPaymentHelper(this, object : OnOrderPayCallback {
            override fun onOrderPayResult(orderStatus: Int) {
            }
        })

        loadingDialog = CommProgressDialog.with(this).apply {
            setTouchOutsideCancel(false)
        }.create()

        downloadEngine = DownloaderEngine(this)

        productId = intent.getLongExtra(IntentConstants.EXTRA_ProductId, 0L)

        setViewListener()
        isRefresh = false
        logEvent(
            AnalyticsConstants.LOG_V1_PRODUCT_DETAIL_OPEN,
            LOG_V1_PARAM_PRODUCT_ID,
            productId.toString()
        )
    }

    override fun onResume() {
        super.onResume()
        if (NetWorkUtils.isNetworkAvailable()) {
            onRefresh(refreshLayout)
        }
    }

    private fun setViewListener() {
        catalogTextView.setOnClickListener(this)
        goBackButton.setOnClickListener(this)
        activityContainerLayout.setOnClickListener(this)
        favoriteImageView.setOnClickListener(this)
        refreshLayout.setOnRefreshListener(this)
        couponContainerLayout.setOnClickListener(this)
    }

    private fun initViewFromXML() {
        topTitleLayout = findViewById(R.id.book_detail_top_TitleLayout)
        bookCoverImageView = findViewById(R.id.book_detail_cover_ImageView)
        refreshLayout = findViewById(R.id.book_detail_RefreshLayout)
        purchaseFlagImageView = findViewById(R.id.book_detail_purchase_flag_ImageView)
        bookNameTextView = findViewById(R.id.book_detail_name_TextView)
        statusTextView = findViewById(R.id.book_detail_status_TextView)
        buyButton = findViewById(R.id.book_detail_buy_Button)
        buyContainerLayout = findViewById(R.id.book_detail_bottom_buy_container_Layout)
        authorTextView = findViewById(R.id.book_detail_author_TextView)
        ectTextView = findViewById(R.id.book_detail_author_ect_TextView)
        priceTextView = findViewById(R.id.book_detail_price_TextView)
        priceBeforeTextView = findViewById(R.id.book_detail_price_before_TextView)
        catalogTextView = findViewById(R.id.book_detail_catalog_TextView)
        summaryLinearLayout = findViewById(R.id.book_detail_summary_LinearLayout)
        descTextView = findViewById(R.id.book_detail_summary_TextView)
        propertyRecyclerView = findViewById(R.id.book_detail_property_RecyclerView)
        referenceRecyclerView = findViewById(R.id.book_detail_reference_RecyclerView)
        notExistContainerLayout = findViewById(R.id.book_detail_not_exist_container_layout)
        errorInfoTextView = findViewById(R.id.book_detail_service_error)
        disablePayTextView = findViewById(R.id.book_detail_disable_pay_TextView)
        goBackButton = findViewById(R.id.book_detail_back_to_home_Button)
        tipTextView = findViewById(R.id.book_detail_tip_TextView)
        favoriteImageView = findViewById(R.id.book_detail_favorite_ImageView)
        couponContainerLayout = findViewById(R.id.book_detail_coupon_container_layout)
        couponRecyclerView = findViewById(R.id.book_detail_coupon_RecyclerView)
        activityContainerLayout = findViewById(R.id.book_detail_activity_container_layout)
        activityNameTextView = findViewById(R.id.book_detail_activity_title_TextView)
        viewActivityLayout = findViewById(R.id.book_detail_view_Layout)
    }

    private fun setLayoutByTheme() {
        if (APPConfig.isCurrentThemeLight()) {
            val param = summaryLinearLayout.layoutParams as ViewGroup.MarginLayoutParams
            param.setMargins(0, DensityUtil.dp2px(0f), 0, DensityUtil.dp2px(0f))
            summaryLinearLayout.layoutParams = param
            val param2 = catalogTextView.layoutParams as ViewGroup.MarginLayoutParams
            param2.setMargins(0, 0, 0, DensityUtil.dp2px(0f))
            catalogTextView.layoutParams = param2
        } else {
            val param = summaryLinearLayout.layoutParams as ViewGroup.MarginLayoutParams
            param.setMargins(0, DensityUtil.dp2px(8f), 0, DensityUtil.dp2px(8f))
            summaryLinearLayout.layoutParams = param
            val param2 = catalogTextView.layoutParams as ViewGroup.MarginLayoutParams
            param2.setMargins(0, 0, 0, DensityUtil.dp2px(8f))
            catalogTextView.layoutParams = param2
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun doOnLoginEvent(event: OnLoginEvent) {
        doOnRefresh()
    }

    private var mainScope = MainScope()
    override fun onRefresh(layout: RefreshLayout) {
        doOnRefresh()
        if (buyButton.currentType == ProgressButton.BTN_STATUS_NEED_DOWNLOAD) {
            executeSingleBookDownloadFile()
        }
    }

    private fun doOnRefresh() {
        mainScope.launch() {
            try {
                SDKSingleton.storeBl.getProductDetailEntity(productId).also { data ->
                    if (data != null) {
                        if (!data.activitiesList.isNullOrEmpty()) {
                            data.price = data.activitiesList!![0]!!.amount
                            data.priceCNY = data.activitiesList!![0]!!.amountCNY
                            data.discount = data.activitiesList!![0]!!.discount
                            initDetailUI(data)
                        } else {
                            initDetailUI(data)
                        }
                        if (isPackageProduct) {
                            logEvent(
                                AnalyticsConstants.LOG_V1_PRODUCT_PACKAGE_OPEN,
                                LOG_V1_PARAM_PRODUCT_ID,
                                productId.toString()
                            )
                        }
                    } else {
                        refreshLayout.visibility = View.GONE
                        showErrorLayout()
                    }
                    refreshLayout.finishLoadMoreAndRefresh()
                }
            } catch (e: Exception) {
                refreshLayout.visibility = View.GONE
                showErrorLayout()
                refreshLayout.finishLoadMoreAndRefresh()
                ExceptionHandler.handleException(e)
            }
        }
    }

    fun getPaymentStatus(orderId: String) {
        launch(ExceptionHandler.coroutineExceptionHandler) {
            SDKSingleton.paymentBl.getPaymentStatusEntity(orderId).also { entity ->
                if (entity != null) {
                    KLog.d(GsonUtil.objectToJson(entity))
                    when (entity.status) {
                        OrderStatusDefine.ORDER_STATUS_PENDING, OrderStatusDefine.ORDER_STATUS_SUCCEED -> { // 待付款,继续调用查询支付结果的API
                            // 连续请求10秒的时间，如果一直是0 待支付状态就循环刷新10秒
                            if (System.currentTimeMillis() - firstRequestTime < 10_000L) {
                                if (entity.status == OrderStatusDefine.ORDER_STATUS_SUCCEED) { // 如果在10秒内出现支付成功，状态标记为支付成功
                                    isPaySucceeded = true
                                }
                                Handler().postDelayed({ getPaymentStatus(entity.orderId) }, 500)
                            } else {
                                // 10秒之后还没有出现订单完成的状态，如果之前有支付成功就提示成功
                                if (isPaySucceeded) {
                                    var bookShelfBean = ShelfBookEntity().apply {
                                        resourceId = detailEntity.resourceId
                                        cover = detailEntity.cover
                                        resourceName = detailEntity.title
                                    }
                                    SDKSingleton.dbWrapBl.saveShelfItemData(bookShelfBean)
                                    showPayResultDialog(entity.status)
                                    EventBus.getDefault()
                                        .post(OnSuccessBuyEvent(detailEntity.productId))
                                    logEvent(
                                        AnalyticsConstants.LOG_V1_PRODUCT_PAY_SUCCESS,
                                        LOG_V1_PARAM_ORDER_ID,
                                        entity.orderId
                                    )
                                    if (isPackageProduct) {
                                        logEvent(
                                            AnalyticsConstants.LOG_V1_PRODUCT_PACKAGE_PAY_SUCCESS,
                                            LOG_V1_PARAM_ORDER_ID,
                                            entity.orderId,
                                        )
                                    }
                                } else {
                                    CommAlertDialog.with(this@BookDetailActivity)
                                        .setMessage(R.string.order_info_processed)
                                        .setStartText(R.string.got_it).create()
                                        .show()
                                }
                                loadingDialog.dismiss()
                            }
                        }
                        OrderStatusDefine.ORDER_STATUS_FINISH -> { // 订单完成，最后所有流程走通显示成功
                            loadingDialog.dismiss()
                            EventBus.getDefault().post(OnSuccessBuyEvent(detailEntity.productId))

                            showPayResultDialog(entity.status)
                            logEvent(
                                AnalyticsConstants.LOG_V1_PRODUCT_PAY_SUCCESS,
                                LOG_V1_PARAM_ORDER_ID,
                                entity.orderId
                            )
                            if (isPackageProduct) {
                                logEvent(
                                    AnalyticsConstants.LOG_V1_PRODUCT_PACKAGE_PAY_SUCCESS,
                                    LOG_V1_PARAM_ORDER_ID,
                                    entity.orderId
                                )
                            }
                        }
                        else -> { // 相当支付失败
                            loadingDialog.dismiss()
                            showPayResultDialog(entity.status)
                        }
                    }
                }
            }
        }
    }

    var firstRequestTime: Long = 0
    var isPaySucceeded = false

    private fun showPayResultDialog(status: Int) {
        var payResultDialog = PayResultDialog(this)
        payResultDialog.show()
        payResultDialog.initResultUI(status)
        payResultDialog.onViewClickListener = object : OnViewClickListener {
            override fun <T> onClickAction(v: View, str: String, t: T?) {
                t as Int
                when (t) {
                    OrderStatusDefine.ORDER_STATUS_SUCCEED, OrderStatusDefine.ORDER_STATUS_FINISH -> { // 成功
                        val intent = Intent(this@BookDetailActivity, SyncDataService::class.java)
                        startService(intent)
                    }
                }
                doOnRefresh()
            }
        }
    }

    private fun isOverFlowed(textView: TextView): Boolean {
        val paint: Paint = textView.paint
        val width: Float = paint.measureText(textView.text.toString())
        return width > getAvailableWidth(textView)
    }

    private fun getAvailableWidth(textView: TextView): Int {
        return textView.width - textView.paddingLeft - textView.paddingRight
    }

    fun initDetailUI(entity: ProductDetailEntity) {
        if (isFinishing) {
            KLog.d("返回")
            return
        }
        refreshLayout.visibility = View.VISIBLE
        notExistContainerLayout.visibility = View.GONE
        detailEntity = entity
        try {
            PictureUtil.loadImage(bookCoverImageView, entity.cover)
        } catch (e: Exception) {
            e.printStackTrace()
            SDKSingleton.loggerBl.handleThrowable(e, getFunctionInfo())
        }
        bookNameTextView.text = entity.title

        authorTextView.text = SDKSingleton.userBl.formatAuthorName(entity.authorList)

        authorTextView.post(Runnable {
            val l = authorTextView.getLayout()
            if (l != null) {
                if (l.getEllipsisCount(l.getLineCount() - 1) > 0) {
                    ectTextView.visibility = View.VISIBLE
                } else {
                    ectTextView.visibility = View.GONE
                }
            }
        })
        authorTextView.setOnClickListener {
            val list = ArrayList<String>()
            entity.authorList.forEach {
                list.add(it.name)
            }
            if (list.size == 1) {
                val intent = Intent(this@BookDetailActivity, AuthorDetailActivity::class.java)
                intent.putExtra(IntentConstants.EXTRA_AuthorId, entity.authorList[0].authorId)
                intent.putExtra(IntentConstants.EXTRA_AuthorName, entity.authorList[0].name)
                startActivity(intent)
            } else {
                var authorChoiceDialog: AuthorChoiceDialog? = null
                val builder = AuthorChoiceDialog.with(this@BookDetailActivity)
                builder.setTitle(getString(R.string.author))
                    .setItemStringList(list)
                    .setonItemClickListener(object : OnItemClickListener {
                        override fun <T> onItemClick(obj: Any, tag: String, t: T) {
                            val intent =
                                Intent(this@BookDetailActivity, AuthorDetailActivity::class.java)
                            intent.putExtra(
                                IntentConstants.EXTRA_AuthorId,
                                entity.authorList[tag.toInt()].authorId
                            )
                            intent.putExtra(
                                IntentConstants.EXTRA_AuthorName,
                                entity.authorList[tag.toInt()].name
                            )
                            startActivity(intent)
                            authorChoiceDialog?.dismiss()
                        }
                    })
                authorChoiceDialog = builder.create()
                authorChoiceDialog.show()
                var lp = authorChoiceDialog.window?.attributes;
                lp!!.dimAmount = 0.25f;
                authorChoiceDialog.window?.attributes = lp
                authorChoiceDialog.window?.clearFlags(WindowManager.LayoutParams.FLAG_BLUR_BEHIND)
                authorChoiceDialog.window?.addFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND)
            }
        }


        favoriteImageView.isSelected = (entity.inFavorite == 1)
        purchaseFlagImageView.visibility =
            if (entity.purchased == PurchaseStatus.PURCHASED.value) View.VISIBLE else View.GONE

        // 这个是要过滤掉已领取完的优惠券
        var filterList = bookDetailCouponItemAdapter.filterDataList(entity.couponsList)
        if (!filterList.isNullOrEmpty()) {
            couponContainerLayout.visibility = View.VISIBLE
            bookDetailCouponItemAdapter.dataList = filterList
        } else {
            couponContainerLayout.visibility = View.GONE
        }

        if (!entity.activitiesList.isNullOrEmpty()) {
            var activityEntity = entity.activitiesList!![0]
            if (activityEntity.hidden == 0) {
                activityContainerLayout.visibility = View.VISIBLE
                activityNameTextView.text =
                    findString(R.string.format_activity_label).format(activityEntity.activityTitle)
            }
        } else {
            activityContainerLayout.visibility = View.GONE
        }

        if (entity.description.isNullOrEmpty()) {
            descTextView.text = findString(R.string.no_intro)
        } else {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                descTextView.text = Html.fromHtml(entity.description, Html.FROM_HTML_MODE_LEGACY)
            } else {
                descTextView.text = Html.fromHtml(entity.description)
            }
            descTextView.post {
                descTextView.needCollapse = descTextView.lineCount > 5
            }
            descTextView.setCollapsed(true)
            descTextView.movementMethod = LinkMovementMethod.getInstance()
        }

        priceTextView.text = APPUtil.setCurrentPrice(
            entity.currency,
            entity.price,
            entity.priceCNY,
            entity.discount,
            !entity.activitiesList.isNullOrEmpty()
        )
        priceBeforeTextView.text = APPUtil.setOriginalPrice(entity.currency, entity.originalPrice)
        buyButton.setOnClickListener(this)
        if (entity.purchaseSeparately == 0) {
            priceTextView.text =
                Html.fromHtml(
                    findString(R.string.current_price) + "&#8194<font color='#FF342A'>" + findString(
                        R.string.not_support_single_book_buy
                    ) + "</font>"
                )
        }
        if (entity.subProductList.isNullOrEmpty()) {
            isPackageProduct = false
            productItemEntity = entity
            downloadEngine.setOnDownloadingListener(downloadListener)
            initSingleBookInfo(productItemEntity!!)
        } else { // 套装书籍的UI
            isPackageProduct = true
            var bookItemAdapter = PackageBookItemAdapter(downloadEngine)
            propertyRecyclerView.adapter = bookItemAdapter
            propertyRecyclerView.setPadding(dp2px(16), dp2px(16), dp2px(16), dp2px(16))
            bookItemAdapter.dataList = entity.subProductList

            initStatusUI(entity)
            if (entity.status == 1) {
                var isAllPurchased = true
                for (bean in entity.subProductList) {
                    if (bean.purchased == 0) {
                        isAllPurchased = false
                        break
                    }
                }
                if (isAllPurchased) {
                    buyContainerLayout.visibility = View.VISIBLE
                    buyButton.visibility = View.GONE
                    statusTextView.visibility = View.VISIBLE
                    statusTextView.textSize = 12F
                    statusTextView.setText(R.string.click_each_book_to_download_or_read)
                } else {
                    buyContainerLayout.visibility = View.VISIBLE
                    buyButton.currentType = ProgressButton.BTN_STATUS_BUY
                }
            }
        }
        var availableForWDBible = false
        for (index in 0 until entity.subProductList.size) {
            if (entity.subProductList[index].availableForWDBible == 1) {
                availableForWDBible = true
                break
            }
        }
        if (entity.availableForWDBible == 1 || availableForWDBible) {
            tipTextView.visibility = View.VISIBLE
        } else {
            tipTextView.visibility = View.GONE
        }
        if (entity.purchased == PurchaseStatus.PURCHASED.value){
            val resourceDownloadInfo =
                SDKSingleton.dbWrapBl.getResourceDownloadInfo(detailEntity.resourceId)
            if (resourceDownloadInfo == null || resourceDownloadInfo.fileId.isNullOrEmpty()) {
                //从网站购买成功后回到app同步数据
                val intent = Intent(this, SyncDataService::class.java)
                startService(intent)
            }
        }
    }

    fun initStatusUI(entity: ProductDetailEntity) {
        when (entity.status) {
            1 -> { // 正常情况
                buyContainerLayout.visibility = View.VISIBLE
                if (entity.purchaseSeparately == 1) {
                    // 是否支持购买
                    if (APPConfig.isDisablePayment) {
                        disablePayTextView.visibility = View.VISIBLE
                        buyButton.text = findString(R.string.goto_website_buy)
                    } else {
                        buyButton.visibility = View.VISIBLE
                        statusTextView.visibility = View.GONE
                    }
                } else {
                    setNotSingleBuyView(entity)
                }
            }
            -1 -> { // 已下架
                buyContainerLayout.visibility = View.VISIBLE
                statusTextView.visibility = View.VISIBLE
                statusTextView.setText(R.string.sold_out_prompt)
            }
            2 -> {
                showErrorLayout(getString(R.string.no_detail_page))
            }
            else -> {
                showErrorLayout()
            }
        }
    }

    private fun setNotSingleBuyView(entity: ProductDetailEntity) {
        buyButton.visibility = View.VISIBLE
        statusTextView.visibility = View.VISIBLE
        buyButton.setText(R.string.go_to_suit_buy)
        if (entity.purchased != PurchaseStatus.PURCHASED.value) {
            buyButton.isEnabled = false
        }
        priceBeforeTextView.text =
            findString(R.string.original_price) + "  " + UnitFormatUtil.formatPrice(
                entity.currency,
                entity.originalPrice
            )
    }

    private fun showErrorLayout(text: String = "") {
        buyContainerLayout.visibility = View.GONE
        notExistContainerLayout.visibility = View.VISIBLE
        if (!NetWorkUtils.isNetworkAvailable()) {
            errorInfoTextView.setText(R.string.no_network_connect)
        } else {
            if (!text.isNullOrEmpty()) {
                errorInfoTextView.text = text
            } else {
                errorInfoTextView.setText(R.string.no_detail_page)
            }
        }
    }

    /*加载单本书籍的详情内容*/
    fun initSingleBookInfo(entity: ProductDetailEntity) {
        var attributeAdapter = AttributeRecyclerAdapter()
        attributeAdapter.detailEntity = detailEntity
        attributeAdapter.clickListener = object : ClickListener {
            override fun onClick(valueList: ArrayList<AttributeValueEntity>, s: String) {
                if (s.contains("publisher")) {
                    val intent = Intent(this@BookDetailActivity, AuthorDetailActivity::class.java)
                    valueList?.get(0)?.deepLink?.replace("/publisher/", "")
                        ?.let { intent.putExtra(IntentConstants.EXTRA_AuthorId, it.toLong()) }
                    intent.putExtra(IntentConstants.EXTRA_AuthorName, valueList?.get(0)?.name)
                    intent.putExtra(IntentConstants.EXTRA_IsPublisher, true)
                    startActivity(intent)
                } else if (s.contains("editor")) {
                    val list = ArrayList<String>()
                    valueList?.forEach {
                        list.add(it.name)
                    }
                    if (list.size == 1) {
                        val intent = Intent(this@BookDetailActivity, AuthorDetailActivity::class.java)
                        valueList?.get(0)?.deepLink?.replace("/editor/", "")
                            ?.let { intent.putExtra(IntentConstants.EXTRA_AuthorId, it.toLong()) }
                        intent.putExtra(IntentConstants.EXTRA_AuthorName, valueList?.get(0)?.name)
                        startActivity(intent)
                    } else {
                        var authorChoiceDialog: AuthorChoiceDialog? = null
                        val builder = AuthorChoiceDialog.with(this@BookDetailActivity)
                        builder.setTitle(getString(R.string.translator))
                            .setItemStringList(list)
                            .setonItemClickListener(object : OnItemClickListener {
                                override fun <T> onItemClick(obj: Any, tag: String, t: T) {
                                    val intent =
                                        Intent(this@BookDetailActivity, AuthorDetailActivity::class.java)
                                    valueList?.get(tag.toInt())?.deepLink?.replace("/editor/", "")
                                        ?.let {
                                            intent.putExtra(
                                                IntentConstants.EXTRA_AuthorId,
                                                it.toLong()
                                            )
                                        }
                                    intent.putExtra(
                                        IntentConstants.EXTRA_AuthorName,
                                        valueList?.get(tag.toInt())?.name
                                    )
                                    startActivity(intent)
                                    authorChoiceDialog?.dismiss()
                                }
                            })
                        authorChoiceDialog = builder.create()
                        authorChoiceDialog.show()
                        var lp = authorChoiceDialog.window?.attributes;
                        lp!!.dimAmount = 0.25f;
                        authorChoiceDialog.window?.attributes = lp
                        authorChoiceDialog.window?.clearFlags(WindowManager.LayoutParams.FLAG_BLUR_BEHIND)
                        authorChoiceDialog.window?.addFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND)
                    }
                }
            }
        }
        propertyRecyclerView.adapter = attributeAdapter
        propertyRecyclerView.setPadding(0, dp2px(16), 0, dp2px(16))

        attributeAdapter.dataList = entity.basicInfoList
        catalogTextView.visibility =
            if (!TextUtils.isEmpty(detailEntity.toc)) View.VISIBLE else View.GONE

        if (entity.relatedProducts.size > 0) {
            var referenceAdapter = ReferenceRecyclerAdapter()
            referenceRecyclerView.adapter = referenceAdapter
            referenceAdapter.dataList = entity.relatedProducts
        }

        initStatusUI(entity)
        if (entity.status == PutawayStatus.UnGrounding.value) { // 还未上架
            return
        }

        if (entity.purchased == PurchaseStatus.UNDONE.value) { // 未购买
            when (entity.status) {
                PutawayStatus.Normal.value -> { // 正常情况
                    buyContainerLayout.visibility = View.VISIBLE
                    // 是否支持购买
                    if (APPConfig.isDisablePayment) {
                        disablePayTextView.visibility = View.VISIBLE
                        buyButton.text = findString(R.string.goto_website_buy)
                    } else {
                        if (entity.purchaseSeparately == 1) { // 可单独购买
                            buyButton.visibility = View.VISIBLE
                            statusTextView.visibility = View.GONE
                            if (entity.price == 0f) {
                                buyButton.text = getString(R.string.free_get)
                            } else {
                                buyButton.setText(R.string.buy)
                            }
                            buyButton.setCurrentShowType(ProgressButton.BTN_STATUS_BUY)
                            purchaseFlagImageView.visibility = View.GONE
                        } else { // 不可单独购买
                            setNotSingleBuyView(entity)
                        }
                    }
                }
                // 注释先保留
//                ProductStatus.Removed.value -> { //已下架
//                    buyContainerLayout.visibility = View.VISIBLE
//                    statusTextView.visibility = View.VISIBLE
//                    statusTextView.setText(R.string.sold_out_prompt)
//                }
            }
        } else {
            buyContainerLayout.visibility = View.VISIBLE
            statusTextView.visibility = View.GONE
            buyButton.visibility = View.VISIBLE
            purchaseFlagImageView.visibility = View.VISIBLE

            var resourceDownloadInfo =
                SDKSingleton.dbWrapBl.getResourceDownloadInfo(entity.resourceId)
            if ((resourceDownloadInfo != null) && (resourceDownloadInfo.downloadStatus == DownloadStatus.COMPLETE) &&
                File(resourceDownloadInfo.getActualFilePath()).exists()
            ) {
                buyButton.setText(R.string.read)
                buyButton.setCurrentShowType(ProgressButton.BTN_STATUS_READ)
            } else {
                buyButton.setText(R.string.download)
                buyButton.setCurrentShowType(ProgressButton.BTN_STATUS_NEED_DOWNLOAD)
                executeSingleBookDownloadFile()
            }
        }
    }

    fun addOrRemoveFavorite(isRemove: Boolean) {
        MainScope().launch(ExceptionHandler.coroutineExceptionHandler) {
            if (isRemove) {
                SDKSingleton.userBl.removeFavoriteBook(detailEntity.productId)
                ToastUtil.showToastShort(getString(R.string.remove_favorite))
            } else {
                SDKSingleton.userBl.addFavoriteBook(detailEntity.productId)
                ToastUtil.showToastShort(getString(R.string.add_favorite))
            }
            EventBus.getDefault().post(OnFavoriteEvent())
            doOnRefresh()
        }
    }

    fun showDisablePaymentDialog() {
        CommAlertDialog.with(this)
            .setMessage(R.string.goto_website_buy_tips)
            .setMessageMinHeight(80)
            .setStartText(R.string.label_cancel)
            .setEndText(R.string.label_OK)
            .setAllButtonColorRes(R.color.text_color_blue_007AFF)
            .setOnViewClickListener { dialog, view, i ->
                if (i == CommAlertDialog.TAG_CLICK_END) {
                    MainScope().launch(ExceptionHandler.coroutineExceptionHandler) {
                        val url = SDKSingleton.storeBl.getProductWebUrl(detailEntity.productId)
                        val nonce = SDKSingleton.appBl.getTokenNonce()
                        val paramsMap = HashMap<String, String>()
                        if (!nonce.isNullOrEmpty()) {
                            paramsMap["nonce"] = nonce
                        }
                        if (!url.isNullOrEmpty()) {
                            val intent = Intent(
                                Intent.ACTION_VIEW,
                                Uri.parse(APPUtil.formatUrl(url, paramsMap).toString())
                            )
                            <EMAIL>(intent)
                        }
                    }
                }
            }
            .create().show()
    }

    override fun onClick(v: View?) {
        when (v) {
            couponContainerLayout -> {
                val dialog = GetCouponDialog(this)
                dialog.show()
                dialog.initDataList(bookDetailCouponItemAdapter.dataList)
            }
            activityContainerLayout -> {
                if (!detailEntity.activitiesList.isNullOrEmpty()) {
                    if (!NetWorkUtils.isNetworkAvailable()) {
                        NetWorkUtils.showTipDialog(this, getString(R.string.no_network_connect))
                        return
                    }
                    ProductActiveActivity.gotoActivityDetailUI(
                        this,
                        detailEntity.activitiesList!![0]!!.activityId
                    )
                }
            }
            favoriteImageView -> {
                if (!NetWorkUtils.isNetworkAvailable()) {
                    NetWorkUtils.showTipDialog(this, getString(R.string.no_network_connect))
                    return
                }
                if (!SDKSingleton.sessionBl.isLogin()) {
                    SSOLoginActivity.checkAndGotoLogin(this)
                } else {
                    addOrRemoveFavorite(favoriteImageView.isSelected)
                }
            }
            buyButton -> {
                if (!NetWorkUtils.isNetworkAvailable()) {
                    NetWorkUtils.showTipDialog(this, getString(R.string.no_network_connect))
                    return
                }
                when (buyButton.currentType) {
                    ProgressButton.BTN_STATUS_BUY -> {
                        if (!SDKSingleton.sessionBl.isLogin()) {
                            SSOLoginActivity.checkAndGotoLogin(this)
                        } else {
                            // 是否支持购买
                            if (APPConfig.isDisablePayment) {
                                showDisablePaymentDialog()
                            } else {
                                logEvent(
                                    AnalyticsConstants.LOG_V1_PRODUCT_CLICK_BUY,
                                    LOG_V1_PARAM_PRODUCT_ID,
                                    productId.toString()
                                )

                                var couponIdList: ArrayList<Long>? = null
                                if (!detailEntity.couponsList.isNullOrEmpty()) {
                                    couponIdList = ArrayList()
                                    for (item in detailEntity.couponsList!!) {
                                        couponIdList.add(item.couponId)
                                    }
                                }

                                var activityIdList: ArrayList<Long>? = null
                                if (!detailEntity.activitiesList.isNullOrEmpty()) {
                                    activityIdList = ArrayList()
                                    for (item in detailEntity.activitiesList!!) {
                                        activityIdList.add(item.activityId)
                                    }
                                }
                                orderPaymentHelper!!.singlePayAmountAction(
                                    detailEntity,
                                    couponIdList,
                                    activityIdList
                                )
                            }
                        }
                    }
                    ProgressButton.BTN_STATUS_READ -> {
                        if (downloadDataEntity == null) {
                            var resourceDownloadInfo =
                                SDKSingleton.dbWrapBl.getResourceDownloadInfo(detailEntity.resourceId)
                            if (resourceDownloadInfo != null) {
                                downloadDataEntity =
                                    SDKSingleton.downloadBl.fetchDownloadFileEntity(
                                        resourceDownloadInfo.fileId ?: "",
                                        DataPathUtil.getDownloadPath(),
                                    )
                            }
                        }
                        if (downloadDataEntity != null) {
                            BookReadActivity.gotoBookReadActivity(
                                this,
                                downloadDataEntity!!.getActualFilePath(),
                                downloadDataEntity!!.fileId,
                                downloadDataEntity!!.resourceId,
                            )
                        } else {
                            ToastUtil.showToastShort(R.string.can_not_open_exit_again)
                        }
                    }
                    ProgressButton.BTN_STATUS_NEED_DOWNLOAD -> {
                        if (SyncDataService.isInSyncProgress) {
                            ToastUtil.showToastShort(R.string.syncing_data_toast)
                            return
                        }
                        isShowTip = true
                        executeSingleBookDownloadFile()
                    }
                }
            }
            catalogTextView -> {
                var toc = detailEntity.toc
                val intent = Intent(this, ContentsActivity::class.java)
                intent.putExtra(IntentConstants.EXTRA_BookContents, toc)
                startActivity(intent)
            }
            goBackButton -> {
                onBackPressed()
            }
        }
    }

    var isShowTip = false

    /*执行单本书籍显示的下载任务*/
    fun executeSingleBookDownloadFile() {
        if (downloadDataEntity == null) {
            var resourceDownloadInfo =
                SDKSingleton.dbWrapBl.getResourceDownloadInfo(detailEntity.resourceId)
            if (resourceDownloadInfo != null) {
                downloadDataEntity =
                    SDKSingleton.downloadBl.fetchDownloadFileEntity(
                        resourceDownloadInfo.fileId ?: "", DataPathUtil.getDownloadPath()
                    )
            }
            doOnRefresh()
        }

        if (downloadDataEntity == null || downloadDataEntity!!.fileId.isNullOrEmpty()) {
            if (!SyncDataService.isInSyncProgress && isShowTip) {
                NetWorkUtils.showTipDialog(this, getString(R.string.no_network_connect))
//                ToastUtil.showToastShort(R.string.data_exception_prompt)
                isShowTip = false
            }
            return
        }
        downloadEngine.getFileUrlAndDownload(downloadDataEntity!!)
    }

    val downloadListener = object : OnDownloadingListener {

        override fun onWait(entity: BookFileDownloadEntity, isAdded: Boolean) {
            if (entity.fileId.equals(downloadDataEntity?.fileId)) {
                buyButton.needShowProgress = false
                buyButton.text =
                    findString(if (isAdded) R.string.wait_download else R.string.download)
            }
        }

        override fun onBeginning(entity: BookFileDownloadEntity) {
            if (entity.fileId.equals(downloadDataEntity?.fileId)) {
                buyButton.needShowProgress = true
                buyButton.invalidate()
            }
        }

        override fun onDownloadingProgress(
            entity: BookFileDownloadEntity,
            downloadSize: Long,
            totalSize: Long
        ) {
            if (entity.fileId.equals(downloadDataEntity?.fileId)) {
                buyButton.needShowProgress = true
                buyButton.setCurrentShowType(ProgressButton.BTN_STATUS_NEED_DOWNLOAD)
                buyButton.setButtonProgress(downloadSize.toFloat() / totalSize * 100)
            }
        }

        override fun onPause(entity: BookFileDownloadEntity) {
            if (entity.fileId.equals(downloadDataEntity?.fileId)) {
                buyButton.needShowProgress = true
                buyButton.setCurrentShowType(ProgressButton.BTN_STATUS_NEED_DOWNLOAD)
                buyButton.text = findString(R.string.pause) + " ${buyButton.text}"
            }
        }

        override fun onError(entity: BookFileDownloadEntity, errorDesc: String) {
            KLog.e("下载错误")
            if (downloadDataEntity == null) {
                return
            }
            if (!downloadDataEntity!!.fileId.equals(entity.fileId)) {
                return
            }
            if (tryDownloadTimes < 1) {
                buyButton.setText(R.string.download_error)
                return
            }
            try {
                Handler().postDelayed({
                    executeSingleBookDownloadFile()
                }, 5000)
                tryDownloadTimes -= 1
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }

        override fun onComplete(entity: BookFileDownloadEntity) {
            if (entity.fileId.equals(downloadDataEntity?.fileId)) {
                downloadDataEntity = SDKSingleton.dbWrapBl.getFileDownloadDataEntity(entity.fileId)
                buyButton.setCurrentShowType(ProgressButton.BTN_STATUS_READ)
                buyButton.setText(R.string.read)
                EventBus.getDefault().post(OnBookItemDownloadFinish())
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        CustomerSession.endCustomerSession()
        downloadEngine.removeListener()
        downloadEngine.setOnDownloadingListener(null)
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun OnRefreshAfterPayEvent(event: OnRefreshAfterPay) {
        when (event.status) {
            OrderStatusDefine.ORDER_STATUS_SUCCEED, OrderStatusDefine.ORDER_STATUS_FINISH -> { // 成功
                if (isPackageProduct) {
                    logEvent(
                        AnalyticsConstants.LOG_V1_PRODUCT_PACKAGE_PAY_SUCCESS,
                        LOG_V1_PARAM_ORDER_ID,
                        event.orderId,
                    )
                }
            }
        }
        EventBus.getDefault().post(OnSuccessBuyEvent(detailEntity.productId))
    }
}
