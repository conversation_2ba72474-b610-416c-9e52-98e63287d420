package com.wedevote.wdbook.ui.dialogs

import android.app.Dialog
import android.content.Context
import android.os.Bundle
import android.view.Gravity
import android.view.View
import android.view.WindowManager
import android.widget.Button
import android.widget.TextView
import com.aquila.lib.base.OnViewClickListener
import com.wedevote.wdbook.R
import com.wedevote.wdbook.entity.store.PaymentAmountEntity
import com.wedevote.wdbook.entity.store.ProductDetailEntity
import com.wedevote.wdbook.tools.payment.ChoosePayMethodDialog
import com.wedevote.wdbook.tools.util.PayTypeDefine
import com.wedevote.wdbook.tools.util.UnitFormatUtil
import com.wedevote.wdbook.tools.util.findString

/***
 * @date 创建时间 2020/7/22 16:32
 * <AUTHOR> W.YuLong
 * @description
 */
class PaymentInfoDialog(context: Context) : Dialog(context), View.OnClickListener {
    lateinit var priceTextView: TextView
    lateinit var totalAmountTextView: TextView
    lateinit var purchasedTextView: TextView

    lateinit var nameTextView: TextView
    lateinit var buyButton: Button
    var onClickBuyListener: OnClickBuyListener? = null
    var currency: String = "$"
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.dialog_purchase_book_layout)
        initViewFromXML()

        configDialog()
        buyButton.setOnClickListener(this)
    }

    private fun initViewFromXML() {
        priceTextView = findViewById(R.id.purchase_price_TextView)
        nameTextView = findViewById(R.id.purchase_book_name_TextView)
        totalAmountTextView = findViewById(R.id.purchase_total_amount_TextView)
        purchasedTextView = findViewById(R.id.purchase_purchased_amount_TextView)
        buyButton = findViewById(R.id.purchase_buy_Button)
    }

    var paymentAmountEntity: PaymentAmountEntity? = null
    fun setPurchaseInfoBean(productDetailEntity: ProductDetailEntity, paymentAmountEntity: PaymentAmountEntity) {
        this.paymentAmountEntity = paymentAmountEntity

        nameTextView.text = productDetailEntity.title
        currency = UnitFormatUtil.getCurrencySymbol(productDetailEntity.currency)
        priceTextView.text = findString(R.string.actual_amount) + " ${UnitFormatUtil.formatPrice(currency, paymentAmountEntity.actualAmount)}"

        buyButton.text = if (paymentAmountEntity.actualAmount == 0f) findString(R.string.free_get) else findString(R.string.dialog_payment_info_btn)

        if (!productDetailEntity.subProductList.isNullOrEmpty()) {
            if (paymentAmountEntity.originalPrice != paymentAmountEntity.actualAmount) {
                totalAmountTextView.text = findString(R.string.total_amount) + "${
                    UnitFormatUtil.formatPrice(currency, paymentAmountEntity.originalPrice)
                }"
                totalAmountTextView.visibility = View.VISIBLE
            } else {
                totalAmountTextView.visibility = View.GONE
            }
            if (!paymentAmountEntity.purchasedProductList.isNullOrEmpty()) {
                purchasedTextView.text = findString(R.string.dialog_payment_info_purchased_amount) + "-${
                    UnitFormatUtil.formatPrice(currency, paymentAmountEntity.purchasedAmount)
                }"
                purchasedTextView.setOnClickListener(this)
                purchasedTextView.visibility = View.VISIBLE
            } else {
                purchasedTextView.visibility = View.GONE
            }
        } else {
            totalAmountTextView.visibility = View.GONE
            purchasedTextView.visibility = View.GONE
        }
    }

    protected fun configDialog() {
        val wl = window!!.attributes
        wl.gravity = Gravity.BOTTOM // 设置重力
        wl.width = WindowManager.LayoutParams.MATCH_PARENT
        wl.height = WindowManager.LayoutParams.WRAP_CONTENT
        window!!.setWindowAnimations(com.aquila.lib.dialog.R.style.bottomDialogWindowAnim)
        window!!.attributes = wl
        window!!.setBackgroundDrawableResource(android.R.color.transparent)
    }

    var clickBuyTime = 0L
    override fun onClick(v: View) {
        when (v) {
            buyButton -> {
                if (System.currentTimeMillis() - clickBuyTime < 3000) {
                    return
                }
                clickBuyTime = System.currentTimeMillis()
                if (paymentAmountEntity?.actualAmount == 0f) {
                    onClickBuyListener?.onClickBuy(PayTypeDefine.PAY_TYPE_CARD.value, paymentAmountEntity)
                } else {
                    var dialog = ChoosePayMethodDialog(context, currency, paymentAmountEntity!!)
                    dialog.show()
                    dialog.onViewClickListener = object : OnViewClickListener {
                        override fun <T> onClickAction(v: View, str: String, t: T?) {
                            onClickBuyListener?.onClickBuy(str, paymentAmountEntity)
                        }
                    }
                }
            }
            purchasedTextView -> {
                paymentAmountEntity?.let {
                    var purchasedItemListDialog = PartPurchasedItemListDialog(context)
                    purchasedItemListDialog.show()
                    purchasedItemListDialog.initDataList(it.purchasedProductList)
                }
            }
        }
    }

}
