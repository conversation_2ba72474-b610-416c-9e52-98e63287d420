package com.wedevote.wdbook.ui.dialogs

import android.content.Context
import android.os.Bundle
import android.view.Gravity
import android.view.WindowManager
import android.widget.Button
import android.widget.ImageView
import android.widget.LinearLayout
import com.wedevote.wdbook.base.SDKSingleton
import com.wedevote.wdbook.constants.LanguageMode
import com.wedevote.wdbook.R
import com.wedevote.wdbook.tools.util.APPUtil

/***
 * @date 创建时间 2025/09/08
 * <AUTHOR>
 * @description 搜索页筛选功能引导弹窗
 */
class SearchFilterGuideDialog(
    context: Context,
    private val imageTopMarginPx: Int,
) : BaseDialog(context) {

    private lateinit var guideImageView: ImageView
    private lateinit var okButton: Button

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.dialog_search_filter_guide_layout)
        guideImageView = findViewById(R.id.dialog_search_guide_imageView)
        okButton = findViewById(R.id.dialog_search_guide_ok_Button)

        (guideImageView.layoutParams as LinearLayout.LayoutParams).apply {
            topMargin = imageTopMarginPx
        }.also { guideImageView.layoutParams = it }

        okButton.setOnClickListener { dismiss() }

        configDialog(Gravity.CENTER)
        setCancelable(true)
    }

    override fun configDialog(gravity: Int) {
        val wl = window!!.attributes
        wl.gravity = gravity
        window!!.setWindowAnimations(0)
        wl.width = WindowManager.LayoutParams.MATCH_PARENT
        wl.height = WindowManager.LayoutParams.MATCH_PARENT
        window!!.addFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND)
        window!!.attributes = wl
    }
}


