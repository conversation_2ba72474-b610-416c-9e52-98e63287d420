package com.wedevote.wdbook.ui.store

import android.content.Intent
import android.net.Uri
import androidx.appcompat.app.AppCompatActivity
import com.aquila.lib.dialog.CommProgressDialog
import com.braintreepayments.api.paypal.*
import com.wedevote.wdbook.R
import com.wedevote.wdbook.base.APP
import com.wedevote.wdbook.base.ExceptionHandler
import com.wedevote.wdbook.base.SDKSingleton
import com.wedevote.wdbook.constants.Constants
import com.wedevote.wdbook.tools.download.LifecycleFragment
import com.wedevote.wdbook.tools.payment.OrderPaymentResultEnum
import com.wedevote.wdbook.tools.payment.stripe.OnPaymentCallback
import com.wedevote.wdbook.tools.util.findString
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch

/***
 * @date 创建时间 2020/12/22 13:46
 * <AUTHOR> <PERSON><PERSON>
 * @description Paypal支付的工具类
 */
class PayPalPaymentHelper(
    private val activity: AppCompatActivity,
    private val onPaymentCallback: OnPaymentCallback
) : LifecycleFragment.LifecycleListener {
    private lateinit var payPalClient: PayPalClient
    private lateinit var payPalLauncher: PayPalLauncher
    private var pendingRequest: PayPalPendingRequest.Started? = null
    private lateinit var currentOrderId: String
    private lateinit var currentAmount: String
    private var commProgressDialog: CommProgressDialog

    init {
        commProgressDialog = CommProgressDialog.Builder(activity).setTouchOutsideCancel(false)
            .create()
        activity.supportFragmentManager
            .beginTransaction()
            .add(LifecycleFragment(this), LifecycleFragment::class.java.name)
            .commitAllowingStateLoss()
    }

    override fun onLifecycleStop() {
        if (commProgressDialog.isShowing) {
            commProgressDialog.dismiss()
        }
    }

    fun initPayPalToken(orderId: String, amount: String) {
        currentOrderId = orderId
        currentAmount = amount
        commProgressDialog.show()

        MainScope().launch(ExceptionHandler.coroutineExceptionHandler) {
            SDKSingleton.paymentBl.initPayPalToken()?.also { token ->
                activity.runOnUiThread {
                    initializePayPalClient(token)
                    launchPayPal()
                }
            }
        }
    }

    private fun initializePayPalClient(token: String) {
        payPalClient = PayPalClient(
            activity,
            token,
            Uri.parse("${APP.get().packageName}://${Constants.PAYPAL_HOST}")
        )
        payPalLauncher = PayPalLauncher()
    }

    private fun launchPayPal() {
        val payPalRequest = getPayPalRequest(currentAmount)
        payPalClient.createPaymentAuthRequest(activity, payPalRequest) { paymentAuthRequest ->
            when (paymentAuthRequest) {
                is PayPalPaymentAuthRequest.ReadyToLaunch -> {
                    val request = payPalLauncher.launch(activity, paymentAuthRequest)
                    when (request) {
                        is PayPalPendingRequest.Started -> {
                            commProgressDialog.dismiss()
                            pendingRequest = request
                        }

                        is PayPalPendingRequest.Failure -> {
                            commProgressDialog.dismiss()
                            handleError(request.error)
                        }
                    }
                }

                is PayPalPaymentAuthRequest.Failure -> {
                    handleError(paymentAuthRequest.error)
                }
            }
        }
    }

    fun handlePayPalReturn(intent: Intent) {
        pendingRequest?.let { request ->
            val paymentAuthResult = payPalLauncher.handleReturnToApp(request, intent)
            handlePayPalResult(paymentAuthResult)
        } ?: run {
            launchPayPal()
        }
    }

    private fun handlePayPalResult(result: PayPalPaymentAuthResult) {
        when (result) {
            is PayPalPaymentAuthResult.Success -> completePayPalFlow(result)
            is PayPalPaymentAuthResult.Failure -> handleError(result.error)
            PayPalPaymentAuthResult.NoResult -> handleError(Exception("PayPal payment flow did not complete"))
        }
    }

    private fun completePayPalFlow(paymentAuthResult: PayPalPaymentAuthResult.Success) {
        payPalClient.tokenize(paymentAuthResult) { payPalResult ->
            when (payPalResult) {
                is PayPalResult.Success -> handlePaymentSuccess(payPalResult.nonce.string)
                is PayPalResult.Failure -> handleError(payPalResult.error)
                PayPalResult.Cancel -> handleError(Exception("PayPal payment cancelled"))
            }
        }
    }

    private fun handlePaymentSuccess(nonce: String) {
        commProgressDialog.show()
        MainScope().launch(ExceptionHandler.getCoroutineExceptionHandler {
            onPaymentCallback.onPayResult(currentOrderId, OrderPaymentResultEnum.FAILURE)
        }) {
            commProgressDialog.setTitleText(findString(R.string.getting_order_payment_results))
            SDKSingleton.paymentBl.postPayResultCode(currentOrderId, nonce).also { result ->
                val resultCode = when (result) {
                    OrderPaymentResultEnum.SUCCESS.value, OrderPaymentResultEnum.SUCCESS_ALT.value -> OrderPaymentResultEnum.SUCCESS
                    OrderPaymentResultEnum.CANCEL.value -> OrderPaymentResultEnum.CANCEL
                    OrderPaymentResultEnum.FAILURE.value -> OrderPaymentResultEnum.FAILURE
                    else -> OrderPaymentResultEnum.EXCEPTION
                }
                onPaymentCallback.onPayResult(currentOrderId, resultCode)
            }
            commProgressDialog.dismiss()
        }
    }

    private fun handleError(error: Exception) {
        activity.runOnUiThread {
            commProgressDialog.dismiss()
            onPaymentCallback.onPayResult(currentOrderId, OrderPaymentResultEnum.FAILURE)
        }
    }

    private fun getPayPalRequest(amount: String): PayPalCheckoutRequest {
        return PayPalCheckoutRequest(
            amount = amount,
            hasUserLocationConsent = false,
            intent = PayPalPaymentIntent.SALE,
            userAction = PayPalPaymentUserAction.USER_ACTION_COMMIT,
            currencyCode = "USD"
        ).apply {
            shouldRequestBillingAgreement = false
            shouldOfferPayLater = false
            displayName = findString(R.string.pay_order)
        }
    }
}
