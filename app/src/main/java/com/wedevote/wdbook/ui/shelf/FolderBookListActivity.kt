package com.wedevote.wdbook.ui.shelf

import android.app.Dialog
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.view.View
import android.widget.Button
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.aquila.lib.base.OnViewClickListener
import com.aquila.lib.dialog.CommAlertDialog
import com.aquila.lib.layout.SmartRefreshLayout
import com.aquila.lib.layout.api.RefreshLayout
import com.aquila.lib.layout.listener.OnLoadMoreListener
import com.aquila.lib.layout.listener.OnRefreshListener
import com.aquila.lib.tools.util.ToastUtil
import com.aquila.lib.widget.view.CustomRecyclerView
import com.wedevote.wdbook.R
import com.wedevote.wdbook.base.ExceptionHandler
import com.wedevote.wdbook.base.RootActivity
import com.wedevote.wdbook.base.SDKSingleton
import com.wedevote.wdbook.entity.shelf.HomeShelfItemCombineEntity
import com.wedevote.wdbook.entity.shelf.ShelfArchiveItemEntity
import com.wedevote.wdbook.entity.shelf.ShelfBookItemEntity
import com.wedevote.wdbook.entity.shelf.ShelfDataType
import com.wedevote.wdbook.tools.event.HomeShelfDataReloadEvent
import com.wedevote.wdbook.tools.event.OnBookReadEvent
import com.wedevote.wdbook.tools.util.GsonUtil
import com.wedevote.wdbook.tools.util.IntentConstants
import com.wedevote.wdbook.tools.util.findString
import com.wedevote.wdbook.tools.util.toJsonStr
import com.wedevote.wdbook.ui.dialogs.BookShelfGroupDialog
import com.wedevote.wdbook.ui.dialogs.CreateBookGroupDialog
import com.wedevote.wdbook.ui.dialogs.OnCreateBookGroupCallBack
import com.wedevote.wdbook.ui.widgets.CommTopTitleLayout
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

/***
 * @date 创建时间 2020/7/7 17:33
 * <AUTHOR> W.YuLong
 * @description 书籍文件夹的展示页面
 */
class FolderBookListActivity : RootActivity(), View.OnClickListener, OnLoadMoreListener, OnRefreshListener {

    private lateinit var titleLayout: CommTopTitleLayout
    private lateinit var changeNameTextView: TextView
    private lateinit var deleteFolderTextView: TextView
    private lateinit var moveToFolderButton: Button
    private lateinit var dataRecyclerView: CustomRecyclerView
    private lateinit var containerLayout: ConstraintLayout
    private lateinit var selectAllTextView: TextView
    private lateinit var editTextView: TextView
    private lateinit var dataRefreshLayout: SmartRefreshLayout
    private lateinit var emptyTextView: TextView

    private lateinit var bookAdapter: ShelfBookAdapter
    private lateinit var shelfArchiveEntity: ShelfArchiveItemEntity

    var limit = 20L
    var offset = 0L

    companion object {
        fun gotoFolderBookListActivity(context: Context, shelfArchiveItemEntity: ShelfArchiveItemEntity) {
            val intent = Intent(context, FolderBookListActivity::class.java)
            intent.putExtra(IntentConstants.EXTRA_ShelfBookBean, shelfArchiveItemEntity.toJsonStr())
            context.startActivity(intent)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_shelf_folder_book_list_layout)
        initViewFromXML()

        bookAdapter = ShelfBookAdapter(this)
        dataRecyclerView.adapter = bookAdapter

        val shelfArchiveJsonString = intent.getStringExtra(IntentConstants.EXTRA_ShelfBookBean)
        shelfArchiveEntity = GsonUtil.parseJsonToObject(shelfArchiveJsonString)
        titleLayout.setTitle(shelfArchiveEntity.archiveName)
        loadLocalData()
        setViewListeners()
    }

    private fun loadLocalData(offset: Long, limit: Long) {
        val bookItemList = SDKSingleton.dbWrapBl.getBookListByClientArchiveId(shelfArchiveEntity.clientArchiveId, offset, limit)
        if (!bookItemList.isNullOrEmpty()) {
            val combineList = ArrayList<HomeShelfItemCombineEntity>()
            for (item in bookItemList) {
                combineList.add(
                    HomeShelfItemCombineEntity().apply {
                        dataType = ShelfDataType.RESOURCE
                        bookItemEntity = item
                    }
                )
            }
            bookAdapter.addDataList(combineList)
            dataRefreshLayout.isEnableLoadMore = bookItemList.size >= limit
            this.offset = bookAdapter.itemCount.toLong()
        } else {
            if (offset == 0L) {
                editTextView.visibility = View.GONE
                emptyTextView.visibility = View.VISIBLE
                dataRefreshLayout.visibility = View.GONE
            }
            dataRefreshLayout.isEnableLoadMore = false
        }
    }

    override fun onLoadMore(refreshLayout: RefreshLayout) {
        loadLocalData(offset, limit)
        dataRefreshLayout.finishLoadMoreAndRefresh()
    }

    override fun onRefresh(refreshLayout: RefreshLayout?) {
        MainScope().launch(ExceptionHandler.coroutineExceptionHandler) {
            SDKSingleton.syncBl.syncAppAndUserData()
            loadLocalData()
            dataRefreshLayout.isEnableLoadMore = true
        }
    }

    private fun loadLocalData() {
        offset = 0
        bookAdapter.clearDataList()
        onLoadMore(dataRefreshLayout)
    }

    private fun setViewListeners() {
        dataRefreshLayout.setOnRefreshListener(this).setOnLoadMoreListener(this)
        changeNameTextView.setOnClickListener(this)
        moveToFolderButton.setOnClickListener(this)
        deleteFolderTextView.setOnClickListener(this)
        selectAllTextView.setOnClickListener(this)
        editTextView.setOnClickListener(this)
        bookAdapter.onShelfBookAdapterCallback = object : OnShelfBookAdapterCallback {
            override fun onReloadData() {
                offset = 0L
                var itemCountLimit = bookAdapter.itemCount.toLong()
                bookAdapter.clearDataList()
                loadLocalData(offset, itemCountLimit)
            }

            override fun onChangeEditMode(isLongClicked: Boolean) {
                if (isLongClicked) {
                    containerLayout.visibility = View.GONE
                    selectAllTextView.visibility = View.VISIBLE
                    moveToFolderButton.visibility = View.VISIBLE
                    titleLayout.backImageView.visibility = View.GONE
                    editTextView.text = findString(R.string.label_cancel)
                    // 这里是把moveToFolderButton设置显示样式
                    onItemSelect(null)
                } else {
                    containerLayout.visibility = View.VISIBLE
                    selectAllTextView.visibility = View.GONE
                    moveToFolderButton.visibility = View.GONE
                    titleLayout.backImageView.visibility = View.VISIBLE
                    editTextView.text = findString(R.string.edit)
                }
            }

            override fun isAllItemSelected(isAllSelected: Boolean) {
                selectAllTextView.setText(if (isAllSelected) R.string.inverse_select else R.string.select_all)
            }

            override fun onEmptyData(isEmptyData: Boolean) {
                if (isEmptyData) {
                    editTextView.visibility = View.GONE
                    emptyTextView.visibility = View.VISIBLE
                    dataRefreshLayout.visibility = View.GONE
                } else {
                    editTextView.visibility = View.VISIBLE
                    emptyTextView.visibility = View.GONE
                    dataRefreshLayout.visibility = View.VISIBLE
                }
            }

            override fun onItemSelect(selectList: ArrayList<ShelfBookItemEntity>?) {
                moveToFolderButton.alpha = if (selectList.isNullOrEmpty()) 0.6f else 1f
                moveToFolderButton.isEnabled = !selectList.isNullOrEmpty()
            }
        }
    }

    private fun initViewFromXML() {
        titleLayout = findViewById(R.id.book_folder_title_layout)
        containerLayout = findViewById(R.id.shelf_folder_option_container_Layout)
        changeNameTextView = findViewById(R.id.shelf_folder_change_name_TextView)
        moveToFolderButton = findViewById(R.id.shelf_folder_move_folder_Button)
        deleteFolderTextView = findViewById(R.id.shelf_folder_delete_folder_TextView)
        dataRecyclerView = findViewById(R.id.book_folder_data_RecyclerView)
        selectAllTextView = findViewById(R.id.book_folder_select_all_TextView)
        editTextView = findViewById(R.id.book_folder_edit_TextView)
        dataRefreshLayout = findViewById(R.id.book_folder_data_RefreshLayout)
        emptyTextView = findViewById(R.id.book_folder_empty_prompt_TextView)
    }

    override fun onClick(v: View) {
        when (v) {
            editTextView -> {
                bookAdapter.onChangeEditModeAction()
            }
            selectAllTextView -> {
                bookAdapter.onChangeSelectAllData()
            }
            changeNameTextView -> {
                showChangeNameDialog(findString(R.string.change_group_name))
            }
            moveToFolderButton -> {
                if (hasSelectBook()) {
                    moveToAnotherFolder()
                }
            }
            deleteFolderTextView -> {
                deleteCurrentFolder()
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun doOnBookRead(event: OnBookReadEvent) {
        bookAdapter.onShelfBookAdapterCallback?.onReloadData()
    }

    private fun hasSelectBook(): Boolean {
        if (bookAdapter.selectItemList.isEmpty()) {
            return false
        }
        return true
    }

    private fun deleteCurrentFolder() {

        var messageText = if (bookAdapter.dataList.isNullOrEmpty()) {
            getString(R.string.delete_empty_group_prompt)
        } else {
            getString(R.string.sure_delete_current_group)
        }
        CommAlertDialog.with(this)
            .setMessage(messageText)
            .setMessageMinHeight(80)
            .setEndText(R.string.label_OK).setStartText(R.string.label_cancel)
            .setOnViewClickListener { _, _, tag ->
                if (tag == CommAlertDialog.TAG_CLICK_END) {
                    SDKSingleton.dbWrapBl.deleteArchiveItem(shelfArchiveEntity.clientArchiveId)
                    bookAdapter.doCancelEditMode()
                    MainScope().launch(ExceptionHandler.coroutineExceptionHandler) {
                        SDKSingleton.syncBl.syncShelfList()
                        finish()
                        EventBus.getDefault().post(HomeShelfDataReloadEvent(true))
                        ToastUtil.showToastShort(getString(R.string.has_deleted_group))
                    }
                }
            }.create().show()
    }

    private fun moveToAnotherFolder() {
        val bookShelfGroupDialog = BookShelfGroupDialog(this, true)
        bookShelfGroupDialog.onViewClickListener = object : OnViewClickListener {
            override fun <T> onClickAction(v: View, tag: String, t: T?) {
                var clientArchiveId: String? = null
                var archiveName: String = ""
                when (tag) {
                    BookShelfGroupDialog.TAG_REMOVE_CURRENT_FOLDER -> {
                        clientArchiveId = ""
                    }
                    BookShelfGroupDialog.TAG_CREATE_FOLDER -> {
                        val shelfArchiveItem = SDKSingleton.dbWrapBl.transactionSaveShelfArchiveData(t as String)
                        clientArchiveId = shelfArchiveItem.clientArchiveId
                        archiveName = t
                    }
                    BookShelfGroupDialog.TAG_SELECT_FOLDER -> {
                        t as ShelfArchiveItemEntity
                        clientArchiveId = t.clientArchiveId
                        archiveName = (t as ShelfArchiveItemEntity).archiveName
                    }
                    BookShelfGroupDialog.TAG_CANCEL -> {
                        bookAdapter.doCancelEditMode()
                    }
                }
                if (clientArchiveId == null) {
                    return
                }
                SDKSingleton.dbWrapBl.moveShelfBookToArchive(bookAdapter.selectItemList, clientArchiveId)
                bookAdapter.doCancelEditMode()
                loadLocalData()
                onRefresh(dataRefreshLayout)
                bookShelfGroupDialog.dismiss()
                EventBus.getDefault().post(HomeShelfDataReloadEvent(true))

                when (tag) {
                    BookShelfGroupDialog.TAG_REMOVE_CURRENT_FOLDER -> {
                        ToastUtil.showToastShort(R.string.remove_out_group)
                    }
                    BookShelfGroupDialog.TAG_SELECT_FOLDER,
                    BookShelfGroupDialog.TAG_CREATE_FOLDER,
                    -> {
                        ToastUtil.showToastShort(findString(R.string.params_has_move_folder).format(archiveName))
                    }
                }
            }
        }
        bookShelfGroupDialog.show()
        bookShelfGroupDialog.initAttachedArchiveId(shelfArchiveEntity.clientArchiveId)
    }

    fun showChangeNameDialog(title: String) {
        val createBookGroupDialog = CreateBookGroupDialog(this)
        createBookGroupDialog.show()
        createBookGroupDialog.setTitleText(title)
        createBookGroupDialog.setContentText(shelfArchiveEntity.archiveName)
        createBookGroupDialog.onCreateBookGroupCallBack = object : OnCreateBookGroupCallBack {
            override fun onCallBack(dialog: Dialog, name: String) {
                shelfArchiveEntity.archiveName = name
                SDKSingleton.dbWrapBl.renameArchiveName(name, shelfArchiveEntity.clientArchiveId)
                bookAdapter.doCancelEditMode()
                titleLayout.setTitle(name)
                MainScope().launch(ExceptionHandler.coroutineExceptionHandler) {
                    SDKSingleton.syncBl.syncShelfList()
                }
                EventBus.getDefault().post(HomeShelfDataReloadEvent(true))
            }
        }
    }

    override fun onBackPressed() {
        if (bookAdapter.isEditMode) {
            bookAdapter.doCancelEditMode()
        } else {
            super.onBackPressed()
        }
    }
}
