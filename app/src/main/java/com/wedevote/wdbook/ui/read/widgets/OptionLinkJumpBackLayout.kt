package com.wedevote.wdbook.ui.read.widgets

import android.content.Context
import android.util.AttributeSet
import android.view.View
import android.widget.RelativeLayout
import android.widget.TextView
import com.aquila.lib.base.OnViewClickListener
import com.wedevote.wdbook.R

/***
 * @date 创建时间 2022/2/14 11:07
 * <AUTHOR> W<PERSON>
 * @description
 */
class OptionLinkJumpBackLayout constructor(context: Context, attr: AttributeSet? = null) : RelativeLayout(context, attr), View.OnClickListener {

    var backTextView: TextView
    var stayTextView: TextView
    var backLinearLayout: View

    var onViewClickListener: OnViewClickListener? = null

    var lastReadProgress: ReadProcess? = null

    init {
        View.inflate(context, R.layout.option_link_jump_back_layout, this)
        backTextView = findViewById(R.id.option_link_back_TextView)
        stayTextView = findViewById(R.id.option_link_stay_TextView)
        backLinearLayout = findViewById(R.id.option_link_back_LinearLayout)

        backLinearLayout.setOnClickListener(this)
        stayTextView.setOnClickListener(this)
    }

    fun setBackTitle(content: String) {
        backTextView.text = content
    }

    override fun onClick(v: View?) {
        when (v) {
            backLinearLayout -> {
                onViewClickListener?.onClickAction(v, OptionClickTag.TAG_SCOPE_FINISH, lastReadProgress)
                visibility = View.GONE
            }
            stayTextView -> {
                visibility = View.GONE
            }
        }
    }
}
