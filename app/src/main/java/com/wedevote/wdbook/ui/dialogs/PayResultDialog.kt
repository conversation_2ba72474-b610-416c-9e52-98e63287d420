package com.wedevote.wdbook.ui.dialogs

import android.content.Context
import android.os.Bundle
import android.view.Gravity
import android.view.View
import android.widget.Button
import android.widget.ImageView
import android.widget.TextView
import com.aquila.lib.base.OnViewClickListener
import com.wedevote.wdbook.R
import com.wedevote.wdbook.base.APPConfig
import com.wedevote.wdbook.tools.util.findString
import com.wedevote.wdbook.ui.store.OrderStatusDefine

/***
 * @date 创建时间 2020/9/25 17:38
 * <AUTHOR> <PERSON><PERSON>
 * @description
 */
class PayResultDialog(context: Context) : BaseDialog(context), View.OnClickListener {
    lateinit var labelTextView: TextView
    lateinit var iconImageView: ImageView
    lateinit var okButton: Button

    var onViewClickListener: OnViewClickListener? = null

    var currentStatus: Int = 0
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.dialog_payment_result_layout)
        labelTextView = findViewById(R.id.payment_result_label_TextView)
        iconImageView = findViewById(R.id.payment_result_icon_ImageView)
        okButton = findViewById(R.id.payment_result_OK_Button)

        okButton.setOnClickListener(this)

        configDialog(Gravity.BOTTOM)
        setCancelable(false)
        setCanceledOnTouchOutside(false)
    }

    override fun onClick(v: View?) {
        when (v) {
            okButton -> {
                onViewClickListener?.onClickAction(v, "", currentStatus)
                dismiss()
            }
        }
    }

    fun initResultUI(resultCode: Int) {
        when (resultCode) {
            OrderStatusDefine.ORDER_STATUS_PENDING -> { // 待付款
            }
            OrderStatusDefine.ORDER_STATUS_SUCCEED, OrderStatusDefine.ORDER_STATUS_FINISH -> { // 成功
                if (APPConfig.isCurrentThemeLight()) {
                    iconImageView.setImageResource(R.drawable.ic_pay_result_success)
                } else {
                    iconImageView.setImageResource(R.drawable.ic_pay_result_success_dark)
                }
                labelTextView.text = findString(R.string.dialog_pay_result_success)
            }
            OrderStatusDefine.ORDER_STATUS_FAILURE -> { // 失败
                setFailImg()
                labelTextView.text = findString(R.string.dialog_pay_result_failure)
            }
            OrderStatusDefine.ORDER_STATUS_CANCEL -> { // 取消
                setFailImg()
                labelTextView.text = findString(R.string.dialog_pay_result_cancel)
            }
            OrderStatusDefine.ORDER_STATUS_EXCEPTION -> { // 异常
                setFailImg()
                labelTextView.text = findString(R.string.dialog_pay_result_exception)
            }
            else -> { // 其他问题
                labelTextView.text = "未知问题，状态码：$resultCode"
            }
        }
        currentStatus = resultCode
    }

    private fun setFailImg() {
        if (APPConfig.isCurrentThemeLight()) {
            iconImageView.setImageResource(R.drawable.ic_pay_result_failure)
        } else {
            iconImageView.setImageResource(R.drawable.ic_pay_result_failure_dark)
        }
    }
}
