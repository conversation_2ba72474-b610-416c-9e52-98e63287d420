package com.wedevote.wdbook.ui.read

import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.aquila.lib.base.BaseViewHolder
import com.aquila.lib.widget.view.DotView
import com.wedevote.wdbook.R
import com.wedevote.wdbook.base.APPConfig
import com.wedevote.wdbook.entity.NoteEntity
import com.wedevote.wdbook.tools.util.UnitFormatUtil
import com.wedevote.wdbook.tools.util.findString
import com.wedevote.wdbook.tools.util.parseColor

/***
 *@date 创建时间 1/13/21 5:07 PM
 *<AUTHOR> <PERSON><PERSON> kai
 *@description
 */
class NotesRecycleBinViewHolder(parent: ViewGroup) : BaseViewHolder(parent, R.layout.holder_item_book_notes_recycle_layout) {
    val titleTextView: TextView = itemView.findViewById(R.id.item_notes_recycle_toc_title_TextView)
    val pageNumberDotView: DotView = itemView.findViewById(R.id.item_notes_recycle_page_number_DotView)
    val quoteTextView: TextView = itemView.findViewById(R.id.item_notes_recycle_quote_text_TextView)
    val noteTextView: TextView = itemView.findViewById(R.id.item_notes_recycle_content_TextView)
    val dateTextView: TextView = itemView.findViewById(R.id.item_notes_recycle_date_TextView)
    val lineView: View = itemView.findViewById(R.id.item_notes_recycle_vertical_line_View)
    val deleteButton: Button = itemView.findViewById(R.id.notes_recycle_delete_Button)
    val recoverButton: Button = itemView.findViewById(R.id.notes_recycle_recover_Button)
    val contentLayout: ConstraintLayout = itemView.findViewById(R.id.item_notes_recycle_content_ConstraintLayout)
    lateinit var noteEntity: NoteEntity

    override fun <T> initUIData(t: T) {
        noteEntity = t as NoteEntity
        var highlightColor = parseColor(t.highlightColorType)
        titleTextView.text = t.tocTitle
        pageNumberDotView.setDotColor(highlightColor)
        if (APPConfig.isCurrentThemeLight()) {
            lineView.setBackgroundResource(R.color.text_color_gray_8A8A8A)
        } else {
            lineView.setBackgroundResource(R.color.color_dark_DEDEE3)
        }
        dateTextView.text = UnitFormatUtil.formatDate_ymdhm(t.createTime)
        if (t.noteText.isNullOrEmpty()) {
            quoteTextView.visibility = View.GONE
            noteTextView.visibility = View.VISIBLE
            noteTextView.text = t.summary
        } else {
            noteTextView.visibility = View.VISIBLE
            noteTextView.text = "${t.noteText}"
            quoteTextView.text = "${findString(R.string.quote)}  | ${t.summary}"
        }
    }
}
