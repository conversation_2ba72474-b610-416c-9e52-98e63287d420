package com.wedevote.wdbook.ui.account

import android.annotation.SuppressLint
import android.content.Intent
import android.os.Bundle
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.ImageView
import android.widget.TextView
import com.aquila.lib.base.BaseRecycleAdapter
import com.aquila.lib.base.BaseViewHolder
import com.aquila.lib.base.OnViewClickListener
import com.aquila.lib.layout.SmartRefreshLayout
import com.aquila.lib.layout.SmartRefreshLayout.LayoutParams
import com.aquila.lib.layout.api.RefreshLayout
import com.aquila.lib.layout.listener.OnLoadMoreListener
import com.aquila.lib.layout.listener.OnRefreshListener
import com.aquila.lib.layout.util.DensityUtil
import com.aquila.lib.tools.util.ToastUtil
import com.aquila.lib.widget.group.GroupImageTextLayout
import com.aquila.lib.widget.view.CustomRecyclerView
import com.wedevote.wdbook.R
import com.wedevote.wdbook.base.ExceptionHandler
import com.wedevote.wdbook.base.RootActivity
import com.wedevote.wdbook.base.SDKSingleton
import com.wedevote.wdbook.entity.store.OrderProductEntity
import com.wedevote.wdbook.entity.user.OrderEntity
import com.wedevote.wdbook.exception.ApiException
import com.wedevote.wdbook.tools.util.APPUtil
import com.wedevote.wdbook.tools.util.ImageLoadUtil
import com.wedevote.wdbook.tools.util.IntentConstants
import com.wedevote.wdbook.tools.util.UnitFormatUtil
import com.wedevote.wdbook.tools.util.getLastElement
import com.wedevote.wdbook.tools.util.getPictureRemotePath
import com.wedevote.wdbook.tools.util.initAuthorsStringName
import com.wedevote.wdbook.tools.util.toJsonStr
import com.wedevote.wdbook.ui.AccountSecurityActivity
import com.wedevote.wdbook.ui.dialogs.RegisterTipDialog
import com.wedevote.wdbook.ui.user.HomeMineFragment
import com.wedevote.wdbook.ui.widgets.CommTopTitleLayout
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch


/***
 * @date 创建时间 2020/7/28 14:10
 * <AUTHOR> W.YuLong
 * @description 购买记录的页面
 */
class OrderListActivity : RootActivity(), /*BaseViewInterface,*/ OnRefreshListener,
    OnLoadMoreListener, View.OnClickListener {
    lateinit var topTitleLayout: CommTopTitleLayout
    lateinit var dataRecyclerView: CustomRecyclerView
    lateinit var refreshLayout: SmartRefreshLayout
    lateinit var recycleAdapter: OrderListRecyclerAdapter
    lateinit var emptyLayout: GroupImageTextLayout

    private var lastUpdateTime = 0L
    private val paramsMap = HashMap<String, String>()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_order_list_layout)

        topTitleLayout = findViewById(R.id.order_list_top_TitleLayout)
        dataRecyclerView = findViewById(R.id.order_list_data_RecyclerView)
        refreshLayout = findViewById(R.id.order_list_data_RefreshLayout)
        emptyLayout = findViewById(R.id.purchase_list_empty_icon_Layout)

        recycleAdapter = OrderListRecyclerAdapter()
        dataRecyclerView.adapter = recycleAdapter

        refreshLayout.setOnRefreshListener(this)
        refreshLayout.setOnLoadMoreListener(this)

        getDataFromServer(true)
    }

    override fun onLoadMore(refreshLayout: RefreshLayout) {
        getDataFromServer(false)
    }

    override fun onRefresh(refresh: RefreshLayout?) {
        refreshLayout.isEnableLoadMore = true
        lastUpdateTime = 0L
        getDataFromServer(false)
    }

    private fun getDataFromServer(needShowDialog: Boolean) {
        paramsMap["lastUpdateTime"] = lastUpdateTime.toString()

        MainScope().launch() {
            try {
                if (needShowDialog) {
                    APPUtil.showLoadingDialog(this@OrderListActivity)
                }
                val list = SDKSingleton.userBl.getOrderEntityList(lastUpdateTime)
                list?.let {
                    val mutableList = it.toMutableList()
                    if (lastUpdateTime == 0L) {
                        recycleAdapter.dataList = mutableList
                    } else {
                        recycleAdapter.addDataList(mutableList)
                    }
                    refreshLayout.isEnableLoadMore = it.isNotEmpty()

                    if (it.isNotEmpty()) {
                        lastUpdateTime = list.getLastElement()!!.lastUpdateTime
                    }

                    if (recycleAdapter.itemCount == 0) {
                        emptyLayout.visibility = View.VISIBLE
                        refreshLayout.visibility = View.GONE
                    } else {
                        emptyLayout.visibility = View.GONE
                        refreshLayout.visibility = View.VISIBLE
                    }
                }
                if (needShowDialog) {
                    APPUtil.dismissLoadingDialog(this@OrderListActivity)
                }
                refreshLayout.finishLoadMoreAndRefresh()
            } catch (e: Exception) {
                if (needShowDialog) {
                    APPUtil.dismissLoadingDialog(this@OrderListActivity)
                }
                ExceptionHandler.handleException(e)
                e.printStackTrace()
            }
        }
    }

    override fun onClick(v: View?) {
        when (v) {
            emptyLayout -> {
                onRefresh(refreshLayout)
            }
        }
    }
}

/***
 *@date 创建时间 2020/7/28 15:40
 *<AUTHOR> W.YuLong
 *@description
 */
class OrderListRecyclerAdapter : BaseRecycleAdapter<OrderEntity, OrderItemViewHolder>() {
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): OrderItemViewHolder {
        return OrderItemViewHolder(parent)
    }
}

/***
 *@date 创建时间 2023/5/30
 *<AUTHOR> John.Qian
 *@description
 */
class OrderListItemRecyclerAdapter :
    BaseRecycleAdapter<OrderProductEntity, OrderItemProductViewHolder>() {
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): OrderItemProductViewHolder {
        return OrderItemProductViewHolder(parent)
    }
}

class OrderItemProductViewHolder(parent: ViewGroup) :
    BaseViewHolder(parent, R.layout.holder_item_order_book_list_layout) {
    val bookImageView: ImageView = itemView.findViewById(R.id.item_order_book_list_ImageView)
    var itemEntity: OrderProductEntity? = null

    override fun <T> initUIData(t: T) {
        itemEntity = t as OrderProductEntity
        ImageLoadUtil.loadImageWithDefault(
            bookImageView,
            getPictureRemotePath(t.cover),
            R.drawable.ic_default_book_cover
        )
    }
}

/***
 *@date 创建时间 2020/7/28 15:40
 *<AUTHOR> W.YuLong
 *@description
 */
class OrderItemViewHolder(parent: ViewGroup) :
    BaseViewHolder(parent, R.layout.holder_item_order_list_layout), View.OnClickListener {
    val singleLayout: View = itemView.findViewById(R.id.order_list_single_Layout)
    val multipleLayout: View = itemView.findViewById(R.id.order_list_multiple_Layout)
    val recyclerView: CustomRecyclerView = itemView.findViewById(R.id.item_order_list_RecyclerView)
    val timeTextView: TextView = itemView.findViewById(R.id.order_list_name_TextView)
    val infoTextView: TextView = itemView.findViewById(R.id.item_book_info_name_TextView)
    val coverImageView: ImageView = itemView.findViewById(R.id.item_book_info_cover_ImageView)
    val authorTextView: TextView = itemView.findViewById(R.id.item_order_author_TextView)
    val numberOneTextView: TextView = itemView.findViewById(R.id.item_order_number_one_TextView)
    val copyrightTextView: TextView = itemView.findViewById(R.id.item_order_copyright_TextView)
    val moneyTextView: TextView = itemView.findViewById(R.id.item_order_money_TextView)
    val numberTextView: TextView = itemView.findViewById(R.id.item_order_number_TextView)
    val actualPayTextView: TextView = itemView.findViewById(R.id.item_order_actual_pay_TextView)
    val receiptButton: Button = itemView.findViewById(R.id.item_order_get_receipt_Button)
    var itemEntity: OrderEntity? = null

    init {
        itemView.setOnClickListener(this)
        receiptButton.setOnClickListener(this)
    }

    @SuppressLint("ClickableViewAccessibility")
    override fun <T> initUIData(t: T) {
        itemEntity = t as OrderEntity
        timeTextView.text =
            itemView.context.getString(R.string.order_time) + UnitFormatUtil.formatDate_ymdhm(t.lastUpdateTime * 1000)
        actualPayTextView.text =
            itemView.context.getString(R.string.actual_pay_money) + UnitFormatUtil.formatPrice(
                t.currency,
                t.totalAmount
            )
        if (!t.products.isNullOrEmpty() && t.products!!.size > 1) {
            multipleLayout.visibility = View.VISIBLE
            singleLayout.visibility = View.GONE
            numberTextView.text = String.format(
                itemView.context.getString(R.string.sum_items),
                t.products!!.size.toString()
            )
            recyclerView.adapter = OrderListItemRecyclerAdapter().apply {
                t.products?.let {
                    dataList = it.toMutableList()
                }
            }
            recyclerView.setOnTouchListener(View.OnTouchListener { v, event ->
                when (event.action) {
                    MotionEvent.ACTION_UP -> {
                        //判断是否继续传递信号
                        if (recyclerView.scrollState == 0) {
                            itemView.performClick()
                        }
                    }
                }
                false
            })
        } else {
            multipleLayout.visibility = View.GONE
            singleLayout.visibility = View.VISIBLE
            var orderDeductedProduct = if (t.type == 1) {
                t.giftCardVo
            } else {
                t.products!![0]
            }

            if (t.type == 1) {
                coverImageView.layoutParams.height = DensityUtil.dp2px(40f)
                coverImageView.scaleType = ImageView.ScaleType.FIT_XY
            } else {
                coverImageView.layoutParams.height = LayoutParams.WRAP_CONTENT
                coverImageView.scaleType = ImageView.ScaleType.FIT_START
            }

            ImageLoadUtil.loadImageWithDefault(
                coverImageView,
                getPictureRemotePath(orderDeductedProduct?.cover),
                R.drawable.ic_default_book_cover
            )
            copyrightTextView.text = orderDeductedProduct?.publisherName ?: ""
            val initAuthorsStringName = initAuthorsStringName(orderDeductedProduct?.authorList)
            if (initAuthorsStringName.isNotEmpty()) {
                authorTextView.visibility = View.VISIBLE
                authorTextView.text = initAuthorsStringName
            } else {
                authorTextView.visibility = View.GONE
            }
            numberOneTextView.text =
                String.format(
                    itemView.context.getString(R.string.sum_items),
                    orderDeductedProduct?.count.toString()
                )
            moneyTextView.text = if (t.type == 1) {
                infoTextView.text = itemView.context.getString(R.string.wd_book_gift_card)
                UnitFormatUtil.formatPrice(t.currency, orderDeductedProduct?.amount ?: 0F)
            } else {
                infoTextView.text = orderDeductedProduct?.title ?: ""
                UnitFormatUtil.formatPrice(t.currency, orderDeductedProduct?.price ?: 0F)
            }
        }
    }

    override fun onClick(v: View) {
        if (itemView == v) {
            itemEntity?.let {
                val intent = Intent(v.context, OrderDetailActivity::class.java)
                intent.putExtra(IntentConstants.EXTRA_OrderListItemBean, it.toJsonStr())
                v.context.startActivity(intent)
            }
        } else if (receiptButton == v) {
            if (!HomeMineFragment.userInfoEntity?.email.isNullOrEmpty()) {
                //获取收据
                MainScope().launch {
                    try {
                        APPUtil.showLoadingDialog(itemView.context)
                        itemEntity?.let {
                            SDKSingleton.userBl.getOrderReceipt(it.orderId)
                        }
                        APPUtil.showTipDialog(
                            itemView.context,
                            itemView.context.getString(R.string.receit_has_send_to_email),
                            String.format(
                                itemView.context.getString(
                                    R.string.receipt_has_send_to
                                ), HomeMineFragment.userInfoEntity?.email
                            )
                        )
                        APPUtil.dismissLoadingDialog(itemView.context)
                    } catch (exception: Throwable) {
                        APPUtil.dismissLoadingDialog(itemView.context)
                        if (exception is ApiException) {
                            ToastUtil.showToastShort(exception.message)
                        } else {
                            ExceptionHandler.handleException(exception)
                        }
                    }
                }
            } else {
                var tipDialog = RegisterTipDialog(itemView.context)
                tipDialog.show()
                tipDialog.setTitleText(itemView.context.getString(R.string.please_bind_email_first))
                tipDialog.setCancelButtonVisible(true)
                tipDialog.setLeftButtonText(itemView.context.getString(R.string.later))
                tipDialog.setRightButtonText(itemView.context.getString(R.string.bind_now))
                tipDialog.setContentText(itemView.context.getString(R.string.bind_email_first_content))
                tipDialog.onViewClickListener = object : OnViewClickListener {
                    override fun <T> onClickAction(v: View, str: String, t: T?) {
                        itemView.context.startActivity(
                            Intent(
                                itemView.context,
                                AccountSecurityActivity::class.java
                            )
                        )
                    }
                }
            }
        }
    }
}
