package com.wedevote.wdbook.ui.user.notification

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import android.view.View.OnClickListener
import android.webkit.WebResourceRequest
import android.webkit.WebSettings.LayoutAlgorithm
import android.webkit.WebView
import android.webkit.WebViewClient
import android.widget.ImageView
import androidx.webkit.WebSettingsCompat
import androidx.webkit.WebViewFeature
import com.aquila.lib.log.KLog
import com.aquila.lib.tools.util.ToastUtil
import com.wedevote.wdbook.R
import com.wedevote.wdbook.base.ExceptionHandler
import com.wedevote.wdbook.base.RootActivity
import com.wedevote.wdbook.base.SDKSingleton
import com.wedevote.wdbook.entity.notification.NotificationDetailEntity
import com.wedevote.wdbook.entity.notification.NotificationType
import com.wedevote.wdbook.tools.util.IntentConstants
import com.wedevote.wdbook.tools.util.findString
import com.wedevote.wdbook.ui.home.microwidget.DeepLinkUtils
import com.wedevote.wdbook.ui.widgets.CommTopTitleLayout
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch

/***
 * @date 创建时间 2022/5/16 16:32
 * <AUTHOR> W.YuLong
 * @description 点击消息进入的详情页
 */
class NotificationDetailActivity : RootActivity(), OnClickListener {
    lateinit var topTitleLayout: CommTopTitleLayout
    lateinit var shareImageView: ImageView
    lateinit var webView: WebView
    
    //    lateinit var contentTextView: oTextView
    var notificationDetailEntity: NotificationDetailEntity? = null
    
    var notificationId = 0L
    var notificationType = NotificationType.PLATFORM.type
    
    companion object {
        var callBackListener: OnNotificationReadCallback? = null
        
        fun gotoNotificationDetail(
            context: Context,
            notificationId: Long,
            type: Int,
            listener: OnNotificationReadCallback?
        ) {
            val intent = Intent(context, NotificationDetailActivity::class.java)
            intent.putExtra(IntentConstants.EXTRA_NotificationId, notificationId)
            intent.putExtra(IntentConstants.EXTRA_NotificationType, type)
            context.startActivity(intent)
            callBackListener = listener
        }
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_notification_message_detail_layout)
        initViewFromXML()
        configWebViewSetting()
        webView.webViewClient = webClient
        shareImageView.setOnClickListener(this)
        shareImageView.visibility = View.VISIBLE
        
        notificationId = intent.getLongExtra(IntentConstants.EXTRA_NotificationId, 0L)
        notificationType = intent.getIntExtra(
            IntentConstants.EXTRA_NotificationType,
            NotificationType.PLATFORM.type
        )
        getDataFromServer()
    }
    
    fun configWebViewSetting() {
        //支持javascript
        webView.settings.setJavaScriptEnabled(true);
        // 设置可以支持缩放
        webView.settings.setSupportZoom(true);
        // 设置出现缩放工具
        webView.settings.setBuiltInZoomControls(true);
        //扩大比例的缩放
        webView.settings.setUseWideViewPort(true);
        webView.settings.setSupportZoom(false);
        //自适应屏幕
        webView.settings.setLayoutAlgorithm(LayoutAlgorithm.SINGLE_COLUMN);
        webView.settings.setLoadWithOverviewMode(true);
        
        if (WebViewFeature.isFeatureSupported(WebViewFeature.FORCE_DARK)) {
            val isDark = !SDKSingleton.appBl.isCurrentThemeLight()
            WebSettingsCompat.setForceDark(
                webView.settings,
                if (isDark) WebSettingsCompat.FORCE_DARK_ON else WebSettingsCompat.FORCE_DARK_OFF
            )
        }
    }
    
    fun initViewFromXML() {
        topTitleLayout = findViewById(R.id.notification_message_detail_top_title_layout)
        shareImageView = findViewById(R.id.notification_message_detail_share_ImageView)
        webView = findViewById(R.id.notification_message_detail_data_WebView)
//        contentTextView = findViewById(id.notification_message_detail_article_TextView)
    }
    
    val webClient = object : WebViewClient() {
        override fun shouldOverrideUrlLoading(
            view: WebView?,
            request: WebResourceRequest?
        ): Boolean {
    
            KLog.d("deeplink = ${request!!.url}")
            if (DeepLinkUtils.parseDeepLink(this@NotificationDetailActivity, request!!.url.toString())) {
                return true
            }
            return super.shouldOverrideUrlLoading(view, request)
        }
    }
    
    
    fun getDataFromServer() {
        MainScope().launch(ExceptionHandler.coroutineExceptionHandler) {
            SDKSingleton.userBl.getNotificationDetail(notificationId, notificationType)
                ?.let { entity ->
                    notificationDetailEntity = entity
//                    topTitleLayout.setTitle(getMessagePlatformTitle(entity.type))
                    webView.loadDataWithBaseURL("", entity.content, "text/html", "UTF-8", "")
                    shareImageView.visibility =
                        if (entity.allowShare == 1) View.VISIBLE else View.GONE
                    SDKSingleton.userBl.makeLocalNotificationReadById(notificationId)
    
                    callBackListener?.onReadCallback(notificationId)
                }
        }
    }
    
    
    fun getMessagePlatformTitle(type: Int): String {
        return when (type) {
            2 -> findString(R.string.notification_type_recommend)
            3 -> findString(R.string.notification_type_todolist)
            else -> findString(R.string.notification_type_platform)
        }
    }
    
    override fun onClick(v: View?) {
        when (v) {
            shareImageView -> {
                if (notificationDetailEntity != null) {
                    val intent = Intent(Intent.ACTION_SEND)
                    intent.type = "text/html"
                    intent.putExtra(Intent.EXTRA_TEXT, notificationDetailEntity!!.url)
                    intent.putExtra(Intent.EXTRA_SUBJECT, findString(R.string.select_share_app))
                    intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
                    startActivity(
                        Intent.createChooser(
                            intent,
                            findString(R.string.select_share_app)
                        )
                    )
                } else {
                    ToastUtil.showToastShort("未获取到消息详情内容")
                }
            }
        }
    }
    
    override fun onDestroy() {
        super.onDestroy()
        callBackListener = null
    }
    
}

interface OnNotificationReadCallback {
    fun onReadCallback(notificationId: Long)
}