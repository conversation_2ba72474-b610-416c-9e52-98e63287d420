package com.wedevote.wdbook.ui.store

import android.view.ViewGroup
import android.widget.TextView
import com.aquila.lib.base.BaseRecycleAdapter
import com.aquila.lib.base.BaseViewHolder
import com.wedevote.wdbook.R
import com.wedevote.wdbook.entity.base.IStringInterface
import com.wedevote.wdbook.entity.store.StoreCategoryEntity
import com.wedevote.wdbook.tools.interfaces.OnItemClickListener

/***
 * @date 创建时间 2021/12/9 15:51
 * <AUTHOR> W<PERSON><PERSON><PERSON><PERSON>
 * @description
 */
class FlexboxDataAdapter<T>(var viewTypeEnum: FlexboxViewType = FlexboxViewType.BOOK_LIST_FILTER) : BaseRecycleAdapter<T, BaseViewHolder>() {
    var onItemClickListener: OnItemClickListener? = null
    var selectPosition = 0
    var enableSelectItem: Boolean = true

    enum class FlexboxViewType(var value: Int) {
        BOOK_LIST_FILTER(0),
        SEARCH_HISTORY(1),
        SUGGEST_KEYWORDS(2)
    }

    override fun getItemViewType(position: Int): Int {
        return viewTypeEnum.value
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BaseViewHolder {
        return when (viewType) {
            FlexboxViewType.SEARCH_HISTORY.value,
            FlexboxViewType.SUGGEST_KEYWORDS.value -> {
                SearchHistoryViewHolder(parent)
            }
            else -> {
                FlexboxItemViewHolder(parent)
            }
        }
    }

    override fun onBindViewHolder(holder: BaseViewHolder, position: Int) {
        super.onBindViewHolder(holder, position)
        if (enableSelectItem) {
            holder.onItemSelected(position == selectPosition)
        }
        holder.itemView.setOnClickListener {
            selectPosition = position
            onItemClickListener?.onItemClick(position, "", dataList?.get(position)!!)
            notifyDataSetChanged()
        }
    }
}

/***
 *@date 创建时间 2021/12/16 19:16
 *<AUTHOR> W.YuLong
 *@description
 */
class FlexboxItemViewHolder(parent: ViewGroup) : BaseViewHolder(parent, R.layout.cell_category_item_layout) {
    val nameTextView: TextView = itemView as TextView
    override fun <T> initUIData(t: T) {
        if (t is String) {
            nameTextView.text = t
        } else if (t is StoreCategoryEntity) {
            nameTextView.text = t.categoryName
        }
    }

    override fun onItemSelected(isSelected: Boolean) {
        itemView.isSelected = isSelected
    }
}

/***
 *@date 创建时间 2021/12/16 19:16
 *<AUTHOR> W.YuLong
 *@description
 */
class SearchHistoryViewHolder(parent: ViewGroup) : BaseViewHolder(parent, R.layout.cell_search_recommend_history_item_layout) {
    val nameTextView: TextView = itemView as TextView
    override fun <T> initUIData(t: T) {
        if (t is String) {
            nameTextView.text = t
        } else if (t is IStringInterface) {
            nameTextView.text = t.getText()
        }
    }

    override fun onItemSelected(isSelected: Boolean) {
        itemView.isSelected = isSelected
    }
}
