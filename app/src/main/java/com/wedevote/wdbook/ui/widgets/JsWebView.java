package com.wedevote.wdbook.ui.widgets;

import android.content.Context;
import android.content.res.Resources;
import android.os.Build;
import android.util.AttributeSet;
import android.webkit.WebSettings;
import android.webkit.WebView;


/***
 *@date 创建时间 2020/7/24 16:25
 *<AUTHOR> <PERSON><PERSON>
 *@description
 */
public final class JsWebView extends WebView {


    public JsWebView(Context context) {
        super(context);
        initParams();
    }

    public JsWebView(Context context, AttributeSet attrs) {
        this(context, attrs, Resources.getSystem().getIdentifier("webViewStyle", "attr", "android"));
    }

    public JsWebView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initParams();
    }

    public void initParams() {
        WebSettings settings = this.getSettings();
        settings.setJavaScriptEnabled(true);
//        settings.setUserAgentString(settings.getUserAgentString() + JsConstants.OS_NAME);
        settings.setAllowFileAccessFromFileURLs(true);
        settings.setAllowUniversalAccessFromFileURLs(true);
        settings.setLoadWithOverviewMode(true);
        settings.setDomStorageEnabled(true);
        settings.setAllowFileAccess(true);
        settings.setAllowContentAccess(true);
        settings.setDisplayZoomControls(false);
        settings.setLayoutAlgorithm(android.webkit.WebSettings.LayoutAlgorithm.SINGLE_COLUMN);
//        settings.setAppCacheEnabled(true);
//        settings.setAppCachePath(DataPathUtil.getWebCachePath());
        settings.setCacheMode(WebSettings.LOAD_NO_CACHE);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            settings.setMixedContentMode(WebSettings.MIXED_CONTENT_ALWAYS_ALLOW);
        }
    }


}
