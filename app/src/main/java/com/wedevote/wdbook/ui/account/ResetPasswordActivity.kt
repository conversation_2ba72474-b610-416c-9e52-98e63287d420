package com.wedevote.wdbook.ui.account

import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.view.View
import android.view.inputmethod.InputMethodManager
import android.widget.FrameLayout
import android.widget.TextView
import com.aquila.lib.tools.singleton.SPSingleton.Companion.get
import com.wedevote.wdbook.R
import com.wedevote.wdbook.base.RootActivity
import com.wedevote.wdbook.tools.util.IntentConstants
import com.wedevote.wdbook.tools.util.NetWorkUtils
import com.wedevote.wdbook.tools.util.SPKeyDefine
import com.wedevote.wdbook.ui.account.register.RegisterLayoutManager

/***
 *@date 创建时间 2023.04.13
 *<AUTHOR> <PERSON>.<PERSON>
 *@description
 */
class ResetPasswordActivity : RootActivity(), View.OnClickListener {
    private lateinit var backView: View
    private lateinit var contentLayout: FrameLayout
    private lateinit var nextTextView: TextView
    private lateinit var titleTextView: TextView
    private var registerLayoutManager: RegisterLayoutManager? = null

    private fun findViewByIdFromXML() {
        backView = findViewById(R.id.register_back_View)
        contentLayout = findViewById(R.id.register_content_layout)
        nextTextView = findViewById(R.id.register_next_TextView)
        nextTextView.setText(R.string.next_step)
        titleTextView = findViewById<TextView>(R.id.register_login_title_TextView)
        titleTextView.setText(R.string.reset_password)
    }

    public override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        val rootView = layoutInflater.inflate(R.layout.activity_reset_password, null)
        setContentView(rootView)
        findViewByIdFromXML()
        setViewCLickListeners()
        registerLayoutManager = RegisterLayoutManager(this)
        registerLayoutManager!!.isResetPassword = true
        contentLayout.addView(registerLayoutManager!!.rootView)
        val bFormMail = intent.getBooleanExtra(IntentConstants.EXTRA_fromEmail, false)
        if (bFormMail) {
            registerLayoutManager?.setRegisterType(RegisterLayoutManager.REGISTER_MODE_EMAIL)
        }else {
            registerLayoutManager?.setRegisterType(RegisterLayoutManager.REGISTER_MODE_MOBILE)
        }
        registerLayoutManager!!.setOnButtonStateChangeListener {
            nextTextView.isEnabled = it
        }
        var account = intent.getStringExtra(IntentConstants.EXTRA_account)

        Handler().postDelayed({
            registerLayoutManager?.accountEditText?.requestFocus()
            (getSystemService(INPUT_METHOD_SERVICE) as InputMethodManager).showSoftInput(registerLayoutManager?.accountEditText, InputMethodManager.SHOW_IMPLICIT)
            if (!account.isNullOrEmpty()) {
                registerLayoutManager?.accountEditText?.setText(account)
            }
            registerLayoutManager?.accountEditText?.setSelection(registerLayoutManager?.accountEditText!!.text.length)
        },200)

        registerLayoutManager!!.passwordEditText.hint = getString(R.string.please_input_new_password)
        registerLayoutManager!!.passwordEditTextTwo.hint = getString(R.string.please_input_new_password_again)
    }

    private fun setViewCLickListeners() {
        backView.setOnClickListener(this)
        nextTextView.setOnClickListener(this)
    }

    override fun onClick(v: View) {
        if (v === backView) {
            onBackPressed()
        } else if (v === nextTextView) {
            if (!NetWorkUtils.isNetworkAvailable()) {
                NetWorkUtils.showTipDialog(this, getString(R.string.no_network_connect))
                return
            }
            if (registerLayoutManager!!.checkingPassword) {
                registerLayoutManager!!.resetPassword{
                    finish()
                }
            } else if (registerLayoutManager!!.checkInputFormat()) {
                registerLayoutManager!!.checkVerificationCode{
                    goNextStep()
                }
            }
        }
    }

    private fun goNextStep() {
        if (registerLayoutManager!!.registerNextStep()) {
            nextTextView.setText(R.string.reset_password)
            registerLayoutManager!!.checkRegisterButtonState()
        }
    }

    override fun onBackPressed() {
        if (registerLayoutManager!!.checkingPassword) {
            registerLayoutManager!!.registerPrevStep()
            nextTextView.setText(R.string.next_step)
            registerLayoutManager!!.checkNextButtonState()
            registerLayoutManager!!.verifyCodeTitleTextView!!.setText(R.string.input_verify_code)
        } else {
            super.onBackPressed()
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == IntentConstants.INTENT_RESULT_COUNTRY_CODE && data != null) {
            val countryCode = data.getStringExtra(IntentConstants.Extra_countryCode)
            registerLayoutManager!!.setCountryCodeText(countryCode)
            get().putString(SPKeyDefine.SP_defaultCountry, countryCode)
        }
    }
}