package com.wedevote.wdbook.ui.dialogs

import android.app.Dialog
import android.content.Context
import android.os.Bundle
import android.view.Gravity
import android.view.ViewGroup
import android.view.WindowManager
import android.widget.ImageView
import android.widget.TextView
import com.aquila.lib.base.BaseRecycleAdapter
import com.aquila.lib.base.BaseViewHolder
import com.wedevote.wdbook.tools.util.ImageLoadUtil
import com.aquila.lib.widget.view.AdaptiveImageView
import com.aquila.lib.widget.view.CustomRecyclerView
import com.wedevote.wdbook.R
import com.wedevote.wdbook.entity.store.AccountedProductsEntity
import com.wedevote.wdbook.tools.util.PictureUtil
import com.wedevote.wdbook.tools.util.UnitFormatUtil

/***
 * @date 创建时间 2020/9/24 20:43
 * <AUTHOR> <PERSON><PERSON>
 * @description 多本书的订单列表页
 */
class MultipleConfirmItemListDialog(context: Context) : Dialog(context) {
    lateinit var titleTextView: TextView
    lateinit var dismissImageView: ImageView
    lateinit var dataRecyclerView: CustomRecyclerView

    lateinit var purchaseAdapter: MultipleConfirmItemAdapter
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.dialog_mulpitle_order_confirm_list_layout)
        initViewFromXML()
        configDialog()
        purchaseAdapter = MultipleConfirmItemAdapter()
        dataRecyclerView.adapter = purchaseAdapter

        dismissImageView.setOnClickListener {
            dismiss()
        }
    }

    private fun initViewFromXML() {
        titleTextView = findViewById(R.id.multiple_confirm_title_TextView)
        dismissImageView = findViewById(R.id.multiple_confirm_close_ImageView)
        dataRecyclerView = findViewById(R.id.multiple_confirm_data_RecyclerView)
    }

    protected fun configDialog() {
        val wl = window!!.attributes
        wl.gravity = Gravity.BOTTOM // 设置重力
        wl.width = WindowManager.LayoutParams.MATCH_PARENT
        wl.height = WindowManager.LayoutParams.WRAP_CONTENT
        window!!.setWindowAnimations(com.aquila.lib.dialog.R.style.bottomDialogWindowAnim)
        window!!.attributes = wl
        window!!.setBackgroundDrawableResource(android.R.color.transparent)
    }

    fun initDataList(purchasedProductList: ArrayList<AccountedProductsEntity>?) {
        purchaseAdapter.dataList = purchasedProductList
    }
}

/***
 *@date 创建时间 2020/9/24 21:24
 *<AUTHOR> W.YuLong
 *@description
 */
class MultipleConfirmItemAdapter : BaseRecycleAdapter<AccountedProductsEntity, MultipleConfirmViewHolder>() {
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): MultipleConfirmViewHolder {
        return MultipleConfirmViewHolder(parent)
    }
}

/***
 *@date 创建时间 2020/9/24 21:24
 *<AUTHOR> W.YuLong
 *@description
 */
class MultipleConfirmViewHolder(parent: ViewGroup) : BaseViewHolder(parent, R.layout.item_holder_single_confirm_product_layout) {
    val coverImageView: AdaptiveImageView = itemView.findViewById(R.id.order_confirm_single_book_cover_ImageView)
    val titleTextView: TextView = itemView.findViewById(R.id.order_confirm_single_book_name_TextView)
    val priceTextView: TextView = itemView.findViewById(R.id.order_confirm_single_book_price_TextView)

    override fun <T> initUIData(t: T) {
        t as AccountedProductsEntity
        PictureUtil.loadImage(coverImageView, t.cover)
        titleTextView.text = t.title
        priceTextView.text = "${UnitFormatUtil.getCurrencySymbol(t.currency)}${t.price + t.activityAmount + t.couponAmount}"
    }
}
