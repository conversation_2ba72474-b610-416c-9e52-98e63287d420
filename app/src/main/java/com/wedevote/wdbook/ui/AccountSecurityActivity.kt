package com.wedevote.wdbook.ui

import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.view.View
import android.widget.LinearLayout
import android.widget.RelativeLayout
import android.widget.TextView
import com.aquila.lib.base.OnViewClickListener
import com.aquila.lib.widget.group.GroupImageTextLayout
import com.wedevote.wdbook.R
import com.wedevote.wdbook.base.ExceptionHandler
import com.wedevote.wdbook.base.RootActivity
import com.wedevote.wdbook.base.SDKSingleton
import com.wedevote.wdbook.entity.UserInfoEntity
import com.wedevote.wdbook.tools.event.OnChangeAccountSuccess
import com.wedevote.wdbook.tools.util.IntentConstants
import com.wedevote.wdbook.tools.util.UnitFormatUtil
import com.wedevote.wdbook.ui.account.BindPhoneOrEmailActivity
import com.wedevote.wdbook.ui.account.DeviceManagerActivity
import com.wedevote.wdbook.ui.account.OnLoginResultCallBack
import com.wedevote.wdbook.ui.account.SSOLoginActivity
import com.wedevote.wdbook.ui.account.register.RegisterLayoutManager
import com.wedevote.wdbook.ui.dialogs.RegisterTipDialog
import com.wedevote.wdbook.ui.user.DeleteAccountActivity
import com.wedevote.wdbook.ui.user.HomeMineFragment
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

/***
 *@date 创建时间 2023/5/8
 *<AUTHOR> John.Qian
 *@description  个人中心-账号与安全页面
 */
class AccountSecurityActivity : RootActivity(), View.OnClickListener {

    private lateinit var deleteAccountLayout: RelativeLayout
    private lateinit var deviceManagerLinearLayout: LinearLayout
    private lateinit var bindEmailLayout: GroupImageTextLayout
    private lateinit var bindPhoneLayout: GroupImageTextLayout
    private lateinit var changePasswordLayout: GroupImageTextLayout
    private lateinit var bindEmailTextView: TextView
    private lateinit var bindPhoneTextView: TextView
    private lateinit var unbindEmailTextView: TextView
    private lateinit var unbindPhoneTextView: TextView

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_account_security_layout)
        initViewFromXML()
        setViewListeners()
        if (HomeMineFragment.userInfoEntity != null) {
            initUI(HomeMineFragment.userInfoEntity!!)
        } else {
            initData()
        }
    }

    private fun initData() {
        MainScope().launch(ExceptionHandler.coroutineExceptionHandler) {
            SDKSingleton.userBl.getUserInfoEntity(true)?.let {
                initUI(it)
            }
        }
    }

    private fun initUI(userInfoEntity: UserInfoEntity) {
        if (SDKSingleton.sessionBl.isLogin()) {
            deleteAccountLayout.visibility = View.VISIBLE
        } else {
            deleteAccountLayout.visibility = View.GONE
        }
        if (!userInfoEntity.email.isNullOrEmpty()) {
            bindEmailTextView.visibility = View.VISIBLE
            unbindEmailTextView.visibility = View.GONE
            bindEmailTextView.text = userInfoEntity.email
        } else {
            bindEmailTextView.visibility = View.GONE
            unbindEmailTextView.visibility = View.VISIBLE
        }
        if (!userInfoEntity.mobile.isNullOrEmpty()) {
            bindPhoneTextView.visibility = View.VISIBLE
            unbindPhoneTextView.visibility = View.GONE
            bindPhoneTextView.text = UnitFormatUtil.maskPhoneNumber(userInfoEntity.mobile!!)
        } else {
            bindPhoneTextView.visibility = View.GONE
            unbindPhoneTextView.visibility = View.VISIBLE
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun OnChangeAccountSuccessEvent(event: OnChangeAccountSuccess) {
        if (event.isResetPassword) {
            finish()
        } else {
            initData()
        }
    }

    private fun setViewListeners() {
        deleteAccountLayout.setOnClickListener(this)
        deviceManagerLinearLayout.setOnClickListener(this)
        bindEmailLayout.setOnClickListener(this)
        bindPhoneLayout.setOnClickListener(this)
        changePasswordLayout.setOnClickListener(this)
    }

    private fun initViewFromXML() {
        deleteAccountLayout = findViewById(R.id.setting_delete_account_Layout)
        deviceManagerLinearLayout = findViewById(R.id.account_security_device_manager_LinearLayout)
        bindEmailLayout = findViewById(R.id.account_security_bind_email_layout)
        bindPhoneLayout = findViewById(R.id.account_security_bind_phone_layout)
        changePasswordLayout = findViewById(R.id.account_security_change_password_layout)
        bindEmailTextView = findViewById(R.id.account_security_bind_email_TextView)
        bindPhoneTextView = findViewById(R.id.account_security_bind_phone_TextView)
        unbindEmailTextView = findViewById(R.id.account_security_unbind_email_TextView)
        unbindPhoneTextView = findViewById(R.id.account_security_unbind_phone_TextView)
    }

    override fun onClick(v: View?) {
        if (SDKSingleton.sessionBl.isLogin()) {
            when (v) {
                deleteAccountLayout -> {
                    startActivity(
                        Intent(
                            this@AccountSecurityActivity,
                            DeleteAccountActivity::class.java
                        )
                    )
                }
                deviceManagerLinearLayout -> {
                    startActivity(Intent(this@AccountSecurityActivity, DeviceManagerActivity::class.java))
                }
                bindEmailLayout -> {
                    val email = bindEmailTextView.text.trim().toString()
                    if (!email.isEmpty()) {
                        val intent = Intent(this@AccountSecurityActivity, SecurityVerifyActivity::class.java)
                        intent.putExtra(
                            IntentConstants.EXTRA_Account_Security_Type,
                            RegisterLayoutManager.BIND_MODE_EMAIL
                        )
                        startActivity(intent)
                    } else {
                        val intent = Intent(
                            this@AccountSecurityActivity,
                            BindPhoneOrEmailActivity::class.java
                        )
                        intent.putExtra(IntentConstants.EXTRA_Account_Security_Type, RegisterLayoutManager.BIND_MODE_EMAIL)
                        startActivity(intent)
                    }
                }
                bindPhoneLayout -> {
                    val phone = bindPhoneTextView.text.trim().toString()
                    if (!phone.isEmpty()) {
                        val intent = Intent(this@AccountSecurityActivity, SecurityVerifyActivity::class.java)
                        intent.putExtra(
                            IntentConstants.EXTRA_Account_Security_Type,
                            RegisterLayoutManager.BIND_MODE_MOBILE
                        )
                        startActivity(intent)
                    } else {
                        val intent = Intent(
                            this@AccountSecurityActivity,
                            BindPhoneOrEmailActivity::class.java
                        )
                        intent.putExtra(IntentConstants.EXTRA_Account_Security_Type, RegisterLayoutManager.BIND_MODE_MOBILE)
                        startActivity(intent)
                    }

                }
                changePasswordLayout -> {
                    val intent = Intent(this@AccountSecurityActivity, SecurityVerifyActivity::class.java)
                    intent.putExtra(
                        IntentConstants.EXTRA_Account_Security_Type,
                        RegisterLayoutManager.ACCOUNT_MODE_CHANGE_PASSWORD
                    )
                    startActivity(intent)
                }
            }
        } else {
            bindEmailTextView.text = ""
            bindPhoneTextView.text = ""
            SSOLoginActivity.checkAndGotoLogin(
                this,
                callBack = object : OnLoginResultCallBack {
                    override fun onLoginResult(isSuccess: Boolean) {
                        if (isSuccess) {
                            Handler().postDelayed({initData()},1000)
                        }
                    }
                },
            )
        }

    }

    private fun showConfirmDialog(title: String, content: String, function: () -> Unit) {
        var tipDialog = RegisterTipDialog(this)
        tipDialog.show()
        tipDialog.setTitleText(title)
        tipDialog.setCancelButtonVisible(true)
        tipDialog.setRightButtonText(getString(R.string.change_bind))
        tipDialog.setContentText(content)
        tipDialog.onViewClickListener = object : OnViewClickListener {
            override fun <T> onClickAction(v: View, str: String, t: T?) {
                function.invoke()
            }
        }
    }
}
