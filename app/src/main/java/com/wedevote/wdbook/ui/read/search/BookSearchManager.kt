package com.wedevote.wdbook.ui.read.search

import android.annotation.SuppressLint
import android.content.Context
import com.aquila.lib.tools.util.ToastUtil
import com.wedevote.wdbook.R
import com.wedevote.wdbook.base.APPConfig
import com.wedevote.wdbook.base.ExceptionHandler
import com.wedevote.wdbook.base.SDKSingleton
import com.wedevote.wdbook.entity.search.SearchResultChapter
import com.wedevote.wdbook.entity.search.SearchResultSentence
import com.wedevote.wdbook.entity.store.SearchItemEntity
import com.wedevote.wdbook.tools.event.OnBookSearchNavigateEvent
import com.wedevote.wdbook.tools.event.OnBookSearchNavigateResultEvent
import com.wedevote.wdbook.tools.util.NetWorkUtils
import com.wedevote.wdbook.ui.read.lib.EPubBook
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus

/**
 * 书籍搜索管理器
 * 将BookSearchActivity的数据处理和存储逻辑抽取到单独的类
 */
class BookSearchManager {
    // 搜索数据
    var currentPage = 1
    var totalPage = 99
    var currentSearchEntity = SearchItemEntity()
    var resourceId: String = ""

    // 搜索结果
    private var searchResults: ArrayList<Any>? = null
    private var selectedPosition: Int = -1
    private var keywords: String = ""
    private var currentResourceId: String = ""
    private var savedCurrentPage: Int = 1
    private var savedTotalPage: Int = 99
    // 添加用于保存滚动位置的变量
    private var savedFirstVisibleItemPosition: Int = -1
    private var savedFirstVisibleItemOffset: Int = 0

    // 加载历史记录的条数
    val maxSearchHistoryCount = 8L

    // 结果适配器
    var resultDataAdapter: BookSearchResultAdapter? = null

    // 是否可加载更多
    var isEnableLoadMore: Boolean = true

    // 增加私有变量来保存isEnableLoadMore状态
    private var savedIsEnableLoadMore: Boolean = true

    companion object {
        // 单例实例
        private var instance: BookSearchManager? = null

        fun getInstance(): BookSearchManager {
            if (instance == null) {
                instance = BookSearchManager()
            }
            return instance!!
        }
    }

    /**
     * 清除保存的数据
     */
    fun clearSavedData() {
        searchResults = null
        selectedPosition = -1
        keywords = ""
        currentResourceId = ""
        savedCurrentPage = 1
        savedTotalPage = 99
        isEnableLoadMore = true  // 重置isEnableLoadMore状态
        // 重置滚动位置
        savedFirstVisibleItemPosition = -1
        savedFirstVisibleItemOffset = 0
    }

    /**
     * 保存当前数据
     * @param firstVisibleItemPosition 第一个可见项的位置. 如果是 -2，表示下次恢复时需要居中。
     * @param firstVisibleItemOffset 第一个可见项的偏移量
     */
    fun saveCurrentData(firstVisibleItemPosition: Int = -1, firstVisibleItemOffset: Int = 0) {
        resultDataAdapter?.let { adapter ->
            if (adapter.dataList != null && adapter.dataList!!.isNotEmpty()) {
                searchResults = ArrayList(adapter.dataList!!)
                selectedPosition = adapter.selectedPosition
                keywords = currentSearchEntity.keyWords
                currentResourceId = resourceId
                savedCurrentPage = currentPage
                savedTotalPage = totalPage
                savedIsEnableLoadMore = isEnableLoadMore  // 保存加载更多状态
                // 保存滚动位置
                savedFirstVisibleItemPosition = firstVisibleItemPosition
                savedFirstVisibleItemOffset = firstVisibleItemOffset
            } else {
                savedFirstVisibleItemPosition = -1
                savedFirstVisibleItemOffset = 0
            }
        }
    }

    /**
     * 还原保存的数据
     * @return Triple<Boolean, Int, Int> - (是否需要恢复, 第一个可见项位置, 第一个可见项偏移量)
     */
    @SuppressLint("NotifyDataSetChanged")
    fun restoreSavedData(): Triple<Boolean, Int, Int> {
        var needRestore = false
        var position = -1
        var offset = 0 // 默认偏移量

        if (searchResults != null && currentResourceId == resourceId) {
            resultDataAdapter?.dataList = searchResults
            resultDataAdapter?.selectedPosition = selectedPosition
            resultDataAdapter?.notifyDataSetChanged() // 先更新数据
            currentPage = savedCurrentPage
            totalPage = savedTotalPage
            isEnableLoadMore = savedIsEnableLoadMore  // 恢复加载更多状态

            currentSearchEntity.keyWords = keywords

            if (resultDataAdapter?.itemCount ?: 0 > 0) {
                // 检查是否需要居中显示
                if (savedFirstVisibleItemPosition == -2 && selectedPosition >= 0 && selectedPosition < (resultDataAdapter?.itemCount ?: 0)) {
                    needRestore = true
                    position = selectedPosition
                    offset = -1 // 特殊偏移量标记，表示需要居中
                } else if (savedFirstVisibleItemPosition >= 0 && savedFirstVisibleItemPosition < (resultDataAdapter?.itemCount ?: 0)) {
                    // 恢复精确位置
                    needRestore = true
                    position = savedFirstVisibleItemPosition
                    offset = savedFirstVisibleItemOffset
                } else if (selectedPosition >= 0 && selectedPosition < (resultDataAdapter?.itemCount ?: 0)) {
                    needRestore = true
                    position = selectedPosition
                    offset = 0 // 默认偏移量，不居中
                 }
            }
        }

        return Triple(needRestore, position, offset)
    }

    /**
     * 设置关键词
     */
    fun setKeywords(newKeywords: String) {
        keywords = newKeywords
        currentSearchEntity.keyWords = newKeywords
    }

    /**
     * 获取关键词
     */
    fun getKeywords(): String {
        return keywords
    }

    /**
     * 用户进行搜索操作
     */
    fun userDoSearchAction(context: Context, entity: SearchItemEntity? = null, onSearchStarted: () -> Unit, onSearchFinished: (success: Boolean) -> Unit) {
        if (APPConfig.isFastClick()) return
        if (!NetWorkUtils.isNetworkAvailable()) {
            ToastUtil.showToastShort(R.string.no_network_connect)
            onSearchFinished(false)
            return
        }

        if (entity == null) {
            val searchEntity = SearchItemEntity()
            searchEntity.keyWords = keywords
            currentSearchEntity = searchEntity
        } else {
            currentSearchEntity = entity
        }

        if (currentSearchEntity.keyWords.isBlank()) {
            ToastUtil.showToastShort(R.string.input_search_keyword)
            return
        }

        currentSearchEntity.keyWords = EPubBook.removeRTLControlChars(currentSearchEntity.keyWords)

        val textLength = currentSearchEntity.keyWords.trim().length
        if (textLength < 2) {
            ToastUtil.showToastCenter(context.getString(R.string.search_keyword_length_limit))
            return
        }

        keywords = currentSearchEntity.keyWords

        SDKSingleton.userBl.saveBookSearchData(currentSearchEntity.keyWords, resourceId)

        onSearchStarted()
        currentPage = 1
        isEnableLoadMore = true
        resultDataAdapter?.clearDataList()
        resultDataAdapter?.selectedPosition = -1

        loadMoreData(context) { success ->
            onSearchFinished(success)
        }
    }

    /**
     * 加载更多数据
     */
    fun loadMoreData(context: Context, callback: (success: Boolean) -> Unit = {}) {
        MainScope().launch(ExceptionHandler.getCoroutineExceptionHandler {
            callback(false)
        }) {
            try {
                // 首次加载22条数据，后续加载10条
                val pageSize = if (currentPage == 1) 22 else 10

                val resultPageData = SDKSingleton.storeBl.getSentenceSearchResultList(
                    resourceId,
                    EPubBook.removeRTLControlChars(currentSearchEntity.keyWords),
                    currentPage,
                    pageSize
                )

                if (resultPageData != null) {
                    totalPage = resultPageData.totalPage
                    resultDataAdapter?.addSearchResults(resultPageData.dataContents)
                    isEnableLoadMore = (currentPage < totalPage)
                    currentPage++
                    saveCurrentData()
                    callback(true)
                } else {
                    isEnableLoadMore = false
                    callback(false)
                }
            } catch (e: Exception) {
                isEnableLoadMore = false
                callback(false)
            }
        }
    }

    /**
     * 处理搜索导航事件
     */
    fun handleSearchNavigate(event: OnBookSearchNavigateEvent, context: Context) {
        if (resultDataAdapter?.itemCount ?: 0 <= 0) {
            // 没有搜索结果，发送失败事件
            EventBus.getDefault().post(OnBookSearchNavigateResultEvent(false))
            return
        }

        val currentPosition = resultDataAdapter?.selectedPosition ?: -1
        var targetPosition = -1

        if (event.isNext) {
            // 查找下一条结果
            if (currentPosition < 0 || currentPosition >= (resultDataAdapter?.itemCount ?: 0) - 1) {
                if (isEnableLoadMore) {
                    loadMoreData(context)
                    targetPosition = if (currentPosition < 0) 0 else currentPosition + 1
                } else {
                    targetPosition = 0
                }
            } else {
                // 选择下一项
                targetPosition = currentPosition + 1
                // 检查剩余数据是否不足5条，如果是则提前加载更多
                if (isEnableLoadMore &&
                    (resultDataAdapter?.itemCount ?: 0) - targetPosition <= 5
                ) {
                    loadMoreData(context)
                }
            }
        } else {
            // 查找上一条结果
            if (currentPosition <= 0) {
                targetPosition = (resultDataAdapter?.itemCount ?: 0) - 1
            } else {
                targetPosition = currentPosition - 1
            }
        }

        if (targetPosition >= 0 && targetPosition < (resultDataAdapter?.itemCount ?: 0)) {
            navigateToPosition(targetPosition, context)
        } else {
            // 导航失败
            EventBus.getDefault().post(OnBookSearchNavigateResultEvent(false))
        }
    }

    /**
     * 导航到指定位置的搜索结果
     */
    private fun navigateToPosition(position: Int, context: Context) {
        // 获取选中的搜索结果
        val item = resultDataAdapter?.dataList?.get(position)

        // 如果是章节标题，则跳过，查找下一个或上一个句子
        if (item is SearchResultChapter) {
            val isNext = position > (resultDataAdapter?.selectedPosition ?: -1)

            var nextValidPosition = position
            while (nextValidPosition >= 0 && nextValidPosition < (resultDataAdapter?.itemCount ?: 0)) {
                nextValidPosition = if (isNext) nextValidPosition + 1 else nextValidPosition - 1

                // 检查是否超出范围
                if (nextValidPosition < 0 || nextValidPosition >= (resultDataAdapter?.itemCount ?: 0)) {
                    EventBus.getDefault().post(OnBookSearchNavigateResultEvent(false))
                    return
                }

                // 检查是否找到句子
                if (resultDataAdapter?.dataList?.get(nextValidPosition) is SearchResultSentence) {
                    navigateToPosition(nextValidPosition, context)
                    return
                }
            }

            EventBus.getDefault().post(OnBookSearchNavigateResultEvent(false))
            return
        }

        // 更新选中位置
        resultDataAdapter?.selectedPosition = position
        resultDataAdapter?.notifyDataSetChanged()

        // 保存当前选中位置，并标记需要居中显示
        selectedPosition = position
        saveCurrentData(-2, 0)

        if (item is SearchResultSentence) {
            val parentChapter = findParentChapter(item)
            if (parentChapter != null) {
                val noteEntity = EPubBook.createNoteEntityFromSearchResult(
                    parentChapter, item,
                    resultDataAdapter?.selectedPosition ?: -1,
                    resultDataAdapter?.dataList,
                    resultDataAdapter?.itemCount ?: 0,
                    isEnableLoadMore,
                    keywords
                )

                // 发送导航成功事件
                MainScope().launch(ExceptionHandler.getCoroutineExceptionHandler {}) {
                    val downloadInfo =
                        SDKSingleton.dbWrapBl.getResourceDownloadInfo(parentChapter.resourceId)
                    if (downloadInfo != null) {
                        EventBus.getDefault().post(downloadInfo.fileId?.let {
                            OnBookSearchNavigateResultEvent(
                                true,
                                noteEntity,
                                downloadInfo.resourceId,
                                downloadInfo.getActualFilePath(),
                                it
                            )
                        })
                    } else {
                        EventBus.getDefault().post(OnBookSearchNavigateResultEvent(false))
                    }
                }
            } else {
                EventBus.getDefault().post(OnBookSearchNavigateResultEvent(false))
            }
        } else {
            EventBus.getDefault().post(OnBookSearchNavigateResultEvent(false))
        }
    }

    /**
     * 查找搜索结果所属的章节
     */
    private fun findParentChapter(sentence: SearchResultSentence): SearchResultChapter? {
        resultDataAdapter?.dataList?.let { list ->
            var lastChapter: SearchResultChapter? = null

            for (item in list) {
                if (item is SearchResultChapter) {
                    lastChapter = item
                } else if (item is SearchResultSentence && item == sentence) {
                    return lastChapter
                }
            }
        }
        return null
    }

    /**
     * 加载搜索历史数据
     */
    fun loadSearchHistoryData(resourceId: String): MutableList<SearchItemEntity> {
        return SDKSingleton.userBl.getBookSearchDataList(resourceId, maxSearchHistoryCount)
    }

    /**
     * 清除搜索历史
     */
    fun clearSearchHistory(resourceId: String) {
        SDKSingleton.userBl.clearBookSearchHistory(resourceId)
    }
} 