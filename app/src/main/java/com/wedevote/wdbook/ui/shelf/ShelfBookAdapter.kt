package com.wedevote.wdbook.ui.shelf

import android.text.TextUtils
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.FragmentActivity
import com.aquila.lib.base.BaseRecycleAdapter
import com.wedevote.wdbook.base.SDKSingleton
import com.wedevote.wdbook.constants.DownloadStatus
import com.wedevote.wdbook.entity.shelf.HomeShelfItemCombineEntity
import com.wedevote.wdbook.entity.shelf.ShelfBookItemEntity
import com.wedevote.wdbook.entity.shelf.ShelfDataType
import com.wedevote.wdbook.entity.store.BookFileDownloadEntity
import com.wedevote.wdbook.tools.download.DownloaderEngine
import com.wedevote.wdbook.tools.download.OnDownloadingListener
import com.wedevote.wdbook.ui.account.SSOLoginActivity
import com.wedevote.wdbook.ui.store.DownloadOption

/***
 *@date 创建时间 2020/7/8 10:55
 *<AUTHOR> <PERSON><PERSON>
 *@description
 */
class ShelfBookAdapter(var activity: FragmentActivity) : BaseRecycleAdapter<HomeShelfItemCombineEntity, ShelfItemViewHolder>() {
    //    private var lastClickTime: Long = 0L
    private var bookItemCount: Int = 0
    private val downloadListener = object : OnDownloadingListener {
        override fun onWait(entity: BookFileDownloadEntity, isAdded: Boolean) {
            val position = findDataPosition(entity)
            notifyItemChanged(position, DownloadOption(if (isAdded) DownloadStatus.WAIT else DownloadStatus.CANCEL))
        }

        override fun onBeginning(entity: BookFileDownloadEntity) {
            val position = findDataPosition(entity)
            notifyItemChanged(position, DownloadOption(downloadStatus = DownloadStatus.BEGIN))
        }

        override fun onDownloadingProgress(entity: BookFileDownloadEntity, downloadSize: Long, totalSize: Long) {
            val fileId = entity.fileId ?: return
            val percent = if (totalSize > 0) ((downloadSize * 100) / totalSize).toInt() else 0
            val lastPercent = lastProgressPercentMap[fileId] ?: -1
            val now = System.currentTimeMillis()
            val lastTime = lastNotifyTimeMsMap[fileId] ?: 0L

            if (percent == lastPercent || (lastPercent >= 0 && percent - lastPercent < progressMinDeltaPercent && now - lastTime < progressNotifyMinIntervalMs)) {
                return
            }

            lastProgressPercentMap[fileId] = percent
            lastNotifyTimeMsMap[fileId] = now

            val position = findDataPosition(entity)
            if (position >= 0) {
                val option = DownloadOption(DownloadStatus.DOWNLOADING, downloadSize, totalSize)
                notifyItemChanged(position, option)
            }
        }

        override fun onError(entity: BookFileDownloadEntity, errorDesc: String) {
            notifyItemChanged(findDataPosition(entity), DownloadOption(downloadStatus = DownloadStatus.ERROR))
        }

        override fun onPause(entity: BookFileDownloadEntity) {
            notifyItemChanged(findDataPosition(entity), DownloadOption(downloadStatus = DownloadStatus.PAUSE))
        }

        override fun onComplete(entity: BookFileDownloadEntity) {
            val position = findDataPosition(entity)
            if (position >= 0) {
                notifyItemChanged(position, DownloadOption(downloadStatus = DownloadStatus.COMPLETE))
            } else {
                onShelfBookAdapterCallback?.onReloadData()
            }
        }
    }

    var downloaderEngine: DownloaderEngine = DownloaderEngine(activity)
    val selectItemList = ArrayList<ShelfBookItemEntity>()
    var isEditMode = false
    var onShelfBookAdapterCallback: OnShelfBookAdapterCallback? = null

    // 下载进度回调节流，避免高频 UI 刷新导致掉帧
    private val lastProgressPercentMap = HashMap<String, Int>()
    private val lastNotifyTimeMsMap = HashMap<String, Long>()
    private val progressNotifyMinIntervalMs = 100L
    private val progressMinDeltaPercent = 2

    init {
        downloaderEngine.setOnDownloadingListener(downloadListener)
        setHasStableIds(true)
    }

    private fun findDataPosition(entity: BookFileDownloadEntity): Int {
        var position = -1
        dataList?.let {
            var index = 0
            for (shelfItemEntity in it) {
                if (shelfItemEntity.dataType == ShelfDataType.RESOURCE) {
                    if (TextUtils.equals(shelfItemEntity.bookItemEntity?.resourceDownloadInfo?.fileId, entity.fileId)) {
                        position = index
                        break
                    }
                }
                index++
            }
        }
        return position
    }

    override fun addDataList(list: List<HomeShelfItemCombineEntity>?) {
        super.addDataList(list)
        onShelfBookAdapterCallback?.onEmptyData(dataList.isNullOrEmpty())
        onShelfBookAdapterCallback?.isAllItemSelected(checkIsCurrentSelectAll())
    }

    fun checkAllDataArchiveType(): Boolean {
        if (!dataList.isNullOrEmpty()) {
            for (item in dataList!!) {
                if (item.dataType == ShelfDataType.RESOURCE) {
                    return false
                }
            }
        }
        return true
    }

    override fun onCreateViewHolder(parent: ViewGroup, type: Int): ShelfItemViewHolder {
        return ShelfItemViewHolder(parent, downloaderEngine)
    }

    override fun updateDataCount() {
        super.updateDataCount()
        if (dataList.isNullOrEmpty()) {
            bookItemCount = 0
        } else {
            bookItemCount = 0
            for (item in dataList!!) {
                if (item is HomeShelfItemCombineEntity) {
                    if (item.dataType == ShelfDataType.RESOURCE) {
                        bookItemCount++
                    }
                }
            }
        }
    }

    /*取消编辑模式*/
    fun doCancelEditMode(): Boolean {
        if (isEditMode) {
            isEditMode = false
            selectItemList.clear()
            onShelfBookAdapterCallback?.onChangeEditMode(false)
            onShelfBookAdapterCallback?.isAllItemSelected(checkIsCurrentSelectAll())
            notifyDataSetChanged()
            return true
        }
        return false
    }

    /*更改当前长按编辑模式*/
    fun onChangeEditModeAction() {
        if (isEditMode) {
            doCancelEditMode()
        } else {
            isEditMode = true
            onShelfBookAdapterCallback?.onChangeEditMode(true)
            notifyDataSetChanged()
        }
        onShelfBookAdapterCallback?.isAllItemSelected(checkIsCurrentSelectAll())
    }

    /*执行点击添加/删除多选*/
    private fun doOnItemSelectAction(data: ShelfBookItemEntity) {
        if (selectItemList.contains(data)) {
            selectItemList.remove(data)
        } else {
            selectItemList.add(data)
        }
        onShelfBookAdapterCallback?.isAllItemSelected(checkIsCurrentSelectAll())
    }

    /*全选或反选*/
    fun onChangeSelectAllData() {
        if (selectItemList.size < bookItemCount) {
            selectItemList.clear()
            for (item in dataList!!) {
                if (item is HomeShelfItemCombineEntity) {
                    if (item.dataType == ShelfDataType.RESOURCE) {
                        selectItemList.add(item.bookItemEntity!!)
                    }
                }
            }
        } else {
            selectItemList.clear()
        }
        onShelfBookAdapterCallback?.isAllItemSelected(checkIsCurrentSelectAll())
        onShelfBookAdapterCallback?.onItemSelect(selectItemList)
        notifyDataSetChanged()
    }

    private fun checkIsCurrentSelectAll(): Boolean {
        return selectItemList.size >= bookItemCount && bookItemCount > 0
    }

    override fun getItemViewType(position: Int): Int {
        val item = dataList?.getOrNull(position)
        return if (item is HomeShelfItemCombineEntity) {
            when (item.dataType) {
                ShelfDataType.RESOURCE -> 1
                ShelfDataType.ARCHIVE -> 2
            }
        } else 0
    }

    override fun getItemId(position: Int): Long {
        val item = dataList?.getOrNull(position)
        if (item is HomeShelfItemCombineEntity) {
            return when (item.dataType) {
                ShelfDataType.RESOURCE -> (item.bookItemEntity?.resourceDownloadInfo?.fileId ?: item.bookItemEntity?.resourceId ?: position.toString()).hashCode().toLong()
                ShelfDataType.ARCHIVE -> (item.archiveEntity?.clientArchiveId ?: position.toString()).hashCode().toLong()
            }
        }
        return position.toLong()
    }

    override fun onBindViewHolder(holder: ShelfItemViewHolder, position: Int, payloads: MutableList<Any>) {
        if (!payloads.isNullOrEmpty()) {
            if (payloads[0] is DownloadOption) {
                val option = payloads[0] as DownloadOption
                holder.updateDownloadStatus(option)
            }
        } else {
            val shelfItemEntity = dataList!![position]
            holder.initUIData(shelfItemEntity)
            holder.updateEditModeUI(isEditMode)
            holder.setItemSelectedState(selectItemList.contains(shelfItemEntity.bookItemEntity))
            holder.itemView.setOnLongClickListener(object : View.OnLongClickListener {
                override fun onLongClick(v: View?): Boolean {
                    if (shelfItemEntity.dataType == ShelfDataType.ARCHIVE) {
                        return true
                    }

                    if (!isEditMode) {
                        if (shelfItemEntity.dataType == ShelfDataType.RESOURCE) {
                            selectItemList.add(shelfItemEntity.bookItemEntity!!)
                        }
                        onChangeEditModeAction()
                        onShelfBookAdapterCallback?.onChangeEditMode(true)
                        onShelfBookAdapterCallback?.onItemSelect(selectItemList)
                    }
                    return true
                }
            })

            holder.itemView.setOnClickListener {
                if (!SDKSingleton.sessionBl.isLogin()) {
                    SSOLoginActivity.checkAndGotoLogin(activity)
                } else {
                    if (isEditMode) {
                        when (shelfItemEntity.dataType) {
                            ShelfDataType.RESOURCE -> {
                                doOnItemSelectAction(shelfItemEntity.bookItemEntity!!)
                                onShelfBookAdapterCallback?.onItemSelect(selectItemList)
                                notifyItemChanged(position)
                            }
//                        ShelfDataType.ARCHIVE -> {
//                            val clickTime = System.currentTimeMillis()
//                            if (clickTime - lastClickTime < 500) {
//                                doCancelEditMode()
//                                FolderBookListActivity.gotoFolderBookListActivity(holder.itemView.context, shelfItemEntity.archiveEntity!!)
//                            } else {
//                                ToastUtil.showToastShort(R.string.double_click_into_book_file)
//                            }
//                            lastClickTime = clickTime
//                        }
                            else -> {}
                        }
                    } else {
                        holder.doItemClick()
                    }
                }
            }
        }
    }

    override fun onBindViewHolder(holder: ShelfItemViewHolder, position: Int) {
    }
}

/***
 *@date 创建时间 2020/9/9 17:19
 *<AUTHOR> W.YuLong
 *@description
 */
interface OnShelfBookAdapterCallback {
    fun onReloadData()
    fun onChangeEditMode(isLongClicked: Boolean)
    fun isAllItemSelected(isAllSelected: Boolean)
    fun onEmptyData(isEmptyData: Boolean)
    fun onItemSelect(selectList: ArrayList<ShelfBookItemEntity>?)
}
