package com.wedevote.wdbook.ui.widgets

import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.util.AttributeSet
import com.wedevote.wdbook.tools.util.dp2px

/***
 * @date 创建时间 2020/7/20 13:57
 * <AUTHOR> <PERSON><PERSON>
 * @description
 */
class DialSeekBar constructor(context: Context, attr: AttributeSet? = null) : androidx.appcompat.widget.AppCompatSeekBar(context, attr) {

    val paint = Paint(Paint.ANTI_ALIAS_FLAG)

    init {
        paint.color = Color.BLACK
        paint.strokeWidth = 2f
    }

    var viewWidth: Int = 0
    var viewHeight: Int = 0
    var cellWidth: Int = 0
    var lineHeight: Int = 30

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        viewWidth = w
        viewHeight = h
    }

    override fun setMax(max: Int) {
        super.setMax(max)
        if (max > 1) {
            cellWidth = viewWidth / (max - 1)
        } else {
            cellWidth = viewWidth
        }
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        cellWidth = (viewWidth) / max

        for (i in 0..max) {
            canvas.drawLine(
                dp2px(15) + cellWidth * i.toFloat(), ((viewHeight - lineHeight) / 2).toFloat(),
                dp2px(15) + cellWidth * i.toFloat(), ((viewHeight + lineHeight) / 2).toFloat(), paint
            )
        }
    }
}
