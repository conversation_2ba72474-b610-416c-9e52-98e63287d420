package com.wedevote.wdbook.ui.store

import android.graphics.Color
import android.view.ViewGroup
import android.widget.TextView
import com.aquila.lib.base.BaseRecycleAdapter
import com.aquila.lib.base.BaseViewHolder
import com.wedevote.wdbook.R
import com.wedevote.wdbook.entity.store.AttributeValueEntity
import com.wedevote.wdbook.entity.store.ProductAttributeEntity
import com.wedevote.wdbook.entity.store.ProductDetailEntity

/***
 *@date 创建时间 2020/8/20 17:26
 *<AUTHOR> W.<PERSON>Long
 *@description
 */
class AttributeRecyclerAdapter : BaseRecycleAdapter<ProductAttributeEntity, AttributeViewHolder>() {
    var clickListener: ClickListener? = null
    var detailEntity: ProductDetailEntity? = null
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): AttributeViewHolder {
        return AttributeViewHolder(parent, clickListener, detailEntity)
    }
}

interface ClickListener {
    fun onClick(valueEntityList: ArrayList<AttributeValueEntity>, s: String)
}

/***
 *@date 创建时间 2020/8/20 17:26
 *<AUTHOR> W.YuLong
 *@description
 */
class AttributeViewHolder(parent: ViewGroup, clickListener: ClickListener? = null, detailEntity: ProductDetailEntity? = null) :
    BaseViewHolder(parent, R.layout.item_book_detail_entry_layout) {
    val labelTextView: TextView = itemView.findViewById(R.id.book_detail_publisher_label_TextView)
    val descTextView: TextView = itemView.findViewById(R.id.book_detail_publisher_value_TextView)
    var clickListener = clickListener
    var detailEntity = detailEntity
    override fun <T> initUIData(t: T) {
        t as ProductAttributeEntity
        labelTextView.text = t.key + " :"
        if (!t.key.isNullOrEmpty() && !t.valueEntityList.isNullOrEmpty() && !t.valueEntityList!![0].deepLink.isNullOrEmpty() && t.valueEntityList!![0].deepLink.contains("editor")) {
            descTextView.setTextColor(Color.parseColor("#006FFF"))
            descTextView.setOnClickListener{
                clickListener?.onClick(t.valueEntityList!!, "/editor/")
            }
            descTextView.text = detailEntity?.translatorNames ?: ""
        } else if (!t.key.isNullOrEmpty() && !t.valueEntityList.isNullOrEmpty() && !t.valueEntityList!![0].deepLink.isNullOrEmpty() && t.valueEntityList!![0].deepLink.contains("publisher")) {
            descTextView.setTextColor(Color.parseColor("#006FFF"))
            descTextView.setOnClickListener{
                clickListener?.onClick(t.valueEntityList!!, "/publisher/")
            }
            descTextView.text = detailEntity?.publisherName ?: ""
        } else {
            descTextView.text = formatValueName(t.valueEntityList)
        }
    }

    fun formatValueName(list: ArrayList<AttributeValueEntity>?): String {
        if (list.isNullOrEmpty()) {
            return ""
        }
        val sb = StringBuilder()
        for (i in list.indices) {
            sb.append(list[i].name)
            if (i < list.size - 1) {
                sb.append(",")
            }
        }
        return sb.toString()
    }
}
