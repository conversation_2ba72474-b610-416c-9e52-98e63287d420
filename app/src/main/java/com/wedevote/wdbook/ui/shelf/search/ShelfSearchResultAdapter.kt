package com.wedevote.wdbook.ui.shelf.search

import android.app.Activity
import android.graphics.Typeface
import android.text.Html
import android.text.Spannable
import android.text.SpannableString
import android.text.style.ForegroundColorSpan
import android.text.style.StyleSpan
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.wedevote.wdbook.ui.home.microwidget.BookProductViewHolder
import com.wedevote.wdbook.ui.store.StoreFilterBarView
import com.wedevote.wdbook.constants.OrderField
import com.aquila.lib.base.BaseViewHolder
import com.aquila.lib.tools.util.ToastUtil
import com.aquila.lib.widget.view.AdaptiveImageView
import com.wedevote.wdbook.R
import com.wedevote.wdbook.base.SDKSingleton
import com.wedevote.wdbook.constants.DownloadStatus
import com.wedevote.wdbook.entity.shelf.ShelfArchiveItemEntity
import com.wedevote.wdbook.entity.shelf.ShelfBookItemEntity
import com.wedevote.wdbook.entity.store.AuthorEntity
import com.wedevote.wdbook.entity.store.ProductEntity
import com.wedevote.wdbook.tools.util.DataPathUtil
import com.wedevote.wdbook.tools.util.FilePathUtil
import com.wedevote.wdbook.tools.util.ImageLoadUtil
import com.wedevote.wdbook.tools.util.MD5EncryptUtil
import com.wedevote.wdbook.tools.util.PictureUtil
import com.wedevote.wdbook.tools.util.dp2px
import com.wedevote.wdbook.tools.util.getPictureRemotePath
import com.wedevote.wdbook.tools.util.initAuthorsName
import com.wedevote.wdbook.tools.util.parseHtmlTag
import com.wedevote.wdbook.ui.read.BookReadActivity
import com.wedevote.wdbook.ui.shelf.FolderBookListActivity
import com.wedevote.wdbook.ui.store.BookDetailActivity
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File
import java.util.regex.Pattern

/***
 *@date 创建时间 2025/04/25
 *<AUTHOR> John.Qian
 *@description 书架搜索结果适配器
 */

enum class MessageItemType {
    SHELF_EMPTY,
    STORE_EMPTY,
    STORE_NO_NETWORK
}

// 封装搜索结果项
sealed class SearchResultItem {
    data class ShelfBook(val book: ShelfBookItemEntity, val keyword: String) : SearchResultItem()
    data class ShelfArchive(val archive: ShelfArchiveItemEntity, val keyword: String) :
        SearchResultItem()

    data class StoreBook(val product: ProductEntity, val keyword: String) : SearchResultItem()
    data class TitleHeader(val title: String) : SearchResultItem()
    data class MessageItem(val message: String, val itemType: MessageItemType) : SearchResultItem()
    object FilterBar : SearchResultItem()

    companion object {
        const val VIEW_TYPE_SHELF_BOOK = 0
        const val VIEW_TYPE_SHELF_ARCHIVE = 1
        const val VIEW_TYPE_STORE_BOOK = 2
        const val VIEW_TYPE_TITLE_HEADER = 3
        const val VIEW_TYPE_MESSAGE_ITEM = 4
        const val VIEW_TYPE_FILTER_BAR = 5
    }
}

class ShelfSearchResultAdapter : RecyclerView.Adapter<BaseViewHolder>() {
    private val dataList = mutableListOf<SearchResultItem>()
    private var shelfData = mutableListOf<SearchResultItem>()
    private var storeData = mutableListOf<SearchResultItem>()
    private var headerTitles = mutableListOf<String>()

    private var shelfMessage: SearchResultItem.MessageItem? = null
    private var storeMessage: SearchResultItem.MessageItem? = null

    var onFilterChangedListener: StoreFilterBarView.OnFilterChangedListener? = null
    var selectedLanguage: String = ""
        private set
    var selectedOrderField: OrderField = OrderField.CreateTime_DESC
        private set
    var selectedLanguageName: String = ""
        private set
    private var showStoreFilterBar: Boolean = false

    private val internalFilterChangedListener = object : StoreFilterBarView.OnFilterChangedListener {
        override fun onOrderFieldChanged(orderField: OrderField) {
            selectedOrderField = orderField
            onFilterChangedListener?.onOrderFieldChanged(orderField)
        }

        override fun onLanguageChanged(language: String) {
            selectedLanguage = language
            selectedLanguageName = ""
            onFilterChangedListener?.onLanguageChanged(language)
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BaseViewHolder {
        return when (viewType) {
            SearchResultItem.VIEW_TYPE_SHELF_BOOK -> ShelfBookViewHolder(parent)
            SearchResultItem.VIEW_TYPE_SHELF_ARCHIVE -> ShelfArchiveViewHolder(parent)
            SearchResultItem.VIEW_TYPE_STORE_BOOK -> BookProductViewHolder(parent, BookProductViewHolder.TYPE_PRODUCT_LIST)
            SearchResultItem.VIEW_TYPE_TITLE_HEADER -> TitleHeaderViewHolder(parent)
            SearchResultItem.VIEW_TYPE_MESSAGE_ITEM -> MessageItemViewHolder(parent)
            SearchResultItem.VIEW_TYPE_FILTER_BAR -> FilterBarViewHolder(parent, internalFilterChangedListener)
            else -> throw IllegalArgumentException("未知的视图类型：$viewType")
        }
    }

    override fun onBindViewHolder(holder: BaseViewHolder, position: Int) {
        val item = dataList[position]
        applyBackgroundAndPadding(holder.itemView, position)
        if (holder is FilterBarViewHolder) {
            holder.bind(selectedLanguageName, selectedLanguage, selectedOrderField)
        }
        if (item is SearchResultItem.StoreBook && holder is BookProductViewHolder) {
            holder.initUIData(item.product)
        } else {
            holder.initUIData(item, position)
        }
    }

    private fun applyBackgroundAndPadding(itemView: View, position: Int) {

        if (getItemViewType(position) == SearchResultItem.VIEW_TYPE_TITLE_HEADER ||
            getItemViewType(position) == SearchResultItem.VIEW_TYPE_MESSAGE_ITEM ||
            getItemViewType(position) == SearchResultItem.VIEW_TYPE_FILTER_BAR) {
            return
        }
        itemView.setBackgroundResource(0)

        if (getItemViewType(position) == SearchResultItem.VIEW_TYPE_STORE_BOOK) {
            itemView.setPadding(dp2px(24), 0, dp2px(24), dp2px(20))
        }

        val currentGroup = getCurrentGroup(position)
        if (currentGroup != null) {
            val (groupStart, groupEnd) = currentGroup

            // 只有一个内容项的情况
            if (groupEnd == groupStart + 1 && position == groupStart + 1) {
                if (SDKSingleton.appBl.isCurrentThemeLight()) {
                    itemView.setBackgroundResource(R.drawable.shape_white_conner_8dp_bg)
                } else {
                    itemView.setBackgroundResource(R.drawable.shape_white_conner_8dp_bg_dark)
                }
                itemView.setPadding(dp2px(12), dp2px(12), dp2px(12), dp2px(12))
            } else if (position == groupStart + 1) {
                // 第一个内容项
                if (SDKSingleton.appBl.isCurrentThemeLight()) {
                    itemView.setBackgroundResource(R.drawable.shape_white_conner_8dp_top_bg)
                } else {
                    itemView.setBackgroundResource(R.drawable.shape_white_conner_8dp_top_bg_dark)
                }
                itemView.setPadding(dp2px(12), dp2px(12), dp2px(12), dp2px(8))
            } else if (position == groupEnd) {
                // 最后一个内容项
                if (SDKSingleton.appBl.isCurrentThemeLight()) {
                    itemView.setBackgroundResource(R.drawable.shape_white_conner_8dp_bottom_bg)
                } else {
                    itemView.setBackgroundResource(R.drawable.shape_white_conner_8dp_bottom_bg_dark)
                }
                if (getItemViewType(position) != SearchResultItem.VIEW_TYPE_STORE_BOOK) {
                    itemView.setPadding(dp2px(12), dp2px(8), dp2px(12), dp2px(12))
                }
            } else if (position > groupStart && position < groupEnd) {
                // 中间内容项
                if (SDKSingleton.appBl.isCurrentThemeLight()) {
                    itemView.setBackgroundColor(ContextCompat.getColor(itemView.context, R.color.white))
                } else {
                    itemView.setBackgroundColor(ContextCompat.getColor(itemView.context, R.color.color_dark_1E1E1E))
                }
            }
        }
    }

    private fun getCurrentGroup(position: Int): Pair<Int, Int>? {
        val shelfTitleIndex =
            dataList.indexOfFirst { it is SearchResultItem.TitleHeader && dataList.indexOf(it) == 0 }
        val storeTitleIndex =
            dataList.indexOfFirst { it is SearchResultItem.TitleHeader && dataList.indexOf(it) > shelfTitleIndex }

        // 书架组
        if (shelfTitleIndex != -1 && position > shelfTitleIndex) {
            val shelfGroupEnd =
                if (storeTitleIndex != -1) storeTitleIndex - 1 else dataList.size - 1
            if (position <= shelfGroupEnd) {
                return Pair(shelfTitleIndex, shelfGroupEnd)
            }
        }

        // 书城组
        if (storeTitleIndex != -1 && position > storeTitleIndex) {
            return Pair(storeTitleIndex, dataList.size - 1)
        }

        return null
    }

    override fun getItemCount(): Int = dataList.size

    override fun getItemViewType(position: Int): Int {
        return when (dataList[position]) {
            is SearchResultItem.ShelfBook -> SearchResultItem.VIEW_TYPE_SHELF_BOOK
            is SearchResultItem.ShelfArchive -> SearchResultItem.VIEW_TYPE_SHELF_ARCHIVE
            is SearchResultItem.StoreBook -> SearchResultItem.VIEW_TYPE_STORE_BOOK
            is SearchResultItem.TitleHeader -> SearchResultItem.VIEW_TYPE_TITLE_HEADER
            is SearchResultItem.MessageItem -> SearchResultItem.VIEW_TYPE_MESSAGE_ITEM
            is SearchResultItem.FilterBar -> SearchResultItem.VIEW_TYPE_FILTER_BAR
        }
    }

    fun clearDataList() {
        shelfData.clear()
        storeData.clear()
        headerTitles.clear()
        shelfMessage = null
        storeMessage = null
        updateDataList()
    }

    fun addTitleHeader(title: String) {
        headerTitles.add(title)
        updateDataList()
    }

    fun updateHeaderTitle(index: Int, title: String) {
        if (index < headerTitles.size) {
            headerTitles[index] = title
            updateDataList()
        }
    }

    fun addShelfData(items: List<SearchResultItem>) {
        shelfData.addAll(items)
        updateDataList()
    }

    fun addStoreData(items: List<SearchResultItem>) {
        storeData.addAll(items)
        updateDataList()
    }

    fun showEmptyShelfState(activity: Activity) {
        shelfMessage = SearchResultItem.MessageItem(activity.getString(R.string.no_search_results_in_shelf), MessageItemType.SHELF_EMPTY)
        updateDataList()
    }

    fun removeEmptyShelfState() {
        shelfMessage = null
        updateDataList()
    }

    fun showEmptyStoreState(activity: Activity) {
        storeMessage = SearchResultItem.MessageItem(activity.getString(R.string.no_search_results_in_shelf), MessageItemType.STORE_EMPTY)
        updateDataList()
    }

    fun showNoNetworkForStoreState(activity: Activity) {
        storeMessage = SearchResultItem.MessageItem(activity.getString(R.string.no_network_connect), MessageItemType.STORE_NO_NETWORK)
        updateDataList()
    }

    fun clearShelfMessage() {
        shelfMessage = null
        updateDataList()
    }

    fun clearStoreMessage() {
        storeMessage = null
        updateDataList()
    }

    fun getShelfItemCount(): Int = shelfData.size

    fun getStoreItemCount(): Int = storeData.size

    fun isDataEmpty(): Boolean = shelfData.isEmpty() && storeData.isEmpty()

    fun resetFilterLanguageToAll(allLanguageName: String, code: String) {
        selectedLanguageName = allLanguageName
        selectedLanguage = code
        notifyDataSetChanged()
    }

    fun setShowStoreFilterBar(show: Boolean) {
        if (showStoreFilterBar != show) {
            showStoreFilterBar = show
            updateDataList()
        }
    }

    fun setStoreData(newItems: List<SearchResultItem>) {
        storeData.clear()
        storeData.addAll(newItems)
        updateDataList()
    }

    private fun updateDataList() {
        dataList.clear()

        // 添加书架标题和数据
        if (headerTitles.isNotEmpty()) {
            dataList.add(SearchResultItem.TitleHeader(headerTitles[0]))
        }

        if (shelfMessage != null) {
            dataList.add(shelfMessage!!)
        } else if (shelfData.isNotEmpty()) {
            dataList.addAll(shelfData)
        }

        // 添加书城标题、筛选栏、数据
        if (headerTitles.size > 1) {
            dataList.add(SearchResultItem.TitleHeader(headerTitles[1]))
            if (showStoreFilterBar) {
                dataList.add(SearchResultItem.FilterBar)
            }
            if (storeMessage != null) {
                dataList.add(storeMessage!!)
            } else if (storeData.isNotEmpty()) {
                dataList.addAll(storeData)
            }
        }

        notifyDataSetChanged()
    }
}

// 书架书籍视图
class ShelfBookViewHolder(parent: ViewGroup) : BaseViewHolder(
    LayoutInflater.from(parent.context)
        .inflate(R.layout.view_holder_search_result_shelf_layout, parent, false)
) {
    private val coverImageView: AdaptiveImageView =
        itemView.findViewById(R.id.search_result_cover_ImageView)
    private val nameTextView: TextView = itemView.findViewById(R.id.search_result_name_TextView)
    private val authorTextView: TextView = itemView.findViewById(R.id.search_result_author_TextView)

    override fun <T> initUIData(t: T, position: Int) {
        if (t !is SearchResultItem.ShelfBook) return

        val book = t.book
        val keyword = t.keyword

        PictureUtil.loadRoundImageWithDefault(coverImageView, getPictureRemotePath(book.cover), dp2px(4))

        val title = removeAndHighlight(book.resourceName, keyword, nameTextView)
        nameTextView.text = title

        val authorText =
            SDKSingleton.userBl.formatAuthorName(book.authorList as ArrayList<AuthorEntity>?)
        authorTextView.text = removeAndHighlight(authorText, keyword, authorTextView)

        itemView.setOnClickListener {
            handleBookItemClick(book, it.context)
        }
    }

    override fun <T> initUIData(t: T) {
        initUIData(t, adapterPosition)
    }
}

class ShelfArchiveViewHolder(parent: ViewGroup) : BaseViewHolder(
    LayoutInflater.from(parent.context)
        .inflate(R.layout.view_holder_shelf_archive_search_item, parent, false)
) {
    private val titleTextView: TextView = itemView.findViewById(R.id.archive_title_TextView)
    private val archiveView: View = itemView.findViewById(R.id.archive_cover_ConstraintLayout)
    private val archiveCoverImageView: AdaptiveImageView = itemView.findViewById(R.id.archive_cover_ImageView)
    private val booksContainer: ViewGroup =
        itemView.findViewById(R.id.archive_matched_books_container)

    override fun <T> initUIData(t: T, position: Int) {
        if (t !is SearchResultItem.ShelfArchive) return

        val archive = t.archive
        val keyword = t.keyword

        titleTextView.text = removeAndHighlight(archive.archiveName, keyword, titleTextView)

        setupArchiveCoverImages(archive)

        // 清空并添加匹配的书籍
        booksContainer.removeAllViews()
        archive.matchedBooks?.forEach { book ->
            addMatchedBookView(book, keyword)
        }
        
        archiveView.setOnClickListener {
            handleArchiveItemClick(archive, it.context)
        }
    }

    override fun <T> initUIData(t: T) {
        initUIData(t, adapterPosition)
    }

    private fun setupArchiveCoverImages(archive: ShelfArchiveItemEntity) {
        // 获取封面URL列表（最多4个）
        val coverUrls = archive.matchedBooks?.take(4)?.mapNotNull { getPictureRemotePath(it.cover) } ?: emptyList()

        if (coverUrls.isNotEmpty()) {
            val pictureFolderPath = DataPathUtil.getPictureCachePath(archive.clientArchiveId)
            // 从 GlideUrl 提取 URL 字符串列表用于生成缓存文件名
            val urlStrings = coverUrls.mapNotNull { it?.toStringUrl() }
            val pictureName: String = MD5EncryptUtil.md5String(PictureUtil.appendUrlToStr(urlStrings))
            val cachedFile = File(pictureFolderPath, SDKSingleton.appBl.isCurrentThemeLight().toString() + pictureName)

            if (cachedFile.exists()) {
                // 如果缓存存在，直接加载缓存
                PictureUtil.loadImageWithRemotePath(archiveCoverImageView, cachedFile.absolutePath, R.drawable.ic_folder_black)
            } else {
                // 如果缓存不存在，生成复合图并加载
                archiveCoverImageView.post {
                    val width = archiveCoverImageView.width
                    val height = archiveCoverImageView.height
                    if (width <= 0 || height <= 0) return@post // 确保宽高有效

                    val builder = PictureUtil.Builder(width, height).apply {
                        roundSize = dp2px(2f)
                        paddingSize = dp2px(5)
                        itemMargin = dp2px(3)
                    }

                    GlobalScope.launch(Dispatchers.IO) {
                        val bitmap = PictureUtil.formatCellUrlBitmap(coverUrls, builder)
                        withContext(Dispatchers.Main) {
                            if (bitmap != null) {
                                FilePathUtil.deleteFolderFile(File(pictureFolderPath), false)
                                ImageLoadUtil.writeBitmapToFile(bitmap, cachedFile.absolutePath)
                                ImageLoadUtil.loadBitMapImage(archiveCoverImageView, bitmap)
                            } else {
                                setDefaultFolderIcon()
                            }
                        }
                    }
                }
            }
        } else {
            setDefaultFolderIcon()
        }
    }

    private fun setDefaultFolderIcon() {
        archiveCoverImageView.scaleType = ImageView.ScaleType.CENTER
        archiveCoverImageView.setBackgroundColor(ContextCompat.getColor(itemView.context, R.color.color_F5F5F5))
        archiveCoverImageView.setImageResource(R.drawable.ic_folder_black)
    }

    private fun addMatchedBookView(book: ShelfBookItemEntity, keyword: String) {
        val bookView = LayoutInflater.from(itemView.context)
            .inflate(R.layout.view_holder_shelf_store_item_layout, booksContainer, false)

        // 设置左边距（缩进）
        val params = bookView.layoutParams as ViewGroup.MarginLayoutParams
        params.leftMargin = itemView.resources.getDimensionPixelSize(R.dimen.micro_item_layout_padding)
        bookView.layoutParams = params

        val coverImageView =
            bookView.findViewById<AdaptiveImageView>(R.id.search_result_cover_ImageView)
        val nameTextView = bookView.findViewById<TextView>(R.id.search_result_name_TextView)
        val authorTextView = bookView.findViewById<TextView>(R.id.search_result_author_TextView)

        PictureUtil.loadRoundImageWithDefault(coverImageView, getPictureRemotePath(book.cover), dp2px(4))

        nameTextView.text = removeAndHighlight(book.resourceName, keyword, nameTextView)

        val authorText =
            SDKSingleton.userBl.formatAuthorName(book.authorList as ArrayList<AuthorEntity>?)
        authorTextView.text = removeAndHighlight(authorText, keyword, authorTextView)

        bookView.setOnClickListener {
            handleBookItemClick(book, it.context)
        }

        booksContainer.addView(bookView)
    }
}

// 标题头部视图
class TitleHeaderViewHolder(parent: ViewGroup) : BaseViewHolder(
    LayoutInflater.from(parent.context).inflate(R.layout.view_holder_title_header, parent, false)
) {
    private val titleKeywordTextView: TextView = itemView.findViewById(R.id.title_keyword_TextView)
    private val bookCountTextView: TextView = itemView.findViewById(R.id.book_count_TextView)

    override fun <T> initUIData(t: T, position: Int) {
        if (t is SearchResultItem.TitleHeader) {
            val fullTitle = t.title

            val parts = fullTitle.split(" ")

            if (parts.size >= 3) {
                try {
                    val count = parts.last().toInt()
                    // 前缀为第一个元素，关键词为中间所有部分
                    val prefix = parts[0]
                    val keywordParts = parts.subList(1, parts.size - 1)
                    val keywordPhrase = keywordParts.joinToString(" ")
                    val highlightedTitle = SpannableString("$prefix $keywordPhrase")
                    val startIndex = prefix.length + 1
                    val endIndex = startIndex + keywordPhrase.length
                    val highlightColor = ContextCompat.getColor(itemView.context, R.color.color_orange_FF8A00)
                    highlightedTitle.setSpan(
                        ForegroundColorSpan(highlightColor),
                        startIndex,
                        endIndex,
                        Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                    )
                    highlightedTitle.setSpan(
                        StyleSpan(Typeface.BOLD),
                        startIndex,
                        endIndex,
                        Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                    )
                    titleKeywordTextView.text = highlightedTitle
                    bookCountTextView.text = String.format(itemView.context.getString(R.string.book_count), count.toString())
                } catch (e: NumberFormatException) {
                    titleKeywordTextView.text = fullTitle
                    bookCountTextView.text = ""
                }
            } else {
                titleKeywordTextView.text = fullTitle
                bookCountTextView.text = ""
            }
        }
    }

    override fun <T> initUIData(t: T) {
        initUIData(t, adapterPosition)
    }
}

class MessageItemViewHolder(parent: ViewGroup) : BaseViewHolder(
    LayoutInflater.from(parent.context)
        .inflate(R.layout.view_holder_empty_shelf_state, parent, false)
) {
    private val messageTextView: TextView = itemView.findViewById(R.id.empty_state_message_TextView)

    override fun <T> initUIData(t: T, position: Int) {
        if (t is SearchResultItem.MessageItem) {
            messageTextView.text = t.message
        }
    }

    override fun <T> initUIData(t: T) {
        initUIData(t, adapterPosition)
    }
}

class FilterBarViewHolder(parent: ViewGroup, private val externalListener: StoreFilterBarView.OnFilterChangedListener?) : BaseViewHolder(
    LayoutInflater.from(parent.context).inflate(R.layout.view_holder_store_filter_bar, parent, false)
) {
    private val filterBarView: StoreFilterBarView = itemView as StoreFilterBarView

    init {
        filterBarView.setOnFilterChangedListener(object : StoreFilterBarView.OnFilterChangedListener {
            override fun onOrderFieldChanged(orderField: OrderField) {
                externalListener?.onOrderFieldChanged(orderField)
            }

            override fun onLanguageChanged(language: String) {
                externalListener?.onLanguageChanged(language)
            }
        })
    }

    fun bind(languageName: String, languageCode: String, orderField: OrderField) {
        if (languageName.isNotEmpty()) {
            filterBarView.setLanguage(languageName, languageCode)
        }
    }
}

fun removeAndHighlight(text: String, keyword: String, textView: TextView): CharSequence {
    if (text.isEmpty()) return ""

    val pattern = Pattern.compile("\\{\\{\\{(.+?)\\}\\}\\}")
    val matcher = pattern.matcher(text)

    val cleanedText = matcher.replaceAll("$1")

    // 如果没有关键词或者清理后的文本不包含关键词，直接返回清理后的文本
    if (keyword.isEmpty() || !cleanedText.contains(keyword, ignoreCase = true)) {
        return cleanedText
    }

    val spannableString = SpannableString(cleanedText)

    // 查找所有关键词的位置并高亮
    var startIndex = cleanedText.toLowerCase().indexOf(keyword.toLowerCase())
    while (startIndex >= 0) {
        val endIndex = startIndex + keyword.length
        val highlightColor = ContextCompat.getColor(textView.context, R.color.color_orange_FF8A00)

        spannableString.setSpan(
            ForegroundColorSpan(highlightColor),
            startIndex,
            endIndex,
            Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
        )

        spannableString.setSpan(
            StyleSpan(Typeface.BOLD),
            startIndex,
            endIndex,
            Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
        )

        // 继续查找下一个匹配项
        startIndex = cleanedText.toLowerCase().indexOf(keyword.toLowerCase(), startIndex + 1)
    }

    return spannableString
}

// 处理书籍点击事件的公共方法
private fun handleBookItemClick(book: ShelfBookItemEntity, context: android.content.Context) {
    val downloadInfo = book.resourceDownloadInfo
    if ((downloadInfo.downloadStatus == DownloadStatus.COMPLETE || downloadInfo.downloadStatus == DownloadStatus.UPDATE) &&
        File(downloadInfo.getActualFilePath()).exists()
    ) {
        book.lastVisitTime = System.currentTimeMillis()
        BookReadActivity.gotoBookReadActivity(
            context,
            downloadInfo.getActualFilePath(),
            downloadInfo.fileId,
            book.resourceId
        )
    } else {
        ToastUtil.showToastCenter(R.string.book_not_downloaded_please_wait)
    }
}

// 处理文件夹点击事件的公共方法
private fun handleArchiveItemClick(archive: ShelfArchiveItemEntity, context: android.content.Context) {
    val pattern = Pattern.compile("\\{\\{\\{(.+?)\\}\\}\\}")
    val matcher = pattern.matcher(archive.archiveName)
    archive.archiveName = matcher.replaceAll("$1")
    
    FolderBookListActivity.gotoFolderBookListActivity(context, archive)
}
