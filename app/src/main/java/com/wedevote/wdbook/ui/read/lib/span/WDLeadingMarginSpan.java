package com.wedevote.wdbook.ui.read.lib.span;

import android.graphics.Canvas;
import android.graphics.Paint;
import android.text.Layout;

/**
 * 无论是否首行都缩进
 */
public class WDLeadingMarginSpan extends
		WDMarginSpan {

	private final int marginLead;
	private final int marginNormal;
	private final int lines;
	private final int id;

	public WDLeadingMarginSpan(int lines, int marginLead, int marginNormal, int id) {
		this.marginLead = marginLead;
		this.marginNormal = marginNormal;
		this.lines = lines;
		this.id = id;
	}

	public int getId(){
		return id;
	}

	/**
	 * Apply the margin
	 * 
	 * @param first
	 * @return
	 */
	@Override
	public int getLeadingMargin(boolean first) {
		if(first){
			return marginLead;
		}else{
			return marginNormal;
		}
	}

	@Override
	public void drawLeadingMargin(Canvas c, Paint p, int x, int dir, int top,
			int baseline, int bottom, CharSequence text, int start, int end,
			boolean first, Layout layout) {
	}

	@Override
	public int getLeadingMarginLineCount() {
		return lines;
	}

    @Override
    public int getLeadingMargin(int lineNumber) {
        if (lineNumber <= lines) {
            return marginLead;
        }else{
        	return marginNormal;
        }
    }
}
