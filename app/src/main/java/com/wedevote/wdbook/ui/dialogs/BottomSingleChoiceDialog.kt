package com.wedevote.wdbook.ui.dialogs

import android.app.Dialog
import android.content.Context
import android.os.Bundle
import android.view.Gravity
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.widget.Button
import android.widget.ImageView
import android.widget.TextView
import com.aquila.lib.base.BaseRecycleAdapter
import com.aquila.lib.base.BaseViewHolder
import com.aquila.lib.base.IGetStringInterface
import com.aquila.lib.widget.view.CustomRecyclerView
import com.wedevote.wdbook.R
import com.wedevote.wdbook.tools.interfaces.IGetStringInterfaceImpl
import com.wedevote.wdbook.tools.interfaces.OnItemClickListener

/***
 * @date 创建时间 2020/4/25 16:47
 * <AUTHOR> W.YuLong
 * @description 从底部弹出的单选对话框
 */
class BottomSingleChoiceDialog(val builder: Builder) : Dialog(builder.context), View.OnClickListener {
    lateinit var dataRecyclerView: CustomRecyclerView
    lateinit var cancelButton: Button
    lateinit var itemChoiceAdapter: ItemChoiceAdapter

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.dialog_single_choice_bottom_layout)
        dataRecyclerView = findViewById(R.id.bottom_single_choice_data_RecyclerView)
        cancelButton = findViewById(R.id.bottom_single_choice_cancel_Button)
        itemChoiceAdapter = ItemChoiceAdapter(builder)
        dataRecyclerView.adapter = itemChoiceAdapter
        configDialog()

        cancelButton.setOnClickListener(this)
    }

    protected fun configDialog() {
        val wl = window!!.attributes
        wl.gravity = Gravity.BOTTOM // 设置重力
        wl.width = WindowManager.LayoutParams.MATCH_PARENT
        wl.height = WindowManager.LayoutParams.WRAP_CONTENT
        window!!.setWindowAnimations(com.aquila.lib.dialog.R.style.bottomDialogWindowAnim)
        window!!.attributes = wl
        window!!.setBackgroundDrawableResource(android.R.color.transparent)
    }

    override fun onClick(v: View?) {
        if (v == cancelButton) {
            dismiss()
        }
    }

    /***
     *@date 创建时间 2020/4/25 17:16
     *<AUTHOR> W.YuLong
     *@description
     */
    class ItemChoiceAdapter(val builder: Builder) : BaseRecycleAdapter<IGetStringInterface, ItemChoiceViewHolder>() {

        init {
            dataList = builder.itemList
        }

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ItemChoiceViewHolder {
            return ItemChoiceViewHolder(parent)
        }

        override fun onBindViewHolder(holder: ItemChoiceViewHolder, position: Int) {
            val data = dataList!![position]
            holder.initUIData(data, position)
            holder.setChecked(position == builder.currentPosition)
            holder.itemView.setOnClickListener { v ->
                builder.setCurrentPosition(position)
                builder.onItemClickListener?.onItemClick(v, position.toString(), data)
                notifyDataSetChanged()
            }
        }
    }

    /***
     *@date 创建时间 2020/4/25 17:15
     *<AUTHOR> W.YuLong
     *@description
     */
    class ItemChoiceViewHolder(parent: ViewGroup) : BaseViewHolder(parent, R.layout.item_single_choice_layout) {
        val descTextView: TextView = itemView.findViewById(R.id.item_bottom_single_choice_desc_TextView)
        val checkImageView: ImageView = itemView.findViewById(R.id.item_bottom_single_choice_choice_ImageView)

        override fun <T> initUIData(t: T, position: Int) {
            t as IGetStringInterface
            descTextView.text = t.getString()
        }

        fun setChecked(isChekced: Boolean) {
            checkImageView.visibility = if (isChekced) View.VISIBLE else View.GONE
        }
    }

    /***
     *@date 创建时间 2020/4/25 17:14
     *<AUTHOR> W.YuLong
     *@description
     */
    class Builder(val context: Context) {
        var itemList: ArrayList<IGetStringInterface> = ArrayList<IGetStringInterface>()
            private set
        var currentPosition: Int = -1
            private set
        var onItemClickListener: OnItemClickListener? = null
            private set

        var cancelable: Boolean = true
            private set

        companion object {
            fun with(context: Context): Builder {
                return Builder(context)
            }
        }

        fun setCancelable(b: Boolean): Builder {
            cancelable = b
            return this
        }

        fun setItemStringList(list: ArrayList<String>?): Builder {
            itemList.clear()
            if (list != null) {
                for (str in list) {
                    itemList.add(object : IGetStringInterfaceImpl<String>(str) {
                        override fun getString(): String {
                            return str
                        }
                    })
                }
            }
            return this
        }

        fun setItemList(list: ArrayList<IGetStringInterface>?): Builder {
            itemList.clear()
            if (list != null) {
                this.itemList.addAll(list)
            }
            return this
        }

        fun setCurrentPosition(position: Int): Builder {
            currentPosition = position
            return this
        }

        fun setonItemClickListener(onItemClickListener: OnItemClickListener?): Builder {
            this.onItemClickListener = onItemClickListener
            return this
        }

        fun create(): BottomSingleChoiceDialog {
            return BottomSingleChoiceDialog(this)
        }
    }
}
