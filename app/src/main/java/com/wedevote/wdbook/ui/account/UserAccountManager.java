package com.wedevote.wdbook.ui.account;

import android.app.Activity;
import android.widget.TextView;

import com.wedevote.wdbook.R;

import java.util.Locale;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Created by theophilus on 10/6/16.
 */

public class UserAccountManager {

    public static String getCountryCode(TextView codeText) {
        String code = codeText.getText().toString();
        if (code.isEmpty()) {
            return "+86";
        } else {
            return code;
        }
    }

    public static String getCurrentCountryCode(Activity activity) {
        String code = Locale.getDefault().getCountry();
        String[] country_items = activity.getResources().getStringArray(R.array.country_name_and_code);
        String countryCode = "86";
        for (String item:country_items) {
            if (item.contains(code)) {
                String[] codes = item.split(" ");
                countryCode = codes[codes.length-1];
            }
        }
        return "+"+countryCode;
    }

    //判断email格式是否正确
    public static boolean isEmail(String email) {
        String emailRegex = "^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Z]{2,}$";
        Pattern pattern = Pattern.compile(emailRegex, Pattern.CASE_INSENSITIVE);
        Matcher matcher = pattern.matcher(email);
        return matcher.matches();
    }

    public static boolean isMobileNO(String code, String mobiles) {
        String parameter = "^1[345789]\\d{9}";
        if (!"+86".equals(code)) {
            parameter = "^\\d{10}";
        }
        Pattern p = Pattern.compile(parameter);
        Matcher m = p.matcher(mobiles);
        return m.matches();
    }

    /**
     * 将完整的电话号码拆分为国家代码和号码的其余部分
     */
    public static String[] splitPhoneNumber(String fullNumber, Activity activity) {
        if (fullNumber == null || !fullNumber.startsWith("+")) {
            return new String[]{"", fullNumber == null ? "" : fullNumber};
        }
        String[] items = activity.getResources().getStringArray(R.array.country_name_and_code);
        java.util.List<String> codes = new java.util.ArrayList<>();
        for (String item : items) {
            String[] parts = item.split(" ");
            codes.add(parts[parts.length - 1]);
        }
        java.util.Collections.sort(codes, (a, b) -> b.length() - a.length());
        for (String code : codes) {
            String prefix = "+" + code;
            if (fullNumber.startsWith(prefix)) {
                return new String[]{prefix, fullNumber.substring(prefix.length())};
            }
        }
        String prefix = fullNumber.length() > 1 ? fullNumber.substring(0, 2) : fullNumber;
        String rest = fullNumber.length() > 2 ? fullNumber.substring(2) : "";
        return new String[]{prefix, rest};
    }
}
