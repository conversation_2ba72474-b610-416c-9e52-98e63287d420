package com.wedevote.wdbook.ui.account.register

import android.app.Activity
import android.content.Intent
import android.os.Message
import android.text.Editable
import android.text.InputFilter
import android.text.TextWatcher
import android.text.method.HideReturnsTransformationMethod
import android.text.method.PasswordTransformationMethod
import android.view.View
import android.widget.EditText
import android.widget.ImageView
import android.widget.TextView
import com.aquila.lib.dialog.CommProgressDialog
import com.aquila.lib.dialog.CommProgressDialog.Companion.with
import com.aquila.lib.tools.singleton.SPSingleton
import com.aquila.lib.tools.singleton.SPSingleton.Companion.get
import com.aquila.lib.tools.util.ToastUtil
import com.aquila.lib.tools.util.ToastUtil.showToastShort
import com.wdbible.app.wedevotebible.tools.security.EncodingUtils
import com.wedevote.wdbook.R
import com.wedevote.wdbook.base.APPConfig
import com.wedevote.wdbook.base.ExceptionHandler
import com.wedevote.wdbook.base.SDKSingleton
import com.wedevote.wdbook.constants.CodeType
import com.wedevote.wdbook.constants.Constants
import com.wedevote.wdbook.exception.ApiException
import com.wedevote.wdbook.exception.ErrorInfo
import com.wedevote.wdbook.tools.util.APPUtil
import com.wedevote.wdbook.tools.util.IntentConstants
import com.wedevote.wdbook.tools.util.NetWorkUtils
import com.wedevote.wdbook.tools.util.SPKeyDefine
import com.wedevote.wdbook.tools.util.UnitFormatUtil
import com.wedevote.wdbook.tools.util.WeakHandler
import com.wedevote.wdbook.ui.account.LoginManager
import com.wedevote.wdbook.ui.account.UserAccountManager
import com.wedevote.wdbook.ui.dialogs.RegisterTipDialog
import com.wedevote.wdbook.ui.user.HomeMineFragment
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import java.util.*

/***
 * @date 创建时间 2023.04.13
 * <AUTHOR> John.Qian
 * @description
 */
class RegisterLayoutManager(protected var activity: Activity) : View.OnClickListener {
    var rootView: View
        protected set
    var waitSecondMobile = 0
    var waitSecondEmail = 0
    val progressDialog: CommProgressDialog
    var loginManager: LoginManager? = null
    var accountEditText: EditText? = null
    var verifyEditText: EditText? = null
    var countryCodeTextView: TextView? = null
    var mobileVerifyTextView: TextView? = null
    var emailVerifyTextView: TextView? = null
    var phoneTitleTextView: TextView? = null
    var nameEditText: EditText? = null
    var phoneDivideLine: View? = null
    var nameTextView: View? = null
    var codeLayout: View? = null
    var isResetPassword: Boolean = false
    var isBindPhoneOrEmail: Boolean = false
    lateinit var passwordEditText: EditText
    lateinit var passwordEditTextTwo: EditText
    private lateinit var accountLayout: View
    private lateinit var passwordlayout: View
    private lateinit var passwordTwolayout: View
    private lateinit var tipTextView: TextView
    private lateinit var passwordErrorTipTextView: TextView
    private lateinit var passwordErrorTipTextViewTwo: TextView
    private lateinit var codeErrorTextView: TextView
    private lateinit var nameErrorTextView: TextView
    lateinit var verifyCodeTitleTextView: TextView
    private lateinit var tipImageView: ImageView
    private lateinit var clearImageView: ImageView
    private lateinit var passwordImageView: ImageView
    private lateinit var passwordTwoImageView: ImageView
    private lateinit var securityCheckingTextView: TextView
    var registerType = REGISTER_MODE_EMAIL
    var checkingPassword = false

    private fun initListener() {
        mobileVerifyTextView!!.setOnClickListener(this)
        emailVerifyTextView!!.setOnClickListener(this)
        clearImageView!!.setOnClickListener(this)
        countryCodeTextView!!.setOnClickListener(this)
        passwordImageView!!.setOnClickListener(this)
        passwordTwoImageView!!.setOnClickListener(this)
        passwordImageView.isSelected = false
        passwordTwoImageView.isSelected = false
        accountEditText!!.setOnFocusChangeListener(object : View.OnFocusChangeListener {
            override fun onFocusChange(p0: View?, p1: Boolean) {
                if (!p1 && accountEditText!!.text.isNotEmpty()) {
                    checkInputFormat()
                }
                if (!p1) {
                    clearImageView.visibility = View.GONE
                } else if (accountEditText!!.text.isNotEmpty()) {
                    clearImageView.visibility = View.VISIBLE
                    setErrorTip(false)
                }
            }
        })
        accountEditText!!.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(charSequence: CharSequence, i: Int, i1: Int, i2: Int) {}
            override fun onTextChanged(charSequence: CharSequence, i: Int, i1: Int, i2: Int) {}
            override fun afterTextChanged(editable: Editable) {
                checkNextButtonState()
                if (accountEditText!!.text.isNotEmpty()) {
                    checkInputFormat(false)
                } else {
                    setErrorTip(false)
                    checkVerifyButtonState(true)
                }
                tipImageView.visibility = View.GONE
                if (accountEditText!!.text.isNotEmpty()) {
                    clearImageView.visibility = View.VISIBLE
                } else {
                    clearImageView.visibility = View.GONE
                }
            }
        })
        verifyEditText!!.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(charSequence: CharSequence, i: Int, i1: Int, i2: Int) {}
            override fun onTextChanged(charSequence: CharSequence, i: Int, i1: Int, i2: Int) {}
            override fun afterTextChanged(editable: Editable) {
                checkNextButtonState()
                codeErrorTextView.visibility = View.GONE
                codeLayout!!.isSelected = false
            }
        })
        nameEditText!!.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(charSequence: CharSequence, i: Int, i1: Int, i2: Int) {}
            override fun onTextChanged(charSequence: CharSequence, i: Int, i1: Int, i2: Int) {
                if (charSequence.length > 30) {
                    ToastUtil.showToastShort(R.string.max_length_30)
                    var text = charSequence.toString().substring(0,30)
                    nameEditText!!.setText(text)
                    nameEditText!!.setSelection(text.length)
                } else if (charSequence.length < 30) {
                    nameErrorTextView.visibility = View.GONE
                    nameEditText!!.isSelected = false
                }
            }
            override fun afterTextChanged(editable: Editable) {
                checkRegisterButtonState()
            }
        })
        passwordEditText!!.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(charSequence: CharSequence, i: Int, i1: Int, i2: Int) {}
            override fun onTextChanged(charSequence: CharSequence, i: Int, i1: Int, i2: Int) {}
            override fun afterTextChanged(editable: Editable) {
                checkRegisterButtonState()
                passwordEditText.isSelected = false
                passwordErrorTipTextView.visibility = View.GONE
            }
        })
        passwordEditTextTwo!!.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(charSequence: CharSequence, i: Int, i1: Int, i2: Int) {}
            override fun onTextChanged(charSequence: CharSequence, i: Int, i1: Int, i2: Int) {}
            override fun afterTextChanged(editable: Editable) {
                checkRegisterButtonState()
                passwordEditTextTwo.isSelected = false
                passwordErrorTipTextViewTwo.visibility = View.GONE
            }
        })

        passwordEditText!!.setOnFocusChangeListener(object : View.OnFocusChangeListener {
            override fun onFocusChange(p0: View?, p1: Boolean) {
                if (!p1) {
                    isPasswordCorrect()
                } else {
                    passwordEditText.isSelected = false
                    passwordErrorTipTextView.visibility = View.GONE
                }
            }
        })
        passwordEditTextTwo!!.setOnFocusChangeListener(object : View.OnFocusChangeListener {
            override fun onFocusChange(p0: View?, p1: Boolean) {
                if (p1) {
                    passwordEditTextTwo.isSelected = false
                    passwordErrorTipTextViewTwo.visibility = View.GONE
                } else {
                    if (isPasswordCorrect(false)) {
                        isPasswordTwoCorrect()
                    }
                }
            }
        })
    }

    fun checkNextButtonState(): Boolean {
        if (accountEditText?.text.toString().trim()
                .isEmpty() || verifyEditText?.text.toString().trim().isEmpty()
        ) {
            buttonStateChangeListener?.invoke(false)
            return false
        } else {
            buttonStateChangeListener?.invoke(true)
            return true
        }
    }

    fun checkRegisterButtonState() {
        if (isResetPassword && passwordEditText?.text.toString().trim()
                .isNotEmpty() && passwordEditTextTwo?.text.toString().trim().isNotEmpty()
        ) {
            buttonStateChangeListener?.invoke(true)
        } else if (nameEditText?.text.toString().trim()
                .isEmpty() || passwordEditText?.text.toString().trim()
                .isEmpty() || passwordEditTextTwo?.text.toString().trim().isEmpty()
        ) {
            buttonStateChangeListener?.invoke(false)
        } else {
            buttonStateChangeListener?.invoke(true)
        }
    }

    fun isPasswordCorrect(isShowErrorTip: Boolean = true): Boolean {
        if (!isResetPassword && nameEditText!!.text.toString().trim { it <= ' ' }.length <= 0) {
            if (isShowErrorTip) {
                nameEditText!!.isSelected = true
                nameErrorTextView.visibility = View.VISIBLE
                nameErrorTextView.text = activity.getString(R.string.name_can_not_empty)
            }
            return false
        } else if (passwordEditText!!.text.length <= 0) {
            if (isShowErrorTip) {
                showToastShort(R.string.password_can_not_be_empty)
            }
            return false
        } else if (passwordEditText!!.text.length < 7) {
            if (isShowErrorTip) {
                passwordEditText.isSelected = true
                passwordErrorTipTextView.visibility = View.VISIBLE
                passwordErrorTipTextView.setText(R.string.password_length_can_not_less_than_seven)
            }
            return false
        }
        return true
    }

    fun isPasswordTwoCorrect(isShowErrorTip: Boolean = true): Boolean {
        if (passwordEditText!!.text.toString() != passwordEditTextTwo!!.text.toString()) {
            if (isShowErrorTip) {
                passwordEditTextTwo.isSelected = true
                passwordErrorTipTextViewTwo.visibility = View.VISIBLE
                passwordErrorTipTextViewTwo.setText(R.string.password_not_match)
            }
            return false
        }
        return true
    }

    fun getCurrentCountryCode(activity: Activity): String {
        val code = Locale.getDefault().country
        val country_items = activity.resources.getStringArray(R.array.country_name_and_code)
        var countryCode = "86"
        for (item in country_items) {
            if (item.contains(code)) {
                val codes = item.split(" ".toRegex()).dropLastWhile { it.isEmpty() }.toTypedArray()
                countryCode = codes[codes.size - 1]
            }
        }
        return "+$countryCode"
    }

    @JvmName("setRegisterType1")
    fun setRegisterType(type: Int, isEditable: Boolean = true) {
        registerType = type
        nameTextView!!.visibility = View.GONE
        nameEditText!!.visibility = View.GONE
        if (registerType == REGISTER_MODE_MOBILE) {
            countryCodeTextView!!.visibility = View.VISIBLE
            phoneDivideLine!!.visibility = View.VISIBLE
            passwordlayout!!.visibility = View.GONE
            passwordTwolayout!!.visibility = View.GONE
            phoneTitleTextView!!.setText(R.string.phone_number)
            accountEditText?.hint = activity.getString(R.string.please_input_phone_number)
            mobileVerifyTextView!!.visibility = View.VISIBLE
            emailVerifyTextView!!.visibility = View.GONE
        } else {
            countryCodeTextView!!.visibility = View.GONE
            phoneDivideLine!!.visibility = View.GONE
            passwordlayout!!.visibility = View.GONE
            passwordTwolayout!!.visibility = View.GONE
            phoneTitleTextView!!.setText(R.string.email_address)
            accountEditText?.hint = activity.getString(R.string.please_input_email_address)
            emailVerifyTextView!!.visibility = View.VISIBLE
            mobileVerifyTextView!!.visibility = View.GONE
        }

        if (!isEditable) {
            accountLayout.visibility = View.GONE
            verifyCodeTitleTextView.visibility = View.GONE
            securityCheckingTextView.visibility = View.VISIBLE
            phoneTitleTextView!!.visibility = View.GONE
            if (HomeMineFragment.userInfoEntity != null) {
                if (registerType == REGISTER_MODE_MOBILE) {
                    securityCheckingTextView.setText(String.format(
                        activity.getString(R.string.click_to_get_and_enter_verification_code),
                        UnitFormatUtil.maskPhoneNumber(HomeMineFragment.userInfoEntity!!.mobile!!)
                    ))
                    val result = UserAccountManager.splitPhoneNumber(HomeMineFragment.userInfoEntity!!.mobile, activity)
                    setCountryCodeText(result[0])
                    accountEditText!!.setText(result[1])
                } else if (registerType == REGISTER_MODE_EMAIL) {
                    securityCheckingTextView.setText(String.format(
                        activity.getString(R.string.click_to_get_and_enter_verification_code),
                        HomeMineFragment.userInfoEntity!!.email
                    ))
                    accountEditText!!.setText(HomeMineFragment.userInfoEntity!!.email)
                }
            }
        }
    }

    fun findViewsByIdFromXML() {
        accountEditText = rootView.findViewById(R.id.register_mobile_mobile_EditText)
        securityCheckingTextView = rootView.findViewById(R.id.security_checking_TextView)
        verifyEditText = rootView.findViewById(R.id.register_mobile_verifyCode_EditText)
        mobileVerifyTextView = rootView.findViewById(R.id.register_mobile_get_verifyCode_TextView)
        emailVerifyTextView = rootView.findViewById(R.id.register_email_get_verifyCode_TextView)
        mobileVerifyTextView?.setText(R.string.get_verify_code)
        emailVerifyTextView?.setText(R.string.get_verify_code)
        countryCodeTextView = rootView.findViewById(R.id.register_country_code_TextView)
        phoneTitleTextView = rootView.findViewById(R.id.register_phone_title_TextView)
        phoneDivideLine = rootView.findViewById(R.id.register_country_divide_TextView)
        nameTextView = rootView.findViewById(R.id.register_name_title_TextView)
        nameEditText = rootView.findViewById(R.id.register_name_EditText)
        passwordEditText = rootView.findViewById(R.id.register_password_EditText)
        passwordErrorTipTextView = rootView.findViewById(R.id.password_error_tip_TextView)
        passwordErrorTipTextViewTwo = rootView.findViewById(R.id.password_error_tip_Two_TextView)
        passwordEditTextTwo = rootView.findViewById(R.id.register_password1_EditText)
        codeLayout = rootView.findViewById(R.id.register_verifyCode_layout)
        tipTextView = rootView.findViewById(R.id.phone_error_tip_TextView)
        tipImageView = rootView.findViewById(R.id.error_tip_ImageView)
        clearImageView = rootView.findViewById(R.id.clear_text_ImageView)
        passwordImageView = rootView.findViewById(R.id.register_password_ImageView)
        passwordTwoImageView = rootView.findViewById(R.id.register_password_two_ImageView)
        accountLayout = rootView.findViewById(R.id.register_account_layout)
        passwordlayout = rootView.findViewById(R.id.register_password_layout)
        passwordTwolayout = rootView.findViewById(R.id.register_password_two_layout)
        codeErrorTextView = rootView.findViewById(R.id.code_error_tip_TextView)
        nameErrorTextView = rootView.findViewById(R.id.name_error_tip_TextView)
        verifyCodeTitleTextView = rootView.findViewById(R.id.register_verifyCode_title_TextView)
        setEditTextInputSpace(accountEditText)
        setEditTextInputSpace(verifyEditText)
        setEditTextInputSpace(nameEditText)
        setEditTextInputSpace(passwordEditText)
        setEditTextInputSpace(passwordEditTextTwo)
    }

    override fun onClick(v: View) {
        if (v === mobileVerifyTextView) {
            if (APPConfig.isFastClick()) return
            if (!NetWorkUtils.isNetworkAvailable()) {
                NetWorkUtils.showTipDialog(activity, activity.getString(R.string.no_network_connect))
                return
            }
            if (checkInputFormat()) {
                var account = accountEditText!!.text.toString()
                if (registerType == REGISTER_MODE_MOBILE) {
                    account = UserAccountManager.getCountryCode(countryCodeTextView) + account
                    sendVerifyCode(account, true)
                }
            }
        } else if (v === emailVerifyTextView) {
            if (APPConfig.isFastClick()) return
            if (!NetWorkUtils.isNetworkAvailable()) {
                NetWorkUtils.showTipDialog(activity, activity.getString(R.string.no_network_connect))
                return
            }
            if (checkInputFormat()) {
                var account = accountEditText!!.text.toString()
                if (registerType == REGISTER_MODE_EMAIL) {
                    sendVerifyCode(account, false)
                }
            }
        } else if (v === countryCodeTextView) {
            val intent = Intent(activity, CountryCodeChooseActivity::class.java)
            activity.startActivityForResult(intent, IntentConstants.INTENT_RESULT_COUNTRY_CODE)
        } else if (v === passwordImageView) {
            setPasswordShowState(passwordImageView.isSelected, passwordImageView, passwordEditText)
        } else if (v === passwordTwoImageView) {
            setPasswordShowState(
                passwordTwoImageView.isSelected,
                passwordTwoImageView,
                passwordEditTextTwo
            )
        } else if (v === clearImageView) {
            accountEditText!!.setText("")
        }
    }

    private fun sendVerifyCode(account: String, isMobile: Boolean) {
        MainScope().launch {
            try {
                if (!progressDialog.isShowing) {
                    progressDialog.show()
                }
                val encryptSign = EncodingUtils.rsaEncryptByPublicKey(
                    Constants.APP_KEY_ANDROID,
                    Constants.PUBLICK_KEY
                )
                val codeToken = SDKSingleton.sessionBl.getCodeToken()
                if (codeToken != null) {
                    val byteArray = codeToken.toByteArray()
                    val secret = EncodingUtils.getMD5(byteArray)
                    val encryptToken = EncodingUtils.encryptData(byteArray, secret)
                    SDKSingleton.sessionBl.getAccessToken()
                    if (isMobile) {
                        SDKSingleton.sessionBl.sendVerifyCode(
                            "",
                            account,codeToken,encryptToken,
                            if (isResetPassword) CodeType.RESET else CodeType.REGISTER0,
                            encryptSign
                        )
                    } else {
                        SDKSingleton.sessionBl.sendVerifyCode(
                            account,
                            "",codeToken,encryptToken,
                            if (isResetPassword) CodeType.RESET else CodeType.REGISTER0,
                            encryptSign
                        )
                        var tipDialog = RegisterTipDialog(activity)
                        tipDialog.show()
                        tipDialog.setContentText(
                            activity.getString(R.string.have_send_an_verify_code_to_your_email)
                                .format(accountEditText!!.text.toString())
                        )
                    }
                    val msg = Message()
                    msg.what = Constants.MSG_GET_VERIFY_CODE
                    msg.arg1 = Constants.HTTP_RESULT_SUCCESS
                    handler.sendMessage(msg)
                }
                progressDialog.dismiss()
            } catch (exception: Throwable) {
                progressDialog.dismiss()
                val msg = Message()
                msg.what = Constants.MSG_GET_VERIFY_CODE
                msg.arg1 = Constants.HTTP_RESULT_FAILED
                handler.sendMessage(msg)
                if (exception is ApiException) {
                    when (exception.code) {
                        ErrorInfo.UserNotExists.code, ErrorInfo.UserNotExists2.code -> {
                            setErrorTip(true, activity.getString(R.string.this_account_not_exist), true)
                        }
                        ErrorInfo.SmsExceedLimit.code -> {
                            APPUtil.showTipDialog(
                                activity,
                                activity.getString(R.string.warm_prompt_title),
                                activity.getString(R.string.verification_code_too_often)
                            )
                        }
                        ErrorInfo.UserExists.code -> {
                            if (isBindPhoneOrEmail) {
                                if (registerType == REGISTER_MODE_MOBILE) {
                                    setErrorTip(true, activity.getString(R.string.phone_number_already_exist))
                                } else {
                                    setErrorTip(true, activity.getString(R.string.email_already_exist))
                                }
                            } else {
                                setErrorTip(true, activity.getString(R.string.user_already_exist))
                            }
                        }
                        else -> {
                            showToastShort(activity.getString(R.string.send_failed_please_check))
                        }
                    }
                } else {
                    ExceptionHandler.handleException(exception)
                }
            }
        }
    }

    fun setPasswordShowState(
        isHidePassword: Boolean,
        viewImageView: ImageView,
        passwordEditText: EditText
    ) {
        if (isHidePassword) {
            passwordEditText.transformationMethod = PasswordTransformationMethod.getInstance()
            viewImageView.contentDescription = "点击显示密码"
        } else {
            passwordEditText.transformationMethod = HideReturnsTransformationMethod.getInstance()
            viewImageView.contentDescription = "点击隐藏密码"
        }
        viewImageView.isSelected = !viewImageView.isSelected
        passwordEditText.setSelection(passwordEditText.text.length)
    }

    fun setCountryCodeText(codeText: String?) {
        countryCodeTextView!!.text = codeText
    }

    fun setErrorTip(isError: Boolean, text: String = "", isShowError: Boolean = true) {
        if (isShowError) {
            if (isError) {
                clearImageView.visibility = View.GONE
                tipImageView.visibility = View.VISIBLE
                tipTextView.visibility = View.VISIBLE
                tipTextView.text = text
            } else {
                tipTextView.visibility = View.GONE
            }
            accountLayout.isSelected = isError
        } else {
            tipImageView.visibility = View.GONE
            tipTextView.visibility = View.GONE
            accountLayout.isSelected = false
        }
    }

    fun checkVerifyButtonState(isError: Boolean) {
        if (registerType == REGISTER_MODE_MOBILE) {
            if (waitSecondMobile > 0 && !isError) {
                return
            }
            mobileVerifyTextView!!.isEnabled = !isError
        } else if (registerType == REGISTER_MODE_EMAIL){
            if (waitSecondEmail > 0 && !isError) {
                return
            }
            emailVerifyTextView!!.isEnabled = !isError
        }
    }

    fun checkInputFormat(isShowError: Boolean = true): Boolean {
        val account = accountEditText!!.text.toString()
        if (registerType == REGISTER_MODE_MOBILE) {
            if (!UserAccountManager.isMobileNO(
                    UserAccountManager.getCountryCode(countryCodeTextView),
                    account
                )
            ) {
                setErrorTip(true, activity.getString(R.string.invalid_phone_number), isShowError)
                checkVerifyButtonState(true)
                return false
            }
        } else {
            if (!UserAccountManager.isEmail(account)) {
                setErrorTip(true, activity.getString(R.string.invalid_email_number), isShowError)
                checkVerifyButtonState(true)
                return false
            }
        }
        setErrorTip(false, "", isShowError)
        checkVerifyButtonState(false)
        return true
    }

    /**
     * 禁止EditText输入空格和换行符
     *
     * @param editText EditText输入框
     */
    internal fun setEditTextInputSpace(editText: EditText?) {
        val filter = InputFilter { source, start, end, dest, dstart, dend ->
            if (source == " " || source.toString().contentEquals("\n")) {
                ""
            } else {
                null
            }
        }
        editText!!.filters = arrayOf(filter)
    }

    fun registerUserAccount(callBack: (isSuccess: Boolean) -> Unit) {
        if (isPasswordCorrect() && isPasswordTwoCorrect()) {
            MainScope().launch {
                try {
                    if (!progressDialog.isShowing) {
                        progressDialog.show()
                    }
                    val sign =
                        Constants.APP_KEY_ANDROID + "\n" + 1 + "\n" + passwordEditText.text.toString() + "\n" + nameEditText!!.text.toString()
                    val encryptSign =
                        EncodingUtils.rsaEncryptByPublicKey(sign, Constants.PUBLICK_KEY)
                    verifyTokenStr?.let { SDKSingleton.sessionBl.registration(encryptSign, it) }

                    showToastShort(R.string.register_success)
                    if (loginManager == null) {
                        loginManager = LoginManager(activity)
                    }
                    val userName: String
                    userName = if (registerType == REGISTER_MODE_EMAIL) {
                        accountEditText!!.text.toString()
                    } else {
                        UserAccountManager.getCountryCode(countryCodeTextView) + accountEditText!!.text.toString()
                    }

                    tipTextView.visibility = View.GONE
                    tipImageView.visibility = View.VISIBLE
                    accountLayout.isSelected = false
                    clearImageView.visibility = View.GONE

                    progressDialog.dismiss()
                    loginManager!!.startLogin(
                        activity,
                        userName,
                        passwordEditText!!.text.toString()
                    ) {
                        callBack.invoke(it)
                    }
                } catch (exception: Throwable) {
                    progressDialog.dismiss()
                    callBack.invoke(false)
                    if (exception is ApiException) {
                        when (exception.code) {
                            ErrorInfo.InvalidVerificationCode.code -> {
                                codeErrorTextView.visibility = View.VISIBLE
                                codeLayout!!.isSelected = true
                                codeErrorTextView.text =
                                    activity.getString(R.string.invalid_verification_code)
                            }
                            else -> {
                                showToastShort(exception.message)
                            }
                        }
                    } else {
                        ExceptionHandler.handleException(exception)
                    }
                }
            }
        }
    }

    var verifyTokenStr: String? = null
    fun checkVerificationCode(isChangePassword: Boolean = false, function: () -> Unit) {
        MainScope().launch {
            try {
                if (!progressDialog.isShowing) {
                    progressDialog.show()
                }
                val params = buildVerifyParams()
                verifyTokenStr = if (isChangePassword) {
                    SDKSingleton.sessionBl.checkVerifyCodeForChangePassword(
                        params.first,
                        params.second,
                        params.third,
                    )
                } else {
                    val encryptSign = EncodingUtils.rsaEncryptByPublicKey(
                        Constants.APP_KEY_ANDROID,
                        Constants.PUBLICK_KEY
                    )
                    SDKSingleton.sessionBl.checkVerifyCode(
                        params.first,
                        params.second,
                        params.third,
                        encryptSign
                    )
                }
                progressDialog.dismiss()
                function.invoke()
            } catch (exception: Throwable) {
                progressDialog.dismiss()
                handleVerifyException(exception)
            }
        }
    }

    private fun buildVerifyParams(): Triple<String, String, String> {
        var account = accountEditText!!.text.toString()
        val verificationCode = verifyEditText!!.text.toString()
        return if (registerType == REGISTER_MODE_MOBILE) {
            account = UserAccountManager.getCountryCode(countryCodeTextView) + account
            Triple("", account, verificationCode)
        } else {
            Triple(account, "", verificationCode)
        }
    }

    private fun handleVerifyException(exception: Throwable) {
        if (exception is ApiException) {
            when (exception.code) {
                ErrorInfo.UserNotExists.code, ErrorInfo.UserNotExists2.code -> {
                    APPUtil.showTipDialog(
                        activity,
                        activity.getString(R.string.warm_prompt_title),
                        activity.getString(R.string.this_account_not_exist)
                    )
                }
                ErrorInfo.InvalidVerificationCode.code -> {
                    codeErrorTextView.visibility = View.VISIBLE
                    codeLayout!!.isSelected = true
                    codeErrorTextView.text =
                        activity.getString(R.string.invalid_verification_code)
                }
                else -> {
                    showToastShort(exception.message)
                }
            }
        } else {
            ExceptionHandler.handleException(exception)
        }
    }

    fun resetPassword(function: () -> Unit) {
        if (!isPasswordCorrect() || !isPasswordTwoCorrect()) return
        MainScope().launch {
            try {
                if (!progressDialog.isShowing) {
                    progressDialog.show()
                }
                val signPassword = EncodingUtils.rsaEncryptByPublicKey(
                    passwordEditText.text.toString(),
                    Constants.PUBLICK_KEY
                )

                verifyTokenStr?.let { SDKSingleton.sessionBl.resetPwd(signPassword, it) }

                showToastShort(activity.getString(R.string.password_reseted))
                progressDialog.dismiss()
                function.invoke()
            } catch (exception: Throwable) {
                progressDialog.dismiss()
                if (exception is ApiException) {
                    when (exception.code) {
                        ErrorInfo.UserNotExists.code, ErrorInfo.UserNotExists2.code -> {
                            APPUtil.showTipDialog(
                                activity,
                                activity.getString(R.string.warm_prompt_title),
                                activity.getString(R.string.this_account_not_exist)
                            )
                        }
                        ErrorInfo.InvalidVerificationCode.code -> {
                            codeErrorTextView.visibility = View.VISIBLE
                            codeLayout!!.isSelected = true
                            codeErrorTextView.text =
                                activity.getString(R.string.invalid_verification_code)
                        }
                        else -> {
                            showToastShort(exception.message)
                        }
                    }
                } else {
                    ExceptionHandler.handleException(exception)
                }
            }
        }
    }

    fun registerPrevStep() {
        accountLayout!!.visibility = View.VISIBLE
        codeLayout!!.visibility = View.VISIBLE
        setRegisterType(registerType)
        checkingPassword = false
    }

    fun registerNextStep(): Boolean {
        return if (isInputCorrect) {
            setNextStepUi()
            checkingPassword = true
            true
        } else {
            false
        }
    }

    fun setNextStepUi() {
        passwordlayout!!.visibility = View.VISIBLE
        passwordTwolayout!!.visibility = View.VISIBLE
        phoneTitleTextView!!.setText(R.string.password)
        verifyCodeTitleTextView!!.setText(R.string.confirm_password)
        accountLayout!!.visibility = View.GONE
        codeLayout!!.visibility = View.GONE
        if (!isResetPassword) {
            nameTextView!!.visibility = View.VISIBLE
            nameEditText!!.visibility = View.VISIBLE
        }
    }

    private fun updateGetVerifyCodeButtonMobile(second: Int) {
        if (second > 0) {
            mobileVerifyTextView!!.text =
                String.format(activity.getString(R.string.sent_again_after), second)
            mobileVerifyTextView!!.isEnabled = false
        }
    }

    private fun updateGetVerifyCodeButtonEmail(second: Int) {
        if (second > 0) {
            emailVerifyTextView!!.text =
                String.format(activity.getString(R.string.sent_again_after), second)
            emailVerifyTextView!!.isEnabled = false
        }
    }

    private var buttonStateChangeListener: ((Boolean) -> Unit)? = null

    fun setOnButtonStateChangeListener(listener: (itemBean: Boolean) -> Unit) {
        buttonStateChangeListener = listener
    }

    private val handler: WeakHandler<RegisterLayoutManager?> =
        object : WeakHandler<RegisterLayoutManager?>(this) {
            override fun handleMessage(msg: Message) {
                super.handleMessage(msg)
                if (msg.what == Constants.MSG_UPDATE_MOBILE_VERIFY_BUTTON) {
                    if (waitSecondMobile > 0) {
                        updateGetVerifyCodeButtonMobile(waitSecondMobile)
                        waitSecondMobile--
                        SPSingleton.get().putInt(SPKeyDefine.SP_waitTime_mobile,
                            (System.currentTimeMillis()/1000 + waitSecondMobile).toInt()
                        )
                        sendEmptyMessageDelayed(Constants.MSG_UPDATE_MOBILE_VERIFY_BUTTON, 1000)
                    } else {
                        mobileVerifyTextView!!.isEnabled = true
                        mobileVerifyTextView!!.setText(R.string.get_verify_code)
                    }
                } else if (msg.what == Constants.MSG_UPDATE_EMAIL_VERIFY_BUTTON) {
                    if (waitSecondEmail > 0) {
                        updateGetVerifyCodeButtonEmail(waitSecondEmail)
                        waitSecondEmail--
                        SPSingleton.get().putInt(SPKeyDefine.SP_waitTime_email,
                            (System.currentTimeMillis()/1000 + waitSecondEmail).toInt()
                        )
                        sendEmptyMessageDelayed(Constants.MSG_UPDATE_EMAIL_VERIFY_BUTTON, 1000)
                    } else {
                        emailVerifyTextView!!.isEnabled = true
                        emailVerifyTextView!!.setText(R.string.get_verify_code)
                    }
                } else if (msg.what == Constants.MSG_GET_VERIFY_CODE) { //获取验证码
                    if (registerType == REGISTER_MODE_MOBILE && msg.arg1 == Constants.HTTP_RESULT_SUCCESS) {
                        showToastShort(activity.getString(R.string.already_send_code) + UserAccountManager.getCountryCode(countryCodeTextView) + " " +accountEditText!!.text.toString())
                        waitSecondMobile = 60
                        sendEmptyMessage(Constants.MSG_UPDATE_MOBILE_VERIFY_BUTTON)
                    }
                    if (registerType == REGISTER_MODE_EMAIL && msg.arg1 == Constants.HTTP_RESULT_SUCCESS) {
                        waitSecondEmail = 60
                        sendEmptyMessage(Constants.MSG_UPDATE_EMAIL_VERIFY_BUTTON)
                    }
                }
            }
        }

    init {
        rootView = activity.layoutInflater.inflate(R.layout.register_by_mobile_layout, null)
        findViewsByIdFromXML()
        setRegisterType(REGISTER_MODE_EMAIL)
        progressDialog = with(activity)
            .create()
        progressDialog.setCanceledOnTouchOutside(false)
        initListener()
        countryCodeTextView!!.text = get().getString(
            SPKeyDefine.SP_defaultCountry, getCurrentCountryCode(
                activity
            )
        )
        val timeMobile = SPSingleton.get().getInt(SPKeyDefine.SP_waitTime_mobile, (System.currentTimeMillis() / 1000).toInt()) - (System.currentTimeMillis() / 1000).toInt()
        if (timeMobile > 0) {
            waitSecondMobile = timeMobile
            handler.sendEmptyMessage(Constants.MSG_UPDATE_MOBILE_VERIFY_BUTTON)
        }
        val timeEmail = SPSingleton.get().getInt(SPKeyDefine.SP_waitTime_email, (System.currentTimeMillis() / 1000).toInt()) - (System.currentTimeMillis() / 1000).toInt()
        if (timeEmail > 0) {
            waitSecondEmail = timeEmail
            handler.sendEmptyMessage(Constants.MSG_UPDATE_EMAIL_VERIFY_BUTTON)
        }
    }

    val isInputCorrect: Boolean
        get() = if (verifyEditText!!.text.length <= 0) {
            showToastShort(R.string.input_verify_code)
            false
        } else checkInputFormat()

    /**
     * 立即重置验证码倒计时
     * 可以让用户立即重新发送验证码，无需等待倒计时结束
     */
    fun resetVerificationCodeTimer() {
        resetMobileVerificationTimer()
        resetEmailVerificationTimer()
    }

    /**
     * 立即重置手机验证码倒计时
     */
    fun resetMobileVerificationTimer() {
        waitSecondMobile = 0
        SPSingleton.get().putInt(SPKeyDefine.SP_waitTime_mobile, 0)
        mobileVerifyTextView?.isEnabled = true
        mobileVerifyTextView?.setText(R.string.get_verify_code)
        handler.removeMessages(Constants.MSG_UPDATE_MOBILE_VERIFY_BUTTON)
    }

    /**
     * 立即重置邮箱验证码倒计时
     */
    fun resetEmailVerificationTimer() {
        waitSecondEmail = 0
        SPSingleton.get().putInt(SPKeyDefine.SP_waitTime_email, 0)
        emailVerifyTextView?.isEnabled = true
        emailVerifyTextView?.setText(R.string.get_verify_code)
        handler.removeMessages(Constants.MSG_UPDATE_EMAIL_VERIFY_BUTTON)
    }

    companion object {
        const val REGISTER_MODE_MOBILE = 1
        const val REGISTER_MODE_EMAIL = 2
        const val BIND_MODE_MOBILE = 3
        const val BIND_MODE_EMAIL = 4
        const val ACCOUNT_MODE_CHANGE_PASSWORD = 5
    }
}