package com.wedevote.wdbook.ui.user.feedback

import android.view.ViewGroup
import android.widget.TextView
import com.aquila.lib.base.BaseRecycleAdapter
import com.aquila.lib.base.BaseViewHolder
import com.wedevote.wdbook.R
import com.wedevote.wdbook.entity.feedback.HelpArticleEntity

/***
 * @date 创建时间 2022/4/28 18:32
 * <AUTHOR> W.<PERSON>
 * @description
 */
class FaqArticleItemAdapter : BaseRecycleAdapter<HelpArticleEntity, FaqItemViewHolder>() {
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): FaqItemViewHolder {
        return FaqItemViewHolder(parent)
    }
}

/***
 *@date 创建时间 2022/4/28 19:54
 *<AUTHOR> W.YuLong
 *@description
 */
class FaqItemViewHolder(parent: ViewGroup) : BaseViewHolder(parent, R.layout.holder_item_faq_item_layout) {
    val contentTextView: TextView = itemView.findViewById(R.id.item_help_center_content_TextView)
    
    override fun <T> initUIData(t: T) {
        t as HelpArticleEntity
        contentTextView.text = t.title
        itemView.setOnClickListener {
            FaqArticleDetailActivity.gotoFaqArticalDetailActivity(itemView.context, t.id)
        }
    }
}
