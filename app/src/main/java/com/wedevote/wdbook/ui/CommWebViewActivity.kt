package com.wedevote.wdbook.ui

import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.os.Bundle
import android.view.View
import android.webkit.JsPromptResult
import android.webkit.WebChromeClient
import android.webkit.WebResourceError
import android.webkit.WebResourceRequest
import android.webkit.WebView
import android.webkit.WebViewClient
import android.widget.ProgressBar
import com.aquila.lib.log.KLog
import com.aquila.lib.widget.group.GroupImageTextLayout
import com.wedevote.wdbook.R
import com.wedevote.wdbook.base.RootActivity
import com.wedevote.wdbook.base.SDKSingleton
import com.wedevote.wdbook.ui.account.SSOLoginActivity
import com.wedevote.wdbook.ui.widgets.CommTopTitleLayout

/***
 * @date 创建时间 2020/7/23 17:57
 * <AUTHOR> <PERSON><PERSON>
 * @description 通用的 WebView 的页面
 */
class CommWebViewActivity : RootActivity() {
    lateinit var topTitleLayout: CommTopTitleLayout
    lateinit var webView: WebView
    lateinit var progressBar: ProgressBar
    lateinit var emptyLayout: GroupImageTextLayout

    var url: String = ""
    private var titleName: String = ""

    companion object {
        fun gotoWebView(
            context: Context,
            url: String,
            showTitle: Boolean = true,
            titleName: String = ""
        ) {
            val intent = Intent(context, CommWebViewActivity::class.java)
            intent.putExtra("url", url)
            intent.putExtra("showTitle", showTitle)
            intent.putExtra("titleName", titleName)
            context.startActivity(intent)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_comm_webview_layout)
        topTitleLayout = findViewById(R.id.comm_web_top_title_Layout)
        webView = findViewById(R.id.comm_web_WebView)
        progressBar = findViewById(R.id.comm_web_loading_ProgressBar)
        emptyLayout = findViewById(R.id.comm_web_empty_Layout)

        webView.webViewClient = webClient
        webView.webChromeClient = webChromeClient

        url = intent.getStringExtra("url") ?: ""
        titleName = intent.getStringExtra("titleName") ?: ""
        if (titleName.isNotEmpty()) {
            topTitleLayout.setTitle(titleName)
        }
        val showTitle = intent.getBooleanExtra("showTitle", true)
        topTitleLayout.visibility = if (showTitle) View.VISIBLE else View.GONE

        webView.loadUrl(url)

        emptyLayout.setOnClickListener {
            webView.loadUrl(url)
        }
    }

    private val webClient = object : WebViewClient() {
        override fun onPageStarted(view: WebView?, url: String?, favicon: Bitmap?) {
            super.onPageStarted(view, url, favicon)
            progressBar.visibility = View.VISIBLE
            progressBar.progress = 0

            emptyLayout.visibility = View.GONE
            webView.visibility = View.VISIBLE
        }

        override fun onPageFinished(view: WebView?, url: String?) {
            super.onPageFinished(view, url)
            progressBar.visibility = View.GONE
        }

        override fun onReceivedError(
            view: WebView?,
            request: WebResourceRequest?,
            error: WebResourceError?
        ) {
            super.onReceivedError(view, request, error)
            KLog.e("onReceivedError:$error")
            progressBar.visibility = View.GONE
            emptyLayout.visibility = View.VISIBLE
            webView.visibility = View.GONE
        }
    }

    private val webChromeClient = object : WebChromeClient() {
        override fun onReceivedTitle(view: WebView?, title: String?) {
            super.onReceivedTitle(view, title)
        }

        override fun onJsPrompt(
            view: WebView?,
            url: String?,
            message: String?,
            defaultValue: String?,
            result: JsPromptResult?
        ): Boolean {
            if (SDKSingleton.sessionBl.getAuthorizeUrl() == url) {
                val intent = Intent(this@CommWebViewActivity, SSOLoginActivity::class.java)
                message?.let { SDKSingleton.sessionBl.setUserTokenString(message) }
                intent.putExtra("UserTokenBeanJson", message)
                startActivity(intent)
                finish()
                return true
            } else {
                return super.onJsPrompt(view, url, message, defaultValue, result)
            }
        }

        override fun onProgressChanged(view: WebView?, newProgress: Int) {
            super.onProgressChanged(view, newProgress)
            progressBar.progress = newProgress
        }
    }
}
