package com.wedevote.wdbook.ui.user

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.ObjectAnimator
import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.View
import android.view.View.OnClickListener
import android.view.Window
import android.widget.ImageView
import android.widget.ProgressBar
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.aquila.lib.log.KLog
import com.wedevote.wdbook.tools.util.ImageLoadUtil
import com.aquila.lib.tools.util.ToastUtil
import com.aquila.lib.widget.view.AdaptiveImageView
import com.wedevote.wdbook.R
import com.wedevote.wdbook.R.string
import com.wedevote.wdbook.base.RootActivity
import com.wedevote.wdbook.base.SDKSingleton
import com.wedevote.wdbook.constants.DownloadStatus
import com.wedevote.wdbook.entity.resource.ResourceDownloadInfo
import com.wedevote.wdbook.entity.shelf.ShelfBookItemEntity
import com.wedevote.wdbook.entity.store.BookFileDownloadEntity
import com.wedevote.wdbook.tools.download.DownloaderEngine
import com.wedevote.wdbook.tools.download.OnDownloadingListener
import com.wedevote.wdbook.tools.util.DataPathUtil
import com.wedevote.wdbook.tools.util.IntentConstants
import com.wedevote.wdbook.tools.util.PictureUtil
import com.wedevote.wdbook.tools.util.scanForActivity
import java.io.File

/***
 * @date 创建时间 2022/4/27 15:47
 * <AUTHOR> W.YuLong
 * @description 书籍下载专用的UI
 */
class BookDownloadActivity : RootActivity(), OnClickListener {
    lateinit var closeImageView: ImageView
    lateinit var coverImageView: AdaptiveImageView
    lateinit var nameTextView: TextView
    lateinit var percentTextView: TextView
    lateinit var promptTextView: TextView
    lateinit var progressBar: ProgressBar
    lateinit var failureTextView: TextView
    lateinit var containerLayout: ConstraintLayout

    lateinit var downloadEngine: DownloaderEngine

    var downloadFileId: String? = ""

    companion object {
        var onBookDownloadCallback: OnBookDownloadCallback? = null
        fun gotoDownloadBook(
            context: Context,
            resourceId: String,
            callback: OnBookDownloadCallback? = null,
        ) {
            val intent = Intent(context, BookDownloadActivity::class.java)
            intent.putExtra(IntentConstants.EXTRA_ResourceId, resourceId)
            context.startActivity(intent)
            onBookDownloadCallback = callback
            context.scanForActivity()?.overridePendingTransition(R.anim.anim_normal, R.anim.anim_normal)
        }
    }

    var resourceId: String = ""
    lateinit var resourceEntity: ShelfBookItemEntity

    override fun onCreate(savedInstanceState: Bundle?) {
        this.requestWindowFeature(Window.FEATURE_NO_TITLE)
        super.onCreate(savedInstanceState)
        this.window.setBackgroundDrawableResource(R.color.transparent)
        setContentView(R.layout.dialog_note_book_downloading_layout)
        initViewFromXML()
        resourceId = intent.getStringExtra(IntentConstants.EXTRA_ResourceId) ?: ""
        var entity = SDKSingleton.userBl.getShelfBookItemEntityByResourceId(resourceId)
        if (entity == null) {
            ToastUtil.showToastShort("无此资源")
            onBackPressed()
            return
        }
        resourceEntity = entity!!
        var info = resourceEntity.resourceDownloadInfo
        if (!info?.getActualFilePath().isNullOrEmpty() && File(info?.getActualFilePath()).exists()) {
            onBookDownloadCallback?.onDownloadComplete(info)
            onBackPressed()
        }

        downloadEngine = DownloaderEngine(this)
        downloadEngine.setOnDownloadingListener(onDownloadListener)
        initUI()

        containerLayout.post {
            ObjectAnimator.ofFloat(containerLayout, "alpha", 0.2f, 1f).apply {
                addListener(object : AnimatorListenerAdapter() {
                    override fun onAnimationStart(animation: Animator) {
                        super.onAnimationStart(animation)
                        containerLayout.visibility = View.VISIBLE
                    }
                })
                duration = 300
            }.start()
        }
        setViewListeners()
    }

    private fun initUI() {
        PictureUtil.loadImage(coverImageView, resourceEntity.cover)
        nameTextView.text = resourceEntity.resourceName
        val info = resourceEntity.resourceDownloadInfo
        downloadFileId = info?.fileId
        var downloadEntity = downloadFileId?.let { SDKSingleton.downloadBl.fetchDownloadFileEntity(it, DataPathUtil.getDownloadPath()) }
        if (downloadEntity != null && downloadEntity.downloadStatus != DownloadStatus.DOWNLOADING && downloadEntity.downloadStatus != DownloadStatus.COMPLETE) {
            Handler().postDelayed({
                downloadEngine.readyDownloadOnlyByFileId(downloadFileId!!)
            }, 500)
        }
    }

    private fun initViewFromXML() {
        containerLayout = findViewById(R.id.note_book_download_container_layout)
        closeImageView = findViewById(R.id.note_book_download_close_ImageView)
        coverImageView = findViewById(R.id.note_book_download_cover_ImageView)
        nameTextView = findViewById(R.id.note_book_download_name_TextView)
        promptTextView = findViewById(R.id.note_book_download_prompt_TextView)
        progressBar = findViewById(R.id.note_book_download_ProgressBar)
        percentTextView = findViewById(R.id.note_book_download_percent_TextView)
        failureTextView = findViewById(R.id.note_book_download_failure_TextView)
    }

    private fun setViewListeners() {
        closeImageView.setOnClickListener(this)
        coverImageView.setOnClickListener(this)
        nameTextView.setOnClickListener(this)
    }

    val onDownloadListener = object : OnDownloadingListener {
        var percent: Int = 0

        override fun onBeginning(entity: BookFileDownloadEntity) {
            progressBar.visibility = View.VISIBLE
            percentTextView.visibility = View.VISIBLE
            promptTextView.text = getString(string.download_finish_and_jump_prompt)
        }

        override fun onDownloadingProgress(entity: BookFileDownloadEntity, downloadSize: Long, totalSize: Long) {
            if (entity.fileId == downloadFileId) {
                KLog.d("${entity.fileId}, downloadSize = $downloadSize, totalSize = $totalSize")
                percent = (downloadSize * 100 / totalSize).toInt()
                progressBar.progress = percent
                percentTextView.text = "$percent" + "%"
                KLog.d("percentTextView.text = $percent\\%")
            }
        }

        override fun onComplete(entity: BookFileDownloadEntity) {
            coverImageView.setMaskColor(Color.TRANSPARENT)
            progressBar.visibility = View.GONE
            percentTextView.visibility = View.GONE
            ToastUtil.showToastShort(getString(string.download_complete))
            Handler(Looper.getMainLooper()).postDelayed({
                val info = SDKSingleton.dbWrapBl.getResourceDownloadInfo(resourceId!!)
                info?.let {
                    onBookDownloadCallback?.onDownloadComplete(info)
                    onBackPressed()
                }
            }, 1000)
        }

        override fun onError(entity: BookFileDownloadEntity, errorDesc: String) {
            progressBar.visibility = View.GONE
            percentTextView.visibility = View.GONE
            failureTextView.visibility = View.VISIBLE
            promptTextView.text = getString(R.string.download_failure_prompt)
        }

        override fun onCancel(entity: BookFileDownloadEntity) {
            onBackPressed()
            ToastUtil.showToastShort(R.string.cancel_download_toast)
        }
    }

    override fun exitPendingAnim() {
        overridePendingTransition(com.aquila.lib.base.R.anim.base_anim_normal, com.aquila.lib.dialog.R.anim.anim_alpha_out)
    }

    override fun enterPendingAnim() {
        overridePendingTransition(com.aquila.lib.dialog.R.anim.anim_alpha_in, com.aquila.lib.base.R.anim.base_anim_normal)
    }

    override fun onDestroy() {
        super.onDestroy()
        onBookDownloadCallback = null
    }

    override fun onClick(v: View?) {
        when (v) {
            closeImageView -> {
                onBackPressed()
            }
            coverImageView, nameTextView -> {
                downloadEngine.readyDownloadOnlyByFileId(downloadFileId!!)
            }
        }
    }
}

/***
 *@date 创建时间 2022/4/27 15:57
 *<AUTHOR> W.YuLong
 *@description
 */
interface OnBookDownloadCallback {
    fun onDownloadComplete(info: ResourceDownloadInfo)

    fun downloadFailure() {}
}
