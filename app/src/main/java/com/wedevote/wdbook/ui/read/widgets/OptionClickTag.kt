package com.wedevote.wdbook.ui.read.widgets

/***
 * @date 创建时间 2020/7/20 16:06
 * <AUTHOR> W<PERSON><PERSON><PERSON><PERSON>
 * @description
 */
object OptionClickTag {
    const val TAG_THEME_BLACK = "TAG_THEME_BLACK"
    const val TAG_THEME_WHITE = "TAG_THEME_WHITE"

    const val TAG_SCOPE_REVOKE = "TAG_SCOPE_REVOKE"
    const val TAG_SCOPE_PROGRESS = "SCOPE_PROGRESS"
    const val TAG_SCOPE_FINISH = "TAG_SCOPE_FINISH"

    const val TAG_FONT_SIZE = "FONT_SIZE"
    const val TAG_FONT_STYLE = "FONT_STYLE"
    const val TAG_CONTENT = "CONTENT"
    const val TAG_INNER_JUMP = "INNER_JUMP"
    const val TAG_CLICK_TOOL_LAYOUT = "CLICK_TOOL_LAYOUT"
    const val TAG_BOOK_NOTE = "BOOK_NOTE"
    const val TAG_BOOK_SEARCH = "BOOK_SEARCH"
    const val TAG_BOOK_MARK = "BOOK_MARK"
    const val TAG_CLICK_TOOL_TITLE = "CLICK_TOOL_TITLE"
}
