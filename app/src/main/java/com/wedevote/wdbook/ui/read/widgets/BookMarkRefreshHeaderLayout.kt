package com.wedevote.wdbook.ui.read.widgets

import android.content.Context
import android.view.View
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import com.aquila.lib.layout.api.RefreshHeader
import com.aquila.lib.layout.api.RefreshKernel
import com.aquila.lib.layout.api.RefreshLayout
import com.aquila.lib.layout.constant.RefreshState
import com.aquila.lib.layout.constant.SpinnerStyle
import com.aquila.lib.log.KLog
import com.wedevote.wdbook.R
import com.wedevote.wdbook.tools.util.findString
import com.wedevote.wdbook.ui.read.OnBookmarkActionListener

/***
 * @date 创建时间 1/20/21 5:57 PM
 * <AUTHOR> W.YuLong
 * @description
 */
class BookMarkRefreshHeaderLayout(context: Context) : LinearLayout(context), RefreshHeader {

    val titleTextView: TextView
    val arrowView: ImageView
    val markImageView: ImageView

    var isAdded: Boolean = false

    var onBookmarkActionListener: OnBookmarkActionListener? = null

    val delayTime = 500L

    init {
        inflate(context, R.layout.header_bookmark_layout, this)
        titleTextView = findViewById(R.id.refresh_header_title_TextView)
        arrowView = findViewById(R.id.refresh_header_arrow_ImageView)
        markImageView = findViewById(R.id.refresh_header_bookmark_flag_ImageView)
    }

    var isActive = false
    var currentState = RefreshState.None
    override fun onStateChanged(refreshLayout: RefreshLayout, oldState: RefreshState, newState: RefreshState) {
        when (newState) {
            RefreshState.None -> {
                if (isActive) {
                    onBookmarkActionListener?.doBookmark(isAdded)
                    isActive = false
                }
            }
            RefreshState.PullDownToRefresh -> {
                if (isAdded) {
                    titleTextView.text = findString(R.string.pull_down_refresh_bookmark)
                } else {
                    titleTextView.text = findString(R.string.pull_down_add_bookmark)
                }
                arrowView.visibility = VISIBLE
                arrowView.animate().rotation(0f)
            }
            RefreshState.Refreshing, RefreshState.RefreshReleased -> {
                titleTextView.text = if (isAdded) findString(R.string.deleteing_bookmark) else findString(R.string.adding_bookmark)
            }
            RefreshState.RefreshFinish -> {
                titleTextView.text = if (isAdded) findString(R.string.delete_success) else findString(R.string.add_success)
                isActive = true
            }
            RefreshState.ReleaseToRefresh -> {
                titleTextView.text = if (isAdded) findString(R.string.finger_up_delete) else findString(R.string.find_add_bookmark)
                arrowView.animate().rotation(180f)
            }
            RefreshState.ReleaseToTwoLevel -> {
            }
            RefreshState.Loading -> {
            }
            else -> {

            }
        }
        currentState = newState
    }

    fun isRefreshing(): Boolean {
        return currentState != RefreshState.None
    }

    override fun getView(): View {
        return this
    }

    override fun getSpinnerStyle(): SpinnerStyle {
        return SpinnerStyle.Translate
    }

    override fun setPrimaryColors(vararg colors: Int) {
    }

    override fun onInitialized(kernel: RefreshKernel, height: Int, maxDragHeight: Int) {
    }

    override fun onMoving(isDragging: Boolean, percent: Float, offset: Int, height: Int, maxDragHeight: Int) {
    }

    override fun onReleased(refreshLayout: RefreshLayout, height: Int, maxDragHeight: Int) {
        KLog.d("onReleased")
    }

    override fun onStartAnimator(refreshLayout: RefreshLayout, height: Int, maxDragHeight: Int) {
    }

    override fun onFinish(refreshLayout: RefreshLayout, success: Boolean): Int {
        return delayTime.toInt()
    }

    override fun onHorizontalDrag(percentX: Float, offsetX: Int, offsetMax: Int) {
    }

    override fun isSupportHorizontalDrag(): Boolean {
        return false
    }
}
