package com.wedevote.wdbook.ui.user

import android.os.Bundle
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import com.aquila.lib.base.BaseRecycleAdapter
import com.aquila.lib.base.BaseViewHolder
import com.aquila.lib.layout.SmartRefreshLayout
import com.aquila.lib.layout.api.RefreshLayout
import com.aquila.lib.layout.listener.OnLoadMoreListener
import com.aquila.lib.layout.listener.OnRefreshListener
import com.aquila.lib.log.KLog
import com.wedevote.wdbook.tools.util.ImageLoadUtil
import com.aquila.lib.widget.group.GroupImageTextLayout
import com.aquila.lib.widget.view.AdaptiveImageView
import com.aquila.lib.widget.view.CustomRecyclerView
import com.wedevote.wdbook.R
import com.wedevote.wdbook.base.ExceptionHandler
import com.wedevote.wdbook.base.RootActivity
import com.wedevote.wdbook.base.SDKSingleton
import com.wedevote.wdbook.entity.BookNoteCountEntity
import com.wedevote.wdbook.tools.event.OnSyncNoteFinish
import com.wedevote.wdbook.tools.util.PictureUtil
import com.wedevote.wdbook.tools.util.NetWorkUtils
import com.wedevote.wdbook.tools.util.findString
import com.wedevote.wdbook.ui.widgets.CommTopTitleLayout
import com.wedevote.wdbook.utils.JsonUtility
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus

/***
 * @date 创建时间 2021/7/21 18:42
 * <AUTHOR> W.YuLong
 * @description
 */
class BookNoteCountActivity : RootActivity(), OnRefreshListener, OnLoadMoreListener {
    lateinit var titleLayout: CommTopTitleLayout
    lateinit var refreshLayout: SmartRefreshLayout
    lateinit var dataRecyclerView: CustomRecyclerView
    lateinit var noteAdapter: BookNoteRecyclerAdapter
    lateinit var emptyPromptLayout: GroupImageTextLayout

    private var offset: Long = 0
    val limit: Long = 20

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_all_note_layout)
        titleLayout = findViewById(R.id.all_note_top_title_layout)
        refreshLayout = findViewById(R.id.all_note_container_RefreshLayout)
        dataRecyclerView = findViewById(R.id.all_note_data_RecyclerView)
        emptyPromptLayout = findViewById(R.id.book_shelf_empty_icon_Layout)

        noteAdapter = BookNoteRecyclerAdapter()
        dataRecyclerView.adapter = noteAdapter
        refreshLayout.setOnRefreshListener(this).setOnLoadMoreListener(this)
    }

    override fun onResume() {
        super.onResume()
        val count = noteAdapter.itemCount
        offset = 0
        noteAdapter.clearDataList()
        refreshLayout.isEnableLoadMore = true
        loadDataFromDB(offset, Math.max(limit, count.toLong()))
    }

    override fun onRefresh(refreshLayout: RefreshLayout) {
        if (!NetWorkUtils.isNetworkAvailable()) {
            NetWorkUtils.showTipDialog(this, getString(R.string.no_network_connect))
            refreshLayout.finishRefresh()
            return
        }
        MainScope().launch(ExceptionHandler.getCoroutineExceptionHandler {
            refreshLayout.finishRefresh()
        }) {
            SDKSingleton.syncBl.syncNoteData()
            EventBus.getDefault().post(OnSyncNoteFinish())
            offset = 0
            noteAdapter.clearDataList()
            refreshLayout.isEnableLoadMore = true
            onLoadMore(refreshLayout)
        }
    }

    override fun onLoadMore(layout: RefreshLayout) {
        loadDataFromDB(offset)
        refreshLayout.finishLoadMoreAndRefresh()
    }

    private fun loadDataFromDB(pos: Long, limit: Long = 20) {
        val dbList = SDKSingleton.dbWrapBl.getBookNoteCountList(pos)
        noteAdapter.addDataList(dbList)
        KLog.d(JsonUtility.encodeToString(dbList))
        offset += dbList.size
        refreshLayout.isEnableLoadMore = dbList.size >= limit
        if (noteAdapter.itemCount == 0) {
            emptyPromptLayout.visibility = View.VISIBLE
            dataRecyclerView.visibility = View.GONE
        }else {
            emptyPromptLayout.visibility = View.GONE
            dataRecyclerView.visibility = View.VISIBLE
        }
    }
}

/***
 *@date 创建时间 2021/7/22 11:00
 *<AUTHOR> W.YuLong
 *@description
 */
class BookNoteRecyclerAdapter : BaseRecycleAdapter<BookNoteCountEntity, BookNoteItemViewHolder>() {
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BookNoteItemViewHolder {
        return BookNoteItemViewHolder(parent)
    }
}

/***
 *@date 创建时间 2021/7/22 11:00
 *<AUTHOR> W.YuLong
 *@description
 */
class BookNoteItemViewHolder(parent: ViewGroup) : BaseViewHolder(parent, R.layout.holder_item_book_note_layout) {
    val coverImageView: AdaptiveImageView = itemView.findViewById(R.id.item_book_note_cover_ImageView)
    val nameTextView: TextView = itemView.findViewById(R.id.item_book_note_name_TextView)
    val noteCountLayout: GroupImageTextLayout = itemView.findViewById(R.id.item_book_note_count_layout)

    override fun <T> initUIData(t: T) {
        t as BookNoteCountEntity
        nameTextView.text = t.name
        noteCountLayout.setText(findString(R.string.format_text_count_note).format(t.count))
        PictureUtil.loadImage(coverImageView, t.cover)
        itemView.setOnClickListener {
            BookNoteDetailActivity.gotoBookNoteDetailActivity(itemView.context, t)
        }
    }
}
