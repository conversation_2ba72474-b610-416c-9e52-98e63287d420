package com.wedevote.wdbook.ui.read

import android.content.Context
import android.graphics.Typeface
import android.os.Bundle
import android.view.Gravity
import android.widget.ImageView
import android.widget.TextView
import com.wedevote.wdbook.R
import com.wedevote.wdbook.base.SDKSingleton
import com.wedevote.wdbook.tools.define.TextFontType
import com.wedevote.wdbook.ui.dialogs.BaseDialog
import com.wedevote.wdbook.ui.read.lib.EPubBook

/***
 *@date 创建时间 2020/10/27 13:44
 *<AUTHOR> W.<PERSON>Long
 *@description 圣经经文的对话框
 */
class BibleVerseDialog(context: Context) : BaseDialog(context) {
    val LINK_HEAD = "wdbible://bible/"

    lateinit var titleTextView: TextView
    lateinit var contentTextView: TextView
    lateinit var closeImageView: ImageView
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.dialog_show_verse_layout)
        titleTextView = findViewById(R.id.dialog_verse_title_TextView)
        contentTextView = findViewById(R.id.dialog_verse_content_TextView)
        closeImageView = findViewById(R.id.dialog_verse_close_ImageView)
        configDialog(Gravity.BOTTOM)
        closeImageView.setOnClickListener { dismiss() }
        setTextFont()
    }

    private fun setTextFont() {
        if (EPubBook.textFont == TextFontType.SERIF.value) {
            titleTextView.typeface = EPubBook.typefaceSerif
            contentTextView.typeface = EPubBook.typefaceSerif
        } else {
            titleTextView.typeface = Typeface.DEFAULT
            contentTextView.typeface = Typeface.DEFAULT
        }
    }

    /*将圣经的经文标识解析读取经文内容,eg.[col.4.18]*/
    private fun parseBibleToVerse(bibleInfo: String, defaultLast: Boolean): BibleVerseEntity {
        var items = bibleInfo.split('.')
        var entity = BibleVerseEntity()
        if (!items.isNullOrEmpty() && items.size >= 2) {
            entity.bookUsfm = items[0]
            entity.chapterId = items[1].toInt()
            if (items.size == 3) {
                entity.verseId = items[2].toInt()
            } else if (defaultLast) {
                entity.verseId =
                    SDKSingleton.bibleBl.getChapterVerseCount(entity.bookUsfm!!, entity.chapterId)
            }
        }
        return entity
    }

    fun initBibleContentUI(link: String) {
        var bibleInfo = link.substring(LINK_HEAD.length)
        var verseInfoArray = bibleInfo.split("-")
        if (verseInfoArray.isNullOrEmpty()) {
            return
        }
        var resultContentBuilder = StringBuilder()
        var startVerseEntity = parseBibleToVerse(verseInfoArray[0], false)
        val allChapter = verseInfoArray[0].split('.').size < 3
        var bookName = getBookName(startVerseEntity.bookUsfm)

        if (!allChapter && verseInfoArray.size == 1) {
            // 只有一节的情况
            val contentList = SDKSingleton.bibleBl.getVerseContent(
                startVerseEntity.bookUsfm!!.uppercase(java.util.Locale.ROOT),
                startVerseEntity.chapterId, startVerseEntity.verseId, startVerseEntity.verseId
            )
            if (contentList.isNotEmpty()) {
                resultContentBuilder.append("${startVerseEntity.chapterId}:${startVerseEntity.verseId}")
                resultContentBuilder.append(contentList[0].content)
            }
            titleTextView.text =
                bookName + startVerseEntity.chapterId + ":${startVerseEntity.verseId}"
        } else {
            // 多章节情况,不支持跨书卷
            val endVerseEntity = if (verseInfoArray.size == 2) {
                parseBibleToVerse(verseInfoArray[1], true)
            } else {
                parseBibleToVerse(verseInfoArray[0], true)
            }
            if (!startVerseEntity.bookUsfm.equals(endVerseEntity.bookUsfm)) {
                return
            }

            if (startVerseEntity.chapterId <= endVerseEntity.chapterId) {
                var startVerse = -1
                var endVerse = -1
                for (i in startVerseEntity.chapterId..endVerseEntity.chapterId) {
                    startVerse = if (i == startVerseEntity.chapterId) {
                        startVerseEntity.verseId
                    } else {
                        1
                    }

                    endVerse = if (i == endVerseEntity.chapterId) {
                        endVerseEntity.verseId
                    } else {
                        SDKSingleton.bibleBl.getChapterVerseCount(startVerseEntity.bookUsfm!!, i)
                    }

                    //解决合并经节不显示情况
                    var startContentList = SDKSingleton.bibleBl.getVerseContent(
                        startVerseEntity.bookUsfm!!.uppercase(java.util.Locale.ROOT), i, startVerse, startVerse
                    )
                    while (startContentList.isEmpty() && startVerse > 1) {
                        startVerse -= 1
                        startContentList = SDKSingleton.bibleBl.getVerseContent(
                            startVerseEntity.bookUsfm!!.uppercase(java.util.Locale.ROOT), i, startVerse, startVerse
                        )
                    }

                    var contentList = SDKSingleton.bibleBl.getVerseContent(
                        startVerseEntity.bookUsfm!!.uppercase(java.util.Locale.ROOT), i, startVerse, endVerse
                    )

                    if (contentList.size == 1) {
                        resultContentBuilder.append("${contentList[0].content}")
                    } else {
                        for (index in contentList.indices) {
                            if (index < contentList.size - 1) {
                                //解决合并经节经文节数显示错误问题
                                if (contentList[index + 1].verse - contentList[index].verse > 1) {
                                    resultContentBuilder.append("${i}:${contentList[index].verse}-${contentList[index + 1].verse - 1} ${contentList[index].content}\n")
                                } else {
                                    resultContentBuilder.append("$i:${contentList[index].verse} ${contentList[index].content}\n")
                                }
                            } else {
                                resultContentBuilder.append("$i:${contentList[index].verse} ${contentList[index].content}\n")
                            }

                        }
                    }
                }
                if (startVerseEntity.chapterId == endVerseEntity.chapterId) {
                    if (startVerse == endVerse) {
                        titleTextView.text =
                            bookName + startVerseEntity.chapterId + ":$startVerse"
                    } else {
                        titleTextView.text =
                            bookName + startVerseEntity.chapterId + ":$startVerse-$endVerse"
                    }
                } else {
                    titleTextView.text =
                        bookName + startVerseEntity.chapterId + ":$startVerse-${endVerseEntity.chapterId}:$endVerse"
                }
            }
        }
        contentTextView.text = resultContentBuilder.toString()
    }

    private fun getBookName(bookUsfm: String?): String {
        var bookName = ""
        if (!bookUsfm.isNullOrEmpty()) {
            val bookIds = context.resources.getStringArray(R.array.book_id)
            val names = context.resources.getStringArray(R.array.book_name)
            for (i in bookIds.indices) {
                if (bookIds[i].uppercase(java.util.Locale.ROOT).equals(bookUsfm.uppercase(java.util.Locale.ROOT))) {
                    bookName = names[i]
                    break
                }
            }
        }
        return bookName
    }
}
