package com.wedevote.wdbook.ui.read.lib

import android.graphics.Paint
import android.graphics.Typeface
import android.text.TextPaint
import com.aquila.lib.tools.util.ScreenUtil
import com.wedevote.wdbook.base.APPConfig
import com.wedevote.wdbook.base.SDKSingleton
import com.wedevote.wdbook.entity.BookmarkEntity
import com.wedevote.wdbook.tools.util.APPUtil
import com.wedevote.wdbook.tools.util.dp2px
import com.wedevote.wdbook.ui.read.lib.data.FormatTextRange
import com.wedevote.wdbook.ui.read.lib.span.FootnoteSpan
import com.wedevote.wdbook.ui.read.lib.view.TextDrawViewHelper
import com.wedevote.wdbook.ui.read.lib.view.TextPageList
import com.wedevote.wdbook.ui.read.lib.xml.XMLParser

/***
 * @date 创建时间 2020/11/24 15:52
 * <AUTHOR> <PERSON><PERSON>
 * @description 书籍文本的页数计算的辅助类
 */
class BookTextControllerHelper {
    private var textPaint: TextPaint = TextPaint(Paint.ANTI_ALIAS_FLAG)

    var anchorList = ArrayList<FormatTextRange>()
    val lineHeightFactor = 1.35f
    var pageHeight: Float = (ScreenUtil.getScreenHeight() - dp2px(145f)).toFloat()

    companion object {
        @JvmStatic
        var overridePageHeightForAll: Float = -1f
    }
    var lineHeight = 0.0f
    lateinit var textPageList: TextPageList
    var pageEndLineIndexList: ArrayList<Int> = ArrayList()
    var pathIndex: Int = 0
    var indexInChapter = -1
    var resourceId: String? = null
    var fileId: String? = null

    // 当前本章内容大概需要显示几屏
    fun getChapterPageCount(): Int {
        return pageEndLineIndexList.size
    }

    init {
        textPaint.apply {
            textAlign = Paint.Align.LEFT
            isAntiAlias = true
            color = if (APPConfig.isCurrentThemeLight()) {
                BookParameters.textNormalColor
            } else {
                BookParameters.textNormalColorDark
            }
        }
    }

    fun initCurrentBookmarkData(): BookmarkEntity? {
        if (!::textPageList.isInitialized || pageEndLineIndexList.isEmpty() || indexInChapter < 0) {
            return null
        }
        val firstWordLocation = getFirstWordOffset(indexInChapter)
        val lastWordLocation = getPageLastWordOffset(indexInChapter)
        return try {
            SDKSingleton.dbWrapBl.getBookmarkEntity(resourceId!!, EPubBook.pathList[pathIndex], firstWordLocation, lastWordLocation)
        } catch (e: Exception) {
            null
        }
    }

    fun setTextSize(textSize: Float) {
        textPaint.textSize = textSize
    }

    private fun calculateLinePosition() {
        textPageList.computeLinePosition(lineHeight)
    }

    fun parseText(text: String, paragraphIntend: Boolean, showPageInfo: Boolean) {
        if (overridePageHeightForAll > 0) {
            pageHeight = overridePageHeightForAll
        }
        val parser = XMLParser(textPaint)
        var formatTextSpannableStr = parser.parseXml(text, paragraphIntend, showPageInfo)
        anchorList = parser.anchors

        val helper = TextDrawViewHelper(textPaint, -textPaint.ascent() * lineHeightFactor)
        helper.setText(formatTextSpannableStr)
        textPageList = helper.measureAll(ContentUtils.contentWidth, pageHeight)

        lineHeight = (textPaint.descent() - textPaint.ascent()) * lineHeightFactor

        pageEndLineIndexList.clear()

        calculateLinePosition()

        var totalLineHeight = 0
        var currentLineIndex = 0
        for (textLine in textPageList) {
            totalLineHeight += textLine.getLineHeight(lineHeight).toInt()
            if (totalLineHeight > pageHeight) {
                pageEndLineIndexList.add(currentLineIndex)
                totalLineHeight = textLine.getLineHeight(lineHeight).toInt()
            }
            currentLineIndex++
        }

        if (pageEndLineIndexList.isEmpty()) {
            pageEndLineIndexList.add(currentLineIndex)
            return
        }

        val prevEndLine = pageEndLineIndexList.last()
        var line = currentLineIndex
        while (line > prevEndLine) {
            line-- // 避免最后一页全空白的情况
            if (!textPageList[line].isEmptyLine || textPageList[line].isImageLine) {
                pageEndLineIndexList.add(currentLineIndex)
                break
            }
        }
    }

    /**
     * 在已有 textPageList 的情况下，基于新的页面高度重新计算分页，避免因高度估算不准导致的底部截断。
     */
    fun recalculatePagesForHeight(newPageHeight: Float) {
        if (newPageHeight <= 0 || !::textPageList.isInitialized) {
            return
        }
        pageHeight = newPageHeight
        pageEndLineIndexList.clear()

        // 依据新的高度重新分页
        var totalLineHeight = 0
        var currentLineIndex = 0
        for (textLine in textPageList) {
            totalLineHeight += textLine.getLineHeight(lineHeight).toInt()
            if (totalLineHeight > pageHeight) {
                pageEndLineIndexList.add(currentLineIndex)
                totalLineHeight = textLine.getLineHeight(lineHeight).toInt()
            }
            currentLineIndex++
        }

        if (pageEndLineIndexList.isEmpty()) {
            pageEndLineIndexList.add(currentLineIndex)
            return
        }

        val prevEndLine = pageEndLineIndexList.last()
        var line = currentLineIndex
        while (line > prevEndLine) {
            line--
            if (!textPageList[line].isEmptyLine || textPageList[line].isImageLine) {
                pageEndLineIndexList.add(currentLineIndex)
                break
            }
        }
    }

    /*获取当前页的最后一个字符的位置*/
    fun getPageLastWordOffset(page: Int): Int {
        val line = getPageFirstLineIndex(page)
        for (i in (line + getPageTotalLines(page) - 1) downTo 0) {
            val textLine = textPageList[i]
            if (!textLine.isNullOrEmpty()) {
                return textLine[textLine.size - 1].location
            }
        }
        return 0
    }

    fun getFirstWordOffset(page: Int): Int {
        val line = getPageFirstLineIndex(page)
        for (i in line until textPageList.size) {
            val textLine = textPageList[i]
            if (!textLine.isNullOrEmpty()) {
                return textLine[0].location
            }
        }
        return 0
    }

    fun getPageFirstLineIndex(page: Int = indexInChapter): Int {
        if (page <= 0) return 0
        if (pageEndLineIndexList.isEmpty()) return 0
        val idx = page - 1
        return if (idx in 0 until pageEndLineIndexList.size) pageEndLineIndexList[idx] else 0
    }

    fun getPageLastLineIndex(page: Int = indexInChapter): Int {
        if (pageEndLineIndexList.isEmpty()) return 0
        if (page < 0) return 0
        return if (page < pageEndLineIndexList.size) pageEndLineIndexList[page] - 1 else pageEndLineIndexList.last() - 1
    }

    fun findPageFromOffset(offset: Int): Int {
        if (offset <= 0) {
            return 0
        }
        val pageNumber = getChapterPageCount()
        for (i in 1 until pageNumber) {
            val firstWord = getFirstWordOffset(i)
            if (offset < firstWord) {
                return i - 1
            }
        }
        return pageNumber - 1
    }

    fun getPageTotalLines(pageIndex: Int): Int {
        if (pageEndLineIndexList.isEmpty()) return 0
        if (pageIndex <= 0) return pageEndLineIndexList[0]
        return if (pageIndex < pageEndLineIndexList.size) {
            pageEndLineIndexList[pageIndex] - pageEndLineIndexList[pageIndex - 1]
        } else 0
    }

    fun findAnchor(anchor: String): Int {
        if (getPageTotalLines(indexInChapter) > 0 && !anchorList.isNullOrEmpty()) {
            for (r in anchorList) {
                if (r.data == anchor) {
                    val position = r.location
                    var index = 0
                    for (pageItem in textPageList) {
                        if (!pageItem.isEmpty() && pageItem[0].location > position) {
                            return getPageFromLine(index - 1)
                        }
                        index++
                    }
                    break
                }
            }
        }
        return 0
    }

    private fun getPageFromLine(line: Int): Int {
        for (i in pageEndLineIndexList.indices) {
            if (line < pageEndLineIndexList[i]) {
                return i
            }
        }
        return 0
    }

    fun getSummary(page: Int): String {
        var line = getPageFirstLineIndex(page)
        val sb = StringBuilder()
        while (line < textPageList.size && textPageList[line].isEmptyLine) {
            line++
        }
        if (line == textPageList.size) {
            return ""
        }
        for (word in textPageList[line]) {
            sb.append(word.word.toString())
        }
        return sb.toString()
    }

    fun getPageSummary(page: Int): String {
        var line = getPageFirstLineIndex(page)
        val sb = StringBuilder()
        while (line < textPageList.size && textPageList[line].isEmptyLine) {
            line++
        }
        if (line == textPageList.size) {
            return ""
        }

        for (i in 0 until textPageList.size) {
            if (line < textPageList.size && sb.length < 100) {
                for (word in textPageList[line]) {
                    val linkSpan = word.findLink()
                    if (linkSpan == null || linkSpan !is FootnoteSpan) {
                        sb.append(word.word.toString())
                    }
                }
                if (textPageList[line].isBlockLastLine) {
                    sb.append(' ')
                }
                line++
            } else {
                break
            }
        }

        return if (sb.length > 100) {
            sb.toString().substring(0, 100)
        } else {
            sb.toString()
        }
    }

    /**
     * 获取指定页面的完整文本内容
     */
    fun getPageText(page: Int): String {
        val startLine = getPageFirstLineIndex(page)
        val totalLines = getPageTotalLines(page)
        val endLine = startLine + totalLines - 1
        
        val sb = StringBuilder()
        for (i in startLine..endLine) {
            if (i < textPageList.size) {
                val line = textPageList[i]
                if (!line.isEmptyLine) {
                    for (word in line) {
                        val linkSpan = word.findLink()
                        if (linkSpan == null || linkSpan !is FootnoteSpan) {
                            sb.append(word.word.toString())
                        }
                    }
                    if (line.isBlockLastLine) {
                        sb.append(' ')
                    } else if (!line.isEmptyLine) {
                        sb.append('\n')
                    }
                }
            }
        }
        
        return sb.toString()
    }

    fun setTextFontType(typeface: Typeface) {
        textPaint.typeface = typeface
    }
}
