package com.wedevote.wdbook.ui.read.lib

import android.graphics.Color
import com.aquila.lib.tools.singleton.SPSingleton
import com.wedevote.wdbook.constants.HighlightColorType
import com.wedevote.wdbook.tools.util.SPKeyDefine
import com.wedevote.wdbook.tools.util.dp2px

object BookParameters {
    @JvmStatic
    val textNormalColor = Color.parseColor("#FF242424")

    @JvmStatic
    val textNormalColorDark = Color.parseColor("#A0FFFFFF")
    @JvmStatic
    val textGrayColor = Color.parseColor("#9B9B9B")
    
    @JvmStatic
    val searchHighlightColor = Color.parseColor("#66FF8A00")

    var lastMarkType =
        HighlightColorType.contentOf(SPSingleton.get().getString(SPKeyDefine.SP_LastMarkType, HighlightColorType.COLOR_YELLOW.value))

//    var markColor = "FFF7AF" //SPSingleton.get().getInt(SPKeyDefine.SP_LastHighlightColor, HIGHLIGHT_COLOR_YELLOW)

    var textLevel: Int = SPSingleton.get().getInt(SPKeyDefine.SP_BookTextLevel, 5)

    fun getTextSizePx(level: Int = textLevel): Float {
        val dp: Int = if (level < 7) {
            level * 2 + 16
        } else {
            level * 4 + 4
        }
        return dp2px(dp) * 0.8f
    }
}
