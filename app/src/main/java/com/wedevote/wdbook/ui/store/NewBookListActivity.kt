package com.wedevote.wdbook.ui.store

import android.content.Intent
import android.os.Bundle
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.appcompat.widget.Toolbar
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.Group
import androidx.coordinatorlayout.widget.CoordinatorLayout
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.aquila.lib.layout.SmartRefreshLayout
import com.aquila.lib.layout.api.RefreshLayout
import com.aquila.lib.layout.listener.OnLoadMoreListener
import com.aquila.lib.layout.listener.OnRefreshListener
import com.aquila.lib.tools.util.ToastUtil
import com.aquila.lib.widget.group.GroupImageTextLayout
import com.aquila.lib.widget.view.CustomRecyclerView
import com.google.android.material.appbar.AppBarLayout
import com.google.android.material.appbar.CollapsingToolbarLayout
import com.wedevote.wdbook.R
import com.wedevote.wdbook.base.ExceptionHandler
import com.wedevote.wdbook.base.RootActivity
import com.wedevote.wdbook.base.SDKSingleton
import com.wedevote.wdbook.constants.AnalyticsConstants
import com.wedevote.wdbook.constants.AnalyticsConstants.LOG_V1_PARAM_CATEGORY_ID
import com.wedevote.wdbook.constants.OrderField
import com.wedevote.wdbook.entity.store.ProductFilterEntity
import com.wedevote.wdbook.entity.store.StoreCategoryEntity
import com.wedevote.wdbook.exception.SDKException
import com.wedevote.wdbook.tools.event.OnSuccessBuyEvent
import com.wedevote.wdbook.tools.interfaces.OnItemClickListener
import com.wedevote.wdbook.tools.util.AnimatorUtil
import com.wedevote.wdbook.tools.util.AnalyticsUtils.logEvent
import com.wedevote.wdbook.tools.util.IntentConstants
import com.wedevote.wdbook.ui.home.microwidget.BookItemRecyclerAdapter
import com.wedevote.wdbook.ui.store.search.SearchActivity
import com.wedevote.wdbook.ui.dialogs.LoadingProgressDialog
import com.wedevote.wdbook.ui.widgets.CommTopTitleLayout
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

/***
 * @date 创建时间 2020/5/12 18:38
 * <AUTHOR> W.YuLong
 * @description 书籍列表的界面
 */
class NewBookListActivity : RootActivity(), OnRefreshListener, OnLoadMoreListener, View.OnClickListener {
    lateinit var rootView: View
    lateinit var topTitleLayout: CommTopTitleLayout
    
    lateinit var refreshLayout: SmartRefreshLayout
    
    lateinit var searchImageView: ImageView
    lateinit var containerCoordinatorLayout: CoordinatorLayout
    lateinit var appbarLayout: AppBarLayout
    lateinit var collapsingToolbarLayout: CollapsingToolbarLayout
    
    lateinit var expandCategoryImageView: ImageView
    lateinit var subCategoryImageView: ImageView
    
    lateinit var collapsingToolBar: Toolbar
    lateinit var descContentLayout: GroupImageTextLayout
    
    lateinit var filterContainerLayout: ConstraintLayout
    lateinit var categoryRecyclerView: CustomRecyclerView
    lateinit var subCategoryRecyclerView: CustomRecyclerView
    lateinit var sortContainerLayout: ConstraintLayout
    lateinit var defaultSortTextView: TextView
    lateinit var soldSortTextView: TextView
    lateinit var priceSortTextView: TextView
    lateinit var languageLayout: GroupImageTextLayout
    lateinit var dataRecyclerView: CustomRecyclerView
    lateinit var subGroup: Group
    lateinit var emptyPromptTextView: TextView
    
    lateinit var itemDataAdapter: BookItemRecyclerAdapter
    lateinit var categoryAdapter: FlexboxDataAdapter<StoreCategoryEntity>
    lateinit var subCategoryAdapter: FlexboxDataAdapter<StoreCategoryEntity>
    
    lateinit var languagePopupWindow: SortPopupWindow
    lateinit var filterEntity: ProductFilterEntity
    
    var currentSortMode: OrderField = OrderField.CreateTime_DESC
    var title: String = ""
    var type: Int = 0
    
    lateinit var allEntity: StoreCategoryEntity
    var categoryId: Long = 0L
    var categoryName: String? = ""
    var typeKey: String? = ""
    var lastPublishId: Long? = 0L
    
    private var loadingDialog: LoadingProgressDialog? = null
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        rootView = layoutInflater.inflate(R.layout.activity_new_book_list_layout, null)
        setContentView(rootView)
        initViewFromXML()
        
        filterEntity = ProductFilterEntity()
        allEntity = StoreCategoryEntity().apply {
            categoryName = getString(R.string.all_books)
        }
        categoryId = allEntity.categoryId
        
        getArgumentsFromIntent()
        languagePopupWindow = SortPopupWindow(this)
        initAdapters()
        setViewListeners()
        initDataUI()
        setCurrentSortModeUI(OrderField.CreateTime_DESC)
        
        if (categoryId != allEntity.categoryId) {
            logEvent(AnalyticsConstants.LOG_V1_PRODUCT_CATEGORY, LOG_V1_PARAM_CATEGORY_ID, categoryId.toString())
        }
        topTitleLayout.setTitle(categoryName)
        loadingDialog = LoadingProgressDialog(this)
        showLoadingDialog()
        getDataFromServer()
    }
    
    fun initViewFromXML() {
        topTitleLayout = findViewById(R.id.book_list_top_TitleLayout)
        
        searchImageView = findViewById(R.id.book_list_search_ImageView)
        containerCoordinatorLayout = findViewById(R.id.book_list_container_CoordinatorLayout)
        appbarLayout = findViewById(R.id.book_list_AppBarLayout)
        
        collapsingToolbarLayout = findViewById(R.id.book_list_CollapsingToolbarLayout)
        
        collapsingToolBar = findViewById(R.id.book_list_collapsing_title_Toolbar)
        descContentLayout = findViewById(R.id.book_list_top_collapsing_content_Layout)
        
        filterContainerLayout = findViewById(R.id.book_list_filter_container_layout)
        categoryRecyclerView = findViewById(R.id.book_list_category_RecyclerView)
        expandCategoryImageView = findViewById(R.id.book_list_category_expand_ImageView)
        subCategoryImageView = findViewById(R.id.book_list_sub_category_expand_ImageView)
        subCategoryRecyclerView = findViewById(R.id.book_list_sub_category_RecyclerView)
        subGroup = findViewById(R.id.book_list_sub_category_Group)
        emptyPromptTextView = findViewById(R.id.book_list_empty_prompt_TextView)
        
        sortContainerLayout = findViewById(R.id.book_list_sort_container_Layout)
        defaultSortTextView = findViewById(R.id.book_list_sort_default_TextView)
        soldSortTextView = findViewById(R.id.book_list_sort_sold_TextView)
        priceSortTextView = findViewById(R.id.book_list_sort_price_TextView)
        languageLayout = findViewById(R.id.book_list_language_layout)
        
        refreshLayout = findViewById(R.id.book_list_SmartRefreshLayout)
        dataRecyclerView = findViewById(R.id.book_list_data_RecyclerView)
    }
    
    private fun getArgumentsFromIntent() {
        categoryName = intent.getStringExtra(IntentConstants.EXTRA_CategoryName)
        typeKey = intent.getStringExtra(IntentConstants.EXTRA_TypeKey)
        lastPublishId = intent.getStringExtra(IntentConstants.EXTRA_LastPublishId)?.toLong()
        categoryId = intent.getLongExtra(IntentConstants.EXTRA_CategoryId, allEntity.categoryId)
        
        filterEntity.categoryId = categoryId
    }
    
    private fun initAdapters() {
        categoryAdapter = FlexboxDataAdapter()
        categoryRecyclerView.adapter = categoryAdapter
        
        subCategoryAdapter = FlexboxDataAdapter()
        subCategoryRecyclerView.adapter = subCategoryAdapter
        
        itemDataAdapter = BookItemRecyclerAdapter()
        dataRecyclerView.adapter = itemDataAdapter
    }
    
    fun initDataUI() {
        categoryAdapter.dataList = SDKSingleton.storeBl.getLocalStoreCategoryEntityList()?.toMutableList()
            ?.also {
                it.add(0, allEntity)
            }
        var categoryEntity = SDKSingleton.appBl.getCategoryById(this.categoryId)
        if (categoryEntity != null) {
            if (categoryEntity.parentCategoryId > 0) {
                this.categoryId = categoryEntity.parentCategoryId
            }
        }
    
        if (!categoryAdapter.dataList.isNullOrEmpty()) {
            var position = 0
            for (item in categoryAdapter.dataList!!) {
                if (item.categoryId == this.categoryId) {
                    categoryAdapter.selectPosition = position
                    categoryName = item.categoryName
                    categoryRecyclerView.post { categoryRecyclerView.scrollToPosition(position) }
                    break
                }
                position++
            }
        }
    
        categoryRecyclerView.setLayoutStyle(CustomRecyclerView.LIST_HORIZONTAL)
        initSubCategoryList(this.categoryId)
    
        if (categoryEntity != null && !subCategoryAdapter.dataList.isNullOrEmpty()) {
            var i = 0
            for (entity in subCategoryAdapter.dataList!!) {
                if (entity.categoryId == categoryEntity.categoryId) {
                    subCategoryAdapter.selectPosition = i
                    subCategoryAdapter.notifyDataSetChanged()
                    break
                }
                i++
            }
        }
    
    }
    
    private fun initSubCategoryList(parentCategoryId: Long) {
        if (parentCategoryId == -1L) {
            subGroup.visibility = View.GONE
            subCategoryAdapter.selectPosition = 0
        } else {
            var list = SDKSingleton.storeBl.getSubCategoryEntityList(parentCategoryId)?.toMutableList()
                ?.also { it.add(0, allEntity) }
            subCategoryAdapter.dataList = list
            subGroup.visibility = if (list?.size!! <= 1) View.GONE else View.VISIBLE
        }
    }
    
    private fun setViewListeners() {
        refreshLayout.setOnRefreshListener(this).setOnLoadMoreListener(this)
        searchImageView.setOnClickListener(this)
        defaultSortTextView.setOnClickListener(this)
        priceSortTextView.setOnClickListener(this)
        soldSortTextView.setOnClickListener(this)
        languageLayout.setOnClickListener(this)
        expandCategoryImageView.setOnClickListener(this)
        subCategoryImageView.setOnClickListener(this)
        descContentLayout.setOnClickListener(this)
        appbarLayout.addOnOffsetChangedListener(object : AppBarLayout.OnOffsetChangedListener {
            override fun onOffsetChanged(appBarLayout: AppBarLayout?, verticalOffset: Int) {
                var alpha: Float =
                    Math.abs(verticalOffset).toFloat() / (collapsingToolbarLayout.height - collapsingToolBar.height).toFloat()
                collapsingToolBar.alpha = alpha
                filterContainerLayout.alpha = 1 - alpha
                collapsingToolBar.visibility = if (alpha == 0f) View.GONE else View.VISIBLE
            }
        })
        
        categoryAdapter.onItemClickListener = object : OnItemClickListener {
            override fun <T> onItemClick(obj: Any, tag: String, t: T) {
                t as StoreCategoryEntity
                topTitleLayout.setTitle(t.categoryName)
                initSubCategoryList(t.categoryId)
                filterEntity.categoryId = t.categoryId
                onRefresh(refreshLayout)
                makeItemCenter(categoryRecyclerView, categoryAdapter.selectPosition)
//                categoryRecyclerView.scrollToPosition(categoryAdapter.selectPosition)
            }
        }
        
        subCategoryAdapter.onItemClickListener = object : OnItemClickListener {
            override fun <T> onItemClick(obj: Any, tag: String, t: T) {
                t as StoreCategoryEntity
                if (t.categoryId == allEntity.categoryId) {
                    filterEntity.categoryId = categoryAdapter.getDataFromPosition(categoryAdapter.selectPosition)?.categoryId ?: -1
                } else {
                    filterEntity.categoryId = t.categoryId
                }
                onRefresh(refreshLayout)
                makeItemCenter(subCategoryRecyclerView, subCategoryAdapter.selectPosition)
//                subCategoryRecyclerView.smoothScrollToPosition(subCategoryAdapter.selectPosition )
            }
        }
        
        languagePopupWindow.onItemClickListener = object : OnItemClickListener {
            override fun <T> onItemClick(obj: Any, tag: String, t: T) {
                t as LanguageEntity
                filterEntity.language = t.language
                languageLayout.setText(t.name)
                onRefresh(refreshLayout)
                languagePopupWindow.dismiss()
            }
        }
    }
    
    
    fun makeItemCenter(recyclerView: RecyclerView, selectPosition: Int) {
        if (recyclerView.layoutManager is LinearLayoutManager) {
            var layoutManager = recyclerView.layoutManager as LinearLayoutManager
            var firstPosition = layoutManager.findFirstVisibleItemPosition()
            var lastPosition = layoutManager.findLastVisibleItemPosition()
            var left = recyclerView.getChildAt(selectPosition - firstPosition).left
            var right = recyclerView.getChildAt(lastPosition - selectPosition).left
            recyclerView.scrollBy((left - right) / 2, 0)
        }
    }
    
    /*折叠的title*/
    fun initCollapsingTitle() {
        var categoryTitle = categoryAdapter.getDataFromPosition(categoryAdapter.selectPosition)?.categoryName ?: ""
        if (subCategoryRecyclerView.visibility == View.VISIBLE) {
            subCategoryAdapter.getDataFromPosition(subCategoryAdapter.selectPosition)?.categoryName?.let {
                categoryTitle += " · $it"
            }
        }
        descContentLayout.setText("$categoryTitle · $sortText · ${languageLayout.text}")
    }
    
    var sortText: String = ""
    private fun setCurrentSortModeUI(sort: OrderField) {
        when (sort) {
            OrderField.CreateTime_DESC -> {
                defaultSortTextView.isSelected = true
                priceSortTextView.isSelected = false
                soldSortTextView.isSelected = false
                filterEntity.orderField = OrderField.CreateTime_DESC.value
                sortText = defaultSortTextView.text.toString()
            }
            OrderField.Price_ASC -> {
                defaultSortTextView.isSelected = false
                priceSortTextView.isSelected = true
                soldSortTextView.isSelected = false
                filterEntity.orderField = OrderField.Price_ASC.value
                sortText = priceSortTextView.text.toString()
            }
            OrderField.SaleCount_DESC -> {
                defaultSortTextView.isSelected = false
                priceSortTextView.isSelected = false
                soldSortTextView.isSelected = true
                filterEntity.orderField = OrderField.SaleCount_DESC.value
                sortText = soldSortTextView.text.toString()
            }
            else -> {}
        }
        filterEntity.orderField = sort.value
        currentSortMode = sort
        onRefresh(refreshLayout)
    }
    
    override fun onRefresh(refreshLayout: RefreshLayout) {
        itemDataAdapter.clearDataList()
        filterEntity.page = 1
        initCollapsingTitle()
        showLoadingDialog()
        getDataFromServer()
    }
    
    override fun onLoadMore(refreshLayout: RefreshLayout) {
        filterEntity.page++
        getDataFromServer()
    }
    
    private fun getDataFromServer() {
        MainScope().launch(ExceptionHandler.getCoroutineExceptionHandler {
            if (it is SDKException) {
                ToastUtil.showToastShort(R.string.get_list_data_failure)
            }
            refreshLayout.finishLoadMoreAndRefresh()
            dismissLoadingDialogIfShowing()
        }) {
            SDKSingleton.storeBl.getBookProductFilter(filterEntity.transferToMap()).also { t ->
                if (filterEntity.page == 1) {
                    itemDataAdapter.dataList = t.productList
                } else {
                    itemDataAdapter.addDataList(t.productList)
                }
                refreshLayout.isEnableLoadMore = t.currentPage < t.totalPage
                if (itemDataAdapter.itemCount == 0) {
                    emptyPromptTextView.visibility = View.VISIBLE
                    refreshLayout.visibility = View.GONE
                } else {
                    emptyPromptTextView.visibility = View.GONE
                    refreshLayout.visibility = View.VISIBLE
                }
            }
            refreshLayout.finishLoadMoreAndRefresh()
            dismissLoadingDialogIfShowing()
        }
    }
    
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onReceiveBuyResultEvent(event: OnSuccessBuyEvent) {
        itemDataAdapter.updateProductPurchaseStatus(event.productId)
    }
    
    override fun onClick(v: View?) {
        when (v) {
            searchImageView -> {
                val intent = Intent(this, SearchActivity::class.java)
                startActivity(intent)
            }
            descContentLayout -> {
                appbarLayout.setExpanded(true)
            }
            expandCategoryImageView -> {
                if (categoryRecyclerView.layoutStyle == CustomRecyclerView.LIST_HORIZONTAL) {
                    categoryRecyclerView.setLayoutStyle(CustomRecyclerView.FLEXBOX)
                    AnimatorUtil.rotateAnimator(expandCategoryImageView, 0f, 180f)
                } else {
                    categoryRecyclerView.setLayoutStyle(CustomRecyclerView.LIST_HORIZONTAL)
                    categoryRecyclerView.scrollToPosition(categoryAdapter.selectPosition)
                    AnimatorUtil.rotateAnimator(expandCategoryImageView, 180f, 0f)
                }
            }
            subCategoryImageView -> {
                if (subCategoryRecyclerView.layoutStyle == CustomRecyclerView.LIST_HORIZONTAL) {
                    subCategoryRecyclerView.setLayoutStyle(CustomRecyclerView.FLEXBOX)
                    AnimatorUtil.rotateAnimator(subCategoryImageView, 0f, 180f)
                } else {
                    subCategoryRecyclerView.setLayoutStyle(CustomRecyclerView.LIST_HORIZONTAL)
                    subCategoryRecyclerView.scrollToPosition(subCategoryAdapter.selectPosition)
                    AnimatorUtil.rotateAnimator(subCategoryImageView, 180f, 0f)
                }
            }
            defaultSortTextView -> {
                setCurrentSortModeUI(OrderField.CreateTime_DESC)
            }
            soldSortTextView -> {
                setCurrentSortModeUI(OrderField.SaleCount_DESC)
            }
            priceSortTextView -> {
                setCurrentSortModeUI(OrderField.Price_ASC)
            }
            languageLayout -> {
                languagePopupWindow.showAsDropDown(languageLayout)
            }
        }
    }

    private fun showLoadingDialog() {
        loadingDialog?.show()
        loadingDialog?.setTitleText(getString(R.string.loading))
    }

    private fun dismissLoadingDialogIfShowing() {
        if (loadingDialog?.isShowing == true) {
            loadingDialog?.dismiss()
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        dismissLoadingDialogIfShowing()
        loadingDialog = null
    }
}
