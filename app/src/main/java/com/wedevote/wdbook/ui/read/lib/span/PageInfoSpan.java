package com.wedevote.wdbook.ui.read.lib.span;

import android.graphics.Canvas;
import android.graphics.Paint;

public class PageInfoSpan implements SpecialElementSpan{
    private final String pageInfo;
    private final static int pageColor = 0xFFE9973E;
    public PageInfoSpan(String page){
        this.pageInfo = page + ' ';
    }

    @Override
    public float getElementWidth(Paint paint) {
        return paint.measureText(pageInfo) * 0.7f;
    }

    @Override
    public void drawIt(Canvas canvas, Paint paint, float x, float y, int height, float ascent){
        int oldColor = paint.getColor();
        float oldSize = paint.getTextSize();
        paint.setColor(pageColor);
        canvas.drawLine(x, y+ascent, x+getElementWidth(paint), y+ascent, paint);
        canvas.drawLine(x, y+ascent, x, y+ascent+height, paint);
        paint.setTextSize(oldSize * 0.7f);
        canvas.drawText(pageInfo, x+2, y, paint);
        paint.setColor(oldColor);
        paint.setTextSize(oldSize);
    }
}
