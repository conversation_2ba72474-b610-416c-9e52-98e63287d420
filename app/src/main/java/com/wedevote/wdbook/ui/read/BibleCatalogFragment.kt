package com.wedevote.wdbook.ui.read

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.GridView
import android.widget.TextView
import com.aquila.lib.base.BaseListAdapter
import com.aquila.lib.base.BaseViewHolder
import com.aquila.lib.base.OnViewClickListener
import com.wedevote.wdbook.R
import com.wedevote.wdbook.base.BaseRootFragment
import com.wedevote.wdbook.ui.read.lib.EPubBook
import java.io.Serializable

/***
 * @date 创建时间 2020/5/27 14:42
 * <AUTHOR> <PERSON><PERSON><PERSON>
 * @description
 */
class BibleCatalogFragment : BaseRootFragment(), OnViewClickListener {
    lateinit var bookGridView: GridView
    lateinit var chapterGridView: GridView
    lateinit var verseGridView: GridView

    lateinit var bookAdapter: ItemBookGridAdapter
    lateinit var chapterAdapter: ItemGridAdapter
    lateinit var verseAdapter: ItemGridAdapter

    lateinit var bookIds: Array<String>
    lateinit var bookNames: Array<String>
    lateinit var bookAbbrNames: Array<String>

    val labelBook = "book"
    val labelChapter = "章"
    val labelVerse = "节"

    var onBibleTabChangeCallback: OnBibleTabChangeCallback? = null

    var dialoglistener: BookContentsDialog.ContentsDialogListener? = null

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        val v = inflater.inflate(R.layout.fragment_child_bible_catalog_layout, container, false)
        bookGridView = v.findViewById(R.id.child_bible_book_name_GridView)
        chapterGridView = v.findViewById(R.id.child_bible_chapter_GridView)
        verseGridView = v.findViewById(R.id.child_bible_verse_GridView)

        bookAdapter = ItemBookGridAdapter(labelBook).apply { onViewClickListener = this@BibleCatalogFragment }
        chapterAdapter = ItemGridAdapter(labelChapter).apply { onViewClickListener = this@BibleCatalogFragment }
        verseAdapter = ItemGridAdapter(labelVerse).apply { onViewClickListener = this@BibleCatalogFragment }
        bookGridView.adapter = bookAdapter
        chapterGridView.adapter = chapterAdapter
        verseGridView.adapter = verseAdapter

        bookAbbrNames = resources.getStringArray(R.array.book_abbr_name)
        bookNames = resources.getStringArray(R.array.book_name)
        bookIds = resources.getStringArray(R.array.book_id)

        bookAdapter.setDataList(createData())
        setCurrentStepShow(0)
        return v
    }

    fun createData(): ArrayList<BibleIndexBean> {
        val dataList: ArrayList<BibleIndexBean> = ArrayList()
        var id: Int = 0
        for (r in EPubBook.referenceList) {
            dataList.add(BibleIndexBean(id++, getBookAbbrName(r.bookId), getBookName(r.bookId)))
        }
        return dataList
    }

    private fun getBookName(id: String): String {
        for ((i, bookId) in bookIds.withIndex()) {
            if (bookId.compareTo(id, true) == 0) {
                return bookNames[i]
            }
        }
        return ""
    }

    private fun getBookAbbrName(id: String): String {
        for ((i, bookId) in bookIds.withIndex()) {
            if (bookId.compareTo(id, true) == 0) {
                return bookAbbrNames[i]
            }
        }
        return ""
    }

    var currentStep = 0
    private fun setCurrentStepShow(step: Int) {
        bookGridView.visibility = View.GONE
        chapterGridView.visibility = View.GONE
        verseGridView.visibility = View.GONE
        when (step) {
            1 -> {
                val bookIndex = bookAdapter.selectItem?.id
                chapterAdapter.setDataList(
                    EPubBook.getReferenceChapters(bookIndex!!).toMutableList()
                )
                chapterGridView.visibility = View.VISIBLE
            }
            2 -> {
                val bookIndex = bookAdapter.selectItem?.id
                val chapterId = chapterAdapter.selectPosition
                verseAdapter.setDataList(
                    EPubBook.getReferenceVerses(
                        bookIndex!!,
                        chapterId
                    ).toMutableList()
                )
                verseGridView.visibility = View.VISIBLE
            }
            else -> {
                bookGridView.visibility = View.VISIBLE
            }
        }
        currentStep = step
        onBibleTabChangeCallback?.onCurrentStepSelected(getCurrentTitleStr())
    }

    fun getCurrentTitleStr(): String? {
        return when (currentStep) {
            1 -> bookAdapter.selectItem?.subTitle
            2 -> bookAdapter.selectItem?.subTitle + (chapterAdapter.selectPosition + 1).toString()
            else -> null
        }
    }

    fun doOnBackPress(): Boolean {
        if (currentStep > 0) {
            setCurrentStepShow(currentStep - 1)
            return true
        }
        return false
    }

    override fun <T> onClickAction(v: View, str: String, t: T?) {
        when (tag) {
            labelBook -> {
                setCurrentStepShow(1)
            }
            labelChapter -> {
                setCurrentStepShow(2)
            }
            labelVerse -> {
                val bookIndex = bookAdapter.selectItem?.id
                val chapterId = chapterAdapter.selectPosition
                val verseId = verseAdapter.selectPosition
                val link = EPubBook.getReferencePath(bookIndex!!, chapterId, verseId)
                dialoglistener?.onCallBack(link)
            }
        }
    }

    fun setOnDialogListener(listener: BookContentsDialog.ContentsDialogListener?) {
        dialoglistener = listener
    }

    /***
     *@date 创建时间 2020/5/28 14:07
     *<AUTHOR> W.YuLong
     *@description
     */
    class ItemBookGridAdapter(var labelBook: String) : BaseListAdapter<BibleIndexBean, ItemViewHolder>() {
        var onViewClickListener: OnViewClickListener? = null
        var selectItem: BibleIndexBean? = null
        override fun onBindHolder(holder: ItemViewHolder, position: Int) {
            val data = getDataList()!![position]
            holder.initUIData(data)
            holder.setItemSelectState(data.equals(selectItem))
            holder.itemView.setOnClickListener { v ->
                selectItem = data
                onViewClickListener?.onClickAction(v, labelBook, data)
                notifyDataSetChanged()
            }
        }

        override fun onCreateViewHolder(parent: ViewGroup, type: Int): ItemViewHolder {
            return ItemViewHolder(parent)
        }
    }
}

interface OnBibleTabChangeCallback {
    fun onCurrentStepSelected(currentTitle: String?)
}

/***
 *@date 创建时间 2020/5/28 14:07
 *<AUTHOR> W.YuLong
 *@description
 */
class ItemGridAdapter(var label: String) : BaseListAdapter<Any, ItemViewHolder>() {
    var selectPosition: Int = 0
    var onViewClickListener: OnViewClickListener? = null

    override fun onBindHolder(holder: ItemViewHolder, position: Int) {
        holder.abbrNameTextView.text = getDataList()?.get(position)?.toString()
        holder.fullNameTextView.text = label
        holder.setItemSelectState(position == selectPosition)
        holder.itemView.setOnClickListener { v ->
            selectPosition = position
            onViewClickListener?.onClickAction(v, label, position)
            notifyDataSetChanged()
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, type: Int): ItemViewHolder {
        return ItemViewHolder(parent)
    }
}

/***
 *@date 创建时间 2020/5/28 14:07
 *<AUTHOR> W.YuLong
 *@description
 */
class ItemViewHolder(parent: ViewGroup) : BaseViewHolder(parent, R.layout.item_bible_catalog_layout) {
    val abbrNameTextView: TextView = itemView.findViewById(R.id.item_bible_abbr_name_TextView)
    val fullNameTextView: TextView = itemView.findViewById(R.id.item_bible_full_name_TextView)

    override fun <T> initUIData(t: T) {
        t as BibleIndexBean
        abbrNameTextView.text = t.title
        fullNameTextView.text = t.subTitle
    }

    fun setItemSelectState(isSelected: Boolean) {
        abbrNameTextView.isSelected = isSelected
        fullNameTextView.isSelected = isSelected
    }
}

data class BibleIndexBean(var id: Int, var title: String, var subTitle: String) : Serializable
