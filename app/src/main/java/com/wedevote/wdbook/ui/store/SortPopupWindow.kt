package com.wedevote.wdbook.ui.store

import android.content.Context
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.PopupWindow
import android.widget.TextView
import com.aquila.lib.base.BaseRecycleAdapter
import com.aquila.lib.base.BaseViewHolder
import com.aquila.lib.widget.view.CustomRecyclerView
import com.wedevote.wdbook.R
import com.wedevote.wdbook.constants.LanguageMode
import com.wedevote.wdbook.tools.interfaces.OnItemClickListener
import com.wedevote.wdbook.tools.util.findString

/***
 * @date 创建时间 2021/12/13 14:01
 * <AUTHOR> W.YuLong
 * @description
 */
class SortPopupWindow(context: Context?, attrs: AttributeSet? = null) : PopupWindow(context, attrs) {
    var dataRecyclerView: CustomRecyclerView
    var maskView: View

    var onItemClickListener: OnItemClickListener? = null
        set(value) {
            field = value
            adapter.onItemClickListener = value
        }

    var adapter: SortPopupAdapter

    val itemList = arrayListOf(
        LanguageEntity(findString(R.string.all_language), ""),
        LanguageEntity("简体中文", LanguageMode.SimplifiedChinese.value),
        LanguageEntity("繁體中文", LanguageMode.TraditionalChinese.value),
    )

    init {
        height = ViewGroup.LayoutParams.WRAP_CONTENT
        width = ViewGroup.LayoutParams.MATCH_PARENT
        isOutsideTouchable = true
        isFocusable = true
        setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        val v = LayoutInflater.from(context).inflate(R.layout.popup_windown_sort_layout, null)
        contentView = v

        dataRecyclerView = v.findViewById(R.id.sort_popup_data_RecyclerView)
        maskView = v.findViewById(R.id.sort_popup_mask_View)

        adapter = SortPopupAdapter()
        adapter.onItemClickListener = onItemClickListener

        dataRecyclerView.adapter = adapter
        initSortDataList(itemList)

        maskView.setOnClickListener { dismiss() }
    }

    fun initSortDataList(list: MutableList<LanguageEntity>) {
        adapter.dataList = list
    }
}

/***
 *@date 创建时间 2021/12/13 15:51
 *<AUTHOR> W.YuLong
 *@description
 */
class SortPopupAdapter : BaseRecycleAdapter<LanguageEntity, SortPopupItemViewHolder>() {
    var selectPosition = 0
    var onItemClickListener: OnItemClickListener? = null
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): SortPopupItemViewHolder {
        return SortPopupItemViewHolder(parent)
    }

    override fun onBindViewHolder(holder: SortPopupItemViewHolder, position: Int) {
        super.onBindViewHolder(holder, position)
        holder.setItemSelected(position == selectPosition)
        holder.itemView.setOnClickListener {
            selectPosition = position
            onItemClickListener?.onItemClick(it, "", getDataFromPosition(position))
            notifyDataSetChanged()
        }
    }
}

/***
 *@date 创建时间 2021/12/13 15:51
 *<AUTHOR> W.YuLong
 *@description
 */
class SortPopupItemViewHolder(parent: ViewGroup) : BaseViewHolder(parent, R.layout.holder_item_popupwindown_layout) {
    val checkFlagImageView: ImageView = itemView.findViewById(R.id.item_popup_checked_flag_ImageView)
    val contentTextView: TextView = itemView.findViewById(R.id.item_popup_content_TextView)

    override fun <T> initUIData(t: T) {
        t as LanguageEntity
        contentTextView.text = t.name
    }

    fun setItemSelected(isSelected: Boolean) {
        checkFlagImageView.visibility = if (isSelected) View.VISIBLE else View.INVISIBLE
    }
}
