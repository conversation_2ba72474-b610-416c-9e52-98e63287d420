package com.wedevote.wdbook.ui.read

import android.app.Activity
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.LinearLayout
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.aquila.lib.base.BaseRecycleAdapter
import com.aquila.lib.base.BaseViewHolder
import com.aquila.lib.base.OnViewClickListener
import com.aquila.lib.layout.SmartRefreshLayout
import com.aquila.lib.layout.api.RefreshLayout
import com.aquila.lib.layout.listener.OnLoadMoreListener
import com.aquila.lib.layout.listener.OnRefreshListener
import com.aquila.lib.widget.view.CustomRecyclerView
import com.chauthai.swipereveallayout.SwipeRevealLayout
import com.wedevote.wdbook.R
import com.wedevote.wdbook.base.SDKSingleton
import com.wedevote.wdbook.entity.BookmarkEntity
import com.wedevote.wdbook.tools.event.BookDataChangeEvent
import com.wedevote.wdbook.tools.util.UnitFormatUtil
import com.wedevote.wdbook.ui.read.lib.EPubBook
import org.greenrobot.eventbus.EventBus

/***
 *@date 创建时间 2020/11/17 5:01 PM
 *<AUTHOR> John
 *@description 书签
 */
class BookMarkFragment(activity: Activity, val resourceId: String) : OnLoadMoreListener, OnRefreshListener {

    val dataRecycleView: CustomRecyclerView
    var noDataLinearLayout: LinearLayout
    var refreshLayout: SmartRefreshLayout
    var bookMarkAdapter: BookMarkAdapter = BookMarkAdapter()
    val rootView: View = activity.layoutInflater.inflate(R.layout.fragment_book_mark_layout, null)
    var swipeStatusList: ArrayList<Boolean> = ArrayList()
    var markList = ArrayList<BookmarkEntity>()

    init {
        dataRecycleView = rootView.findViewById(R.id.book_mark_data_RecyclerView)
        noDataLinearLayout = rootView.findViewById(R.id.book_mark_no_data_LinearLayout)
        refreshLayout = rootView.findViewById(R.id.book_mark_SmartRefreshLayout)
        refreshLayout.setOnRefreshListener(this).setOnLoadMoreListener(this)
        dataRecycleView.adapter = bookMarkAdapter
        reloadBookmarkData()
        bookMarkAdapter.onViewClickListener = object : OnViewClickListener {
            override fun <T> onClickAction(v: View, str: String, t: T?) {
                if (str.equals("Delete")) {
                    setCurrentUIShowStatus(bookMarkAdapter.dataList)
                }
            }
        }
    }

    private fun reloadBookmarkData() {
        markList.clear()
        swipeStatusList.clear()
        var offset = 0L
        while (true) {
            val newList = SDKSingleton.dbWrapBl.getBookmarkEntityList(resourceId, offset, 20)
            if (newList.isNullOrEmpty()) {
                break
            }
            offset += newList.size
            for (n in newList) {
                if (n.pagePath.isNotEmpty()) {
                    markList.add(n)
                    swipeStatusList.add(false)
                }
            }
        }
        bookMarkAdapter.dataList = markList
        setCurrentUIShowStatus(markList)
    }

    private fun setCurrentUIShowStatus(list: List<BookmarkEntity>?) {
        if (!list.isNullOrEmpty()) {
            noDataLinearLayout.visibility = View.GONE
            dataRecycleView.visibility = View.VISIBLE
        } else {
            noDataLinearLayout.visibility = View.VISIBLE
            dataRecycleView.visibility = View.GONE
        }
    }

    override fun onLoadMore(layout: RefreshLayout) {
        refreshLayout.isEnableLoadMore = false
        refreshLayout.finishLoadMoreAndRefresh()
    }

    override fun onRefresh(layout: RefreshLayout) {
        reloadBookmarkData()
        refreshLayout.finishLoadMoreAndRefresh()
    }

    fun setOnJumpListener(l: OnJumpActionListener) {
        bookMarkAdapter.onJumpListener = l
    }

    /***
     *@date 创建时间 12/25/20 10:36 AM
     *<AUTHOR> Qian kai
     *@description
     */
    inner class BookMarkAdapter : BaseRecycleAdapter<BookmarkEntity, BookMarkViewHolder>() {
        var onViewClickListener: OnViewClickListener? = null
        var onJumpListener: OnJumpActionListener? = null

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BookMarkViewHolder {
            return BookMarkViewHolder(parent)
        }

        override fun onBindViewHolder(holder: BookMarkViewHolder, position: Int) {
            val data = dataList!![position]
            holder.initUIData(data)
            val swipeLayout = holder.itemView as SwipeRevealLayout
            swipeLayout.setSwipeListener(object : SwipeRevealLayout.SwipeListener {
                override fun onClosed(view: SwipeRevealLayout?) {
                    swipeStatusList[position] = false
                }

                override fun onOpened(view: SwipeRevealLayout?) {
                    swipeStatusList[position] = true
                    var statusChanged = false
                    for (i in 0 until swipeStatusList.size) {
                        if (i != position && swipeStatusList[i]) {
                            swipeStatusList[i] = false
                            statusChanged = true
                        }
                    }
                    if (statusChanged) {
                        notifyDataSetChanged()
                    }
                }

                override fun onSlide(view: SwipeRevealLayout?, slideOffset: Float) {}
            })

            if (swipeStatusList[position]) {
                swipeLayout.open(false)
            } else {
                swipeLayout.close(true)
            }

            holder.deleteButton.setOnClickListener { v ->
                val swipeRevealLayout = holder.itemView as? SwipeRevealLayout
                swipeRevealLayout?.close(false)
                data.dataId?.let { SDKSingleton.dbWrapBl.deleteBookmark(it) }
                swipeStatusList.removeAt(position)
                markList.removeAt(position)
                dataList = markList
                setCurrentUIShowStatus(markList)
                onViewClickListener?.onClickAction(v, "Delete", data)
                EventBus.getDefault().post(BookDataChangeEvent())
            }

            holder.contentLayout.setOnClickListener { v ->
                onJumpListener?.doJump(EPubBook.getPathIndex(data.pagePath), data.firstWordOffset)
            }
        }
    }

    /***
     *@date 创建时间 12/25/20 10:36 AM
     *<AUTHOR> Qian kai
     *@description
     */
    class BookMarkViewHolder(parent: ViewGroup) :
        BaseViewHolder(parent, R.layout.holder_item_book_mark_layout) {
        val nameTextView: TextView = itemView.findViewById(R.id.book_mark_name_TextView)
        val dateTextView: TextView = itemView.findViewById(R.id.book_mark_date_TextView)
        val summaryTextView: TextView = itemView.findViewById(R.id.book_mark_summary_TextView)
        val deleteButton: Button = itemView.findViewById(R.id.book_mark_delete_Button)
        val contentLayout: ConstraintLayout = itemView.findViewById(R.id.book_mark_ConstraintLayout)
        var recyclerHeight: Int = 0
        lateinit var dataEntity: BookmarkEntity

        override fun <T> initUIData(t: T) {
            dataEntity = t as BookmarkEntity
            nameTextView.text = t.tocTitle
            summaryTextView.text = t.summary
            dateTextView.text = UnitFormatUtil.formatDate_ymdhm(t.createTime)
        }
    }
}
