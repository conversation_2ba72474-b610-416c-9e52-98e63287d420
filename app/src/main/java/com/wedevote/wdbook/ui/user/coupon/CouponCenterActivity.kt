package com.wedevote.wdbook.ui.user

import android.graphics.Color
import android.os.Bundle
import android.view.View
import android.view.View.OnClickListener
import android.widget.TextView
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.FragmentPagerAdapter
import androidx.viewpager.widget.ViewPager
import androidx.viewpager.widget.ViewPager.OnPageChangeListener
import com.aquila.lib.widget.view.TabLinearLayout
import com.wedevote.wdbook.R
import com.wedevote.wdbook.base.APPConfig
import com.wedevote.wdbook.base.RootActivity
import com.wedevote.wdbook.ui.user.coupon.CouponPageFragment
import com.wedevote.wdbook.ui.widgets.CommTopTitleLayout

/***
 * @date 创建时间 2022/5/30 20:07
 * <AUTHOR> <PERSON><PERSON>
 * @description
 */

class CouponCenterActivity : RootActivity(), OnClickListener {
    lateinit var topTitleLayout: CommTopTitleLayout
    lateinit var tabLinearLayout: TabLinearLayout
    lateinit var viewPager: ViewPager

    lateinit var pageAdapter: CouponPageAdapter

    lateinit var activeTabTextView: TextView
    lateinit var usedTabTextView: TextView
    lateinit var expiredTabTextView: TextView

    var tabTextColorNormal: Int = Color.BLACK
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_coupon_center_layout)
        initViewFromXML()
        if (!APPConfig.isCurrentThemeLight()) {
            tabTextColorNormal = Color.parseColor("#A0FFFFFF")
        }
        pageAdapter = CouponPageAdapter(supportFragmentManager)
        viewPager.adapter = pageAdapter
        tabLinearLayout.bindViewPager(viewPager)
        initCurrentTabUI(0)
        setViewListener()
    }

    private fun initViewFromXML() {
        topTitleLayout = findViewById(R.id.coupon_center_top_title_Layout)
        tabLinearLayout = findViewById(R.id.coupon_center_TabLinearLayout)
        activeTabTextView = findViewById(R.id.coupon_center_tab_active_TextView)
        usedTabTextView = findViewById(R.id.coupon_center_tab_used_TextView)
        expiredTabTextView = findViewById(R.id.coupon_center_tab_expired_TextView)

        viewPager = findViewById(R.id.coupon_center_content_ViewPager)
    }

    private fun setViewListener() {
        viewPager.addOnPageChangeListener(object : OnPageChangeListener {
            override fun onPageScrolled(position: Int, positionOffset: Float, positionOffsetPixels: Int) {
            }

            override fun onPageSelected(position: Int) {
                initCurrentTabUI(position)
            }

            override fun onPageScrollStateChanged(state: Int) {
            }
        })
    }

    internal fun initCurrentTabUI(position: Int) {
        when (position) {
            0 -> {
                activeTabTextView.setTextColor(Color.parseColor("#FF8A00"))
                usedTabTextView.setTextColor(tabTextColorNormal)
                expiredTabTextView.setTextColor(tabTextColorNormal)
            }
            1 -> {
                usedTabTextView.setTextColor(Color.parseColor("#FF8A00"))
                activeTabTextView.setTextColor(tabTextColorNormal)
                expiredTabTextView.setTextColor(tabTextColorNormal)

            }
            2 -> {
                expiredTabTextView.setTextColor(Color.parseColor("#FF8A00"))
                usedTabTextView.setTextColor(tabTextColorNormal)
                activeTabTextView.setTextColor(tabTextColorNormal)
            }
        }
    }


    override fun onClick(v: View?) {
        when (v) {


        }
    }

}


/***
 *@date 创建时间 2022/5/31 14:02
 *<AUTHOR> W.YuLong
 *@description
 */
class CouponPageAdapter(manager: FragmentManager) : FragmentPagerAdapter(manager) {

    override fun getCount(): Int {
        return 3
    }

    override fun getItem(position: Int): Fragment {
        return CouponPageFragment(position)
    }

}