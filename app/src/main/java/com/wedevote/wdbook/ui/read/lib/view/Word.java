package com.wedevote.wdbook.ui.read.lib.view;
import android.graphics.Canvas;
import android.text.Spanned;
import android.text.TextPaint;

import com.wedevote.wdbook.ui.read.lib.TextHelper;
import com.wedevote.wdbook.ui.read.lib.span.SpecialElementSpan;
import com.wedevote.wdbook.ui.read.lib.span.WDLinkSpan;

public class Word {
    private float x;
    private final float y;
    private final int location;
    private final Spanned word;
    private final float wordWidth;
    private TextPaint paint, workPaint;
    private WDLinkSpan linkSpan;
    private SpecialElementSpan specialElementSpan;

    private final boolean blankSpace;
    private final boolean symbol;
    private final boolean inTable;

    public Word(Word w) {
        this(w.x,w.y,w.location,w.word,w.wordWidth,w.paint,w.symbol,w.blankSpace,w.inTable);
    }

    public Word (float x, float y, int location, Spanned word, float wordWidth, TextPaint paint, boolean symbol,boolean blankSpace, boolean inTable) {
        this.x = x;
        this.y = y;
        this.location = location;
        this.word = word;
        this.blankSpace = blankSpace;
        this.wordWidth = wordWidth;
        this.paint = paint;
        this.symbol = symbol;
        this.inTable = inTable;
        checkLink();
        checkSpecialElement();
    }

    public float setX(float xx) {
        return x = xx;
    }
    public float getX() {
        return x;
    }

    private void checkLink(){
        WDLinkSpan[] links = word.getSpans(0,word.length(),WDLinkSpan.class);
        if(links.length != 0){
            linkSpan = links[0];
        }
    }

    private void checkSpecialElement(){
        SpecialElementSpan[] spans = word.getSpans(0, word.length(), SpecialElementSpan.class);
        if(spans.length != 0) {
            specialElementSpan = spans[0];
        }
    }

    public int getLocation() {
        return this.location;
    }

    public WDLinkSpan findLink(){
        return linkSpan;
    }

    public boolean isSpecialElement(){
        return specialElementSpan != null;
    }

    public float getWordWidth() {
        return wordWidth;
    }

    public void addRightPosition(float num) {
        this.x += num;
    }

    public void draw(Canvas canvas,float topMargin,int height) {
        if(word.length() > 0){
            if(workPaint == null){
                workPaint = new TextPaint(paint);
            }
            if(specialElementSpan != null) {
                specialElementSpan.drawIt(canvas, paint, x, y + topMargin ,height, workPaint.ascent());
            } else {
                TextHelper.drawWord(canvas,word,x,(int)(y+topMargin),paint,workPaint);
            }
        }
    }

    public boolean isBlankSpace() {
        return blankSpace && specialElementSpan == null;
    }

    public boolean isInTable() {
        return inTable;
    }

    public boolean isSymbol() {
        return symbol;
    }

    public boolean isChineseCharacter(){
        return word.length() == 1 && word.charAt(0) >= 0x4E00;
    }

    public boolean isHebrewLetter(){
        return word.length() >= 1 && word.charAt(0) >= 0x0590 && word.charAt(0) <= 0x05ff;
    }

    public Spanned getWord() {
        return word;
    }

    @Override
    public String toString() {
        String strBuf = "{word=\"" + word.toString() + "\"" +
                ", x=" + x +
                ", y=" + y +
                "}";
        return strBuf;
    }

    public void setPaint(TextPaint paint) {
        this.paint = paint;
        this.workPaint = null;
    }
}
