package com.wedevote.wdbook.ui.dialogs

import android.app.Dialog
import android.content.Context
import android.os.Bundle
import android.view.Gravity
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.widget.Button
import android.widget.ImageView
import android.widget.TextView
import com.aquila.lib.base.BaseRecycleAdapter
import com.aquila.lib.base.BaseViewHolder
import com.aquila.lib.base.IGetStringInterface
import com.aquila.lib.widget.view.CustomRecyclerView
import com.wedevote.wdbook.R
import com.wedevote.wdbook.tools.interfaces.IGetStringInterfaceImpl
import com.wedevote.wdbook.tools.interfaces.OnItemClickListener

/***
 *@date 创建时间 2023/9/5
 *<AUTHOR> <PERSON>.<PERSON>
 *@description  作者、译者列表底部弹窗
 */
class AuthorChoiceDialog(val builder: Builder) : Dialog(builder.context), View.OnClickListener {
    lateinit var dataRecyclerView: CustomRecyclerView
    lateinit var cancelButton: Button
    lateinit var titleTextView: TextView
    lateinit var itemChoiceAdapter: ItemChoiceAdapter

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.dialog_author_choice_bottom_layout)
        dataRecyclerView = findViewById(R.id.bottom_single_choice_data_RecyclerView)
        titleTextView = findViewById(R.id.bottom_author_choice_title_TextView)
        cancelButton = findViewById(R.id.bottom_single_choice_cancel_Button)
        itemChoiceAdapter = ItemChoiceAdapter(builder)
        titleTextView.text = builder.title
        setCancelable(builder.cancelable)
        dataRecyclerView.adapter = itemChoiceAdapter
        configDialog()

        cancelButton.setOnClickListener(this)
    }

    private fun configDialog() {
        val wl = window!!.attributes
        wl.gravity = Gravity.CENTER // 设置重力
        wl.width = WindowManager.LayoutParams.MATCH_PARENT
        wl.height = WindowManager.LayoutParams.WRAP_CONTENT
        window!!.setWindowAnimations(com.aquila.lib.dialog.R.style.centerDialogWindowAnim)
        window!!.attributes = wl
        window!!.setBackgroundDrawableResource(android.R.color.transparent)
    }

    override fun onClick(v: View?) {
        if (v == cancelButton) {
            dismiss()
        }
    }

    class ItemChoiceAdapter(val builder: Builder) :
        BaseRecycleAdapter<IGetStringInterface, ItemChoiceViewHolder>() {

        init {
            dataList = builder.itemList
        }

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ItemChoiceViewHolder {
            return ItemChoiceViewHolder(parent)
        }

        override fun onBindViewHolder(holder: ItemChoiceViewHolder, position: Int) {
            val data = dataList!![position]
            holder.initUIData(data, position)
            holder.itemView.setOnClickListener { v ->
                builder.onItemClickListener?.onItemClick(v, position.toString(), data)
                notifyDataSetChanged()
            }
        }
    }

    class ItemChoiceViewHolder(parent: ViewGroup) :
        BaseViewHolder(parent, R.layout.item_author_choice_layout) {
        val descTextView: TextView =
            itemView.findViewById(R.id.item_bottom_single_choice_desc_TextView)
        val checkImageView: ImageView =
            itemView.findViewById(R.id.item_bottom_single_choice_choice_ImageView)

        override fun <T> initUIData(t: T, position: Int) {
            t as IGetStringInterface
            descTextView.text = t.getString()
        }

        fun setChecked(isChekced: Boolean) {
            checkImageView.visibility = if (isChekced) View.VISIBLE else View.GONE
        }
    }

    class Builder(val context: Context) {
        var itemList: ArrayList<IGetStringInterface> = ArrayList<IGetStringInterface>()
            private set
        var currentPosition: Int = -1
            private set
        var onItemClickListener: OnItemClickListener? = null
            private set

        var cancelable: Boolean = true
            private set

        var title: String = ""
            private set

        companion object {
            fun with(context: Context): Builder {
                return Builder(context)
            }
        }

        fun setCancelable(b: Boolean): Builder {
            cancelable = b
            return this
        }

        fun setTitle(title: String): Builder {
            this.title = title
            return this
        }

        fun setItemStringList(list: ArrayList<String>?): Builder {
            itemList.clear()
            if (list != null) {
                for (str in list) {
                    itemList.add(object : IGetStringInterfaceImpl<String>(str) {
                        override fun getString(): String {
                            return str
                        }
                    })
                }
            }
            return this
        }

        fun setItemList(list: ArrayList<IGetStringInterface>?): Builder {
            itemList.clear()
            if (list != null) {
                this.itemList.addAll(list)
            }
            return this
        }

        fun setCurrentPosition(position: Int): Builder {
            currentPosition = position
            return this
        }

        fun setonItemClickListener(onItemClickListener: OnItemClickListener?): Builder {
            this.onItemClickListener = onItemClickListener
            return this
        }

        fun create(): AuthorChoiceDialog {
            return AuthorChoiceDialog(this)
        }
    }

    companion object {
        @JvmStatic
        fun with(context: Context): AuthorChoiceDialog.Builder {
            return AuthorChoiceDialog.Builder(context)
        }
    }
}
