package com.wedevote.wdbook.ui.store

/***
 * @date 创建时间 2020/11/12 10:56
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON>
 * @description
 */
object OrderStatusDefine {
    // 订单处理中
    const val ORDER_STATUS_PENDING = 0
    // 订单支付完成
    const val ORDER_STATUS_SUCCEED = 1
    // 订单支付失败
    const val ORDER_STATUS_FAILURE = 2
    // 订单支付取消
    const val ORDER_STATUS_CANCEL = 3
    // 订单支付异常
    const val ORDER_STATUS_EXCEPTION = 4
    // 订单退款完成
    const val ORDER_STATUS_REFUND = 5
    // 订单完成
    const val ORDER_STATUS_FINISH = 6
}
