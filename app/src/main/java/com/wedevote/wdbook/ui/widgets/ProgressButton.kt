package com.wedevote.wdbook.ui.widgets

import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.PorterDuff
import android.graphics.PorterDuffXfermode
import android.util.AttributeSet
import com.wedevote.wdbook.R
import com.wedevote.wdbook.tools.util.UnitFormatUtil

/***
 * @date 创建时间 2020/8/27 11:23
 * <AUTHOR> W.<PERSON>
 * @description
 */
class ProgressButton(context: Context, attr: AttributeSet? = null) : androidx.appcompat.widget.AppCompatButton(context, attr) {

    private var viewWidth: Int = 0
    private var viewHeight: Int = 0
    private var myPaint: Paint = Paint()
    private var bgPaint = Paint()
    var progress = 0f
        set(value) {
            field = value
            invalidate()
        }
    var currentType: Int = 0
    private var bgColor: Int = Color.TRANSPARENT
    private var progressColor: Int = Color.TRANSPARENT
    var needShowProgress: Boolean = false

    init {
        attr?.let {
            val a = context.obtainStyledAttributes(it, R.styleable.ProgressButton)
            bgColor = a.getColor(R.styleable.ProgressButton_attr_bg_color, Color.TRANSPARENT)
            progressColor = a.getColor(R.styleable.ProgressButton_attr_progress_color, Color.TRANSPARENT)
            progress = a.getFloat(R.styleable.ProgressButton_attr_button_progress, 0f)
            needShowProgress = a.getBoolean(R.styleable.ProgressButton_attr_need_show_Progress, false)
            a.recycle()
        }
        bgPaint.color = bgColor
        setWillNotDraw(false)
    }

    fun setCurrentShowType(type: Int) {
        currentType = type
    }

    fun setButtonProgress(p: Float) {
        this.progress =
            if (p > 100) 100f else p
        text = UnitFormatUtil.trimDotEndZero(String.format("%.1f", progress)).plus("%")
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        viewWidth = w
        viewHeight = h
    }

    override fun onDraw(canvas: Canvas) {
        if (needShowProgress) {
            myPaint.color = Color.TRANSPARENT
            canvas.drawRoundRect(
                0f, 0f, viewWidth.toFloat(), viewHeight.toFloat(),
                (viewHeight / 2).toFloat(), (viewHeight / 2).toFloat(), myPaint
            )

            myPaint.xfermode = PorterDuffXfermode(PorterDuff.Mode.DST_ATOP)
            myPaint.color = progressColor
            canvas.drawRect(0f, 0f, progress / 100f * viewWidth, viewHeight.toFloat(), myPaint)

            bgPaint.xfermode = PorterDuffXfermode(PorterDuff.Mode.DST_OVER)
            canvas.drawRoundRect(
                0f, 0f, viewWidth.toFloat(), viewHeight.toFloat(),
                (viewHeight / 2).toFloat(), (viewHeight / 2).toFloat(), bgPaint
            )
        }

        super.onDraw(canvas)
    }

    companion object {
//        val BTN_STATUS_WAIT = -1
//        val BTN_STATUS_DOWNLOADING = 1
//        val BTN_STATUS_PAUSE = 2

        val BTN_STATUS_BUY = 0
        val BTN_STATUS_NEED_DOWNLOAD = 1
        val BTN_STATUS_READ = 2
//        val BTN_STATUS_PAUSE = 4
    }
}
