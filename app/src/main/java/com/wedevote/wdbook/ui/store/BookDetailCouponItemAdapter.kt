package com.wedevote.wdbook.ui.store

import android.view.ViewGroup
import android.widget.TextView
import com.aquila.lib.base.BaseRecycleAdapter
import com.aquila.lib.base.BaseViewHolder
import com.wedevote.wdbook.R
import com.wedevote.wdbook.entity.coupon.CouponEntity
import com.wedevote.wdbook.entity.coupon.CouponStatus
import com.wedevote.wdbook.ui.dialogs.GetCouponDialog

/***
 * @date 创建时间 2022/6/8 10:44
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON>
 * @description
 */
class BookDetailCouponItemAdapter : BaseRecycleAdapter<CouponEntity, DetailCouponItemViewHolder>() {
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): DetailCouponItemViewHolder {
        return DetailCouponItemViewHolder(parent)
    }
    
    fun filterDataList(couponsList: List<CouponEntity>?): ArrayList<CouponEntity>? {
        var filterCouponList = ArrayList<CouponEntity>()
        if (!couponsList.isNullOrEmpty()) {
            for (item in couponsList) {
                if (item.status != CouponStatus.SOLD_OUT.status) {
                    filterCouponList.add(item)
                }
            }
        }
        return filterCouponList
    }
    
    override fun onBindViewHolder(holder: DetailCouponItemViewHolder, position: Int) {
        super.onBindViewHolder(holder, position)
        holder.itemView.setOnClickListener {
            val dialog = GetCouponDialog(holder.itemView.context)
            dialog.show()
            dialog.initDataList(dataList)
        }
    }
}

/***
 *@date 创建时间 2022/6/8 10:52
 *<AUTHOR> W.YuLong
 *@description
 */
class DetailCouponItemViewHolder(parent: ViewGroup) : BaseViewHolder(parent, R.layout.item_book_detail_coupon_layout) {
    val couponTextView: TextView = itemView.findViewById(R.id.item_detail_coupon_TextView)
    
    override fun <T> initUIData(t: T) {
        super.initUIData(t)
        t as CouponEntity
        couponTextView.text = t.couponName
    }
    
}