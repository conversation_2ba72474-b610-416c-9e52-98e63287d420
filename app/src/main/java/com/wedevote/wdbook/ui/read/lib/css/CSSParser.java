package com.wedevote.wdbook.ui.read.lib.css;

import android.text.SpannableStringBuilder;

import com.wedevote.wdbook.base.APP;
import com.wedevote.wdbook.base.SDKSingleton;
import com.wedevote.wdbook.ui.read.lib.BookParameters;

import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;

public class CSSParser {
    static CSSParser instance;

    private final HashMap<String, CssElement> cssMap = new HashMap<>();

    private CSSParser(String cssText) {
        loadCSSText(cssText);
    }

    private CSSParser() {
        String result = "";
        String cssFile;
        if(SDKSingleton.INSTANCE.getAppBl().isCurrentThemeLight()){
            cssFile = "style.css";
        }else{
            cssFile = "style_night.css";
        }

        try {
            InputStream in = APP.get().getResources().getAssets().open(cssFile);
            int length = in.available();
            byte[] buffer = new byte[length];
            in.read(buffer);
            result = new String(buffer, StandardCharsets.UTF_8);
        } catch (Exception e) {
            e.printStackTrace();
        }
        loadCSSText(result);
    }

    public static synchronized CSSParser getInstance() {
        if (instance == null) {
            instance = new CSSParser();
        }
        return instance;
    }

    public static synchronized void releaseInstance() {
        instance = null;
    }

    public void loadCSSText(String cssText) {
        cssMap.clear();
        StringBuilder sb = new StringBuilder();
        int length = cssText.length();

        int start = 0;
        int commentStart;
        do {
            commentStart = cssText.indexOf("/*", start);
            if (commentStart >= 0) {
                int commentEnd = cssText.indexOf("*/", commentStart);
                if (commentEnd >= 0) {
                    sb.append(cssText.substring(start, commentStart));
                    start = commentEnd + 2;
                } else {
                    return;//bad format
                }
            }
        } while (commentStart >= 0);
        if (start < length) {
            sb.append(cssText.substring(start));
        }

        cssText = sb.toString().replaceAll("!important", "");
        start = 0;
        int contentStart;
        do {
            contentStart = cssText.indexOf("{", start);
            if (contentStart >= 0) {
                int end = cssText.indexOf("}", contentStart);
                if (end >= 0) {
                    addElement(cssText, start, contentStart, end);
                    start = end + 1;
                } else {
                    return;//bad format
                }
            }
        } while (contentStart >= 0);

    }

    private void addElement(String text, int nameStart, int contentStart, int contentEnd) {
        String[] names = text.substring(nameStart, contentStart).split(",");
        String content = text.substring(contentStart + 1, contentEnd);
        CssElement element = new CssElement(content);
        for (String s : names) {
            String name = s.trim();
            CssElement oldElement = cssMap.get(name);
            if (oldElement == null) {
                cssMap.put(name, element);
            } else {
                element.merge(oldElement);
                cssMap.put(name, element);
            }
        }
    }

    public int getTextColor(String tag) {
        CssElement element = cssMap.get(tag);
        if (element != null) {
            return element.getColor();
        } else {
            return BookParameters.getTextNormalColor();
        }
    }

    public Object getDecoration(String tag) {
        CssElement element = cssMap.get(tag);
        if (element != null) {
            return element.getDecoration();
        } else {
            return null;
        }
    }

    static private final String[] listLevelTags = {"ol", "ol ol", "ol ol ol", "ol ol ol ol",
            "ol ol ol ol ol",
            "ol ol ol ol ol ol",
            "ol ol ol ol ol ol ol"};

    public int getListStyleType(int lv) {
        lv = lv % 7;
        CssElement element = null;
        do {
            element = cssMap.get(listLevelTags[lv]);
            if (element != null) {
                return element.getListStyle();
            }
            lv--;
        } while (lv >= 0);
        return 0;
    }

    public String getListStyleString(int type, int index, int level) {
        switch (type) {
            case CssElement.CSSListStyle.NONE:
                if(level <= 1){
                    return getOrderString(index, '▪', 0);
                }else{
                    return getOrderString(index, '▫', 0);
                }
            case CssElement.CSSListStyle.DECIMAL:
                return index+".";
            case CssElement.CSSListStyle.DECIMAL_LEADING_ZERO:
                return String.format("%02d.", index);
            case CssElement.CSSListStyle.LOWER_ROMAN:
                return intToRoman(index, false);
            case CssElement.CSSListStyle.UPPER_ROMAN:
                return intToRoman(index, true);
            case CssElement.CSSListStyle.LOWER_GREEK:
                return getOrderString(index, 'α', 10);
            case CssElement.CSSListStyle.LOWER_LATIN:
            case CssElement.CSSListStyle.LOWER_ALPHA:
                return getOrderString(index, 'a', 26);
            case CssElement.CSSListStyle.UPPER_LATIN:
            case CssElement.CSSListStyle.UPPER_ALPHA:
                return getOrderString(index, 'A', 26);
        }
        return "●";
    }

    static private final String[] LARGE_ROMAN_SYMBOLS = {"M", "CM", "D", "CD", "C", "XC", "L", "XL", "X", "IX", "V", "IV", "I"};
    static private final int[] ROMAN_INT = {1000, 900, 500, 400, 100, 90, 50, 40, 10, 9, 5, 4, 1};

    private static String intToRoman(int index, boolean upper) {
        String[] arrayOfString;
        StringBuilder stringBuilder = new StringBuilder();
        arrayOfString = LARGE_ROMAN_SYMBOLS;

        for (int i = 0; index != 0; i++) {
            while (index >= ROMAN_INT[i]) {
                index -= ROMAN_INT[i];
                stringBuilder.append(arrayOfString[i]);
            }
        }

        stringBuilder.append('.');
        if (upper) {
            return stringBuilder.toString();
        } else {
            return stringBuilder.toString().toLowerCase();
        }
    }

    private String getOrderString(int index, char c, int max) {
        if (max == 0) {
            return String.valueOf(c);
        }
        StringBuilder stringBuilder = new StringBuilder();
        while (index > 0) {
            int j = index % max;
            int i = j;
            if (j == 0) {
                i = max;
            }
            stringBuilder.append((char) (c - 1 + i));
            index = (index - i) / max;
        }
        return stringBuilder.reverse().append(".").toString();
    }

    public void setTextSpans(SpannableStringBuilder ssb, String tag, String cls, int start, int end, int flags) {
        if(tag.equals("body") || tag.equals("html")){
            return;
        }
        CssElement element = null;
        if (cls != null && !cls.isEmpty()) {
            //Log.e("wd","get--->"+tag+">"+cls);
            element = cssMap.get(tag + "." + cls);
        }
        if (element == null) {
            element = cssMap.get(tag);
        }
        if (element != null) {
            ArrayList<Object> spans = element.getTextSpans();
            if (spans != null) {
                for (Object o : spans) {
                    ssb.setSpan(o, start, end, flags);
                }
            }
        }
    }
}
