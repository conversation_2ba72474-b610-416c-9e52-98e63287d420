package com.wedevote.wdbook.ui.read

import android.content.Context
import android.graphics.PointF
import android.util.AttributeSet
import android.view.MotionEvent
import com.aquila.lib.widget.view.CustomViewPager
import com.wedevote.wdbook.base.SDKSingleton
import com.wedevote.wdbook.tools.util.getFunctionInfo

/**
 * Created by theophilus on 9/30/17.
 */
/***
 *@date 重构时间 2020/10/28 10:09
 *<AUTHOR> <PERSON><PERSON>
 *@description
 */
class BookReadViewPager(context: Context, attrs: AttributeSet? = null) : CustomViewPager(context, attrs) {
    var point = PointF()
    var nonLeftSlide = false
    var nonRightSlide = false

    override fun onInterceptTouchEvent(event: MotionEvent): Boolean {
        try {
            val action = event.action
            if (!nonLeftSlide && !nonRightSlide) {
                return super.onInterceptTouchEvent(event)
            }
            if (action == MotionEvent.ACTION_DOWN) {
                point.x = event.x
                point.y = event.y
            } else if (action == MotionEvent.ACTION_MOVE) {
                if (event.x >= point.x && nonLeftSlide) {
                    return false
                }
                if (event.x <= point.x && nonRightSlide) {
                    return false
                }
            }
            setSlide(true)
            return super.onInterceptTouchEvent(event)
        } catch (ex: IllegalArgumentException) {
            ex.printStackTrace()
            SDKSingleton.loggerBl.handleThrowable(ex, getFunctionInfo())
        }
        return false
    }

    override fun onTouchEvent(ev: MotionEvent): Boolean {
        try {
            return super.onTouchEvent(ev)
        } catch (ex: IllegalArgumentException) {
            ex.printStackTrace()
            SDKSingleton.loggerBl.handleThrowable(ex, getFunctionInfo())
        }
        return false
    }

    fun lockLeftSlide() {
        nonLeftSlide = true
    }

    fun lockRightSlide() {
        nonRightSlide = true
    }

    var oldLeftLock = false
    var oldRightLock = false
    var saveSuccess = false

    fun saveAndLockAll() {
        oldLeftLock = nonLeftSlide
        oldRightLock = nonRightSlide
        nonLeftSlide = true
        nonRightSlide = true
        saveSuccess = true
    }

    fun resumeFromLockAll() {
        if (saveSuccess) {
            nonLeftSlide = oldLeftLock
            nonRightSlide = oldRightLock
            saveSuccess = false
        }
    }

    fun enableSlide() {
        nonLeftSlide = false
        nonRightSlide = false
    }
}
