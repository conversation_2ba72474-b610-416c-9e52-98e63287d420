package com.wedevote.wdbook.ui.read.widgets

import android.app.Activity
import android.content.Context
import android.util.AttributeSet
import android.view.View
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.cardview.widget.CardView
import androidx.constraintlayout.widget.ConstraintLayout
import com.wedevote.wdbook.R
import com.wedevote.wdbook.tools.util.APPUtil
import com.wedevote.wdbook.tools.util.WindowInsetsUtils
import com.wedevote.wdbook.tools.util.dp2px

class BookSearchResultLayout @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null
) : ConstraintLayout(context, attrs), View.OnClickListener {

    private val topLayout: LinearLayout
    private val bottomLayout: LinearLayout
    private val closeImageView: ImageView
    private val prevLayout: View
    private val resultLayout: View
    private val nextLayout: View
    private val searchTopLayout: View
    private val topTextView: TextView
    private val chapterTextView: TextView
    private val searchTopCardView: CardView

    var onSearchResultListener: OnSearchResultListener? = null

    init {
        View.inflate(context, R.layout.layout_book_search_layout, this)
        
        topLayout = findViewById(R.id.book_search_top_layout)
        searchTopLayout = findViewById(R.id.book_search_text_top_layout)
        bottomLayout = findViewById(R.id.book_search_bottom_layout)
        closeImageView = findViewById(R.id.book_search_close_ImageView)
        prevLayout = findViewById(R.id.book_search_prev_layout)
        resultLayout = findViewById(R.id.book_search_text_result_layout)
        nextLayout = findViewById(R.id.book_search_next_layout)
        topTextView = findViewById(R.id.book_search_text_top_TextView)
        chapterTextView = findViewById(R.id.book_search_text_chapter_TextView)
        searchTopCardView = findViewById(R.id.book_search_top_CardView)
        topLayout.visibility = View.GONE
        bottomLayout.visibility = View.GONE
        if (APPUtil.isAboveAndroid15()) {
            WindowInsetsUtils.applyTopMarginAfterInsets(searchTopCardView, dp2px(2))
        }

        setViewListeners()
    }

    private fun setViewListeners() {
        closeImageView.setOnClickListener(this)
        prevLayout.setOnClickListener(this)
        resultLayout.setOnClickListener(this)
        nextLayout.setOnClickListener(this)
        searchTopLayout.setOnClickListener(this)
    }

    fun isVisible(): Boolean {
        return topLayout.visibility == View.VISIBLE
    }

    fun showMenu() {
        topLayout.visibility = View.VISIBLE
        bottomLayout.visibility = View.VISIBLE
    }

    fun hideMenu() {
        topLayout.visibility = View.GONE
        bottomLayout.visibility = View.GONE
    }

    /**
     * 设置上一条按钮的可用状态
     */
    fun setPreviousButtonEnabled(enabled: Boolean) {
        prevLayout.isEnabled = enabled
        prevLayout.alpha = if (enabled) 1.0f else 0.5f
    }
    
    /**
     * 设置下一条按钮的可用状态
     */
    fun setNextButtonEnabled(enabled: Boolean) {
        nextLayout.isEnabled = enabled
        nextLayout.alpha = if (enabled) 1.0f else 0.5f
    }

    /**
     * 设置搜索关键词文本
     * @param keywords 搜索关键词
     */
    fun setSearchKeywords(keywords: String) {
        val searchResultText = context.getString(R.string.this_book_contains_keywords, keywords)
        topTextView.text = searchResultText
    }

    /**
     * 设置章节名称文本
     * @param chapterName 章节名称
     */
    fun setChapterName(chapterName: String) {
        chapterTextView.text = chapterName
    }

    override fun onClick(v: View) {
        when (v.id) {
            R.id.book_search_close_ImageView -> {
                hideMenu()
                onSearchResultListener?.onCloseClick()
            }
            R.id.book_search_prev_layout -> onSearchResultListener?.onPreviousClick()
            R.id.book_search_text_top_layout -> onSearchResultListener?.onResultClick()
            R.id.book_search_text_result_layout -> onSearchResultListener?.onResultClick()
            R.id.book_search_next_layout -> onSearchResultListener?.onNextClick()
        }
    }

    interface OnSearchResultListener {
        fun onPreviousClick()
        fun onResultClick()
        fun onNextClick()
        fun onCloseClick()
    }
}