package com.wedevote.wdbook.ui.shelf

import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.aquila.lib.base.OnViewClickListener
import com.aquila.lib.layout.SmartRefreshLayout
import com.aquila.lib.layout.api.RefreshLayout
import com.aquila.lib.layout.listener.OnLoadMoreListener
import com.aquila.lib.layout.listener.OnRefreshListener
import com.aquila.lib.log.KLog
import com.aquila.lib.tools.util.ScreenUtil
import com.aquila.lib.widget.group.GroupImageTextLayout
import com.aquila.lib.widget.view.CustomRecyclerView
import com.wedevote.wdbook.R
import com.wedevote.wdbook.base.BaseRootFragment
import com.wedevote.wdbook.base.ExceptionHandler
import com.wedevote.wdbook.base.SDKSingleton
import com.wedevote.wdbook.entity.shelf.HomeShelfItemCombineEntity
import com.wedevote.wdbook.entity.shelf.ShelfArchiveItemEntity
import com.wedevote.wdbook.entity.shelf.ShelfBookItemEntity
import com.wedevote.wdbook.exception.SDKException
import com.wedevote.wdbook.tools.event.EditShelfEvent
import com.wedevote.wdbook.tools.event.HomeShelfDataReloadEvent
import com.wedevote.wdbook.tools.event.LogoutEvent
import com.wedevote.wdbook.tools.event.OnBookReadEvent
import com.wedevote.wdbook.tools.event.OnLoginEvent
import com.wedevote.wdbook.ui.account.OnLoginResultCallBack
import com.wedevote.wdbook.ui.account.SSOLoginActivity
import com.wedevote.wdbook.ui.dialogs.BookShelfGroupDialog
import com.wedevote.wdbook.ui.home.HomeTab
import com.wedevote.wdbook.ui.home.OnHomeTabListener
import com.wedevote.wdbook.ui.home.OnShelfItemEditListener
import com.wedevote.wdbook.ui.shelf.search.ShelfSearchActivity
import com.wedevote.wdbook.ui.widgets.CommTopTitleLayout
import com.wedevote.wdbook.ui.widgets.WidgetLoadingLayout
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import kotlin.math.max

/***
 * @date 创建时间 2020/4/8 17:57
 * <AUTHOR> W.YuLong
 * @description 首页书架的页面
 */
class HomeBookShelfFragment(var onHomeTabListener: OnHomeTabListener? = null) :
    BaseRootFragment(), View.OnClickListener, OnRefreshListener, OnLoadMoreListener {
    private lateinit var topTitleLayout: CommTopTitleLayout
    private lateinit var dataRecyclerView: CustomRecyclerView
    private lateinit var moveToFolderButton: Button
    private lateinit var refreshLayout: SmartRefreshLayout
    private lateinit var editTextView: TextView
    private lateinit var allSelectTextView: TextView
    private lateinit var searchImageView: ImageView
    private lateinit var emptyLayout: ConstraintLayout
    private lateinit var emptyPromptLayout: GroupImageTextLayout
    private lateinit var gotoStoreButton: Button
    private lateinit var shelfAdapter: ShelfBookAdapter
    private lateinit var loadingLayout: WidgetLoadingLayout

    var offset: Long = 0
    val limit: Long = 20

    var onShelfItemEditListener: OnShelfItemEditListener? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        val rootView = inflater.inflate(R.layout.fragment_home_book_shelf_layout, container, false)
        initViewFromXML(rootView)

        shelfAdapter = ShelfBookAdapter(requireActivity())
        dataRecyclerView.adapter = shelfAdapter
        // RecyclerView 性能优化：禁用变更动画、扩大缓存、预取
        dataRecyclerView.itemAnimator = null
        dataRecyclerView.setHasFixedSize(true)
        if (ScreenUtil.isTablet(requireContext())) {
            dataRecyclerView.setItemViewCacheSize(20)
            dataRecyclerView.isDrawingCacheEnabled = true
            dataRecyclerView.drawingCacheQuality = View.DRAWING_CACHE_QUALITY_LOW
        } else {
            dataRecyclerView.setItemViewCacheSize(6)
        }

        loadLocalData(offset, limit)
        setViewListeners()
        EventBus.getDefault().register(this)
        return rootView
    }

    override fun onResume() {
        super.onResume()
        if (isAdded && isVisible) {
            // Fragment is visible and active
            activity?.let {
                SSOLoginActivity.checkAndGotoLogin(
                    it,
                    callBack = object : OnLoginResultCallBack {
                        override fun onLoginResult(isSuccess: Boolean) {
                            if (!isSuccess) {
                                onHomeTabListener?.onChangeHomeTab(HomeTab.STORE.tab)
                            }
                        }
                    },
                )
            }
        }
    }

    private fun setViewListeners() {
        topTitleLayout.setOnClickListener(this)
        editTextView.setOnClickListener(this)
        moveToFolderButton.setOnClickListener(this)
        gotoStoreButton.setOnClickListener(this)
        allSelectTextView.setOnClickListener(this)
        searchImageView.setOnClickListener(this)
        refreshLayout.setOnRefreshListener(this).setOnLoadMoreListener(this)
        // 预取下一屏
        (dataRecyclerView.layoutManager as? androidx.recyclerview.widget.LinearLayoutManager)?.initialPrefetchItemCount = if (ScreenUtil.isTablet(requireContext())) 12 else 6

        shelfAdapter.onShelfBookAdapterCallback = object : OnShelfBookAdapterCallback {
            override fun onReloadData() {
                reloadData()
            }

            override fun onChangeEditMode(isEditMode: Boolean) {
                onShelfItemEditListener?.onShelfEditMode(isEditMode)
                if (isEditMode) {
                    moveToFolderButton.visibility = View.VISIBLE
                    // 必须编辑按钮显示的时候，全反选按钮才可以显示
                    if (editTextView.visibility == View.VISIBLE) {
                        allSelectTextView.visibility = View.VISIBLE
                    }
                    editTextView.setText(R.string.label_cancel)
                    topTitleLayout.setTitle(getString(R.string.select_books))
                    topTitleLayout.setLeftTitle("")
                    onItemSelect(null)
                } else {
                    moveToFolderButton.visibility = View.GONE
                    allSelectTextView.visibility = View.GONE
                    editTextView.setText(R.string.edit)
                    topTitleLayout.setTitle("")
                    topTitleLayout.setLeftTitle(getString(R.string.book_shelf))
                }
            }

            override fun isAllItemSelected(isAllSelected: Boolean) {
                allSelectTextView.setText(if (isAllSelected) R.string.inverse_select else R.string.select_all)
            }

            override fun onEmptyData(isEmptyData: Boolean) {
                onShowDataUI(isEmptyData)
            }

            override fun onItemSelect(selectList: ArrayList<ShelfBookItemEntity>?) {
                moveToFolderButton.alpha = if (selectList.isNullOrEmpty()) 0.6f else 1f
                moveToFolderButton.isEnabled = !selectList.isNullOrEmpty()
            }
        }
    }

    private fun initViewFromXML(rootView: View) {
        topTitleLayout = rootView.findViewById(R.id.book_shelf_top_title_layout)
        dataRecyclerView = rootView.findViewById(R.id.book_shelf_data_RecyclerView)
        refreshLayout = rootView.findViewById(R.id.book_shelf_data_container_RefreshLayout)
        editTextView = rootView.findViewById(R.id.book_shelf_edit_TextView)
        allSelectTextView = rootView.findViewById(R.id.book_shelf_select_all_TextView)
        emptyLayout = rootView.findViewById(R.id.book_shelf_empty_container_layout)
        emptyPromptLayout = rootView.findViewById(R.id.book_shelf_empty_icon_Layout)
        moveToFolderButton = rootView.findViewById(R.id.book_shelf_move_folder_Button)
        gotoStoreButton = rootView.findViewById(R.id.book_shelf_goto_store_Button)
        loadingLayout = rootView.findViewById(R.id.book_shelf_loading_Layout)
        searchImageView = rootView.findViewById(R.id.book_shelf_search_ImageView)
        topTitleLayout.setLeftTitle(getString(R.string.book_shelf))
    }

    fun onShowDataUI(isEmptyData: Boolean) {
        if (isEmptyData && loadingLayout.visibility != View.VISIBLE) {
            emptyPromptLayout.setText(R.string.no_book_on_bookshelf)
            emptyLayout.visibility = View.VISIBLE
            refreshLayout.visibility = View.GONE
            moveToFolderButton.visibility = View.GONE
            editTextView.visibility = View.GONE
        } else {
            emptyLayout.visibility = View.GONE
            refreshLayout.visibility = View.VISIBLE
            searchImageView.visibility = View.VISIBLE
        }
    }

    fun onBackPress(): Boolean {
        return shelfAdapter.doCancelEditMode()
    }

    private fun reloadData() {
        var dataLimit = max(shelfAdapter.itemCount.toLong(), limit)
        // 这块加载当前显示所有的书籍
        shelfAdapter.clearDataList()
        loadLocalData(0, dataLimit)
    }

    override fun onRefresh(layout: RefreshLayout) {
        if (!SDKSingleton.sessionBl.isLogin()) {
            refreshLayout.finishLoadMoreAndRefresh()
            return
        }

        MainScope().launch() {
            try {
                if (shelfAdapter.itemCount == 0) {
                    loadingLayout.visibility = View.VISIBLE
                    emptyLayout.visibility = View.GONE
                }

                SDKSingleton.syncBl.syncUserData()
                offset = 0
                refreshLayout.isEnableLoadMore = true
                shelfAdapter.clearDataList()
                refreshLayout.finishLoadMoreAndRefresh()
                loadingLayout.visibility = View.GONE
                loadLocalData(offset, limit)
            } catch (e: Exception) {
                refreshLayout.finishLoadMoreAndRefresh()
                ExceptionHandler.handleException(e)
            }
        }
    }

    override fun onLoadMore(refreshLayout: RefreshLayout) {
        offset = shelfAdapter.itemCount.toLong()
        loadLocalData(offset, limit)
    }

    private fun loadLocalData(offset: Long, limit: Long) {
        val shelfEntityList = if (SDKSingleton.sessionBl.isLogin()) {
            SDKSingleton.dbWrapBl.getShelfEntityList(offset, limit)
        } else {
            ArrayList<HomeShelfItemCombineEntity>()
        }

        shelfAdapter.addDataList(shelfEntityList.toMutableList())

        if (shelfEntityList.isNullOrEmpty()) {
            refreshLayout.isEnableLoadMore = false
        } else {
            loadingLayout.visibility = View.GONE
        }
        editTextView.visibility =
            if (shelfAdapter.checkAllDataArchiveType()) View.GONE else View.VISIBLE
        onShowDataUI(shelfAdapter.itemCount == 0)

        refreshLayout.finishLoadMoreAndRefresh()

    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun doOnBookItemDownloadFinish(event: HomeShelfDataReloadEvent) {
        reloadData()
        if (event is OnBookReadEvent) {
            dataRecyclerView.scrollToPosition(0)
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun doOnLoginEvent(event: OnLoginEvent) {
        onRefresh(refreshLayout)
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun doOnLogoutEvent(event: LogoutEvent) {
        KLog.d("退出登录")
        onRefresh(refreshLayout)
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onReceiveEditShelfEvent(event: EditShelfEvent) {
        editTextView.postDelayed({ editTextView.performClick() }, 200)
        KLog.d("onReceiveEditShelfEvent 编辑书架的DEeplink")
    }

    override fun onClick(v: View) {
        when (v) {
            editTextView -> {
                shelfAdapter.onChangeEditModeAction()
            }

            allSelectTextView -> {
                shelfAdapter.onChangeSelectAllData()
            }

            gotoStoreButton -> {
                onHomeTabListener?.onChangeHomeTab(HomeTab.STORE.tab)
            }

            moveToFolderButton -> {
                if (shelfAdapter.selectItemList.isEmpty()) {
                    return
                }
                moveToAnotherFolder()
            }
            searchImageView -> {
                val intent = Intent(requireContext(), ShelfSearchActivity::class.java)
                startActivity(intent)
            }
        }
    }

    private fun moveToAnotherFolder() {
        val bookShelfGroupDialog = BookShelfGroupDialog(requireActivity())
        bookShelfGroupDialog.onViewClickListener = object : OnViewClickListener {
            override fun <T> onClickAction(v: View, tag: String, t: T?) {
                var clientArchiveId: String? = null

                when (tag) {
                    BookShelfGroupDialog.TAG_CREATE_FOLDER -> {
                        val shelfArchiveItem =
                            SDKSingleton.dbWrapBl.transactionSaveShelfArchiveData(t as String)
                        clientArchiveId = shelfArchiveItem.clientArchiveId
                    }

                    BookShelfGroupDialog.TAG_SELECT_FOLDER -> {
                        t as ShelfArchiveItemEntity
                        clientArchiveId = t.clientArchiveId
                    }

                    BookShelfGroupDialog.TAG_CANCEL -> {
                        shelfAdapter.doCancelEditMode()
                    }
                }
                if (clientArchiveId.isNullOrEmpty()) {
                    return
                }
                SDKSingleton.dbWrapBl.moveShelfBookToArchive(
                    shelfAdapter.selectItemList,
                    clientArchiveId
                )
                shelfAdapter.doCancelEditMode()
                reloadData()
                onRefresh(refreshLayout)
                bookShelfGroupDialog.dismiss()
            }
        }
        bookShelfGroupDialog.show()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        EventBus.getDefault().unregister(this)
    }
}
