package com.wedevote.wdbook.ui.dialogs

import android.app.Dialog
import android.content.Context
import android.view.Gravity
import android.view.WindowManager
import com.wedevote.wdbook.R

/***
 * @date 创建时间 2020/9/25 17:38
 * <AUTHOR> <PERSON><PERSON>
 * @description
 */
open class BaseDialog(context: Context, style: Int = R.style.DialogStyle) : Dialog(context, style) {

    open fun configDialog(gravity: Int) {
        val wl = window!!.attributes
        wl.gravity = gravity
        when (gravity) {
            Gravity.BOTTOM -> {
                window!!.setWindowAnimations(com.aquila.lib.dialog.R.style.bottomDialogWindowAnim)
            }
            Gravity.CENTER -> {
                window!!.setWindowAnimations(com.aquila.lib.dialog.R.style.centerDialogWindowAnim)
            }
        }
        wl.width = WindowManager.LayoutParams.MATCH_PARENT
        wl.height = WindowManager.LayoutParams.WRAP_CONTENT
        window!!.attributes = wl
        window!!.setBackgroundDrawableResource(android.R.color.transparent)
    }
}
