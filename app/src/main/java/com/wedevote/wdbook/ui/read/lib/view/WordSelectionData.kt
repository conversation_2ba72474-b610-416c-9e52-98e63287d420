package com.wedevote.wdbook.ui.read.lib.view

/***
 * @date 创建时间 2020/12/4 17:57
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @description
 */
class WordSelectionData {
    var startWord: Int = 0
    var endWord: Int = 0
    var startLineIndex: Int = -1
    var endLineIndex: Int = -1

    fun correctWordData(data: WordSelectionData) {
        if (startLineIndex * 1000 + startWord > endLineIndex * 1000 + endWord) {
            data.endLineIndex = startLineIndex
            data.endWord = startWord
            data.startLineIndex = endLineIndex
            data.startWord = endWord
        } else {
            data.endLineIndex = endLineIndex
            data.endWord = endWord
            data.startLineIndex = startLineIndex
            data.startWord = startWord
        }
    }

    fun hasSelected(): Boolean {
        return startLineIndex != -1 && endLineIndex != -1
    }

    fun reset(): Boolean {
        return if (hasSelected()) {
            startWord = 0
            endWord = 0
            startLineIndex = -1
            endLineIndex = -1
            true
        } else false
    }
}
