package com.wedevote.wdbook.ui.user.coupon

import android.view.ViewGroup
import com.aquila.lib.base.BaseRecycleAdapter
import com.aquila.lib.base.OnViewClickListener
import com.wedevote.wdbook.base.ExceptionHandler
import com.wedevote.wdbook.base.SDKSingleton
import com.wedevote.wdbook.entity.coupon.CouponEntity
import com.wedevote.wdbook.entity.coupon.CouponStatus
import com.wedevote.wdbook.ui.home.HomeMainActivity
import com.wedevote.wdbook.ui.home.HomeTab
import com.wedevote.wdbook.ui.store.BookDetailActivity
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch

/***
 *@date 创建时间 2022/5/31 15:01
 *<AUTHOR> W.YuLong
 *@description
 */
class CouponItemAdapter(var couponType: CouponItemType) : BaseRecycleAdapter<CouponEntity, BaseCouponViewHolder>() {
    
    var onViewClickListener: OnViewClickListener? = null
    var selectItemPosition = -1
    
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BaseCouponViewHolder {
        return when (couponType) {
            CouponItemType.GET_COUPON -> {
                GetCouponViewHolder(parent)
            }
            CouponItemType.USE_COUPON -> {
                UseCouponViewHolder(parent)
            }
            CouponItemType.COUPON_LIST -> {
                CouponItemViewHolder(parent)
            }
        }
    }
    
    fun findCurrentSelectPosition(entity: CouponEntity?) {
        if (entity != null && !dataList.isNullOrEmpty()) {
            var index = 0
            for (item in dataList!!) {
                if (item.couponId == entity.couponId) {
                    selectItemPosition = index
                    break
                }
                index++
            }
            notifyDataSetChanged()
        }
    }
    
    fun getSelectItem(): CouponEntity? {
        return getDataFromPosition(selectItemPosition)
    }
    
    fun getMinCouponId(): Long {
        var minId: Long = 0
        if (!dataList.isNullOrEmpty()) {
            for (item in dataList!!) {
                if (minId > item.couponId) {
                    minId = item.couponId
                }
            }
        }
        return minId
    }
    
    override fun onBindViewHolder(holder: BaseCouponViewHolder, position: Int) {
//        super.onBindViewHolder(holder, position)
        val entity = getDataFromPosition(position)
        holder.initUIData(entity)
        holder.onItemSelected(selectItemPosition == position)
        
        holder.useButton.setOnClickListener {
            when (entity!!.status) {
                //未领取
                CouponStatus.NOT_GET.status -> {
                    MainScope().launch(ExceptionHandler.coroutineExceptionHandler) {
                        val result = SDKSingleton.userBl.getUserCoupon(entity.couponId)
                        if (!result.isNullOrEmpty()) {
                            entity.status = 1
                            notifyItemChanged(position)
                        }
                    }
                }
                CouponStatus.CAN_USE.status -> {
                    if (!entity.productIds.isNullOrEmpty() && entity.productIds != null && entity.productIds!!.size == 1) {
                        BookDetailActivity.gotoBookDetail(it.context, entity.productIds!![0])
                    } else {
                        HomeMainActivity.gotoHomeActivity(it.context, HomeTab.STORE)
                    }
                }
            }
        }
        holder.itemView.setOnClickListener {
            if (selectItemPosition == position) {
                selectItemPosition = -1
            } else {
                selectItemPosition = position
            }
            if (CouponItemType.COUPON_LIST == couponType) {
                CouponDetailActivity.gotoCouponDetail(it.context, entity)
            }
            notifyDataSetChanged()
        }
    }
}


