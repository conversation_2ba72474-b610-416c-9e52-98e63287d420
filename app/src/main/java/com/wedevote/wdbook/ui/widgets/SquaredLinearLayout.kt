package com.wedevote.wdbook.ui.widgets

import android.content.Context
import android.util.AttributeSet
import android.widget.LinearLayout

/***
 * @date 创建时间 2022/4/20 18:40
 * <AUTHOR> <PERSON><PERSON>
 * @description
 */
class SquaredLinearLayout(context: Context, attr: AttributeSet? = null) : LinearLayout(context, attr) {

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec)
        setMeasuredDimension(measuredWidth, measuredWidth)
    }
}
