package com.wedevote.wdbook.ui

import android.content.Intent
import android.graphics.Color
import android.net.Uri
import android.os.Bundle
import android.view.View
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import com.aquila.lib.tools.util.ToastUtil
import com.aquila.lib.widget.group.GroupImageTextLayout
import com.bigkoo.pickerview.builder.TimePickerBuilder
import com.wedevote.wdbook.R
import com.wedevote.wdbook.base.APPConfig
import com.wedevote.wdbook.base.ExceptionHandler
import com.wedevote.wdbook.base.RootActivity
import com.wedevote.wdbook.base.SDKSingleton
import com.wedevote.wdbook.entity.UserInfoEntity
import com.wedevote.wdbook.exception.ApiException
import com.wedevote.wdbook.tools.event.OnChangeAccountSuccess
import com.wedevote.wdbook.tools.util.APPUtil
import com.wedevote.wdbook.tools.util.ImageLoadUtil
import com.wedevote.wdbook.tools.util.SelectImageUtility
import com.wedevote.wdbook.tools.util.UnitFormatUtil
import com.wedevote.wdbook.tools.util.crop.CropImage
import com.wedevote.wdbook.tools.util.getPictureRemotePath
import com.wedevote.wdbook.ui.dialogs.BottomSelectItemDialog
import com.wedevote.wdbook.ui.user.HomeMineFragment
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import java.io.File
import java.util.*

/***
 *@date 创建时间 2023/5/8
 *<AUTHOR> John.Qian
 *@description  个人中心-编辑资料页面
 */
class EditAccountActivity : RootActivity(), View.OnClickListener {

    private lateinit var editAccountLinerLayout: LinearLayout
    private lateinit var nameLayout: GroupImageTextLayout
    private lateinit var believeYearLayout: GroupImageTextLayout
    private lateinit var birthLayout: GroupImageTextLayout
    private lateinit var avatarImageView: ImageView
    private lateinit var birthTextView: TextView
    private lateinit var believeYearTextView: TextView
    private lateinit var nickNameTextView: TextView
    private lateinit var imageUtility: SelectImageUtility

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_edit_account_layout)
        initViewFromXML()
        imageUtility = SelectImageUtility(this)
        setViewListeners()
        if (HomeMineFragment.userInfoEntity != null) {
            initUI(HomeMineFragment.userInfoEntity!!)
        } else {
            initData()
        }
    }

    private fun initData() {
        MainScope().launch(ExceptionHandler.coroutineExceptionHandler) {
            SDKSingleton.userBl.getUserInfoEntity(true)?.let {
                initUI(it)
            }
        }
    }

    private fun initUI(userInfoEntity: UserInfoEntity) {
        ImageLoadUtil.loadCircleImageWithDefault(
            avatarImageView,
            getPictureRemotePath(userInfoEntity.avatarPath),
            R.drawable.ic_default_avatar
        )
        nickNameTextView.text = userInfoEntity.nickname
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun OnChangeAccountSuccessEvent(event: OnChangeAccountSuccess) {
        initData()
    }

    private fun setViewListeners() {
        editAccountLinerLayout.setOnClickListener(this)
        birthLayout.setOnClickListener(this)
        nameLayout.setOnClickListener(this)
        believeYearLayout.setOnClickListener(this)
    }

    private fun initViewFromXML() {
        editAccountLinerLayout = findViewById(R.id.edit_account_LinerLayout)
        avatarImageView = findViewById(R.id.edit_account_avatar_ImageView)
        nameLayout = findViewById(R.id.edit_account_name_layout)
        birthLayout = findViewById(R.id.edit_account_birth_layout)
        believeYearLayout = findViewById(R.id.edit_account_believe_year_layout)
        birthTextView = findViewById(R.id.edit_account_birth_TextView)
        believeYearTextView = findViewById(R.id.edit_account_believe_year_TextView)
        nickNameTextView = findViewById(R.id.edit_account_name_TextView)
    }

    override fun onClick(v: View?) {
        when (v) {
            editAccountLinerLayout -> {
                showAvatarSelectDialog()
            }
            nameLayout -> {
                startActivity(Intent(this, EditNameActivity::class.java))
            }
            birthLayout -> {
                pickBirthYear()
            }
            believeYearLayout -> {
                pickBelieveYear()
            }
        }
    }

    var itemDialog: BottomSelectItemDialog? = null
    private fun showAvatarSelectDialog() {
        if (itemDialog == null) {
            itemDialog = BottomSelectItemDialog(this)
        }
        if (!itemDialog!!.isShowing) {
            itemDialog!!.show()
        }
        itemDialog!!.setItemsText(
            getString(R.string.take_photo),
            getString(R.string.take_from_album),
            getString(R.string.label_cancel)
        )
        itemDialog!!.setOnClickListener(View.OnClickListener { view ->
            if (view === itemDialog!!.firstTextView) {
                imageUtility.gotoTakePhoto(imageUtility.getAvatarFilePath())
            } else if (view === itemDialog!!.secondTextView) {
                imageUtility.gotoPhoneAlbum()
            }
        })
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode != RESULT_CANCELED) {
            if (requestCode == CropImage.CROP_IMAGE_ACTIVITY_REQUEST_CODE) {
                val result: CropImage.ActivityResult = CropImage.getActivityResult(data)
                if (resultCode == RESULT_OK) {
                    val uri: Uri = result.getUri()
                    uri.path?.let { uploadAvatar(it) }
                }
            } else if (requestCode == SelectImageUtility.REQUEST_CODE_LOCAL_IMAGE) {
                if (data != null) {
                    data.data?.let { imageUtility.startCrop(it, this, true) }
                }
            } else if (requestCode == SelectImageUtility.REQUEST_CODE_TAKE_PHOTO) {
                imageUtility.startCrop(
                    Uri.fromFile(File(imageUtility.getAvatarFilePath())),
                    this,
                    true
                )
            }
        }
    }

    private fun uploadAvatar(filePath: String) {
        MainScope().launch {
            try {
                APPUtil.showLoadingDialog(this@EditAccountActivity)
                SDKSingleton.userBl.uploadAvator(File(filePath).readBytes())
                APPUtil.dismissLoadingDialog(this@EditAccountActivity)
                EventBus.getDefault().post(OnChangeAccountSuccess())

            } catch (exception: Throwable) {
                APPUtil.dismissLoadingDialog(this@EditAccountActivity)
                if (exception is ApiException) {
                    ToastUtil.showToastShort(exception.message)
                }
                ExceptionHandler.handleException(exception)
            }
        }
    }


    private fun pickBirthYear() {
        val selectedDate = Calendar.getInstance()
        val startDate = Calendar.getInstance()
        val endDate = Calendar.getInstance()
        startDate[1900, 0] = 1

        var pvTime = TimePickerBuilder(this) { date, v -> //选中get
            birthTextView.text = UnitFormatUtil.getYearFromDate(date).toString() + "年"
        }
            .setType(booleanArrayOf(true, false, false, false, false, false)) // 默认全部显示
            .setCancelText(getString(R.string.label_cancel)) //取消按钮文字
            .setSubmitText(getString(R.string.save)) //确认按钮文字
            .setContentTextSize(18) //滚轮文字大小
            .setTitleSize(16) //标题文字大小
            .setTitleText(getString(R.string.select_birth_date)) //标题文字
            .setTextColorCenter(resources.getColor(if (APPConfig.isCurrentThemeLight()) R.color.black else R.color.color_F5F5F5))
            .setTextColorOut(resources.getColor(if (APPConfig.isCurrentThemeLight()) R.color.text_color_black_4c else R.color.text_color_black_BC))
            .setOutSideCancelable(false) //点击屏幕，点在控件外部范围时，是否取消显示
            .isCyclic(false) //是否循环滚动
            .setTitleColor(if (APPConfig.isCurrentThemeLight()) Color.BLACK else Color.WHITE) //标题文字颜色
            .setSubmitColor(resources.getColor(if (APPConfig.isCurrentThemeLight()) R.color.color_blue_006FFF else R.color.color_blue_4D95F7)) //确定按钮文字颜色
            .setCancelColor(resources.getColor(if (APPConfig.isCurrentThemeLight()) R.color.color_blue_006FFF else R.color.color_blue_4D95F7)) //取消按钮文字颜色
            .setTitleBgColor(resources.getColor(if (APPConfig.isCurrentThemeLight()) R.color.white else R.color.color_icon_normal_373636)) //标题背景颜色 Night mode
            .setBgColor(resources.getColor(if (APPConfig.isCurrentThemeLight()) R.color.white else R.color.color_icon_normal_373636)) //滚轮背景颜色 Night mode
            .setDate(selectedDate) // 如果不设置的话，默认是系统时间*/
            .setRangDate(startDate, endDate) //起始终止年月日设定
            .setLabel("年", "", "", "", "", "") //默认设置为年月日时分秒
            .isCenterLabel(false) //是否只显示中间选中项的label文字，false则每项item全部都带有label。
            .isDialog(false) //是否显示为对话框样式
            .build()
        pvTime.show()
    }

    private fun pickBelieveYear() {
        val selectedDate = Calendar.getInstance()
        val startDate = Calendar.getInstance()
        val endDate = Calendar.getInstance()
        startDate[1900, 0] = 1

        var pvTime = TimePickerBuilder(this) { date, v -> //选中get
            believeYearTextView.text = UnitFormatUtil.getYearFromDate(date).toString() + "年"
        }
            .setType(booleanArrayOf(true, false, false, false, false, false)) // 默认全部显示
            .setCancelText(getString(R.string.label_cancel)) //取消按钮文字
            .setSubmitText(getString(R.string.save)) //确认按钮文字
            .setContentTextSize(18) //滚轮文字大小
            .setTitleSize(16) //标题文字大小
            .setTitleText(getString(R.string.select_believe_year)) //标题文字
            .setTextColorCenter(resources.getColor(if (APPConfig.isCurrentThemeLight()) R.color.black else R.color.color_F5F5F5))
            .setTextColorOut(resources.getColor(if (APPConfig.isCurrentThemeLight()) R.color.text_color_black_4c else R.color.text_color_black_BC))
            .setOutSideCancelable(false) //点击屏幕，点在控件外部范围时，是否取消显示
            .isCyclic(false) //是否循环滚动
            .setTitleColor(if (APPConfig.isCurrentThemeLight()) Color.BLACK else Color.WHITE) //标题文字颜色
            .setSubmitColor(resources.getColor(if (APPConfig.isCurrentThemeLight()) R.color.color_blue_006FFF else R.color.color_blue_4D95F7)) //确定按钮文字颜色
            .setCancelColor(resources.getColor(if (APPConfig.isCurrentThemeLight()) R.color.color_blue_006FFF else R.color.color_blue_4D95F7)) //取消按钮文字颜色
            .setTitleBgColor(resources.getColor(if (APPConfig.isCurrentThemeLight()) R.color.white else R.color.color_icon_normal_373636)) //标题背景颜色 Night mode
            .setBgColor(resources.getColor(if (APPConfig.isCurrentThemeLight()) R.color.white else R.color.color_icon_normal_373636)) //滚轮背景颜色 Night mode
            .setDate(selectedDate) // 如果不设置的话，默认是系统时间*/
            .setRangDate(startDate, endDate) //起始终止年月日设定
            .setLabel("年", "", "", "", "", "") //默认设置为年月日时分秒
            .isCenterLabel(false) //是否只显示中间选中项的label文字，false则每项item全部都带有label。
            .isDialog(false) //是否显示为对话框样式
            .build()
        pvTime.show()
    }

}
