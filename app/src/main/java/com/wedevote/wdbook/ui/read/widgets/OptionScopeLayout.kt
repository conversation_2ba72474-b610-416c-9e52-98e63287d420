package com.wedevote.wdbook.ui.read.widgets

import android.content.Context
import android.util.AttributeSet
import android.view.View
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.SeekBar
import android.widget.TextView
import androidx.cardview.widget.CardView
import com.aquila.lib.base.OnViewClickListener
import com.aquila.lib.tools.interfaceimpl.OnSeekBarListenerImpl
import com.wedevote.wdbook.R

/***
 * @date 创建时间 2020/7/20 11:08
 * <AUTHOR> W.<PERSON>Long
 * @description 目录跳转的布局
 */
class OptionScopeLayout constructor(context: Context, attr: AttributeSet? = null) : LinearLayout(context, attr), View.OnClickListener {
    lateinit var cardView: CardView
    lateinit var indexTextView: TextView
    lateinit var titleTextView: TextView
    lateinit var revokeImageView: ImageView
    lateinit var prevImageView: ImageView
    lateinit var nextImageView: ImageView
    lateinit var seekBar: SeekBar

    var onViewClickListener: OnViewClickListener? = null

    var readProcess = ReadProcess(-1, -1)
    var currentOffset = 0

    init {
        View.inflate(context, R.layout.option_read_scope_layout, this)
        initViewFromXML()
        setViewListeners()
    }

    private fun setViewListeners() {
        revokeImageView.setOnClickListener(this)
        prevImageView.setOnClickListener(this)
        nextImageView.setOnClickListener(this)
        seekBar.setOnSeekBarChangeListener(object : OnSeekBarListenerImpl() {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                if (cardView.visibility != View.VISIBLE) {
                    cardView.visibility = View.VISIBLE
                }
                if (fromUser) {
                    onViewClickListener?.onClickAction(seekBar!!, OptionClickTag.TAG_SCOPE_PROGRESS, progress)
                }
            }

            override fun onStartTrackingTouch(seekBar: SeekBar?) {
                super.onStartTrackingTouch(seekBar)
                saveHistory(ReadProcess(seekBar!!.progress, currentOffset))
            }

            override fun onStopTrackingTouch(seekBar: SeekBar?) {
                onViewClickListener?.onClickAction(seekBar!!, OptionClickTag.TAG_SCOPE_REVOKE, ReadProcess(seekBar!!.progress, 0))
            }
        })
    }

    fun saveCurrentHistory() {
        saveHistory(ReadProcess(seekBar.progress, currentOffset))
    }

    fun saveHistory(progress: ReadProcess) {
        readProcess = progress
        revokeImageView.isEnabled = true
        if (revokeImageView.rotationY > 90f) {
            revokeImageView.rotationY = 0f
        }
    }

    fun showTitle(show: Boolean) {
        if (show) {
            cardView.visibility = View.VISIBLE
        } else {
            cardView.visibility = View.GONE
        }
    }

    fun setTitle(title: String) {
        titleTextView.text = title
    }

    fun setProgressText(text: String, offset: Int) {
        indexTextView.text = text
        if (offset >= 0) {
            currentOffset = offset
        }
    }

    private fun initViewFromXML() {
        cardView = findViewById(R.id.scope_last_title_CardView)
        indexTextView = findViewById(R.id.scope_index_TextView)
        titleTextView = findViewById(R.id.scope_last_chapter_TextView)
        revokeImageView = findViewById(R.id.scope_revoke_ImageView)
        prevImageView = findViewById(R.id.scope_prev_ImageView)
        nextImageView = findViewById(R.id.scope_next_ImageView)
        seekBar = findViewById(R.id.scope_chapter_SeekBar)
        revokeImageView.isEnabled = false
    }

    override fun onClick(v: View) {
        when (v) {
            nextImageView -> {
                saveHistory(ReadProcess(seekBar.progress, currentOffset))
                seekBar.progress = seekBar.progress + 1
                onViewClickListener?.onClickAction(seekBar, OptionClickTag.TAG_SCOPE_REVOKE, ReadProcess(seekBar.progress, 0))
            }
            prevImageView -> {
                saveHistory(ReadProcess(seekBar.progress, currentOffset))
                seekBar.progress = seekBar.progress - 1
                onViewClickListener?.onClickAction(seekBar, OptionClickTag.TAG_SCOPE_REVOKE, ReadProcess(seekBar.progress, 0))
            }
            revokeImageView -> {
                if (readProcess.process >= 0) {
                    val nextProgress = ReadProcess(readProcess.process, readProcess.offset)
                    readProcess.process = seekBar.progress
                    readProcess.offset = currentOffset
                    onViewClickListener?.onClickAction(seekBar, OptionClickTag.TAG_SCOPE_REVOKE, nextProgress)
                    revokeImageView.pivotX = (revokeImageView.width / 2).toFloat()
                    revokeImageView.pivotY = (revokeImageView.height / 2).toFloat()
                    if (revokeImageView.rotationY > 90f) {
                        revokeImageView.rotationY = 0f
                    } else {
                        revokeImageView.rotationY = 180f
                    }
                }
            }
        }
    }
}
