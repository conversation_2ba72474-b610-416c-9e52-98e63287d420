package com.wedevote.wdbook.ui.user.feedback

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.TextView
import com.aquila.lib.base.BaseRecycleAdapter
import com.aquila.lib.base.BaseViewHolder
import com.aquila.lib.widget.group.GroupImageTextLayout
import com.aquila.lib.widget.view.CustomRecyclerView
import com.aquila.lib.widget.view.DotView
import com.wedevote.wdbook.R
import com.wedevote.wdbook.base.ExceptionHandler
import com.wedevote.wdbook.base.RootActivity
import com.wedevote.wdbook.base.SDKSingleton
import com.wedevote.wdbook.entity.feedback.FeedbackListItemEntity
import com.wedevote.wdbook.entity.feedback.FeedbackSaveEntity
import com.wedevote.wdbook.tools.util.UnitFormatUtil
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch

/***
 * @date 创建时间 2022/4/11 16:33
 * <AUTHOR> <PERSON><PERSON>
 * @description 我的反馈列表界面
 */
class UserFeedbackListActivity : RootActivity(), View.OnClickListener {

    lateinit var dataRecyclerView: CustomRecyclerView
    lateinit var feedbackButton: Button
    lateinit var dataAdapter: MyFeedbackRecyclerAdapter

    lateinit var entity: FeedbackSaveEntity

    companion object {
        fun gotoUserFeedbackListActivity(context: Context) {
            val intent = Intent(context, UserFeedbackListActivity::class.java)
            context.startActivity(intent)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_my_feedback_layout)
        dataRecyclerView = findViewById(R.id.my_feedback_data_RecyclerView)
        feedbackButton = findViewById(R.id.my_feedback_new_problem_Button)
        entity = FeedbackSaveEntity()

        dataAdapter = MyFeedbackRecyclerAdapter()
        dataRecyclerView.adapter = dataAdapter

        feedbackButton.setOnClickListener(this)
    }

    override fun onResume() {
        super.onResume()
        getDataFromServer()
    }


    fun getDataFromServer() {
        MainScope().launch(ExceptionHandler.coroutineExceptionHandler) {
            val dataList = SDKSingleton.userBl.getFeedbackTitleList()
            dataAdapter.dataList = dataList?.toMutableList()
        }
    }

    override fun onClick(v: View?) {
        when (v) {
            feedbackButton -> {
                EditFeedbackActivity.gotoEditFeedback(this)
            }
        }
    }
}

/***
 *@date 创建时间 2022/4/11 18:09
 *<AUTHOR> W.YuLong
 *@description
 */
class MyFeedbackRecyclerAdapter : BaseRecycleAdapter<FeedbackListItemEntity, MyFeedbackItemViewHolder>() {
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): MyFeedbackItemViewHolder {
        return MyFeedbackItemViewHolder(parent)
    }


    override fun onBindViewHolder(holder: MyFeedbackItemViewHolder, position: Int) {
        super.onBindViewHolder(holder, position)
        holder.messageCountLayout.setOnClickListener {
            val intent = Intent(it.context, FeedbackDetailActivity::class.java)
            it.context.startActivity(intent)
        }
    }
}

/***
 *@date 创建时间 2022/4/11 18:09
 *<AUTHOR> W.YuLong
 *@description
 */
class MyFeedbackItemViewHolder(parent: ViewGroup) : BaseViewHolder(parent, R.layout.holder_my_feedback_item_layout) {
    val flagDotView: DotView = itemView.findViewById(R.id.item_my_feedback_unread_flag_DotView)
    val titleTextView: TextView = itemView.findViewById(R.id.item_my_feedback_title_TextView)
    val timeTextView: TextView = itemView.findViewById(R.id.item_my_feedback_time_TextView)
    val messageCountLayout: GroupImageTextLayout = itemView.findViewById(R.id.item_my_feedback_message_count_Layout)

    override fun <T> initUIData(t: T) {
        t as FeedbackListItemEntity
        titleTextView.setText(t.messageText)
        timeTextView.setText(UnitFormatUtil.formatDate_ymdhm(t.createTime))
        flagDotView.visibility = if (t.hasNewMessage == 1) View.VISIBLE else View.GONE
        messageCountLayout.text = "${t.replyCount}"

        itemView.setOnClickListener {
            FeedbackDetailActivity.gotoFeedbackDetailActivity(itemView.context, t.feedbackId)
        }
    }
}
