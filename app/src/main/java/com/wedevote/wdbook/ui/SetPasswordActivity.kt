package com.wedevote.wdbook.ui

import android.content.Intent
import android.os.Bundle
import android.view.View
import android.widget.FrameLayout
import android.widget.TextView
import com.aquila.lib.tools.singleton.SPSingleton
import com.aquila.lib.tools.util.ToastUtil
import com.wdbible.app.wedevotebible.tools.security.EncodingUtils
import com.wedevote.wdbook.R
import com.wedevote.wdbook.base.APPConfig
import com.wedevote.wdbook.base.ExceptionHandler
import com.wedevote.wdbook.base.RootActivity
import com.wedevote.wdbook.base.SDKSingleton
import com.wedevote.wdbook.constants.Constants
import com.wedevote.wdbook.exception.ApiException
import com.wedevote.wdbook.exception.ErrorInfo
import com.wedevote.wdbook.tools.event.LogoutEvent
import com.wedevote.wdbook.tools.event.OnChangeAccountSuccess
import com.wedevote.wdbook.tools.util.APPUtil
import com.wedevote.wdbook.tools.util.AnalyticsUtils
import com.wedevote.wdbook.tools.util.IntentConstants
import com.wedevote.wdbook.tools.util.NetWorkUtils
import com.wedevote.wdbook.tools.util.SPKeyDefine
import com.wedevote.wdbook.ui.account.LoginActivity
import com.wedevote.wdbook.ui.account.register.RegisterLayoutManager
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus
import com.wedevote.wdbook.ui.dialogs.RegisterTipDialog

/***
 *@date 创建时间 2023/5/22
 *<AUTHOR> John.Qian
 *@description  设置密码页面
 */
class SetPasswordActivity : RootActivity(), View.OnClickListener {
    private lateinit var backView: View
    private lateinit var contentLayout: FrameLayout
    private lateinit var nextTextView: TextView
    private lateinit var titleTextView: TextView
    private var registerLayoutManager: RegisterLayoutManager? = null

    private fun findViewByIdFromXML() {
        backView = findViewById(R.id.register_back_View)
        contentLayout = findViewById(R.id.register_content_layout)
        nextTextView = findViewById(R.id.register_next_TextView)
        nextTextView.setText(R.string.reset_password)
        titleTextView = findViewById(R.id.register_login_title_TextView)
        titleTextView.setText(R.string.label_change_password)
    }

    public override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        val rootView = layoutInflater.inflate(R.layout.activity_set_password, null)
        setContentView(rootView)
        findViewByIdFromXML()
        setViewCLickListeners()
        registerLayoutManager = RegisterLayoutManager(this)
        contentLayout.addView(registerLayoutManager!!.rootView)
        registerLayoutManager!!.isResetPassword = true
        registerLayoutManager!!.setNextStepUi()
        registerLayoutManager!!.setOnButtonStateChangeListener {
            nextTextView.isEnabled = it
        }
        registerLayoutManager!!.passwordEditText.hint = getString(R.string.new_password)
        registerLayoutManager!!.passwordEditTextTwo.hint = getString(R.string.new_password)
        registerLayoutManager!!.phoneTitleTextView!!.setText(R.string.new_password)
        registerLayoutManager!!.verifyCodeTitleTextView.setText(R.string.please_input_password_again)
    }

    private fun setViewCLickListeners() {
        backView.setOnClickListener(this)
        nextTextView.setOnClickListener(this)
    }

    override fun onClick(v: View) {
        if (APPConfig.isFastClick()) return
        if (v === backView) {
            onBackPressed()
        } else if (v === nextTextView) {
            if (!NetWorkUtils.isNetworkAvailable()) {
                NetWorkUtils.showTipDialog(this, getString(R.string.no_network_connect))
                return
            }
            if (!registerLayoutManager!!.isPasswordCorrect() || !registerLayoutManager!!.isPasswordTwoCorrect()) return
            MainScope().launch {
                try {
                    APPUtil.showLoadingDialog(this@SetPasswordActivity).setCancelable(false)

                    SDKSingleton.sessionBl.getAccessToken()

                    val password = registerLayoutManager!!.passwordEditText.text.toString()
                    val verificationCode = intent.getStringExtra(IntentConstants.EXTRA_Reset_Verification_Code) ?: ""
                    val signPassword = EncodingUtils.rsaEncryptByPublicKey(
                        password,
                        Constants.PUBLICK_KEY
                    )
                    SDKSingleton.userBl.updatePassword(signPassword, verificationCode)

                    APPUtil.dismissLoadingDialog(this@SetPasswordActivity)
                    var tipDialog = RegisterTipDialog(this@SetPasswordActivity)
                    tipDialog.setOnDismissListener{
                        MainScope().launch {
                            EventBus.getDefault().post(OnChangeAccountSuccess(true))
                            logout()
                            finish()
                        }
                    }
                    tipDialog.show()
                    tipDialog.setTitleText(getString(R.string.change_password_success))
                    tipDialog.setContentText(getString(R.string.new_password_apply_to_bible_and_book))
                } catch (exception: Throwable) {
                    APPUtil.dismissLoadingDialog(this@SetPasswordActivity)
                    if (exception is ApiException) {
                        when (exception.code) {
                            ErrorInfo.UserNotExists.code, ErrorInfo.UserNotExists2.code -> {
                                APPUtil.showTipDialog(
                                    this@SetPasswordActivity,
                                    getString(R.string.warm_prompt_title),
                                    getString(R.string.this_account_not_exist)
                                )
                            }
                            ErrorInfo.InvalidVerificationCode.code -> {
                                APPUtil.showTipDialog(
                                    this@SetPasswordActivity,
                                    getString(R.string.warm_prompt_title),
                                    getString(R.string.invalid_verification_code)
                                )
                            }
                            ErrorInfo.SameAsOldPassword.code -> {
                                registerLayoutManager!!.passwordEditText.isSelected = true
                                val passwordErrorTip = registerLayoutManager!!.rootView.findViewById<TextView>(R.id.password_error_tip_TextView)
                                passwordErrorTip.visibility = View.VISIBLE
                                passwordErrorTip.setText(R.string.new_password_not_same_old)
                            }
                            ErrorInfo.RegisterVerificationCodeInvalid.code -> {
                                APPUtil.showTipDialog(
                                    this@SetPasswordActivity,
                                    getString(R.string.warm_prompt_title),
                                    getString(R.string.invalid_verification_code)
                                ){
                                    onBackPressed()
                                }
                            }
                            else -> {
                                ToastUtil.showToastShort(exception.message)
                            }
                        }
                    } else {
                        ExceptionHandler.handleException(exception)
                    }
                }
            }
        }
    }

    private suspend fun logout() {
        SDKSingleton.sessionBl.logout()
        SPSingleton.get().removeKey(SPKeyDefine.SP_LoginUserId)
        EventBus.getDefault().post(LogoutEvent())
        startActivity(Intent(this, LoginActivity::class.java))
        AnalyticsUtils.updateAnalyticsUserID()
    }
}