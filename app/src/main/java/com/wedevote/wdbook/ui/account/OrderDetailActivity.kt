package com.wedevote.wdbook.ui.account

import android.content.Intent
import android.os.Bundle
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import com.aquila.lib.base.BaseRecycleAdapter
import com.aquila.lib.base.BaseViewHolder
import com.aquila.lib.base.OnViewClickListener
import com.aquila.lib.layout.SmartRefreshLayout
import com.aquila.lib.layout.util.DensityUtil
import com.aquila.lib.tools.util.ToastUtil
import com.aquila.lib.widget.view.CustomRecyclerView
import com.wedevote.wdbook.R
import com.wedevote.wdbook.base.ExceptionHandler
import com.wedevote.wdbook.base.RootActivity
import com.wedevote.wdbook.base.SDKSingleton
import com.wedevote.wdbook.entity.store.OrderProductEntity
import com.wedevote.wdbook.entity.user.OrderEntity
import com.wedevote.wdbook.exception.ApiException
import com.wedevote.wdbook.tools.util.APPUtil
import com.wedevote.wdbook.tools.util.GsonUtil
import com.wedevote.wdbook.tools.util.ImageLoadUtil
import com.wedevote.wdbook.tools.util.IntentConstants
import com.wedevote.wdbook.tools.util.UnitFormatUtil
import com.wedevote.wdbook.tools.util.getPictureRemotePath
import com.wedevote.wdbook.tools.util.initAuthorsStringName
import com.wedevote.wdbook.ui.AccountSecurityActivity
import com.wedevote.wdbook.ui.dialogs.RegisterTipDialog
import com.wedevote.wdbook.ui.user.HomeMineFragment
import com.wedevote.wdbook.ui.widgets.CommTopTitleLayout
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch

/***
 * @date 创建时间 2020/7/28 15:50
 * <AUTHOR> W.YuLong
 * @description 订单详情的页面
 */
class OrderDetailActivity : RootActivity() {
    lateinit var topTitleLayout: CommTopTitleLayout
    lateinit var recordTextView: TextView
    lateinit var dataRecyclerView: CustomRecyclerView

    lateinit var recycleAdapter: DetailRecyclerAdapter
    lateinit var orderEntity: OrderEntity
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_order_detail_layout)
        orderEntity = GsonUtil.parseJsonToObject(intent.getStringExtra(IntentConstants.EXTRA_OrderListItemBean))!!
        topTitleLayout = findViewById(R.id.order_detail_top_TitleLayout)
        recordTextView = findViewById(R.id.order_detail_get_record_TextView)
        dataRecyclerView = findViewById(R.id.order_detail_data_RecyclerView)
        recycleAdapter = DetailRecyclerAdapter()
        dataRecyclerView.adapter = recycleAdapter
        initUI(orderEntity)
        initListener()
    }

    private fun initListener() {
        recordTextView.setOnClickListener{
            if (!HomeMineFragment.userInfoEntity?.email.isNullOrEmpty()) {
                //获取收据
                MainScope().launch {
                    //点击获取收据
                    try {
                        APPUtil.showLoadingDialog(this@OrderDetailActivity)
                        SDKSingleton.userBl.getOrderReceipt(orderEntity.orderId)
                        APPUtil.showTipDialog(
                            this@OrderDetailActivity,
                            getString(R.string.receit_has_send_to_email),
                            String.format(
                                getString(
                                    R.string.receipt_has_send_to
                                ), HomeMineFragment.userInfoEntity?.email
                            )
                        )
                        APPUtil.dismissLoadingDialog(this@OrderDetailActivity)
                    } catch (exception: Throwable) {
                        APPUtil.dismissLoadingDialog(this@OrderDetailActivity)
                        if (exception is ApiException) {
                            ToastUtil.showToastShort(exception.message)
                        } else {
                            ExceptionHandler.handleException(exception)
                        }
                    }
                }
            } else {
                var tipDialog = RegisterTipDialog(this@OrderDetailActivity)
                tipDialog.show()
                tipDialog.setTitleText(getString(R.string.please_bind_email_first))
                tipDialog.setCancelButtonVisible(true)
                tipDialog.setLeftButtonText(getString(R.string.later))
                tipDialog.setRightButtonText(getString(R.string.bind_now))
                tipDialog.setContentText(getString(R.string.bind_email_first_content))
                tipDialog.onViewClickListener = object : OnViewClickListener {
                    override fun <T> onClickAction(v: View, str: String, t: T?) {
                        startActivity(
                            Intent(
                                this@OrderDetailActivity,
                                AccountSecurityActivity::class.java
                            )
                        )
                    }
                }
            }
        }
    }

    fun initUI(entity: OrderEntity) {
        val fiveLayout = findViewById<LinearLayout>(R.id.item_detail_value_five_LinearLayout)
        val sixLayout = findViewById<LinearLayout>(R.id.item_detail_value_six_LinearLayout)
        val sevenLayout = findViewById<LinearLayout>(R.id.item_detail_value_seven_LinearLayout)
        val eightLayout = findViewById<LinearLayout>(R.id.item_detail_value_eight_LinearLayout)
        if (entity.type == 1) {
            var listData = ArrayList<OrderProductEntity>()
            entity.giftCardVo?.let {
                it.title = getString(R.string.wd_book_gift_card)
                it.price = entity.giftCardVo!!.amount
                it.count = entity.giftCardVo!!.count
                it.type = entity.type
                listData.add(it)
            }

            fiveLayout.visibility = View.GONE
            sixLayout.visibility = View.GONE
            sevenLayout.visibility = View.GONE
            eightLayout.visibility = View.GONE
            recycleAdapter.dataList = listData
        } else {
            recycleAdapter.dataList = entity.products?.toMutableList()
        }
        recycleAdapter.notifyDataSetChanged()
        (findViewById(R.id.item_detail_value_one_TextView) as TextView).text = UnitFormatUtil.formatDate_ymdhm(entity.lastUpdateTime * 1000)
        (findViewById(R.id.item_detail_value_two_TextView) as TextView).text = entity.orderId
        (findViewById(R.id.item_detail_value_three_TextView) as TextView).text = getPayMethod(entity.payMethod)
        if (entity.payMethod.isNullOrEmpty() || entity.payMethod.equals("APPLEPAY")) {
            (findViewById(R.id.item_detail_value_three_LinearLayout) as LinearLayout).visibility = View.GONE
        }
        (findViewById(R.id.item_detail_value_fore_TextView) as TextView).text = UnitFormatUtil.formatPrice(entity.currency, entity.originalPrice - entity.activityAmount)
        (findViewById(R.id.item_detail_value_five_TextView) as TextView).text = "-" + UnitFormatUtil.formatPrice(entity.currency, entity.deductedAmount)
        (findViewById(R.id.item_detail_value_six_TextView) as TextView).text = "-" + UnitFormatUtil.formatPrice(entity.currency, entity.couponAmount)
        (findViewById(R.id.item_detail_value_seven_TextView) as TextView).text = "-" + UnitFormatUtil.formatPrice(entity.currency, entity.giftCardAmount)
        (findViewById(R.id.item_detail_value_eight_TextView) as TextView).text = "-" + UnitFormatUtil.formatPrice(entity.currency, entity.walletBalance)
        (findViewById(R.id.item_detail_actual_pay_TextView) as TextView).text = UnitFormatUtil.formatPrice(entity.currency, entity.totalAmount)

        fiveLayout.visibility = if(entity.deductedAmount == 0F) View.GONE else View.VISIBLE
        sixLayout.visibility = if(entity.couponAmount == 0F) View.GONE else View.VISIBLE
        sevenLayout.visibility = if(entity.giftCardAmount == 0F) View.GONE else View.VISIBLE
        eightLayout.visibility = if(entity.walletBalance == 0F) View.GONE else View.VISIBLE
    }

    private fun getPayMethod(payMethod: String): String {
        return when (payMethod) {
            "支付宝" -> {
                getString(R.string.alipay)
            }
            "银行卡" -> {
                getString(R.string.bank_card)
            }
            "免费" -> {
                getString(R.string.free)
            }
            else -> {
                payMethod
            }
        }
    }
}

/***
 *@date 创建时间 2020/7/28 17:11
 *<AUTHOR> W.YuLong
 *@description
 */
class DetailRecyclerAdapter : BaseRecycleAdapter<OrderProductEntity, OrderDetailViewHolder>() {
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): OrderDetailViewHolder {
        return OrderDetailViewHolder(parent)
    }
}

/***
 * @date 创建时间 2022/11/9 15:00
 * <AUTHOR> W.YuLong
 * @description
 */
class OrderDetailViewHolder(parent: ViewGroup) :
    BaseViewHolder(parent, R.layout.holder_order_detail_item_layout) {
    val infoTextView: TextView = itemView.findViewById(R.id.item_book_info_name_TextView)
    val coverImageView: ImageView = itemView.findViewById(R.id.item_book_info_cover_ImageView)
    val authorTextView: TextView = itemView.findViewById(R.id.item_order_author_TextView)
    val numberOneTextView: TextView = itemView.findViewById(R.id.item_order_number_one_TextView)
    val copyrightTextView: TextView = itemView.findViewById(R.id.item_order_copyright_TextView)
    val moneyTextView: TextView = itemView.findViewById(R.id.item_order_money_TextView)

    override fun <T> initUIData(t: T) {
        val orderDeductedProduct = t as OrderProductEntity
        ImageLoadUtil.loadImageWithDefault(
            coverImageView,
            getPictureRemotePath(orderDeductedProduct.cover),
            R.drawable.ic_default_book_cover
        )
        infoTextView.text = orderDeductedProduct.title
        copyrightTextView.text = orderDeductedProduct.publisherName
        val initAuthorsStringName = initAuthorsStringName(orderDeductedProduct.authorList)
        if (initAuthorsStringName.isNotEmpty()) {
            authorTextView.visibility = View.VISIBLE
            authorTextView.text = initAuthorsStringName
        }else {
            authorTextView.visibility = View.GONE
        }
        numberOneTextView.text = "x" + orderDeductedProduct.count
        moneyTextView.text = UnitFormatUtil.formatPrice(t.currency, orderDeductedProduct.price)
        if (t.type == 1) {
            coverImageView.layoutParams.height = DensityUtil.dp2px(40f)
            coverImageView.scaleType = ImageView.ScaleType.FIT_XY
        } else {
            coverImageView.layoutParams.height = SmartRefreshLayout.LayoutParams.WRAP_CONTENT
            coverImageView.scaleType = ImageView.ScaleType.FIT_START
        }
    }
}
