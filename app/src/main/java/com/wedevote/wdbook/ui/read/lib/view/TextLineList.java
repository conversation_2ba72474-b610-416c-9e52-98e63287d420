package com.wedevote.wdbook.ui.read.lib.view;


import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.text.Spanned;
import android.text.TextPaint;
import android.text.style.RelativeSizeSpan;

import com.wedevote.wdbook.ui.read.lib.BookParameters;
import com.wedevote.wdbook.ui.read.lib.TextHelper;
import com.wedevote.wdbook.ui.read.lib.css.CssElement;
import com.wedevote.wdbook.ui.read.lib.span.AlignSpan;
import com.wedevote.wdbook.ui.read.lib.span.BlockSpan;
import com.wedevote.wdbook.ui.read.lib.span.TableDataSpan;
import com.wedevote.wdbook.ui.read.lib.span.WDImageSpan;

import java.util.LinkedList;

public class TextLineList extends LinkedList<Word> {
    private TextPaint paint;
    private TextHelper textHelper;
    private float lineWidth = 0;
    private float lineHeight = 0;
    private float y;
    private int nonBlankOrSymbolWordCount = 0;
    private boolean positionAdjusted = false;
    private WDImageSpan imageSpan;
    private TableDataSpan tableDataSpan;
    private BlockSpan blockSpan;

    private boolean blockFirstLine = false;
    private boolean blockLastLine = false;

    private boolean tableLineStart = true;
    private boolean tableLineEnd = true;

    private float sizeChange = 1.0f;
    private CssElement.TextAlignType alignType = CssElement.TextAlignType.left;//默认左
    private boolean alignTypeAssigned = false;

    public TextLineList(){

    }

    public TextLineList(TextPaint textPaint, TextHelper textHelper, float y, RelativeSizeSpan relativeSizeSpan, AlignSpan alignSpan) {
        this.paint = textPaint;
        this.lineWidth = 0;
        this.y = y;
        if (relativeSizeSpan != null) {
            this.sizeChange = relativeSizeSpan.getSizeChange();
        }

        if (alignSpan != null) {
            alignType = alignSpan.getType();
            alignTypeAssigned = true;
        }

        if (sizeChange > 1.0f) {
            this.y *= sizeChange;
        }
        this.textHelper = textHelper;
    }

    public Bitmap getImage(float x) {
        if (imageSpan != null) {
            int bitmapWidth = imageSpan.getBitmap().getWidth();
            if(bitmapWidth < lineWidth){
                float offsetX = (lineWidth-bitmapWidth)/2;
                if(x<offsetX || x >offsetX+bitmapWidth){
                    return null;
                }
            }
            return imageSpan.getOriginalBitmap();
        } else {
            return null;
        }
    }

    public void setDrawImage(WDImageSpan imageSpan, float drawLineEnd, float maxHeight) {
        this.imageSpan = imageSpan;
        imageSpan.scaleBitmap(drawLineEnd, maxHeight);
    }

    public void setBlockSpan(BlockSpan blockSpan) {
        this.blockSpan = blockSpan;
    }

    public boolean isBlockLastLine(){
        return blockLastLine;
    }

    public float getTopMargin() {
        if (blockFirstLine && blockSpan != null) {
            return blockSpan.getTopMargin();
        }
        return 0;
    }

    public void checkPrevLineBlock(TextLineList prev) {
        if (prev == null) {
            blockFirstLine = true;
        } else if (prev.blockSpan != blockSpan) {
            blockFirstLine = true;
            prev.blockLastLine = true;
        }
    }

    public void setTableSpan(TableDataSpan tableDataSpan) {
        this.tableDataSpan = tableDataSpan;
    }

    public TableDataSpan getTableSpan() {
        return tableDataSpan;
    }

    public float getLineWidth() {
        return this.lineWidth;
    }

    public void addBreakLine(float maxLineWidth) {
        if (!isEmpty() && alignType == CssElement.TextAlignType.center) {
            moveToCenter(maxLineWidth);
        }
    }

    private void moveToCenter(float maxLineWidth) {
        if (lineWidth < maxLineWidth) {
            float rightMargin = (maxLineWidth - lineWidth) / 2;
            for (Word w : this) {
                w.addRightPosition(rightMargin);
            }
        }
    }

    public boolean isEmptyLine() {
        for(Word w:this){
            if(!w.isBlankSpace()){
                return false;
            }
        }
        return true;
    }

    public boolean isImageLine(){
        return size() == 1 && imageSpan != null && imageSpan.getBitmap() != null;
    }

    public float getLineHeight(float originalHeight) {
        float extra = 0;
        if (isImageLine()) {
            int bmpHeight = imageSpan.getBitmap().getHeight();
            if (bmpHeight > originalHeight) {
                lineHeight = bmpHeight;
                return lineHeight;
            }
        } else if (blockSpan != null) {
            if (isEmptyLine()) {
                lineHeight = blockSpan.getBorderHeight(originalHeight, blockFirstLine, blockLastLine);
                return lineHeight;
            } else {
                extra = blockSpan.getBorderHeight(originalHeight, blockFirstLine, blockLastLine);
            }
        }

        if (sizeChange > 1.0f) {
            lineHeight = originalHeight * sizeChange + extra;
        } else {
            lineHeight = originalHeight + extra;
        }

        if (isEmptyLine()) {
            lineHeight /= 2;
        }

        return lineHeight;
    }

    public void addWord(Word word) {
        add(word);
        if (!(word.isBlankSpace() || word.isSymbol())) {
            nonBlankOrSymbolWordCount++;
        }
        if(!alignTypeAssigned && word.isChineseCharacter()){
            alignType = CssElement.TextAlignType.justify;//中文排版左右对齐
            alignTypeAssigned = true;
        }
        lineWidth += word.getWordWidth();
    }

    public void setLineWidth(float width) {
        lineWidth = width;
    }

    public void addSpanned(Spanned content, int location, float wordWidth) {
        boolean symbol = false;
        boolean blankSpace = false;
        if (content != null && content.length() == 1) {
            char c = content.charAt(0);
            symbol = textHelper.isSymbol(c);
            blankSpace = (c == ' ');
        }
        addWord(new Word(lineWidth, y, location, content, wordWidth, paint, symbol, blankSpace, tableDataSpan != null));
    }

    public void adjustWordPosition(float maxLineWidth) {
        if (this.size() == 0) {
            return;
        }
        //处理希伯来语排版
        int i = 0;
        int firstHebrew = -1;
        int lastHebrew = -1;
        for (Word word : this) {
            if (word.isHebrewLetter() || (firstHebrew >= 0 && word.isBlankSpace())) {
                if(firstHebrew < 0) {
                    firstHebrew = i;
                }
                lastHebrew = i;
            } else {
                if(firstHebrew >= 0) {
                    reverse(firstHebrew, lastHebrew);
                    firstHebrew = -1;
                    lastHebrew = -1;
                }
            }
            i++;
        }
        if (firstHebrew >= 0) {
            reverse(firstHebrew, lastHebrew);
        }

        if (alignType == CssElement.TextAlignType.left) {
            return;
        }
        if (alignType == CssElement.TextAlignType.center) {
            moveToCenter(maxLineWidth);
            return;
        }

        float firstWordOffset = 0;
        float extraSpace = 0;
        if (!positionAdjusted) {
            positionAdjusted = true;
            float englishSpaceWidth = textHelper.getEnglishSpaceWidth();
            if (isFirstWordSpace()) {
                firstWordOffset = englishSpaceWidth;
                this.getFirst().addRightPosition(-englishSpaceWidth);
                extraSpace += englishSpaceWidth;
            }
            if (isLastWordSpace()) {
                this.getLast().addRightPosition(englishSpaceWidth);
                extraSpace += englishSpaceWidth;
            }
        }
        if (nonBlankOrSymbolWordCount <= 1) {
            return;
        }
        float spaceWidth = maxLineWidth - this.getLineWidth() + extraSpace;// - 10.0f;
        if (spaceWidth <= 0.0f) {
            return;
        }
        float space = spaceWidth / (nonBlankOrSymbolWordCount - 1);
        float previousChangeSpace = 0;
        boolean hasNonBlankSpaceWord = false;
        for (Word word : this) {
            if (word.isBlankSpace() || word.isSymbol()) {
                if (previousChangeSpace != 0) {
                    word.addRightPosition(previousChangeSpace);
                }
                continue;
            }
            if (!hasNonBlankSpaceWord) {
                hasNonBlankSpaceWord = true;
                previousChangeSpace = -firstWordOffset;
            } else {
                previousChangeSpace += space;
            }
            word.addRightPosition(previousChangeSpace);
        }
        lineWidth += (spaceWidth - firstWordOffset);
    }

    private void reverse(int start, int end) {
        int size = end - start +1;
        if(size > 1) {
            LinkedList<Word> tmpList = new LinkedList<>();
            float xx = get(start).getX();
            for(int i=end;i >= start;i--) {
                Word oldWord = get(i);
                Word newWord = new Word(oldWord);
                newWord.setX(xx);
                xx += newWord.getWordWidth();
                tmpList.add(newWord);
            }
            for(int i=0; i<size; i++) {
                set(start+i, tmpList.get(i));
            }
        }
    }

    private boolean isFirstWordSpace() {
        if (this.size() > 0) {
            Word firstWord = this.getFirst();
            return firstWord.isBlankSpace();
        }
        return false;
    }

    private boolean isLastWordSpace() {
        if (this.size() > 0) {
            Word lastWord = this.getLast();
            return lastWord.isBlankSpace();
        }
        return false;
    }

    public void setMargin(float margin) {
        if (lineWidth == 0 && margin > 0) {
            lineWidth = margin;
        }
    }

    public Word removeWordAtIndex(int index) {
        Word word = this.remove(index);
        if (!(word.isBlankSpace() || word.isSymbol())) {
            nonBlankOrSymbolWordCount--;
        }
        float wordWidth = textHelper.getWordWidth(word.getWord()) * sizeChange;
        lineWidth -= wordWidth;
        for (int i = index + 1; i < this.size(); i++) {
            this.get(i).addRightPosition(-wordWidth);
        }
        return word;
    }

    public void removeLastWord() {
        Word word;
        if (this.size() > 0) {
            word = this.removeLast();
            float wordLength = textHelper.getWordWidth(word.getWord()) * sizeChange;
            lineWidth -= wordLength;
            if (!(word.isBlankSpace() || word.isSymbol())) {
                nonBlankOrSymbolWordCount--;
            }
        }
    }

    public void deleteStartTableLine() {
        tableLineStart = false;
    }

    public void deleteEndTableLine() {
        tableLineEnd = false;
    }

    public boolean needDrawText() {
        return !isImageLine();
    }

    public void drawBackground(Canvas canvas, int width, float offsetX, float offsetY) {
        if (isImageLine()) {
            Bitmap bitmap = imageSpan.getBitmap();
            lineWidth = width;
            width -= offsetX;
            if(bitmap.getWidth() < width){
                offsetX += (width-bitmap.getWidth())/2; //居中显示
            }
            canvas.drawBitmap(imageSpan.getBitmap(), offsetX, offsetY, paint);
        } else if (blockSpan != null) {
            blockSpan.drawBlock(canvas, paint, width, lineHeight, offsetX, offsetY, blockFirstLine, blockLastLine);
        } else if (tableDataSpan != null) {
            drawTableBack(canvas, lineHeight, offsetX, offsetY, width);
        }
    }

    private void drawTableBack(Canvas canvas, float height, float startX, float offsetY, int width) {
        int oldColor = paint.getColor();
        float oldWidth = paint.getStrokeWidth();

        int fillColor = tableDataSpan.getBackgroundColor();
        if (fillColor != -1) {
            paint.setColor(fillColor);
            canvas.drawRect(startX, offsetY, width, height + offsetY, paint);
        }

        paint.setStrokeWidth(1.0f);
        paint.setColor(BookParameters.getTextGrayColor());

        if (tableLineStart) {
            canvas.drawLine(startX, offsetY, width, offsetY, paint);
        }
        if (tableLineEnd) {
            canvas.drawLine(startX, height + offsetY, width, height + offsetY, paint);
        }

        for (int i = 0; i <= tableDataSpan.getMaxRow() + 1; i++) {
            float x = (width - startX) * i / (tableDataSpan.getMaxRow() + 1);
            canvas.drawLine(x + startX, offsetY, x + startX, height + offsetY, paint);
        }

        paint.setColor(oldColor);
        paint.setStrokeWidth(oldWidth);
    }
}

