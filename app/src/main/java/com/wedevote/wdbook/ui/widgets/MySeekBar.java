package com.wedevote.wdbook.ui.widgets;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Path;
import android.util.AttributeSet;

import androidx.appcompat.widget.AppCompatSeekBar;

/***
 * @date 创建时间 2020/7/20 15:00
 * <AUTHOR> <PERSON><PERSON>
 * @description
 */
public class MySeekBar extends AppCompatSeekBar {

    private final int mMax;
    private Paint mPaint;
    private final int radius = 30;

    public MySeekBar(Context context) {
        this(context, null);
    }

    public MySeekBar(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public MySeekBar(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        mMax = getMax();
        mPaint = new Paint();
        mPaint.setColor(Color.parseColor("#9E9E9E"));
        mPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        //设置抗锯齿
        mPaint.setAntiAlias(true);
        //设置防抖动
        mPaint.setDither(true);
        mPaint.setStyle(Paint.Style.FILL);
        //mPaint.setStrokeWidth(5);
    }

    @Override
    protected void onDraw(Canvas canvas) {
        drawGraduation(canvas);
        super.onDraw(canvas);

        //canvas.restore();
    }

    private void drawGraduation(Canvas canvas) {
        //绘制中间的线条
        float centerY = canvas.getHeight() / 2;
        Path path = new Path();
        path.moveTo(radius, centerY);

        path.addRect(radius, centerY - 3, canvas.getWidth() - radius, centerY, Path.Direction.CCW);

        //等分长度
        float length = (canvas.getWidth() - 2 * radius) / mMax;
        //绘制刻度
        for (int i = 0; i <= mMax; i++) {
            float startX = i * length + radius;
            path.moveTo(startX, centerY);
            if (i == 0) {
                path.addRect(startX, centerY - radius / 2, startX + 2, centerY + radius / 2, Path.Direction.CCW);
            } else if (i == mMax) {
                path.addRect(canvas.getWidth() - 2 - radius, centerY - radius / 2, canvas.getWidth() - radius, centerY + radius / 2, Path.Direction.CCW);
            } else {
                path.addRect(startX, centerY - radius / 2, startX + 2, centerY + radius / 2, Path.Direction.CCW);
            }
        }

        float progress = getProgress() * length + radius;

        if (getProgress() < 1) {
            path.moveTo(progress, centerY);
        } else if (progress >= mMax) {
            path.moveTo(progress, centerY);
        }
        path.moveTo(progress, centerY);
        path.addCircle(progress, centerY, radius, Path.Direction.CCW);

        path.close();
        canvas.drawPath(path, mPaint);
    }
}
