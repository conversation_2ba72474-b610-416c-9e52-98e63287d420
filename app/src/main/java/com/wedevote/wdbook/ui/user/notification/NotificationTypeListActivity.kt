package com.wedevote.wdbook.ui.user.notification

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import android.view.ViewGroup
import com.aquila.lib.layout.SmartRefreshLayout
import com.aquila.lib.layout.api.RefreshLayout
import com.aquila.lib.layout.listener.OnLoadMoreListener
import com.aquila.lib.layout.listener.OnRefreshListener
import com.aquila.lib.widget.view.CustomRecyclerView
import com.wedevote.wdbook.R
import com.wedevote.wdbook.R.id
import com.wedevote.wdbook.base.ExceptionHandler
import com.wedevote.wdbook.base.RootActivity
import com.wedevote.wdbook.base.SDKSingleton
import com.wedevote.wdbook.entity.notification.NotificationType
import com.wedevote.wdbook.ui.widgets.CommTopTitleLayout
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch

/***
 * @date 创建时间 2022/5/16 16:30
 * <AUTHOR> W.YuLong
 * @description 点击消息类型进入后的查看UI
 */
class NotificationTypeListActivity : RootActivity(), OnRefreshListener, OnLoadMoreListener {

    lateinit var topTitleLayout: CommTopTitleLayout
    lateinit var refreshLayout: SmartRefreshLayout
    lateinit var dataRecyclerView: CustomRecyclerView
    lateinit var emptyLayout: ViewGroup


    var notificationType = NotificationType.PLATFORM.type

    private var offset = 0L
    private var limit = 10L
    lateinit var messageAdapter: NotificationMessageAdapter

    companion object {
        fun gotoTypeListActivity(context: Context, title: String, type: Int) {
            val intent = Intent(context, NotificationTypeListActivity::class.java)
            intent.putExtra("title", title)
            intent.putExtra("type", type)
            context.startActivity(intent)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_notification_type_list_layout)

        initViewFromXML()
        notificationType = intent.getIntExtra("type", NotificationType.PLATFORM.type)
        val title = intent.getStringExtra("title")

        refreshLayout.setOnRefreshListener(this).setOnLoadMoreListener(this)

        topTitleLayout.setTitle(title)
        messageAdapter = NotificationMessageAdapter()
        dataRecyclerView.adapter = messageAdapter

        onRefresh(refreshLayout)
    }

    private fun initViewFromXML() {
        topTitleLayout = findViewById(id.notification_type_top_title_Layout)
        refreshLayout = findViewById(id.notification_type_RefreshLayout)
        dataRecyclerView = findViewById(id.notification_type_data_RecyclerView)
        emptyLayout = findViewById(id.notification_type_empty_container_Layout)

    }

    override fun onRefresh(layout: RefreshLayout?) {
        MainScope().launch(ExceptionHandler.coroutineExceptionHandler) {
            SDKSingleton.syncBl.syncNotificationDataByType(notificationType)
            offset = 0
            messageAdapter.clearDataList()
            onLoadMore(layout!!)
        }
    }

    override fun onLoadMore(layout: RefreshLayout) {
        initMessageDataUI()
        refreshLayout.finishLoadMoreAndRefresh()
    }

    private fun initMessageDataUI() {
        val dataList = SDKSingleton.userBl.loadTypeNotificationListFromDB(notificationType, offset, limit)
        if (!dataList.isNullOrEmpty()) {
            messageAdapter.addDataList(dataList)
            offset += dataList!!.size
            refreshLayout.isEnableLoadMore = dataList.size >= limit
    
            SDKSingleton.userBl.makeNotificationReadStatusByType(notificationType)
        } else {
            refreshLayout.isEnableLoadMore = false
        }

        if (messageAdapter.itemCount == 0) {
            emptyLayout.visibility = View.VISIBLE
            dataRecyclerView.visibility = View.GONE
        } else {
            emptyLayout.visibility = View.GONE
            dataRecyclerView.visibility = View.VISIBLE
        }
    
    }


}