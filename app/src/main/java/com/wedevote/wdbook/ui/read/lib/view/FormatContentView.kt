package com.wedevote.wdbook.ui.read.lib.view

import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.PointF
import android.graphics.RectF
import android.util.AttributeSet
import android.widget.LinearLayout
import com.aquila.lib.tools.util.ToastUtil
import com.wedevote.wdbook.R
import com.wedevote.wdbook.entity.NoteEntity
import com.wedevote.wdbook.tools.util.parseColor
import com.wedevote.wdbook.ui.read.OnCancelHighlightCallback
import com.wedevote.wdbook.ui.read.lib.ContentUtils
import com.wedevote.wdbook.ui.read.lib.BookParameters
import com.wedevote.wdbook.ui.read.lib.EPubBook
import kotlin.math.max

/***
 *@date 重构时间 2020/12/3 11:05
 *<AUTHOR> W.YuLong
 *@description
 */
class FormatContentView(context: Context, attrs: AttributeSet? = null) :
    LinearLayout(context, attrs) {
    private val selectPointStart = PointF()
    private val selectPointEnd = PointF()

    private var margin = ContentUtils.contentHorizonMargin
    private var offset = -1
    private var size = -1

    private var wordSelectionData = WordSelectionData()
    private var selectionData = WordSelectionData()

    private val selectDiameter = 30f
    var textPageList: TextPageList? = null
    private val selectionPaint = Paint(Paint.ANTI_ALIAS_FLAG)
    private var noteList: List<NoteEntity>? = null
    private var hideNoteFlag = true
    private var notePointMap = HashMap<PointF, NoteEntity>()
    private var minRadius = 0f

    var highlightColor = Color.parseColor("#44005AFF")
    var isShowFirstSelectedDot = true
    var isShowLastSelectedDot = true

    fun initNoteEntityList(list: List<NoteEntity>, hideNoteFlag: Boolean) {
        noteList = list
        this.hideNoteFlag = hideNoteFlag
        invalidate()
    }

    /*判断是否点击的高亮区域*/
    fun getTouchNoteData(touchX: Float, touchY: Float): NoteEntity? {
        if (noteList.isNullOrEmpty()) {
            return null
        }
        var lineIndex = getLinePosition(touchY)
        if (lineIndex < 0) {
            return null
        }

        var note: NoteEntity? = null
        var lineList = textPageList!!.get(lineIndex)
        val x = touchX - margin
        for (word in lineList) {
            if (x >= word.x && x <= word.x + word.wordWidth) {
                for (noteBean in noteList!!) {
                    if (word.location >= noteBean.wordStartOffset && word.location <= noteBean.wordEndOffset && !noteBean.isSearchHighlight) {
                        note = noteBean
                    }
                }
                break
            }
        }
        return note
    }

    override fun dispatchDraw(canvas: Canvas) {
        if (textPageList != null) {
            drawBackground(canvas)
        }
        notePointMap.clear()
        if (!noteList.isNullOrEmpty()) {
            var currentNoteY = 0.0f
            for (note in noteList!!) {
                currentNoteY = drawBookmarkBg(canvas, note, currentNoteY)
            }
        }
        drawSelection(canvas)
        super.dispatchDraw(canvas)
    }

    /*绘制之前保存的高亮UI*/
    fun drawBookmarkBg(canvas: Canvas, noteEntity: NoteEntity, previousNoteY: Float): Float {
        val start = max(offset, 0)
        val end = start + if (offset >= 0) (size - 1) else (textPageList!!.size - 1)

        var noteSelectionData = WordSelectionData()
        var finishStartWord = false
        var finishEndWord = false
        
        // 调整可能无法匹配的RTL文本起始和结束位置
        var adjustedStartOffset = noteEntity.wordStartOffset
        var adjustedEndOffset = noteEntity.wordEndOffset
        
        // 检查是否需要调整
        val originalText = getTextFromPosition(adjustedStartOffset, adjustedEndOffset)
        if (originalText.isNullOrEmpty()) {
            // 步骤1: 向前调整起始位置，找到希伯来文的开头
            var validStartOffset = adjustedStartOffset
            var startAdjusted = false
            
            for (i in 1..10) {
                if (adjustedStartOffset - i > 0) {
                    // 检查前一个字符
                    val prevCharText = getTextFromPosition(adjustedStartOffset - i, adjustedStartOffset - i + 1)
                    val currText = getTextFromPosition(adjustedStartOffset - i, adjustedEndOffset)
                    
                    // 判断当前字符是否为希伯来文
                    if (!prevCharText.isNullOrEmpty() && EPubBook.startsWithHebrew(currText)) {
                        validStartOffset = adjustedStartOffset - i
                        startAdjusted = true
                        // 如果前一个字符不是希伯来文或为空，则找到了开头
                        val prevText = if (validStartOffset - 1 > 0) getTextFromPosition(validStartOffset - 1, validStartOffset) else ""
                        if (prevText.isNullOrEmpty() || !EPubBook.endsWithHebrew(prevText)) {
                            break
                        }
                    }
                } else {
                    break // 起始位置不能为负数
                }
            }
            
            // 步骤2: 向后调整结束位置，找到希伯来文的结尾
            var validEndOffset = adjustedEndOffset
            var endAdjusted = false
            
            for (i in 1..10) {
                // 检查后一个字符
                val nextCharText = getTextFromPosition(adjustedEndOffset + i - 1, adjustedEndOffset + i)
                val currText = getTextFromPosition(validStartOffset, adjustedEndOffset + i)
                
                // 判断当前字符是否为希伯来文
                if (!nextCharText.isNullOrEmpty() && EPubBook.endsWithHebrew(currText)) {
                    validEndOffset = adjustedEndOffset + i
                    endAdjusted = true

                    // 如果后一个字符不是希伯来文或为空，则找到了结尾
                    val nextText = getTextFromPosition(validEndOffset, validEndOffset + 1)
                    if (nextText.isNullOrEmpty() || !EPubBook.startsWithHebrew(nextText)) {
                        break
                    }
                }
            }

            if (validStartOffset >= 2) {
                val precedingText = getTextFromPosition(validStartOffset - 2, validStartOffset)
                if (containsOnlyWhitespaceAndHebrewOrRTL(precedingText)) {
                    validStartOffset -= 2
                } else if (validStartOffset >= 1) {
                    val precedingChar = getTextFromPosition(validStartOffset - 1, validStartOffset)
                    if (precedingChar.isBlank()) {
                        validStartOffset -= 1
                    }
                }
            }

            // 检查结尾的两个字符
            val trailingText = getTextFromPosition(validEndOffset, validEndOffset + 2)
            if (containsOnlyWhitespaceAndHebrewOrRTL(trailingText)) {
                validEndOffset += 2
            } else {
                val trailingChar = getTextFromPosition(validEndOffset, validEndOffset + 1)
                if (trailingChar.isBlank() && trailingChar.isNotEmpty()) {
                    validEndOffset += 1
                }
            }

            // 步骤3: 检查调整后的文本是否有效，且确实包含希伯来文
            val adjustedText = getTextFromPosition(validStartOffset, validEndOffset)
            if (startAdjusted || endAdjusted || containsOnlyWhitespaceAndHebrewOrRTL(adjustedText)) {
                if (!adjustedText.isNullOrEmpty() &&
                    (EPubBook.containsHebrew(adjustedText) || EPubBook.containsRTLControls(adjustedText))) {
                    // 应用调整后的位置
                    adjustedStartOffset = validStartOffset
                    adjustedEndOffset = validEndOffset
                } else {
                    adjustedStartOffset = noteEntity.wordStartOffset
                    adjustedEndOffset = noteEntity.wordEndOffset
                }
            } else {
                adjustedStartOffset = noteEntity.wordStartOffset
                adjustedEndOffset = noteEntity.wordEndOffset
            }
        }
        
        // 使用调整后的起始点和结束点
        for (lineIndex in start..end) {
            var line = textPageList!![lineIndex]
            if (line.isNullOrEmpty()) {
                continue
            }
            if (!finishStartWord) {
                if (!line.isNullOrEmpty() && adjustedStartOffset > line[line.size - 1].location) {
                    continue
                } else {
                    // 这个是计算标记的第一个字符所在当前页的行数和位置
                    for (wordIndex in line.indices) {
                        if (adjustedStartOffset <= line[wordIndex].location) {
                            noteSelectionData.startLineIndex = lineIndex
                            noteSelectionData.startWord = wordIndex
                            finishStartWord = true
                            break
                        }
                    }
                }
            }

            if (!finishEndWord) {
                // 这个是计算标记的最后一个字符所在当前页的行数和位置
                val wordLocation = line[line.size - 1].location
                if (wordLocation < 0) {
                    finishEndWord = true
                    break
                } else if (adjustedEndOffset > wordLocation) {
                    // 先暂时记下位置
                    noteSelectionData.endLineIndex = lineIndex
                    noteSelectionData.endWord = line.size
                    continue
                } else {
                    for (wordIndex in line.indices) {
                        if (adjustedEndOffset <= line[wordIndex].location) {
                            // 最终确定位置
                            noteSelectionData.endLineIndex = lineIndex
                            noteSelectionData.endWord = wordIndex
                            finishEndWord = true
                            break
                        }
                    }
                }
            } else {
                break
            }
        } // End For

        // 如果标记的结束位置超过当前页,高亮到最后一个字符
        if (!finishEndWord) {
            noteSelectionData.endLineIndex = end
            noteSelectionData.endWord = textPageList!![end].size
        }

        if (noteSelectionData.startLineIndex == noteSelectionData.endLineIndex && noteSelectionData.startWord == noteSelectionData.endWord) {
            return previousNoteY
        }

        var paint = Paint(Paint.ANTI_ALIAS_FLAG)
        // 是否为搜索高亮
        if (noteEntity.isSearchHighlight) {
            paint.color = BookParameters.searchHighlightColor
        } else {
            paint.color = parseColor(noteEntity.highlightColorType)
        }

        drawHighlightBlock(
            canvas,
            noteSelectionData,
            paint,
            noteEntity.markStyle == NoteEntity.STYLE_HIGHLIGHT
        )

        if (!noteEntity.noteText.isNullOrEmpty() && !hideNoteFlag) {
            val flagStartLine = noteSelectionData.startLineIndex
            return drawNoteFlag(canvas, flagStartLine, paint, previousNoteY, noteEntity)
        } else {
            return previousNoteY
        }
    }

    private fun drawNoteFlag(
        canvas: Canvas,
        startLine: Int,
        paint: Paint,
        previousNoteY: Float,
        note: NoteEntity
    ): Float {
        if (note.markStyle == NoteEntity.STYLE_HIGHLIGHT) {
            return previousNoteY
        }

        val noteFlagMargin = margin * 0.1f
        var rectF = RectF(0F, 0F, 0F, 0F)
        rectF.top = maxOf(getLineTopY(startLine), previousNoteY + noteFlagMargin)
        val flagHeight = margin - noteFlagMargin * 2
        rectF.left = right - margin + noteFlagMargin
        paint.style = Paint.Style.FILL

        rectF.right = rectF.left + flagHeight
        rectF.bottom = rectF.top + flagHeight

        canvas.drawRect(rectF, paint)

        minRadius = flagHeight * flagHeight
        notePointMap[PointF(rectF.centerX(), rectF.centerY())] = note
        return rectF.bottom
    }

    /*是否点击了笔记标记区域*/
    fun getTouchNoteFlagData(x: Float, y: Float): NoteEntity? {
        var note: NoteEntity? = null
        var currentRadius = minRadius
        if (!notePointMap.isNullOrEmpty()) {
            for (entry in notePointMap.entries) {
                val r = (entry.key.x - x) * (entry.key.x - x) + (entry.key.y - y) * (entry.key.y - y)
                if (r < currentRadius) { // 寻找最优解
                    note = entry.value
                    currentRadius = r
                }
            }
        }
        return note
    }

    /*绘制高亮的背景*/
    private fun drawHighlightBlock(
        canvas: Canvas,
        selectionData: WordSelectionData,
        paint: Paint,
        isHighlight: Boolean = true
    ): Boolean {
        var flag = false
        if (selectionData.startLineIndex < 0 || selectionData.endLineIndex < 0) {
            return flag
        }

        if (getLineBottomY(selectionData.endLineIndex) >= 0 && getLineTopY(selectionData.startLineIndex) <= height) {
            for (i in selectionData.startLineIndex..selectionData.endLineIndex) {
                if (i >= textPageList!!.size) {
                    break
                }
                val line = textPageList!![i]
                if (line.isEmpty()) {
                    continue
                }

                if (i == selectionData.startLineIndex && selectionData.startWord >= line.size) {
                    continue
                }

                var left =
                    if (i == selectionData.startLineIndex && selectionData.startWord >= 0 && selectionData.startWord < line.size) {
                        line[selectionData.startWord].x
                    } else {
                        Math.max(0f, line.first.x)
                    }
                var top = getLineTopY(i)
                var right =
                    if (i == selectionData.endLineIndex && selectionData.endWord < line.size) {
                        line[selectionData.endWord].x
                    } else {
                        Math.min(ContentUtils.contentWidth, line.last.x + line.last.wordWidth)
                    }
                var bottom = getLineBottomY(i)
                if (isHighlight) {
                    var rect = RectF(left, top, right, bottom)
                    rect.offset(margin, 0f)
                    canvas.drawRect(rect, paint)
                } else {
                    paint.strokeWidth = 5f
                    canvas.drawLine(left + margin, bottom, right + margin, bottom, paint)
                }
            }
            flag = true
        }
        return flag
    }

    private fun drawSelection(canvas: Canvas) {
        if (!wordSelectionData.hasSelected() || textPageList == null) {
            return
        }
        wordSelectionData.correctWordData(selectionData)

        selectionPaint.color = highlightColor
        if (drawHighlightBlock(canvas, selectionData, selectionPaint, true)) {
            selectionPaint.color = Color.RED
            if (isShowFirstSelectedDot) {
                drawSelectionLine(
                    canvas,
                    selectionPaint,
                    selectionData.startLineIndex,
                    selectionData.startWord,
                    margin,
                    true
                )
            }

            if (isShowLastSelectedDot) {
                drawSelectionLine(
                    canvas,
                    selectionPaint,
                    selectionData.endLineIndex,
                    selectionData.endWord,
                    margin,
                    false
                )
            }
        }
    }

    private fun drawSelectionLine(
        canvas: Canvas,
        paint: Paint,
        linePos: Int,
        wordPos: Int,
        offsetX: Float,
        start: Boolean
    ) {
        val line = textPageList!![linePos]
        var x: Float
        x = if (line.isEmpty()) {
            0.0f
        } else if (wordPos < line.size) {
            line[wordPos].x
        } else {
            line.last.x + line.last.wordWidth
        }
        val startY = getLineTopY(linePos)
        val endY = getLineBottomY(linePos)
        x += offsetX
        canvas.drawLine(x, startY, x, endY, paint)
        canvas.drawCircle(x, endY + selectDiameter / 2, selectDiameter / 2, paint)
        if (start) {
            selectPointStart.x = x
            selectPointStart.y = endY + selectDiameter / 2
        } else {
            selectPointEnd.x = x
            selectPointEnd.y = endY + selectDiameter / 2
        }
    }

    fun getDragPosition(x: Float, y: Float): Int {
        val touchArea = selectDiameter * 2
        if (Math.abs(x - selectPointStart.x) < touchArea && Math.abs(y - selectPointStart.y) < touchArea) {
            return 1
        } else if (Math.abs(x - selectPointEnd.x) < touchArea && Math.abs(y - selectPointEnd.y) < touchArea) {
            return 2
        }
        return 0
    }

    private fun getTouchLineBottomY(touchY: Float): Float {
        return textPageList!!.findTouchLineBottom()
    }

    fun getLinePosition(y: Float): Int {
        val lineIndex = textPageList!!.findLine(y + listViewScrollY)
        if (lineIndex < offset || lineIndex >= offset + size) {
            return -1
        } else {
            return lineIndex
        }
    }

    fun getLineBottomY(i: Int): Float {
        return if (i < 0 || i >= textPageList!!.size) {
            0f
        } else textPageList!!.getLinePosition(i) - listViewScrollY
    }

    fun getLineTopY(i: Int): Float {
        return if (i == 0 || i > textPageList!!.size) {
            0.0f
        } else {
            textPageList!!.getLinePosition(i - 1) - listViewScrollY
        }
    }

    private val listViewScrollY: Float
        private get() {
            if (getChildAt(0) == null) {
                return 0f
            }
            return if (offset > 0) {
                textPageList!!.getLinePosition(offset - 1)
            } else {
                0f
            }
        }

    fun getSelectNoteEntity(): NoteEntity? {
        val selectedPosition = getSelectedWordsBoundary()
        if (selectedPosition.size == 2) {
            val start = selectedPosition[0]
            val end = selectedPosition[1]
            for (note in noteList!!) {
                if (start >= note.wordStartOffset && end <= note.wordEndOffset) {
                    return note
                }
            }
        }
        return null
    }

    fun checkSelectContent(): Boolean {
        if (wordSelectionData.hasSelected()) {
            wordSelectionData.correctWordData(selectionData)
            val endLineIndex = selectionData.endLineIndex
            val startLineIndex = selectionData.startLineIndex
            var emptyContent = true

            for (i in startLineIndex..endLineIndex) {
                val startWordIndex = if (i == startLineIndex) { selectionData.startWord } else { 0 }
                val endWordIndex = if (i == endLineIndex) { selectionData.endWord } else { textPageList!![i].size }
                for (j in startWordIndex until endWordIndex) {
                    if (j >= textPageList!![i].size) {
                        break
                    }
                    if (textPageList!![i][j].isInTable || textPageList!![i][j].location < 0) {
                        ToastUtil.showToastShort(R.string.can_not_select_table)
                        return false
                    }

                    if (!textPageList!![i][j].isSpecialElement) {
                        emptyContent = false
                    }
                }
            }

            if (emptyContent) {
                ToastUtil.showToastShort(R.string.no_empty_note)
                return false
            }
        }
        return true
    }

    /**
     * 判断当前选区是否只包含特殊元素
     */
    fun isSelectedOnlySpecial(): Boolean {
        if (!wordSelectionData.hasSelected() || textPageList == null) {
            return false
        }
        wordSelectionData.correctWordData(selectionData)
        val startLineIndex = selectionData.startLineIndex
        val endLineIndex = selectionData.endLineIndex
        for (i in startLineIndex..endLineIndex) {
            val startWordIndex = if (i == startLineIndex) selectionData.startWord else 0
            val endWordIndex = if (i == endLineIndex) selectionData.endWord else textPageList!![i].size
            for (j in startWordIndex until endWordIndex) {
                if (j >= textPageList!![i].size) break
                if (!textPageList!![i][j].isSpecialElement) {
                    return false
                }
            }
        }
        return true
    }


    /*获取选中的文本*/
    fun getSelectedWordsBoundary(): ArrayList<Int> {
        var list = ArrayList<Int>()
        if (wordSelectionData.hasSelected()) {
            wordSelectionData.correctWordData(selectionData)
            var endLineIndex = selectionData.endLineIndex
            var endWordIndex = selectionData.endWord

            var startLineIndex = selectionData.startLineIndex
            var startWordIndex = selectionData.startWord
            var startWord = 0
            for (i in startLineIndex..endLineIndex) {
                if (startWordIndex < textPageList!![i].size) {
                    startLineIndex = i
                    startWord = textPageList!![i][startWordIndex].location
                    break
                }
                startWordIndex = 0
            }

            for (i in endLineIndex downTo startLineIndex) {
                if (!textPageList!![i].isEmpty()) {
                    endWordIndex = if (endLineIndex == i) {
                        selectionData.endWord
                    } else {
                        textPageList!![i].size
                    }
                    endLineIndex = i
                    break
                }
            }

            val endLine = textPageList!![endLineIndex]
            var endWord = if (endWordIndex >= endLine.size) {
                endLine[endLine.size - 1].location + 1
            } else {
                endLine[endWordIndex].location
            }
            list.add(startWord)
            list.add(endWord)
        }
        return list
    }

    private fun drawBackground(canvas: Canvas) {
        val start = Math.max(offset, 0)
        val end = start + if (offset >= 0) size else textPageList!!.size
        for (i in start until end) {
            var offsetY =
                (if (i == 0) 0f else textPageList!!.getLinePosition(i - 1)) - listViewScrollY
            if (offsetY >= 0 && offsetY < height) {
                textPageList!![i].drawBackground(canvas, width - margin.toInt(), margin, offsetY)
            }
        }
    }

    fun getFirstWordLocation(): Int {
        var location = -1
        val start = Math.max(offset, 0)
        val end = start + if (offset >= 0) size else textPageList!!.size
        for (i in start until end) {
            var offsetY =
                (if (i == 0) 0f else textPageList!!.getLinePosition(i - 1)) - listViewScrollY
            if (offsetY >= 0 && offsetY < height) {
                try {
                    location = textPageList!![i].get(0).location
                } catch (e: Exception) {
                }
                break
            }
        }
        return location
    }

    var onCancelHighlightCallback: OnCancelHighlightCallback? = null

    fun cancelSelection() {
        if (wordSelectionData.reset()) {
            invalidate()
            onCancelHighlightCallback?.onCancelHighlight()
        }
    }

    fun setSelectionPosition(line: Int, wordPosition: Int, isStart: Boolean) {
        if (isStart) {
            wordSelectionData.startLineIndex = line
            wordSelectionData.startWord = wordPosition
        } else {
            wordSelectionData.endLineIndex = line
            wordSelectionData.endWord = wordPosition
        }
        invalidate()
    }

    fun setPageParameters(wordPageList: TextPageList?, offset: Int, size: Int) {
        this.textPageList = wordPageList
        this.offset = offset
        this.size = size
    }

    fun getTextFromPosition(start: Int, end: Int): String {
        var sb = StringBuilder()
        for (line in textPageList!!) {
            for (word in line) {
                if (word.location >= end) {
                    return sb.toString()
                }
                if (word.location >= start) {
                    if (!word.isSpecialElement) {
                        sb.append(word.word)
                    }
                }
            }
        }
        return sb.toString()
    }

    fun getSelectedText(prevWord: Int): String {
        val sb = StringBuilder()
        var isStart = false

        if (wordSelectionData.hasSelected()) {
            wordSelectionData.correctWordData(selectionData)
            var startLine = 0
            var endLine = 0
            var includePrevPage = false

            if (getFirstWordLocation() <= prevWord) { // 向前跨页或者未跨页
                startLine = selectionData.startLineIndex
                endLine = textPageList!!.size - 1
            } else { // 向后跨页
                startLine = 0
                endLine = selectionData.endLineIndex
                includePrevPage = true
            }

            for (i in startLine..endLine) {
                val line = textPageList!![i]
                if (line.isEmpty() && isStart) {
                    sb.append("\n\n")
                } else {
                    var indexInLine = 0
                    for (word in line) {
                        if (includePrevPage) {
                            if (word.location < prevWord) {
                                indexInLine++
                                continue
                            }
                            if (i == selectionData.endLineIndex && indexInLine >= selectionData.endWord) {
                                break
                            }
                        } else {
                            if (word.location >= prevWord) {
                                break
                            }
                            if (i == selectionData.startLineIndex && indexInLine < selectionData.startWord) {
                                indexInLine++
                                continue
                            }
                        }

                        isStart = true
                        indexInLine++
                        if (!word.isSpecialElement) {
                            sb.append(word.word)
                        }
                    }
                }
            }
        }
        return sb.toString()
    }

    /**
     * 检查字符串是否只包含空白字符和希伯来文/RTL控制字符，
     * 并且至少包含一个希伯来文/RTL控制字符。
     */
    private fun containsOnlyWhitespaceAndHebrewOrRTL(text: String): Boolean {
        if (text.isNullOrEmpty()) {
            return false
        }
        var foundHebrewOrRTL =
            text.any { EPubBook.isHebrewChar(it) || it == '\u202A' || it == '\u202B' || it == '\u202C' }
        if (!foundHebrewOrRTL) return false // Must contain at least one Hebrew/RTL char

        // Check if all characters are either whitespace or Hebrew/RTL
        return text.all { it.isWhitespace() || EPubBook.isHebrewChar(it) || it == '\u202A' || it == '\u202B' || it == '\u202C' }
    }
}
