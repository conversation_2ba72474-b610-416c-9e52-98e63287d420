package com.wedevote.wdbook.ui.read.search

import android.text.SpannableStringBuilder
import android.text.style.ForegroundColorSpan
import android.view.ViewGroup
import android.widget.TextView
import com.aquila.lib.base.BaseRecycleAdapter
import com.aquila.lib.base.BaseViewHolder
import com.wedevote.wdbook.R
import com.wedevote.wdbook.entity.search.SearchResultChapter
import com.wedevote.wdbook.entity.search.SearchResultSentence
import androidx.core.content.ContextCompat
import com.wedevote.wdbook.base.ExceptionHandler
import com.wedevote.wdbook.base.SDKSingleton
import com.wedevote.wdbook.entity.NoteEntity
import com.wedevote.wdbook.tools.event.OnBookSearchJumpClickEvent
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus
import com.wedevote.wdbook.ui.read.lib.EPubBook

class BookSearchResultAdapter : BaseRecycleAdapter<Any, BaseViewHolder>() {

    var selectedPosition: Int = -1
    private var currentChapter: SearchResultChapter? = null

    companion object {
        const val TYPE_HEADER = 0
        const val TYPE_ITEM = 1
    }

    override fun getItemViewType(position: Int): Int {
        return when (dataList?.get(position)) {
            is SearchResultChapter -> TYPE_HEADER
            is SearchResultSentence -> TYPE_ITEM
            else -> TYPE_ITEM
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BaseViewHolder {
        return when (viewType) {
            TYPE_HEADER -> HeaderViewHolder(parent)
            else -> ItemViewHolder(parent)
        }
    }

    override fun onBindViewHolder(holder: BaseViewHolder, position: Int) {
        dataList?.get(position)?.let { item ->
            when (holder) {
                is HeaderViewHolder -> holder.bind(item as SearchResultChapter)
                is ItemViewHolder -> {
                    holder.itemView.isSelected = (position == selectedPosition)
                    holder.bind(item as SearchResultSentence)
                }
            }
        }
    }

    fun addSearchResults(chapters: List<SearchResultChapter>) {
        val flattenedList = mutableListOf<Any>()
        chapters.forEach { chapter ->
            chapter.fileName = EPubBook.replaceRTLEntities(chapter.fileName)

            flattenedList.add(chapter)

            chapter.sentences.forEach { sentence ->
                sentence.contentWithTag = EPubBook.replaceRTLEntities(sentence.contentWithTag)
                sentence.content = EPubBook.replaceRTLEntities(sentence.content)

                var mergedContent = sentence.contentWithTag
                var previousContent: String
                do {
                    previousContent = mergedContent
                    mergedContent = previousContent.replace("\\}\\}\\}\\{\\{\\{".toRegex(), "")
                } while (mergedContent != previousContent)
                sentence.contentWithTag = mergedContent
            }

            flattenedList.addAll(chapter.sentences)
        }
        super.addDataList(flattenedList)
    }

    inner class HeaderViewHolder(parent: ViewGroup) :
        BaseViewHolder(parent, R.layout.item_view_holder_book_search_header) {
        private val titleTextView: TextView = itemView.findViewById(R.id.search_section_title)

        fun bind(chapter: SearchResultChapter) {
            titleTextView.text = chapter.fileName
            currentChapter = chapter
        }

        override fun <T> initUIData(t: T) {
            if (t is SearchResultChapter) {
                bind(t)
            }
        }
    }

    inner class ItemViewHolder(parent: ViewGroup) :
        BaseViewHolder(parent, R.layout.item_view_holder_book_search_subitem) {
        private val contentTextView: TextView = itemView.findViewById(R.id.search_item_content)

        fun bind(sentence: SearchResultSentence) {
            val content = sentence.contentWithTag
            val regex = "\\{\\{\\{(.*?)\\}\\}\\}".toRegex()
            val spannable = SpannableStringBuilder()
            var lastIndex = 0
            for (match in regex.findAll(content)) {
                val startIndex = match.range.first
                val endIndex = match.range.last + 1
                spannable.append(content.substring(lastIndex, startIndex))
                val highlightText = match.groupValues[1]
                val spanStart = spannable.length
                spannable.append(highlightText)
                val spanEnd = spannable.length
                spannable.setSpan(
                    ForegroundColorSpan(ContextCompat.getColor(itemView.context, R.color.color_orange_FF8A00)),
                    spanStart, spanEnd,
                    SpannableStringBuilder.SPAN_EXCLUSIVE_EXCLUSIVE
                )
                lastIndex = endIndex
            }
            if (lastIndex < content.length) {
                spannable.append(content.substring(lastIndex))
            }
            contentTextView.text = spannable

            itemView.setOnClickListener { v ->
                <EMAIL> = adapterPosition
                <EMAIL>()

                val parentChapter = findParentChapter(sentence)
                if (parentChapter != null) {
                    val noteEntity = EPubBook.createNoteEntityFromSearchResult(
                        parentChapter,
                        sentence,
                        selectedPosition,
                        dataList,
                        itemCount
                    )

                    val activity = itemView.context as? BookSearchActivity
                    activity?.finishWithAnimation()

                    navigateToReadActivity(parentChapter.resourceId, noteEntity)

                }
            }
        }

        private fun findParentChapter(sentence: SearchResultSentence): SearchResultChapter? {
            dataList?.let { list ->
                var lastChapter: SearchResultChapter? = null

                for (item in list) {
                    if (item is SearchResultChapter) {
                        lastChapter = item
                    } else if (item is SearchResultSentence && item == sentence) {
                        return lastChapter
                    }
                }
            }
            return null
        }

        private fun navigateToReadActivity(resourceId: String, noteEntity: NoteEntity) {
            MainScope().launch(ExceptionHandler.getCoroutineExceptionHandler {}) {
                val downloadInfo = SDKSingleton.dbWrapBl.getResourceDownloadInfo(resourceId)
                if (downloadInfo != null) {
                    EventBus.getDefault().post(OnBookSearchJumpClickEvent(
                        noteEntity,
                        downloadInfo.resourceId,
                        downloadInfo.getActualFilePath(),
                        downloadInfo.fileId,
                        BookSearchManager.getInstance().getKeywords()
                    ))
                }
            }
        }

        override fun <T> initUIData(t: T) {
            if (t is SearchResultSentence) {
                bind(t)
            }
        }
    }
} 