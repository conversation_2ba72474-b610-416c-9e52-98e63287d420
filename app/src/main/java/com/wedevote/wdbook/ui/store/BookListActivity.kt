package com.wedevote.wdbook.ui.store

import android.os.Bundle
import com.aquila.lib.layout.SmartRefreshLayout
import com.aquila.lib.layout.api.RefreshLayout
import com.aquila.lib.layout.listener.OnLoadMoreListener
import com.aquila.lib.layout.listener.OnRefreshListener
import com.aquila.lib.tools.util.ToastUtil
import com.aquila.lib.widget.view.CustomRecyclerView
import com.wedevote.wdbook.R
import com.wedevote.wdbook.base.ExceptionHandler
import com.wedevote.wdbook.base.RootActivity
import com.wedevote.wdbook.base.SDKSingleton
import com.wedevote.wdbook.constants.AnalyticsConstants
import com.wedevote.wdbook.constants.AnalyticsConstants.LOG_V1_PARAM_CATEGORY_ID
import com.wedevote.wdbook.exception.SDKException
import com.wedevote.wdbook.tools.event.OnSuccessBuyEvent
import com.wedevote.wdbook.tools.util.AnalyticsUtils.logEvent
import com.wedevote.wdbook.tools.util.IntentConstants
import com.wedevote.wdbook.ui.home.microwidget.BookItemRecyclerAdapter
import com.wedevote.wdbook.ui.widgets.CommTopTitleLayout
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

/***
 * @date 创建时间 2020/5/12 18:38
 * <AUTHOR> W.YuLong
 * @description 书籍列表的界面
 */
class BookListActivity : RootActivity(), OnRefreshListener, OnLoadMoreListener {
    lateinit var topTitleLayout: CommTopTitleLayout
    lateinit var dataRecyclerView: CustomRecyclerView
    lateinit var refreshLayout: SmartRefreshLayout
    lateinit var dataAdapterItem: BookItemRecyclerAdapter

    var title: String = ""
    var type: Int = 0

    val defaultValue = -100L
    var categoryId: Long = defaultValue
    var categoryName: String? = ""
    var typeKey: String? = ""
    var lastPublishId: Long? = 0L
    var page: Int = 1

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_book_list_layout)
        topTitleLayout = findViewById(R.id.book_list_top_TitleLayout)
        dataRecyclerView = findViewById(R.id.book_list_data_RecyclerView)
        refreshLayout = findViewById(R.id.book_list_SmartRefreshLayout)

        dataAdapterItem = BookItemRecyclerAdapter()
        dataRecyclerView.adapter = dataAdapterItem

        refreshLayout.setOnRefreshListener(this).setOnLoadMoreListener(this)

        categoryName = intent.getStringExtra(IntentConstants.EXTRA_CategoryName)
        typeKey = intent.getStringExtra(IntentConstants.EXTRA_TypeKey)
        lastPublishId = intent.getStringExtra(IntentConstants.EXTRA_LastPublishId)?.toLong()
        categoryId = intent.getLongExtra(IntentConstants.EXTRA_CategoryId, defaultValue)
        if (categoryId != defaultValue) {
            logEvent(AnalyticsConstants.LOG_V1_PRODUCT_CATEGORY, LOG_V1_PARAM_CATEGORY_ID, categoryId.toString())
        }

        if (categoryId == defaultValue) {
            if (!typeKey.isNullOrEmpty()) {
                // 推荐商品列表
                topTitleLayout.setTitle(categoryName)
                getDataFromServer(page)
            } else {
                ToastUtil.showToastShort(R.string.not_send_widge_data)
                onBackPressed()
            }
        } else {
            topTitleLayout.setTitle(categoryName)
            getDataFromServer(page)
        }
    }

    override fun onRefresh(refreshLayout: RefreshLayout) {
        dataAdapterItem.dataList?.clear()
        page = 1
        getDataFromServer(page)
    }

    override fun onLoadMore(refreshLayout: RefreshLayout) {
        page++
        getDataFromServer(page)
    }

    private fun getDataFromServer(page: Int) {
        MainScope().launch(ExceptionHandler.getCoroutineExceptionHandler {
            if (it is SDKException) {
                ToastUtil.showToastShort(getString(R.string.get_list_data_failure))
            }
            refreshLayout.finishLoadMoreAndRefresh()
        }) {
            if (categoryId != defaultValue) {
                SDKSingleton.storeBl.getCategoryProductEntityList(categoryId, page).also { t ->
                    if (page == 1) {
                        dataAdapterItem.dataList = t.productList
                    } else {
                        dataAdapterItem.addDataList(t.productList)
                    }
                    refreshLayout.isEnableLoadMore = t.currentPage < t.totalPage
                }
            } else {
                typeKey?.let {
                    val suggestProductList = SDKSingleton.storeBl.getSuggestedProductList(it, lastPublishId ?: 0)
                    dataAdapterItem.dataList = suggestProductList.productList
                    refreshLayout.isEnableLoadMore = false
                }
            }
            refreshLayout.finishLoadMoreAndRefresh()
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onReceiveBuyResultEvent(event: OnSuccessBuyEvent) {
        dataAdapterItem.updateProductPurchaseStatus(event.productId)
    }
}
