package com.wedevote.wdbook.ui.read.lib;

import android.graphics.Canvas;
import android.graphics.DashPathEffect;
import android.graphics.Paint;
import android.text.Spanned;
import android.text.TextPaint;
import android.text.style.CharacterStyle;
import android.text.style.RelativeSizeSpan;
import android.text.style.StyleSpan;

import com.wedevote.wdbook.ui.read.lib.view.Word;


/**
 * Created by <PERSON> on 14/11/3.
 */
public class TextHelper {
    private static final String CHINESE_SYMBOL_CANNOT_LINE_END = "“‘（《";
    private static final String CHINESE_SYMBOL_CANNOT_LINE_BEGIN = "，。：”’、；！）？》";
    private static final String ENGLISH_SYMBOL_CANNOT_LINE_END = "\"'([<";
    private static final String ENGLISH_SYMBOL_CANNOT_LINE_BEGIN = ",.:!)?;]>";
    private static final String ENGLISH_SYMBOL = ",.:!;]\"'[";
    private static final String CHINESE_SYMBOL = "（，。：、；！）？";

    private float CHINESE_WORD_WIDTH = 0;
    private float CHINESE_SYMBOL_WIDTH = 0;
    private float ENGLISH_SPACE_WIDTH = 0;
    private float QUOTATION_WIDTH = 0;

    private static final int MAX_CACHE_CHAR = 128;
    private final float[] charWidthCache = new float[MAX_CACHE_CHAR];

    private final TextPaint paint;
    private final TextPaint stylePaint;

    public TextHelper(TextPaint paint) {
        this.paint = paint;
        stylePaint = new TextPaint(paint);
        CHINESE_WORD_WIDTH = paint.measureText("好") * .97f;
        CHINESE_SYMBOL_WIDTH = paint.measureText("，") * .95f;
        ENGLISH_SPACE_WIDTH = paint.measureText(" ");
        QUOTATION_WIDTH = paint.measureText("“");
    }


    public float getChineseWordWidth() {
        return CHINESE_WORD_WIDTH;
    }

    public float getEnglishSpaceWidth() {
        return ENGLISH_SPACE_WIDTH;
    }

    public boolean isSymbolCannotLineEnd(char c) {
        return isChineseSymbolCannotLineEnd(c) || isEnglishSymbolCannotLineEnd(c);
    }

    public boolean isSymbolCannotLineBegin(char c) {
        return isChineseSymbolCannotLineBegin(c) || isEnglishSymbolCannotLineBegin(c);
    }


    public boolean isSymbol(char c) {
        return isChineseSymbol(c) || isEnglishSymbol(c);
    }


    public boolean isChineseSymbol(char c) {
        return CHINESE_SYMBOL.indexOf(c) != -1;
    }

    private boolean isChineseCharacter(char c) {
        if (c == '　') {
            return true;
        }
        return c > 0x4e00 && c < 0x9fcc;
    }

    public boolean isEnglishSymbol(char c) {
        return ENGLISH_SYMBOL.indexOf(c) != -1;
    }

    public boolean isChineseSymbolCannotLineEnd(char c) {
        return CHINESE_SYMBOL_CANNOT_LINE_END.indexOf(c) != -1;
    }

    public boolean isChineseSymbolCannotLineBegin(char c) {
        return CHINESE_SYMBOL_CANNOT_LINE_BEGIN.indexOf(c) != -1;
    }

    private boolean isEnglishSymbolCannotLineEnd(char c) {
        return ENGLISH_SYMBOL_CANNOT_LINE_END.indexOf(c) != -1;
    }

    private boolean isEnglishSymbolCannotLineBegin(char c) {
        return ENGLISH_SYMBOL_CANNOT_LINE_BEGIN.indexOf(c) != -1;
    }

    public float getWordWidth(CharSequence word) {
        if (word.length() > 1) {
            float wordWidth = 0;
            for (int i = 0; i < word.length(); i++) {
                char c = word.charAt(i);
                if (c < MAX_CACHE_CHAR) {
                    wordWidth += getCacheWordWidth(c);
                } else {
                    wordWidth = paint.measureText(word.toString());
                    return wordWidth;
                }
            }
            return wordWidth;
        }
        if (word.length() == 1) {
            char c = word.charAt(0);
            if (c >= MAX_CACHE_CHAR) {
                if (c == '“' || c == '·' || c == '”' || c == '‘' || c == '’') {
                    return QUOTATION_WIDTH;
                } else if (isChineseSymbol(c)) {
                    return CHINESE_SYMBOL_WIDTH;
                } else if (isChineseCharacter(c)) {
                    return getChineseWordWidth();
                } else {
                    return paint.measureText(word.toString());
                }
            } else {
                return getCacheWordWidth(c);
            }
        }
        return 0;
    }

    private float getCacheWordWidth(char c) {
        if (charWidthCache[c] == 0) {
            charWidthCache[c] = paint.measureText(String.valueOf(c));
        }
        return charWidthCache[c];
    }

    public float getWordWidth(CharSequence word, RelativeSizeSpan relativeSizeSpan, StyleSpan styleSpan) {
        float wordWidth;
        if (styleSpan != null) {
            styleSpan.updateDrawState(stylePaint);
            wordWidth = stylePaint.measureText(word.toString());
        } else {
            wordWidth = getWordWidth(word);
        }
        if (relativeSizeSpan != null) {
            wordWidth *= relativeSizeSpan.getSizeChange();
        }
        return wordWidth;
    }

    public boolean canFirst(Word word) {
        CharSequence text = word.getWord();
        if (text.length() == 1) {
            char c = text.charAt(0);
            return !(isSymbolCannotLineBegin(c));
        }
        return true;
    }

    private static Paint dashedLinePaint = null;
    private static final float charAddLineWidthRate = 0.4f;
    private static final float charAddLineBottomRate = 0.2f;

    static public synchronized Paint getDashedUnderLinePaint(Paint textPaint, boolean force) {
        if (dashedLinePaint == null || force) {
            int yPadding = (int) Math.abs(textPaint.ascent() * charAddLineBottomRate);
            dashedLinePaint = new Paint();
            dashedLinePaint.setStyle(Paint.Style.STROKE);
            dashedLinePaint.setPathEffect(new DashPathEffect(new float[]{yPadding * charAddLineWidthRate, yPadding * charAddLineWidthRate}, 0));
        }
        return dashedLinePaint;
    }

    private static float drawUniformRun(Canvas canvas, Spanned text, int start, int end,
                                        float x, int y, TextPaint paint, TextPaint workPaint, boolean needWidth) {
        float ret = 0;
        CharacterStyle[] spans = text.getSpans(start, end, CharacterStyle.class);

        // XXX: This shouldn't be modifying paint, only workPaint.
        // However, the members belonging to TextPaint should have default
        // values anyway.  Better to ensure this in the Layout constructor.
        paint.bgColor = 0;
        paint.baselineShift = 0;
        workPaint.set(paint);

        if (spans.length > 0) {
            for (CharacterStyle span : spans) {
                span.updateDrawState(workPaint);
            }
        }

        if (needWidth) {
            ret = workPaint.measureText(text, start, end);
        }
        canvas.drawText(text.toString(), start, end, x, y + workPaint.baselineShift, workPaint);

        return ret;
    }

    public static float drawWord(Canvas canvas, CharSequence text, float x, int y, TextPaint paint, TextPaint workPaint) {
        int start = 0;
        int end = text.length();

        // fast path for unstyled text
        if (!(text instanceof Spanned)) {
            canvas.drawText(text, start, end, x, y, paint);
            return 0;
        }

        float ox = x;

        Spanned sp = (Spanned) text;
        Class<?> division = CharacterStyle.class;

        int next;
        for (int i = start; i < end; i = next) {
            next = sp.nextSpanTransition(i, end, division);
            x += drawUniformRun(canvas, sp, i, next, x, y, paint, workPaint, next != end);
        }

        return x - ox;
    }
}
