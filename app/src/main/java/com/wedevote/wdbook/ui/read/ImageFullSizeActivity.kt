package com.wedevote.wdbook.ui.read

import android.content.Intent
import android.graphics.BitmapFactory
import android.os.Bundle
import android.widget.ImageView
import androidx.fragment.app.FragmentActivity
import com.aquila.lib.tools.util.ScreenUtil.getScreenHeight
import com.aquila.lib.tools.util.ScreenUtil.getScreenWidth
import com.wedevote.wdbook.R
import com.wedevote.wdbook.base.RootActivity
import com.wedevote.wdbook.tools.util.DataPathUtil.getImageTempPath
import com.wedevote.wdbook.tools.util.dp2px

class ImageFullSizeActivity : RootActivity() {
    private lateinit var backView: ImageView
    private lateinit var fullScaleView: FullScaleView

    companion object {

        fun gotoFullImageActivity(activity: FragmentActivity) {
            val intent = Intent(activity, ImageFullSizeActivity::class.java)
            activity.startActivity(intent)
            activity.overridePendingTransition(R.anim.anim_scale_alpha_in, R.anim.anim_normal)
        }
    }

    public override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.full_image_activity)
        backView = findViewById(R.id.full_image_back_ImageView)
        fullScaleView = findViewById(R.id.full_size_ImageView)

        val imagePath = getImageTempPath()
        val options = BitmapFactory.Options()
        options.inScaled = false
        val bitmap = BitmapFactory.decodeFile(imagePath, options)
        if (bitmap != null) {
            fullScaleView.setImage(this, bitmap, getScreenWidth(), getScreenHeight() - dp2px(50))
        }

        backView.setOnClickListener {
            onBackPressed()
        }
    }

    override fun finish() {
        super.finish()
        overridePendingTransition(R.anim.anim_normal, R.anim.anim_scale_alpha_out)
    }
}
