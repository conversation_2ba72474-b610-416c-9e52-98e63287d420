package com.wedevote.wdbook.ui.read.lib.view

import android.content.Context
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import com.wedevote.wdbook.R

class TextPageManager(private val textPageList: TextPageList, val lineHeight: Float, private val pageFirstLineIndex: Int, val lineCount: Int) {
    private var totalHeight = 0
    var firstWord: Word? = null
    var lastWord: Word? = null
    fun initPageView(viewGroup: ViewGroup, pageHeight: Float) {
        viewGroup.removeAllViews()
        totalHeight = 0
        var isGetFirstWord = false
        for (position in 0 until lineCount) {
            val line = textPageList[position + pageFirstLineIndex]
            var height = line.getLineHeight(lineHeight).toInt()
            val convertView = getItemView(viewGroup.context, line, height)
            viewGroup.addView(convertView)
            if (!isGetFirstWord && !line.isEmpty()) {
                firstWord = line[0]
                isGetFirstWord = true
            }

            if (position == lineCount - 1) {
                var isGetLastWord = false
                for (lineIndex in lineCount - 1 downTo 0) {
                    if (isGetLastWord) {
                        break
                    }
                    val lastLine = textPageList[pageFirstLineIndex + lineIndex]
                    for (i in lastLine.indices.reversed()) {
                        if (!lastLine[i].word.isNullOrEmpty() && lastLine[i].location >= 0) {
                            lastWord = lastLine[i]
                            isGetLastWord = true
                            break
                        }
                    }
                }
            }
        }
        if (totalHeight < pageHeight.toInt()) {
            val height = pageHeight.toInt() - totalHeight
            val convertView = getItemView(viewGroup.context, TextLineList(), height)
            viewGroup.addView(convertView)
        }
    }

    private fun getItemView(context: Context, lineList: TextLineList, height: Int): View {
        val convertView = View.inflate(context, R.layout.book_text_listview_item, null)
        val lineView: TextLineView = convertView.findViewById(R.id.book_text_line_context_TextLineView)
        val layoutParams = lineView.layoutParams as LinearLayout.LayoutParams
        layoutParams.height = height
        totalHeight += height
        lineView.layoutParams = layoutParams
        lineView.setText(lineList)
        return convertView
    }

    fun getWordPosition(x: Float, linePosition: Int): Int {
        if (linePosition < 0 || linePosition >= textPageList.size) {
            return 0
        }
        val line = textPageList[linePosition]
        var index = 0
        for (w in line) {
            if (w.x > x) {
                break
            }
            index++
        }
        return index
    }

    fun getPressX(linePosition: Int, wordPosition: Int): Float {
        val line = textPageList[linePosition]
        if (line.size > wordPosition && wordPosition >= 0) {
            return line[wordPosition].x
        } else if (wordPosition >= line.size && !line.isEmpty()) {
            return line.last.x + line.last.wordWidth
        }
        return 0.0f
    }
}
