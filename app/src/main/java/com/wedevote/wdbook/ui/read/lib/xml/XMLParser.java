package com.wedevote.wdbook.ui.read.lib.xml;

import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.TextPaint;
import android.text.style.ForegroundColorSpan;
import android.util.Xml;

import com.wedevote.wdbook.ui.read.lib.TextHelper;
import com.wedevote.wdbook.ui.read.lib.css.CSSParser;
import com.wedevote.wdbook.ui.read.lib.css.CssElement;
import com.wedevote.wdbook.ui.read.lib.css.DataFormatParser;
import com.wedevote.wdbook.ui.read.lib.data.FormatTextRange;
import com.wedevote.wdbook.ui.read.lib.span.FootnoteSpan;
import com.wedevote.wdbook.ui.read.lib.span.PageInfoSpan;
import com.wedevote.wdbook.ui.read.lib.span.WDImageSpan;
import com.wedevote.wdbook.ui.read.lib.span.WDLeadingMarginSpan;
import com.wedevote.wdbook.ui.read.lib.span.WDLinkSpan;

import org.xmlpull.v1.XmlPullParser;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Stack;

public class XMLParser {
    private final int paragraphMargin;
    private final TextHelper textHelper;
    private final CSSParser cssParser;
    private final ArrayList<FormatTextRange> anchors = new ArrayList<>();
    private final ArrayList<FormatTextRange> footNoteList = new ArrayList<>();
    private int footNoteIndex = -1;
    private boolean inFootnote = false;
    private boolean inPageInfo = false;
    private String blockText = "";

    private final Stack<FormatTextRange> stack = new Stack<>();
    private static final int flags = Spanned.SPAN_EXCLUSIVE_EXCLUSIVE;

    public XMLParser(TextPaint textPaint) {
        cssParser = CSSParser.getInstance();
        textHelper = new TextHelper(textPaint);
        paragraphMargin = (int) (textHelper.getChineseWordWidth() * 2);
    }

    public ArrayList<FormatTextRange> getAnchors() {
        return anchors;
    }

    public SpannableStringBuilder parseXml(String xmlContent,boolean paragraphIntend,boolean showPageInfo) {
        xmlContent = xmlContent.replace("&", "#38");//处理&号，不能被识别
        SpannableStringBuilder ssb = new SpannableStringBuilder("");
        XmlPullParser parser = Xml.newPullParser();
        try {
            InputStream inputStream = new ByteArrayInputStream(xmlContent.getBytes());
            parser.setFeature(XmlPullParser.FEATURE_PROCESS_NAMESPACES, false);
            parser.setInput(inputStream, null);

            int tableLineNumber = -1;
            int tableRowNumber = -1;

            boolean inHead = false;
            String listHead = "";
            boolean breakLine = true;
            boolean textIndent = false;
            boolean emptyParagraph = false;
            int marginId = 0;

            for (int event = parser.getEventType(); event != XmlPullParser.END_DOCUMENT; event = parser.next()) {
                if (event != XmlPullParser.START_TAG && event != XmlPullParser.END_TAG && event != XmlPullParser.TEXT) {
                    continue;
                }

                String name = parser.getName();
                if (name != null && name.equals("head")) {
                    if (event == XmlPullParser.START_TAG) {
                        inHead = true;
                    } else if (event == XmlPullParser.END_TAG) {
                        inHead = false;
                        continue;
                    }
                }

                if (inHead) {
                    continue;
                }

                if(event == XmlPullParser.TEXT){
                    if(inFootnote || parser.isWhitespace()){
                        continue;
                    }

                    emptyParagraph = false;

                    String text = processText(parser.getText().replaceAll("\n", ""));
                    breakLine = false;
                    if (footNoteIndex >= 0 || inPageInfo) {
                        blockText += text;
                    } else {
                        int start = ssb.length();
                        if (!listHead.isEmpty()) {
                            ssb.append(listHead);
                            listHead = "";
                        }
                        ssb.append(text);
                        if(textIndent && !text.isEmpty()){
                            ssb.setSpan(new WDLeadingMarginSpan(1, paragraphMargin, 0, marginId++), start, start+1, flags);
                            textIndent = false;
                        }
                    }
                } else if (name.equals("br")) {
                    if (event == XmlPullParser.START_TAG) {
                        ssb.append('\n');
                        breakLine = true;
                    }
                } else if (name.equals("aside")) {
                    checkBlockTag(event, parser.getAttributeValue(null, "id"));
                } else if (footNoteIndex >= 0) {
                    continue;
                } else if (name.equals("p")) {
                    if (event == XmlPullParser.START_TAG) {
                        if (!breakLine) {
                            ssb.append('\n');
                            breakLine = true;
                        }
                        if(paragraphIntend){
                            textIndent = true;
                        }
                        emptyParagraph = true;
                        continue;
                    }
                    if (!emptyParagraph) {
                        ssb.append('\n');
                    }
                    breakLine = true;
                } else if (name.equals("a")) {
                    checkLinkTag(event, parser, ssb);
                } else if (name.equals("div")) {
                    if (event == XmlPullParser.START_TAG) {
                        String link = parser.getAttributeValue(null, "id");
                        if (link != null) {
                            anchors.add(new FormatTextRange(ssb.length(), 0, "a", link));
                        }
                    }
                } else if (name.equals("font")) {
                    checkFontTag(event, ssb, parser.getAttributeValue(null, "color"));
                } else if (name.equals("hr")) {
                    if (event == XmlPullParser.START_TAG) {
                        ssb.append(" \n");
                        breakLine = true;
                        cssParser.setTextSpans(ssb, name, null, ssb.length() - 2, ssb.length(), flags);
                    }
                } else if (name.equals("img")) {
                    if (event == XmlPullParser.START_TAG) {
                        checkImageTag(parser, ssb);
                        breakLine = true;
                    }
                } else if (name.equals("ol")) {
                    checkListGroupTag(event, true, ssb);
                    breakLine = true;
                } else if (name.equals("ul")) {
                    checkListGroupTag(event, false, ssb);
                    breakLine = true;
                } else if (name.equals("li")) {
                    if (event == XmlPullParser.START_TAG) {
                        stack.push(new FormatTextRange(ssb.length(), 0, "li", ""));
                        listHead = checkListTag();
                    } else {
                        FormatTextRange range = stack.peek();
                        if (range.getFormat().equals("li")) {
                            int margin = paragraphMargin * listLv / 2;
                            ssb.setSpan(new WDLeadingMarginSpan(1, margin, margin + (int)textHelper.getWordWidth("▪"), marginId++), range.getLocation(), ssb.length(), flags);
                            ssb.append('\n');
                            breakLine = true;
                            stack.pop();
                        }
                        listHead = "";
                    }
                } else if (name.equals("table")) {
                    if (event == XmlPullParser.START_TAG) {
                        tableLineNumber = 0;
                    } else {
                        ssb.append('\n');
                        breakLine = true;
                        tableLineNumber = -1;
                    }
                } else if (name.equals("tr")) {//行
                    if (tableLineNumber >= 0) {
                        if (event == XmlPullParser.START_TAG) {
                            if (!breakLine) {
                                ssb.append('\n');
                            }
                            ssb.append("        ");
                            breakLine = false;
                            tableRowNumber = 0;
                        } else {
                            tableRowNumber = -1;
                        }
                    }
                } else if (name.equals("td") ||
                        name.equals("th")) {//元素
                    if (tableLineNumber >= 0 && tableRowNumber >= 0) {
                        if (event == XmlPullParser.END_TAG) {
                            FormatTextRange r = stack.peek();
                            stack.pop();
                            cssParser.setTextSpans(ssb, name, r.getData(), r.getLocation(), ssb.length(), flags);
                            ssb.append(' ');
                            breakLine = false;
                        } else {
                            String cls = parser.getAttributeValue(null, "class");
                            stack.push(new FormatTextRange(ssb.length(), 0, name, cls));
                        }
                    }
                } else if(name.equals("span")){
                    if (event == XmlPullParser.START_TAG) {
                        String cls = parser.getAttributeValue(null, "class");
                        if(cls != null && cls.equals("page")){
                            inPageInfo = true;
                        }
                    } else {
                        if(inPageInfo){
                            if(showPageInfo){
                                int start = ssb.length();
                                ssb.append(" ");
                                ssb.setSpan(new PageInfoSpan(blockText), start, start+1, flags);
                                blockText = "";
                            }
                            inPageInfo = false;
                        }
                    }
                }else {
                    if (event == XmlPullParser.START_TAG) {
                        String cls = parser.getAttributeValue(null, "class");
                        stack.push(new FormatTextRange(ssb.length(), 0, name, cls));
                    } else {
                        FormatTextRange r = stack.peek();
                        stack.pop();
                        cssParser.setTextSpans(ssb, name, r.getData(), r.getLocation(), ssb.length(), flags);
                    }

                    if (name.length() == 2 && name.charAt(0) == 'h' && name.charAt(1) >= '0' && name.charAt(1) <= '5') {
                        if (event == XmlPullParser.START_TAG) {
                            if (!breakLine) {
                                ssb.append("\n");//换行
                                breakLine = true;
                            }
                        } else {
                            ssb.append("\n");//换行
                            breakLine = true;
                        }
                    }
                    //todo:其他普通标签一律css处理
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            return ssb;
        }
        int id = 1;
        for (FormatTextRange range : footNoteList) {
            ssb.setSpan(new FootnoteSpan(range.getData(),id), range.getLocation(), range.getLocation() + range.getLength(), flags);
            id++;
        }
        ssb.append("\n\n");
        return ssb;
    }

    //注释块
    private void checkBlockTag(int event, String id) {
        if (event == XmlPullParser.START_TAG) {
            if (id != null) {
                for (int i = 0; i < footNoteList.size(); i++) {
                    if (footNoteList.get(i).getData().equals(id)) {
                        footNoteIndex = i;
                        break;
                    }
                }
            }
        } else if (event == XmlPullParser.END_TAG) {
            if (footNoteIndex >= 0) {
                footNoteList.get(footNoteIndex).setData(blockText);
                blockText = "";
                footNoteIndex = -1;
            }
        }
    }

    //超链接
    void checkLinkTag(int event, XmlPullParser parser, SpannableStringBuilder ssb) {
        if (event == XmlPullParser.START_TAG) {
            String url = parser.getAttributeValue(null, "href");
            if (url != null) {
                String linkType = parser.getAttributeValue(null, "class");
                if (linkType != null && !url.isEmpty()) {
                    if (linkType.equals("footnote")) {
                        int tag = url.indexOf('#');
                        if (tag < 0) {
                            stack.push(new FormatTextRange(ssb.length(), 1, "footnote", url.substring(1)));
                        } else {
                            stack.push(new FormatTextRange(ssb.length(), 1, "footnote", url.substring(tag + 1)));
                        }
                        ssb.append(' ');
                        inFootnote = true;
                    } else if (linkType.equals("anchor")) {
                        String id = parser.getAttributeValue(null, "id");
                        if (id != null) {
                            anchors.add(new FormatTextRange(ssb.length() - 1, 0, "a", id));
                        }
                    }
                } else {
                    stack.push(new FormatTextRange(ssb.length(), 1, "a", url));
                }
            }
        } else {
            FormatTextRange range = stack.peek();
            if (range.getFormat().equals("a")) {
                int end = ssb.length();
                if (end > range.getLocation()) {
                    ssb.setSpan(new WDLinkSpan(range.getData(), cssParser.getTextColor("a")), range.getLocation(), end, flags);
                    Object span = cssParser.getDecoration("a");
                    if (span != null) {
                        ssb.setSpan(span, range.getLocation(), end, flags);
                    }
                }
                stack.pop();
            } else if (range.getFormat().equals("footnote")) {
                footNoteList.add(range);
                stack.pop();
                inFootnote = false;
            }
        }
    }

    void checkFontTag(int event, SpannableStringBuilder ssb, String color) {
        if (event == XmlPullParser.START_TAG) {
            if (color != null) {
                stack.push(new FormatTextRange(ssb.length(), 0, "font", color));
            } else {
                stack.push(new FormatTextRange(ssb.length(), 0, "font", ""));
            }
        } else {
            FormatTextRange range = stack.peek();
            if (range.getFormat().equals("font")) {
                ssb.setSpan(new ForegroundColorSpan(DataFormatParser.parseColor(range.getData())), range.getLocation(), ssb.length(), flags);
                stack.pop();
            }
        }
    }

    void checkImageTag(XmlPullParser parser, SpannableStringBuilder ssb) {
        String src = parser.getAttributeValue(null, "src");
        if (src != null) {
            int relativePath = src.indexOf("base64,");
            if (relativePath > 0) {
                relativePath += 7;
            } else {
                relativePath = src.indexOf("images/");
                if (relativePath < 0) {
                    relativePath = 0;
                }
            }
            int position = ssb.length();
            ssb.append("\n \n");
            ssb.setSpan(new WDImageSpan(src.substring(relativePath)), position, position + 2, flags);
        }
    }

    private int listIndex = 0;
    private int listLv = 0;
    private int listType = 0;
    private final Stack<Integer> listStack = new Stack<>();

    private void checkListGroupTag(int event, boolean bParam, SpannableStringBuilder ssb) {
        if (event == XmlPullParser.START_TAG) {
            if (listIndex != 0) {
                listStack.push(listType);
                listStack.push(listIndex);
            }
            listIndex = 1;
            ssb.append('\n');
            if (bParam) {
                listType = cssParser.getListStyleType(listLv);
            } else {
                listType = CssElement.CSSListStyle.NONE;
            }
            listLv++;
        } else {
            if (listStack.isEmpty()) {
                listIndex = 0;
            } else {
                listIndex = listStack.peek();
                listStack.pop();
                listType = listStack.peek();
                listStack.pop();
            }
            listLv--;
        }
    }

    private String checkListTag() {
        return cssParser.getListStyleString(listType, listIndex++, listLv);
    }

    private char translateChar(String str) {
        try {
            if (str.charAt(0) == 'x') {
                return (char)Integer.parseInt(str.substring(1),16);
            } else {
                return (char)Integer.parseInt(str);
            }
        } catch (Exception e) {
            return ' ';
        }
    }
    private String processText(String text) {
        String newText = text.replace("#38", "&")
                .replace("&gt;", ">")
                .replace("&lt;", "<")
                .replace("&quot;", "\"")
                .replace("&amp;", "&");

        int start = newText.indexOf("&#");
        int prev = 0;
        StringBuilder sb = new StringBuilder();
        while (start >= 0) {
            int end = newText.indexOf(';', start);
            if (end < 0) {
                break;
            }
            sb.append(newText,prev,start);
            sb.append(translateChar(newText.substring(start+2,end)));
            prev = end+1;
            start = newText.indexOf("&#",end);
        }

        if (sb.length() > 0) {
            if (prev < newText.length()) {
                sb.append(newText, prev, newText.length());
            }
            newText = sb.toString();
        }

        int len = newText.length();
        int st = 0;

        while ((st < len) && (newText.charAt(st) == ' ')) {
            st++;
        }
        return (st > 1) ? newText.substring(st) : newText;
    }
}
