package com.wedevote.wdbook.ui.store

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.aquila.lib.widget.group.GroupImageTextLayout
import com.wedevote.wdbook.R
import com.wedevote.wdbook.constants.OrderField
import com.wedevote.wdbook.tools.interfaces.OnItemClickListener

/***
 * @date 创建时间 2025/09/04
 * <AUTHOR>
 * @description 商城筛选栏（排序、语言选择）
 */
class StoreFilterBarView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0,
) : ConstraintLayout(context, attrs, defStyleAttr), View.OnClickListener {

    interface OnFilterChangedListener {
        fun onOrderFieldChanged(orderField: OrderField)
        fun onLanguageChanged(language: String)
    }

    private var onFilterChangedListener: OnFilterChangedListener? = null

    private lateinit var defaultSortTextView: TextView
    private lateinit var soldSortTextView: TextView
    private lateinit var priceSortTextView: TextView
    private lateinit var languageLayout: GroupImageTextLayout

    private val languagePopupWindow: SortPopupWindow by lazy { SortPopupWindow(context) }

    var currentSortMode: OrderField = OrderField.CreateTime_DESC
        private set
    var currentLanguage: String = ""
        private set

    init {
        LayoutInflater.from(context).inflate(R.layout.view_store_filter_bar_layout, this, true)
        initViewFromXML()
        setViewListeners()
        setCurrentSortModeUI(OrderField.CreateTime_DESC)
    }

    private fun initViewFromXML() {
        defaultSortTextView = findViewById(R.id.book_list_sort_default_TextView)
        soldSortTextView = findViewById(R.id.book_list_sort_sold_TextView)
        priceSortTextView = findViewById(R.id.book_list_sort_price_TextView)
        languageLayout = findViewById(R.id.book_list_language_layout)
    }

    private fun setViewListeners() {
        defaultSortTextView.setOnClickListener(this)
        soldSortTextView.setOnClickListener(this)
        priceSortTextView.setOnClickListener(this)
        languageLayout.setOnClickListener(this)

        languagePopupWindow.onItemClickListener = object : OnItemClickListener {
            override fun <T> onItemClick(obj: Any, tag: String, t: T) {
                t as LanguageEntity
                currentLanguage = t.language
                languageLayout.setText(t.name)
                onFilterChangedListener?.onLanguageChanged(t.language)
                languagePopupWindow.dismiss()
            }
        }
    }

    override fun onClick(v: View?) {
        when (v) {
            defaultSortTextView -> setCurrentSortModeUI(OrderField.CreateTime_DESC)
            soldSortTextView -> setCurrentSortModeUI(OrderField.SaleCount_DESC)
            priceSortTextView -> setCurrentSortModeUI(OrderField.Price_ASC)
            languageLayout -> languagePopupWindow.showAsDropDown(languageLayout)
        }
    }

    private fun setCurrentSortModeUI(sort: OrderField) {
        when (sort) {
            OrderField.CreateTime_DESC -> {
                defaultSortTextView.isSelected = true
                priceSortTextView.isSelected = false
                soldSortTextView.isSelected = false
            }
            OrderField.Price_ASC -> {
                defaultSortTextView.isSelected = false
                priceSortTextView.isSelected = true
                soldSortTextView.isSelected = false
            }
            OrderField.SaleCount_DESC -> {
                defaultSortTextView.isSelected = false
                priceSortTextView.isSelected = false
                soldSortTextView.isSelected = true
            }
            else -> {}
        }
        if (currentSortMode != sort) {
            currentSortMode = sort
            onFilterChangedListener?.onOrderFieldChanged(sort)
        }
    }

    fun setOnFilterChangedListener(listener: OnFilterChangedListener?) {
        onFilterChangedListener = listener
    }

    fun getOrderFieldValue(): String = currentSortMode.value

    fun getSelectedLanguage(): String = currentLanguage

    fun setLanguage(name: String, code: String) {
        languageLayout.setText(name)
        currentLanguage = code
        val idx = languagePopupWindow.itemList.indexOfFirst { it.language == code }
        val targetIndex = if (idx >= 0) idx else 0
        languagePopupWindow.adapter.selectPosition = targetIndex
        languagePopupWindow.adapter.notifyDataSetChanged()
    }
}