package com.wedevote.wdbook.ui.dialogs

import android.content.Context
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.text.Html
import android.view.View
import android.widget.Button
import android.widget.TextView
import com.aquila.lib.tools.util.ToastUtil
import com.wedevote.wdbook.R
import com.wedevote.wdbook.base.ExceptionHandler
import com.wedevote.wdbook.base.SDKSingleton
import com.wedevote.wdbook.entity.user.InPaymentOrderEntity
import com.wedevote.wdbook.exception.ApiException
import com.wedevote.wdbook.tools.util.UnitFormatUtil
import com.wedevote.wdbook.tools.util.findString
import com.wedevote.wdbook.ui.store.OrderConfirmActivity
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch

/***
 * @date 创建时间 2022/7/14 14:17
 * <AUTHOR> <PERSON><PERSON>Long
 * @description 显示待支付的订单提示
 */
class UnpaidOrderDialog(context: Context, var inPaymentOrderEntity: InPaymentOrderEntity, var onClickBuyListener: OnClickBuyListener?) :
    BaseDialog(context), View.OnClickListener {
    lateinit var titleTextView: TextView
    lateinit var contentTextView: TextView
    lateinit var cancelTextView: Button
    lateinit var continuePayTextView: Button

    var formatText = findString(R.string.unpaid_prompt_text)
    var remainTime: Long = 0
    val handler = Handler(Looper.getMainLooper(), { msg ->
        if (msg.what == 1) {
            remainTime -= 1000
            initContentText(inPaymentOrderEntity)
        }

        false
    })

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.dialog_in_payment_layout)
        titleTextView = findViewById(R.id.in_payment_title_TextView)
        contentTextView = findViewById(R.id.in_payment_content_TextView)
        cancelTextView = findViewById(R.id.in_payment_cancel_TextView)
        continuePayTextView = findViewById(R.id.in_payment_continue_pay_TextView)

        remainTime = inPaymentOrderEntity.expiredTime

        initContentText(inPaymentOrderEntity)
        cancelTextView.setOnClickListener(this)
        continuePayTextView.setOnClickListener(this)
    }


    fun initContentText(payEntity: InPaymentOrderEntity) {
        var content = formatText.format(payEntity.title, UnitFormatUtil.formatExpireTime(remainTime))
        contentTextView.setText(Html.fromHtml(content))
        if (remainTime > 0) {
            handler.sendEmptyMessageDelayed(1, 1000)
        }
    }


    override fun onClick(v: View?) {
        when (v) {
            cancelTextView -> {
                MainScope().launch {
                    try {
                        SDKSingleton.paymentBl.cancelOrder(inPaymentOrderEntity.orderId.toString())
                        ToastUtil.showToastShort(R.string.order_canceled)
                    } catch (exception: Throwable) {
                        if (exception is ApiException) {
                            ToastUtil.showToastShort(exception.message)
                        } else {
                            ExceptionHandler.handleException(exception)
                        }
                    }
                    dismiss()
                }
            }
            continuePayTextView -> {
                MainScope().launch(ExceptionHandler.coroutineExceptionHandler) {
                    SDKSingleton.paymentBl.getPayAmountEntityWithOrderId(inPaymentOrderEntity.orderId)?.let {
                        OrderConfirmActivity.gotoConfirmOrderActivity(
                            context, it, inPaymentOrderEntity.orderId,
                            listener = onClickBuyListener
                        )
                        dismiss()
                    }
//                    SDKSingleton.storeBl.getProductDetailEntity(inPaymentOrderEntity.productId)?.let { detailEntity ->
//                        dismiss()
//                    }
                }

            }
        }
    }


}