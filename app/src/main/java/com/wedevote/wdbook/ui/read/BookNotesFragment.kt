package com.wedevote.wdbook.ui.read

import android.app.Activity
import android.graphics.Color
import android.os.Bundle
import android.text.Spannable
import android.text.SpannableString
import android.text.style.BackgroundColorSpan
import android.text.style.ForegroundColorSpan
import android.text.style.UnderlineSpan
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.LinearLayout
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.fragment.app.FragmentActivity
import com.aquila.lib.base.BaseRecycleAdapter
import com.aquila.lib.base.BaseViewHolder
import com.aquila.lib.base.OnViewClickListener
import com.aquila.lib.layout.SmartRefreshLayout
import com.aquila.lib.layout.api.RefreshLayout
import com.aquila.lib.layout.listener.OnLoadMoreListener
import com.aquila.lib.layout.listener.OnRefreshListener
import com.aquila.lib.tools.singleton.SPSingleton
import com.aquila.lib.widget.view.CustomRecyclerView
import com.aquila.lib.widget.view.DotView
import com.chauthai.swipereveallayout.SwipeRevealLayout
import com.wedevote.wdbook.R
import com.wedevote.wdbook.base.ExceptionHandler
import com.wedevote.wdbook.base.SDKSingleton
import com.wedevote.wdbook.entity.NoteEntity
import com.wedevote.wdbook.tools.event.BookDataChangeEvent
import com.wedevote.wdbook.tools.event.OnSyncNoteFinish
import com.wedevote.wdbook.tools.util.*
import com.wedevote.wdbook.ui.dialogs.GuideDialog
import com.wedevote.wdbook.ui.read.lib.EPubBook
import com.wedevote.wdbook.ui.user.NoteEditActivity
import com.wedevote.wdbook.ui.user.OnNoteEditCallback
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus

/***
 *@date 创建时间 2020/11/17 5:01 PM
 *<AUTHOR> Qian Kai
 *@description 笔记
 */
class BookNotesFragment(val activity: Activity, private val arguments: Bundle) : OnLoadMoreListener, OnRefreshListener {
    var dataRecycleView: CustomRecyclerView
    var noDataLinearLayout: LinearLayout
    var recycleBinTextView: TextView
    val bookNotesAdapter = BookNotesAdapter()
    var refreshLayout: SmartRefreshLayout
    var pathIndex: Int = -1
    var startWord: Int = -1
    var endWord: Int = -1
    var resourceId: String
    val rootView: View = activity.layoutInflater.inflate(R.layout.fragment_book_notes_layout, null)
    var noteList: ArrayList<NoteEntity> = ArrayList()

    init {
        dataRecycleView = rootView.findViewById(R.id.book_notes_data_RecyclerView)
        noDataLinearLayout = rootView.findViewById(R.id.book_notes_no_data_LinearLayout)
        recycleBinTextView = rootView.findViewById(R.id.notes_recycle_bin_TextView)
        refreshLayout = rootView.findViewById(R.id.book_notes_data_SmartRefreshLayout)

        refreshLayout.setOnRefreshListener(this).setOnLoadMoreListener(this)
        dataRecycleView.adapter = bookNotesAdapter

        resourceId = arguments.getString(IntentConstants.EXTRA_ResourceId)!!
        pathIndex = arguments.getInt(IntentConstants.EXTRA_PathIndex, -1)
        startWord = arguments.getInt(IntentConstants.EXTRA_StartWord, -1)
        endWord = arguments.getInt(IntentConstants.EXTRA_EndWord, -1)

        reloadNotesData()
        initListener()
    }

    private fun initListener() {
        recycleBinTextView.setOnClickListener {
            val dialog = NoteRecycleBinDialogFragment()
            dialog.arguments = Bundle().apply {
                putString(IntentConstants.EXTRA_ResourceId, resourceId)
            }
            (activity as FragmentActivity).supportFragmentManager.let { it1 -> dialog.show(it1, "NoteRecycleBinDialog") }
            dialog.onDataChangeListener = object : OnViewClickListener {
                override fun <T> onClickAction(v: View, str: String, t: T?) {
                    if (str.equals("Recover")) {
                        reloadNotesData()
                    }
                }
            }
        }
        bookNotesAdapter.onViewClickListener = object : OnViewClickListener {
            override fun <T> onClickAction(v: View, str: String, t: T?) {
                if (str == "Delete") {
                    setCurrentUIShowStatus(bookNotesAdapter.dataList)
                }
            }
        }
    }

    private fun reloadNotesData() {
        noteList.clear()
        var offset = 0
        if (pathIndex < 0) {
            while (true) {
                val newList = SDKSingleton.dbWrapBl.getNoteEntityList(resourceId, offset, 20)
                if (newList.isNullOrEmpty()) {
                    break
                }
                offset += newList.size
                for (n in newList) {
                    noteList.add(n)
//                    if (n.pagePath.isNotEmpty()) {
//                    }
                }
            }
        } else {
            val allNoteList = SDKSingleton.dbWrapBl.getPageNoteEntityList(resourceId, EPubBook.pathList[pathIndex], 0, endWord)
            for (n in allNoteList) {
                if (!n.noteText.isNullOrEmpty() && n.wordEndOffset >= startWord) {
                    noteList.add(n)
                }
            }
        }
        bookNotesAdapter.dataList = noteList
        setCurrentUIShowStatus(noteList)
    }

    private fun setCurrentUIShowStatus(list: List<NoteEntity>?) {
        if (!list.isNullOrEmpty()) {
            noDataLinearLayout.visibility = View.GONE
            dataRecycleView.visibility = View.VISIBLE
        } else {
            noDataLinearLayout.visibility = View.VISIBLE
            dataRecycleView.visibility = View.GONE
        }

        if (pathIndex < 0) {
            recycleBinTextView.visibility = View.VISIBLE
        } else {
            recycleBinTextView.visibility = View.GONE
        }
    }

    override fun onLoadMore(layout: RefreshLayout) {
        refreshLayout.isEnableLoadMore = false
        refreshLayout.finishLoadMoreAndRefresh()
    }

    override fun onRefresh(layout: RefreshLayout) {
        MainScope().launch(ExceptionHandler.coroutineExceptionHandler) {
            SDKSingleton.syncBl.syncNoteData()
            EventBus.getDefault().post(OnSyncNoteFinish())
            bookNotesAdapter.dataList = null
            reloadNotesData()
            refreshLayout.finishLoadMoreAndRefresh()
        }
    }

    fun setOnJumpListener(l: OnJumpActionListener) {
        bookNotesAdapter.onJumpListener = l
    }

    private fun editNote(noteEntity: NoteEntity) {

        NoteEditActivity.gotoNoteEdit(
            activity, noteEntity,
            object : OnNoteEditCallback {
                override fun onNoteEditFinish(entity: NoteEntity) {
                    reloadNotesData()
                }
            }
        )
    }

    /***
     *@date 创建时间 2020/5/27 15:08
     *<AUTHOR> W.YuLong
     *@description
     */
    inner class BookNotesAdapter : BaseRecycleAdapter<NoteEntity, BookNotesViewHolder>() {
        var onViewClickListener: OnViewClickListener? = null
        var onJumpListener: OnJumpActionListener? = null
        var itemOpenPosition = -1

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BookNotesViewHolder {
            return BookNotesViewHolder(parent)
        }

        override fun onBindViewHolder(holder: BookNotesViewHolder, position: Int) {
            val note = dataList!![position]
            holder.initUIData(note)
            val swipeLayout = holder.itemView as SwipeRevealLayout

            if (itemOpenPosition != position) {
                swipeLayout.close(true)
            }

            swipeLayout.setSwipeListener(object : SwipeRevealLayout.SwipeListener {
                override fun onClosed(view: SwipeRevealLayout?) {
                }

                override fun onOpened(view: SwipeRevealLayout?) {
                    itemOpenPosition = position
                    notifyDataSetChanged()
                }

                override fun onSlide(view: SwipeRevealLayout?, slideOffset: Float) {}
            })

            holder.deleteButton.setOnClickListener { v ->
                itemOpenPosition = -1
                note.dataId?.let { SDKSingleton.dbWrapBl.removeNote(it) }
                noteList.removeAt(position)
                dataList = noteList
                onViewClickListener?.onClickAction(v, "Delete", note)
                EventBus.getDefault().post(BookDataChangeEvent())
                EventBus.getDefault().post(OnSyncNoteFinish())
                notifyDataSetChanged()
                if (SPSingleton.get().getBoolean(SPKeyDefine.SP_FistTimeDeleteNote, true)) {
                    SPSingleton.get().putBoolean(SPKeyDefine.SP_FistTimeDeleteNote, false)
                    val dialog = GuideDialog(activity, R.drawable.guide_recycle, GuideDialog.BUTTON_MIDDLE)
                    dialog.show()
                }
            }

            holder.contentLayout.setOnClickListener {
                if (note.conflictRemoteId.isNullOrEmpty() && pathIndex < 0) {
                    onJumpListener?.doJump(EPubBook.getPathIndex(note.pagePath), note.wordStartOffset)
                } else {
                    editNote(note)
                }
            }
        }
    }

    /***
     *@date 创建时间 2020/5/27 15:08
     *<AUTHOR> W.YuLong
     *@description 书籍的目录item
     */
    inner class BookNotesViewHolder(parent: ViewGroup) : BaseViewHolder(parent, R.layout.holder_book_item_note_layout) {
        val titleTextView: TextView = itemView.findViewById(R.id.book_note_toc_title_TextView)
        val pageNumberDotView: DotView = itemView.findViewById(R.id.book_note_page_number_DotView)
        val quoteTextView: TextView = itemView.findViewById(R.id.book_note_quote_text_TextView)
        val contentTextView: TextView = itemView.findViewById(R.id.book_note_content_TextView)
        val dateTextView: TextView = itemView.findViewById(R.id.book_note_date_TextView)
        val lineView: View = itemView.findViewById(R.id.book_note_vertical_line_View)
        val deleteButton: Button = itemView.findViewById(R.id.book_note_delete_Button)
        val contentLayout: ConstraintLayout = itemView.findViewById(R.id.book_note_content_ConstraintLayout)
        lateinit var noteEntity: NoteEntity
        private var highlightColor: Int = Color.TRANSPARENT

        override fun <T> initUIData(t: T) {
            noteEntity = t as NoteEntity
            titleTextView.text = t.tocTitle
            highlightColor = parseColor(t.highlightColorType)
            pageNumberDotView.setDotColor(highlightColor)
            dateTextView.text = UnitFormatUtil.formatDate_ymdhm(t.createTime)

            if (t.noteText.isNullOrEmpty()) {
                quoteTextView.visibility = View.GONE
                contentTextView.visibility = View.VISIBLE
                contentTextView.text = t.summary
            } else {
                quoteTextView.visibility = View.VISIBLE
                contentTextView.visibility = View.VISIBLE
                if (!t.conflictRemoteId.isNullOrEmpty()) {
                    val conflictTitle = findString(R.string.conflict_note)
                    val spannableStr = SpannableString("$conflictTitle${t.noteText}")
                    spannableStr.setSpan(
                        ForegroundColorSpan(Color.parseColor("#E53935")),
                        0,
                        conflictTitle.length,
                        Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                    )
                    contentTextView.text = spannableStr
                } else {
                    contentTextView.text = t.noteText
                }

                if (pathIndex >= 0) {
                    quoteTextView.text = getQuoteTextSpannable(t)
                } else {
                    quoteTextView.text = activity.getString(R.string.quote) + "  | " + t.getDisplaySummery()
                }
            }
        }

        private fun getQuoteTextSpannable(note: NoteEntity): SpannableString {
            val spannableStr = SpannableString(activity.getString(R.string.quote) + "  | " + note.getDisplaySummery())
            if (note.markStyle == NoteEntity.STYLE_HIGHLIGHT) {
                spannableStr.setSpan(
                    BackgroundColorSpan(parseColor(note.highlightColorType)),
                    0,
                    spannableStr.length,
                    Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                )
            } else {
                spannableStr.setSpan(UnderlineSpan(), 0, spannableStr.length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
            }
            return spannableStr
        }
    }
}
