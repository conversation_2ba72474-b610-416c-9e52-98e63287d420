package com.wedevote.wdbook.ui.store

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import android.widget.Button
import android.widget.LinearLayout
import android.widget.TextView
import com.aquila.lib.base.OnViewClickListener
import com.aquila.lib.dialog.CommProgressDialog
import com.aquila.lib.tools.util.ToastUtil
import com.aquila.lib.widget.group.GroupImageTextLayout
import com.aquila.lib.widget.view.CustomRecyclerView
import com.wedevote.wdbook.R
import com.wedevote.wdbook.base.ExceptionHandler
import com.wedevote.wdbook.base.RootActivity
import com.wedevote.wdbook.base.SDKSingleton
import com.wedevote.wdbook.entity.store.ProductActivityEntity
import com.wedevote.wdbook.entity.store.ProductDetailEntity
import com.wedevote.wdbook.tools.payment.OnOrderPayCallback
import com.wedevote.wdbook.tools.payment.OrderPaymentHelper
import com.wedevote.wdbook.tools.util.APPUtil
import com.wedevote.wdbook.tools.util.NetWorkUtils
import com.wedevote.wdbook.tools.util.UnitFormatUtil
import com.wedevote.wdbook.tools.util.findString
import com.wedevote.wdbook.ui.account.OnLoginResultCallBack
import com.wedevote.wdbook.ui.account.SSOLoginActivity
import com.wedevote.wdbook.ui.dialogs.SelectActivityProductDialog
import com.wedevote.wdbook.ui.user.OnDataChangeSelectListener
import com.wedevote.wdbook.ui.widgets.CommTopTitleLayout
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.*

/***
 * @date 创建时间 2022/6/20 10:54
 * <AUTHOR> W.YuLong
 * @description 书籍活动促销的Activity
 */
class ProductActiveActivity : RootActivity(), View.OnClickListener {
    lateinit var topTitleLayout: CommTopTitleLayout
    lateinit var subTitleTextView: TextView
    lateinit var timeTextView: TextView
    lateinit var descTextView: TextView
    
    lateinit var titleContainerLayout: LinearLayout
    lateinit var expirePromptTextView: TextView
    
    lateinit var dataRecyclerView: CustomRecyclerView
    
    
    lateinit var allSelectLayout: GroupImageTextLayout
    lateinit var totalPriceTextView: TextView
    lateinit var selectCountLayout: GroupImageTextLayout
    lateinit var buyButton: Button
    lateinit var selectProductDialog: SelectActivityProductDialog
    
    lateinit var activityAdapter: ProductActivityAdapter
    lateinit var mainScope: CoroutineScope
    
    private lateinit var orderPaymentHelper: OrderPaymentHelper
    
    companion object {
        fun gotoActivityDetailUI(context: Context, activityId: Long) {
            val intent = Intent(context, ProductActiveActivity::class.java)
            intent.putExtra("activityId", activityId)
            context.startActivity(intent)
        }
        var isClickable: Boolean = true
    }
    
    var activityId: Long = 0
    var productActivityEntity: ProductActivityEntity? = null
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_book_activity_layout)
        initViewFromXML()
        activityId = intent.getLongExtra("activityId", 0)
        orderPaymentHelper = OrderPaymentHelper(this, object : OnOrderPayCallback {
            override fun onOrderPayResult(orderStatus: Int) {
            }
        })
        initUIData()
        getDataFromServer()
        setViewListener()
    }
    
    
    private fun initUIData() {
        mainScope = MainScope()
        selectProductDialog = SelectActivityProductDialog(this, activityId, object : OnViewClickListener {
            override fun <T> onClickAction(v: View, str: String, t: T?) {
                activityAdapter.removeSelectItem(t as ProductDetailEntity)
    
                if (activityAdapter.selectList.isNullOrEmpty()) {
                    selectProductDialog.dismiss()
                }
            }
        })
        activityAdapter = ProductActivityAdapter(activityId)
        dataRecyclerView.adapter = activityAdapter
    }
    
    private fun setViewListener() {
        buyButton.setOnClickListener(this)
        selectCountLayout.setOnClickListener(this)
        allSelectLayout.setOnClickListener {
            if (!isClickable) {
                return@setOnClickListener
            }
            if (!NetWorkUtils.isNetworkAvailable()) {
                NetWorkUtils.showTipDialog(this, getString(R.string.no_network_connect))
                return@setOnClickListener
            }
            SSOLoginActivity.checkAndGotoLogin(
                this,
                callBack = object : OnLoginResultCallBack {
                    override fun onLoginResult(isSuccess: Boolean) {
                        if (isSuccess) {
                            clickAllSelectAction()
                            initSelectProductUI {
                                clickAllSelectAction()
                                activityAdapter.notifyDataSetChanged()
                            }
                        }
                    }
                })
        }
        
        activityAdapter.onAllDataSelectListener = object : OnDataChangeSelectListener {
            override fun isAllSelected(isAllSelected: Boolean) {
                allSelectLayout.imageView.isSelected = isAllSelected
            }
            
            override fun itemCount(count: Int) {
            
            }
        }
        
        activityAdapter.onItemSelectListener = object : OnItemSelectListener {
            @SuppressLint("NotifyDataSetChanged")
            override fun onItemSelect(entity: ProductDetailEntity) {
                SSOLoginActivity.checkAndGotoLogin(
                    this@ProductActiveActivity,
                    callBack = object : OnLoginResultCallBack {
                        override fun onLoginResult(isSuccess: Boolean) {
                            if (isSuccess) {
                                activityAdapter.doSelectAction(entity)
                                initSelectProductUI {
                                    activityAdapter.selectList.remove(entity)
                                    activityAdapter.notifyDataSetChanged()
                                }
                            }
                        }
                    })
            }
        }
    }

    private fun clickAllSelectAction() {
        if (allSelectLayout.imageView.isSelected) {
            activityAdapter.doUnSelectAll()
        } else {
            activityAdapter.doSelectAll()
        }
    }

    private fun initViewFromXML() {
        topTitleLayout = findViewById(R.id.activity_top_title_layout)
        subTitleTextView = findViewById(R.id.activity_sub_title_TextView)
        titleContainerLayout = findViewById(R.id.activity_title_container_layout)
        expirePromptTextView = findViewById(R.id.activity_expire_TextView)
        timeTextView = findViewById(R.id.activity_time_TextView)
        descTextView = findViewById(R.id.activity_desc_TextView)
        dataRecyclerView = findViewById(R.id.activity_book_list_RecyclerView)
        allSelectLayout = findViewById(R.id.activity_select_all_ImageTextLayout)
        totalPriceTextView = findViewById(R.id.activity_total_price_TextView)
        selectCountLayout = findViewById(R.id.activity_select_book_list_layout)
        buyButton = findViewById(R.id.activity_purchase_immediately_Button)
    }

    var loadingDialog: CommProgressDialog? = null
    private fun getDataFromServer() {
        mainScope.launch(ExceptionHandler.coroutineExceptionHandler) {
            if (loadingDialog == null) {
                loadingDialog = CommProgressDialog.with(this@ProductActiveActivity).create()
            }
            loadingDialog!!.show()
            loadingDialog!!.setTitleText(findString(R.string.label_dialog_loading))

            productActivityEntity = SDKSingleton.userBl.getActivityDetailData(activityId)
            productActivityEntity?.let {
                initDataUI(it)
            }
            loadingDialog!!.dismiss()
        }
    }
    
    fun initDataUI(entity: ProductActivityEntity) {
        if (entity.expired == 1) {
            titleContainerLayout.visibility = View.GONE
            expirePromptTextView.visibility = View.VISIBLE
        } else {
            titleContainerLayout.visibility = View.VISIBLE
            expirePromptTextView.visibility = View.GONE
        }

        topTitleLayout.setTitle(entity.activityTitle)
        if (entity.activitySubTitle.isNullOrEmpty()) {
            subTitleTextView.visibility = View.GONE
        } else {
            subTitleTextView.visibility = View.VISIBLE
            subTitleTextView.text = entity.activitySubTitle
        }
        timeTextView.setText(findString(R.string.format_activity_time).format(formatDate(entity.startTime), formatDate(entity.endTime)))
        if (entity.description.isNullOrEmpty()) {
            descTextView.visibility = View.GONE
        } else {
            descTextView.visibility = View.VISIBLE
            descTextView.setText(findString(R.string.format_activity_introduce).format(entity.description))
        }
        activityAdapter.dataList = entity.productList
        activityAdapter.setExpired(entity.expired == 1)
        initSelectProductUI {}
    }
    
    fun initSelectProductUI(failed: () -> Unit) {
        var totalAmount: Float = 0f
        for (entity in activityAdapter.selectList) {
            totalAmount += entity.price
        }
        var currency = ""
        
        mainScope.launch(ExceptionHandler.getCoroutineExceptionHandler {
            val idList = ArrayList<Long>()
            for (item in activityAdapter.selectList) {
                idList.add(item.productId)
            }
            orderPaymentHelper.doUnpaidInfoException(it, idList) {
                orderPaymentHelper.doPayActivityOrder(activityAdapter.selectList, activityIds = arrayListOf(activityId))
            }
            APPUtil.dismissLoadingDialog(this@ProductActiveActivity)
            failed.invoke()
            isClickable = true
        }) {
            isClickable = false
            APPUtil.showLoadingDialog(this@ProductActiveActivity).setCanceledOnTouchOutside(false)
            if (activityAdapter.selectList.isNotEmpty()) {
                val idList = ArrayList<Long>()
                for (item in activityAdapter.selectList) {
                    idList.add(item.productId)
                    if (currency.isEmpty()) {
                        currency = item.currency
                    }
                }
                val payAmountEntity = SDKSingleton.paymentBl.getPayAmountEntity(idList) ?: return@launch
                totalPriceTextView.text = UnitFormatUtil.formatPrice(currency,
                    payAmountEntity.actualAmount
                )
            } else {
                totalPriceTextView.text = UnitFormatUtil.formatPrice(currency, 0f)
            }
            selectCountLayout.setText(getString(R.string.has_select_count).format(activityAdapter.selectList.size))
            APPUtil.dismissLoadingDialog(this@ProductActiveActivity)
            isClickable = true
        }
    }
    
    val sdf_MD = SimpleDateFormat("M月d日")
    fun formatDate(time: Long): String {
        return sdf_MD.format(Date(time))
    }
    
    override fun onClick(v: View?) {
        when (v) {
            buyButton -> {
                if (activityAdapter.selectList.isNullOrEmpty()) {
                    ToastUtil.showToastShort(R.string.you_have_not_select_product)
                    return
                }
                orderPaymentHelper.doPayActivityOrder(activityAdapter.selectList, activityIds = arrayListOf(activityId))
            }
            selectCountLayout -> {
                if (!activityAdapter.selectList.isNullOrEmpty()) {
                    if (!selectProductDialog.isShowing) {
                        selectProductDialog.show()
                        selectProductDialog.initDataList(activityAdapter.selectList)
                    }
                }
            }
        }
    }
}