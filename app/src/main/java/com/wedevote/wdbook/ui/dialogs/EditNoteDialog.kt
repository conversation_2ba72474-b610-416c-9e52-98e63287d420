package com.wedevote.wdbook.ui.dialogs

import android.app.Dialog
import android.content.Context
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.os.Message
import android.view.Gravity
import android.view.View
import android.view.WindowManager
import android.widget.EditText
import android.widget.TextView
import androidx.core.widget.addTextChangedListener
import com.aquila.lib.base.OnViewClickListener
import com.wedevote.wdbook.R
import com.wedevote.wdbook.base.ExceptionHandler
import com.wedevote.wdbook.base.SDKSingleton
import com.wedevote.wdbook.entity.NoteEntity
import com.wedevote.wdbook.tools.event.OnSyncNoteFinish
import com.wedevote.wdbook.tools.util.parseColor
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus

/***
 * @date 创建时间 2020/12/10 17:04
 * <AUTHOR> <PERSON><PERSON>
 * @description
 */
class EditNoteDialog(context: Context) : Dialog(context, R.style.Dialog_EditNoteScreen), View.OnClickListener {
    lateinit var finishTextView: TextView
    lateinit var flagColorView: View
    lateinit var bookTextTextView: TextView
    lateinit var warningTextView: TextView
    lateinit var descEditText: EditText

    var onViewClickListener: OnViewClickListener? = null
    var noteEntity: NoteEntity? = null
    val MAX_TEXT_LENGTH = 20000
    val MSG_SAVE = 0
    var isFirstTextChange = true
    val SAVE_NOTE_RATE = (10 * 1000).toLong()

    var lastText = ""

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        window!!.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN or WindowManager.LayoutParams.SOFT_INPUT_STATE_HIDDEN)
        var rootView = layoutInflater.inflate(R.layout.dialog_edit_note_layout, null)
        setContentView(rootView)
        initViewFromXML()
        setViewListeners()
        configDialog()
    }

    fun configDialog() {
        val wl = window!!.attributes
        wl.gravity = Gravity.BOTTOM
        window!!.setWindowAnimations(com.aquila.lib.dialog.R.style.bottomDialogWindowAnim)
        wl.width = WindowManager.LayoutParams.MATCH_PARENT
        wl.height = WindowManager.LayoutParams.MATCH_PARENT
        window!!.attributes = wl
    }

    private fun initViewFromXML() {
        finishTextView = findViewById(R.id.note_edit_finish_TextView)
        flagColorView = findViewById(R.id.note_edit_mark_color_View)
        bookTextTextView = findViewById(R.id.note_edit_book_text_TextView)
        descEditText = findViewById(R.id.note_edit_desc_EditText)
        warningTextView = findViewById(R.id.note_warning_TextView)
    }

    private fun setViewListeners() {
        finishTextView.setOnClickListener(this)
        descEditText.addTextChangedListener {
            warningTextView.visibility =
                if (descEditText.text.length > MAX_TEXT_LENGTH) View.VISIBLE else View.GONE

            if (isFirstTextChange) {
                isFirstTextChange = false
                handler.sendEmptyMessageDelayed(MSG_SAVE, SAVE_NOTE_RATE)
            }
        }
    }

    private fun saveCurrentNote() {
        if (lastText != descEditText.text.toString()) {
            val noteText = descEditText.text.toString()

            if ((noteText.isEmpty() || noteText.isBlank()) && noteEntity?.noteText.isNullOrEmpty()) {
                return
            }

            noteEntity?.noteText = noteText
            if (noteEntity?.noteText!!.length > MAX_TEXT_LENGTH) {
                noteEntity?.noteText = noteEntity?.noteText!!.substring(0, MAX_TEXT_LENGTH)
            }
            SDKSingleton.dbWrapBl.saveNote(noteEntity!!)
        } else {
            if (!noteEntity?.conflictRemoteId.isNullOrEmpty()) {
                noteEntity?.conflictRemoteId = "" // KMM层来写
                SDKSingleton.dbWrapBl.solvedConflictData(
                    noteEntity!!.remoteId, noteEntity!!.dataId!!
                )
            }
        }
    }

    override fun dismiss() {
        handler.removeMessages(MSG_SAVE)
        if (lastText != descEditText.text.toString() || !noteEntity?.conflictRemoteId.isNullOrEmpty()) {
            saveCurrentNote()
            MainScope().launch(ExceptionHandler.getCoroutineExceptionHandler {
                super.dismiss()
            }) {
                SDKSingleton.syncBl.syncNoteData()
                EventBus.getDefault().post(OnSyncNoteFinish())
            }
        }
        super.dismiss()
    }

    override fun onClick(v: View?) {
        when (v) {
            finishTextView -> {
                dismiss()
            }
        }
    }

    fun initUI(noteEntity: NoteEntity) {
        this.noteEntity = noteEntity
        lastText = noteEntity.noteText

        flagColorView.setBackgroundColor(parseColor(noteEntity.highlightColorType))
        bookTextTextView.text = noteEntity.getDisplaySummery()
        descEditText.setText(noteEntity.noteText)
        descEditText.setSelection(0)
        descEditText.requestFocus()
    }

    private val handler: Handler = object : Handler(Looper.getMainLooper()) {
        override fun handleMessage(msg: Message) {
            if (isShowing) {
                saveCurrentNote()
                sendEmptyMessageDelayed(MSG_SAVE, SAVE_NOTE_RATE)
            }
        }
    }
}
