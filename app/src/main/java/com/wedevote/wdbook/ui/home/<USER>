package com.wedevote.wdbook.ui.home

import android.os.Bundle
import com.aquila.lib.layout.SmartRefreshLayout
import com.aquila.lib.layout.api.RefreshLayout
import com.aquila.lib.layout.listener.OnLoadMoreListener
import com.aquila.lib.layout.listener.OnRefreshListener
import com.aquila.lib.tools.util.ToastUtil
import com.aquila.lib.widget.view.CustomRecyclerView
import com.wedevote.wdbook.R
import com.wedevote.wdbook.base.ExceptionHandler
import com.wedevote.wdbook.base.RootActivity
import com.wedevote.wdbook.base.SDKSingleton
import com.wedevote.wdbook.entity.home.NewWidgetDetailEntity
import com.wedevote.wdbook.tools.event.OnSuccessBuyEvent
import com.wedevote.wdbook.tools.util.APPUtil
import com.wedevote.wdbook.tools.util.IntentConstants
import com.wedevote.wdbook.ui.home.microwidget.BookItemRecyclerAdapter
import com.wedevote.wdbook.ui.widgets.CommTopTitleLayout
import com.wedevote.wdbook.utils.JsonUtility
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

/***
 * @date 创建时间 2022/2/7 16:12
 * <AUTHOR> W.YuLong
 * @description
 */
class BookSectionListActivity : RootActivity(), OnRefreshListener, OnLoadMoreListener {

    lateinit var titleLayout: CommTopTitleLayout
    lateinit var refreshLayout: SmartRefreshLayout
    lateinit var dataRecyclerView: CustomRecyclerView

    lateinit var detailEntity: NewWidgetDetailEntity
    lateinit var dataAdapter: BookItemRecyclerAdapter
    private var isFirst: Boolean = true

    var page = 1
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_sections_list_layout)
        titleLayout = findViewById(R.id.sections_list_title_layout)
        refreshLayout = findViewById(R.id.sections_list_data_RefreshLayout)
        dataRecyclerView = findViewById(R.id.sections_list_data_RecyclerView)
    
        var json = intent.getStringExtra(IntentConstants.EXTRA_WidgetDetailEntityJson)
        if (!json.isNullOrEmpty()) {
            detailEntity = JsonUtility.decodeFromString(json)
            detailEntity.widgetViewParam = detailEntity.widgetViewParam
            titleLayout.setTitle(detailEntity.widgetTitle)

            dataAdapter = BookItemRecyclerAdapter()
            dataRecyclerView.adapter = dataAdapter
            onRefresh(refreshLayout)
        } else {
            ToastUtil.showToastShort(getString(R.string.access_errors))
            onBackPressed()
        }
        refreshLayout.setOnRefreshListener(this).setOnLoadMoreListener(this)
    }

    override fun onRefresh(layout: RefreshLayout?) {
        page = 1
        dataAdapter.clearDataList()
        refreshLayout.isEnableLoadMore = true
        onLoadMore(refreshLayout)
    }

    override fun onLoadMore(layout: RefreshLayout) {
        MainScope().launch(ExceptionHandler.coroutineExceptionHandler) {
            if (isFirst) {
                APPUtil.showLoadingDialog(this@BookSectionListActivity)
                isFirst = false
            }
            val paramsStr = detailEntity.paramsMap[NewWidgetDetailEntity.KEY_requestParams]
            var subParamsMap = HashMap<String, String?>()
            if (!paramsStr.isNullOrEmpty()) {
                subParamsMap = JsonUtility.decodeFromString(paramsStr)
            }
            subParamsMap["page"] = page.toString()
            page++
            val url = APPUtil.removeUrlParam(detailEntity.paramsMap[NewWidgetDetailEntity.KEY_dataSource].toString(), "page")
            SDKSingleton.storeBl.getRecommendDataList(url, subParamsMap)
                .also { entity ->
                    dataAdapter.addDataList(entity.productList)
                    if (entity.productList.isNullOrEmpty()) {
                        refreshLayout.isEnableLoadMore = false
                    }
                }
            APPUtil.dismissLoadingDialog(this@BookSectionListActivity)
            refreshLayout.finishLoadMoreAndRefresh()
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onReceiveBuyResultEvent(event: OnSuccessBuyEvent) {
        dataAdapter.updateProductPurchaseStatus(event.productId)
    }
}
