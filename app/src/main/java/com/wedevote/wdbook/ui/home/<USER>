package com.wedevote.wdbook.ui.home

import android.content.Context
import android.util.AttributeSet
import android.view.View
import androidx.constraintlayout.widget.ConstraintLayout
import com.aquila.lib.widget.group.GroupImageTextLayout
import com.aquila.lib.widget.view.DotView
import com.wedevote.wdbook.R
import com.wedevote.wdbook.tools.util.AnalyticsUtils

/***
 * @date 创建时间 2020/4/9 13:57
 * <AUTHOR> W<PERSON><PERSON>ong
 * @description
 */
class HomeBottomNavigationLayout(
    context: Context,
    attrs: AttributeSet? = null,
) : ConstraintLayout(context, attrs), View.OnClickListener {

    private val bookShelfLayout: GroupImageTextLayout
    private val bookStoreLayout: GroupImageTextLayout
    private val mineLayout: GroupImageTextLayout
    private val dotView: DotView

    private var currentTab = HomeTab.EMPTY

    var onTabSelectListener: OnTabSelectListener? = null

    init {
        View.inflate(context, R.layout.widget_home_bottom_navigation_layout, this)
        bookShelfLayout = findViewById(R.id.bottom_tab_book_shelf_Layout)
        bookStoreLayout = findViewById(R.id.bottom_tab_book_store_Layout)
        mineLayout = findViewById(R.id.bottom_tab_mine_Layout)
        dotView = findViewById(R.id.bottom_tab_mine_unread_message_View)

        bookShelfLayout.setOnClickListener(this)
        bookStoreLayout.setOnClickListener(this)
        mineLayout.setOnClickListener(this)
    }

    fun setCurrentTabUI(tab: HomeTab) {
        if (tab != currentTab) {
            bookShelfLayout.setSelectStatus(false)
            bookStoreLayout.setSelectStatus(false)
            mineLayout.setSelectStatus(false)

            currentTab = tab
            if (tab == HomeTab.EMPTY) {
                return
            }
            val currentLayout = when (tab) {
                HomeTab.SHELF -> bookShelfLayout
                HomeTab.STORE -> bookStoreLayout
                HomeTab.MINE -> mineLayout
                else -> bookStoreLayout
            }
            currentLayout.setSelectStatus(true)

            // submit all Pinpoint events saved in local Sqlite database
            AnalyticsUtils.pinpointSubmitEvents()
        }
    }

    fun setDotViewShowState(show: Boolean) {
        dotView.visibility = if (show) View.VISIBLE else View.GONE
    }

    override fun onClick(v: View) {
        when (v) {
            bookShelfLayout -> onTabSelectListener?.onTabSelect(HomeTab.SHELF)
            bookStoreLayout -> onTabSelectListener?.onTabSelect(HomeTab.STORE)
            mineLayout -> onTabSelectListener?.onTabSelect(HomeTab.MINE)
        }
    }

    interface OnTabSelectListener {
        fun onTabSelect(tab: HomeTab)
    }
}
