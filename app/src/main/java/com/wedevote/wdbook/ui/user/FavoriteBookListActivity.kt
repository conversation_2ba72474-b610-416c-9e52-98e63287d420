package com.wedevote.wdbook.ui.user

import android.annotation.SuppressLint
import android.os.Bundle
import android.text.Html
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.aquila.lib.base.BaseRecycleAdapter
import com.aquila.lib.base.BaseViewHolder
import com.aquila.lib.layout.SmartRefreshLayout
import com.aquila.lib.layout.api.RefreshLayout
import com.aquila.lib.layout.listener.OnLoadMoreListener
import com.aquila.lib.layout.listener.OnRefreshListener
import com.aquila.lib.log.KLog
import com.aquila.lib.tools.util.ToastUtil
import com.aquila.lib.widget.view.AdaptiveImageView
import com.aquila.lib.widget.view.CustomRecyclerView
import com.chauthai.swipereveallayout.SwipeRevealLayout
import com.wedevote.wdbook.R
import com.wedevote.wdbook.base.ExceptionHandler
import com.wedevote.wdbook.base.RootActivity
import com.wedevote.wdbook.base.SDKSingleton
import com.wedevote.wdbook.entity.store.FavoriteBookEntity
import com.wedevote.wdbook.tools.event.OnFavoriteEvent
import com.wedevote.wdbook.tools.util.APPUtil
import com.wedevote.wdbook.tools.util.PictureUtil
import com.wedevote.wdbook.tools.util.UnitFormatUtil
import com.wedevote.wdbook.tools.util.findString
import com.wedevote.wdbook.ui.store.BookDetailActivity
import com.wedevote.wdbook.ui.widgets.CommTopTitleLayout
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

/***
 * @date 创建时间 2022/2/17 14:31
 * <AUTHOR> W.YuLong
 * @description 我的收藏列表页面
 */
class FavoriteBookListActivity : RootActivity(), View.OnClickListener, OnLoadMoreListener, OnRefreshListener {

    private var isFirst: Boolean = true
    lateinit var titleLayout: CommTopTitleLayout
    lateinit var editTextView: TextView
    lateinit var allCheckTextView: TextView
    lateinit var nodataTextView: TextView
    lateinit var deleteButton: Button
    lateinit var dataRecyclerView: CustomRecyclerView
    lateinit var dataRefreshLayout: SmartRefreshLayout

    lateinit var favoriteAdapter: FavoriteAdapter

    var lastUpdateTime = 0L

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_favorite_book_list_layout)
        initViewFromXML()

        favoriteAdapter = FavoriteAdapter()
        dataRecyclerView.adapter = favoriteAdapter

        serViewListeners()
        onRefresh(dataRefreshLayout)
    }

    private fun serViewListeners() {
        allCheckTextView.setOnClickListener(this)
        editTextView.setOnClickListener(this)
        deleteButton.setOnClickListener(this)
        dataRefreshLayout.setOnRefreshListener(this).setOnLoadMoreListener(this)
        favoriteAdapter.onAllDataSelectListener = object : OnDataChangeSelectListener {
            override fun isAllSelected(isAllSelected: Boolean) {
                allCheckTextView.text = findString(if (isAllSelected) R.string.inverse_select else R.string.select_all)
            }

            override fun itemCount(count: Int) {
                editTextView.visibility = if (count == 0) View.GONE else View.VISIBLE
            }
        }
        favoriteAdapter.onLongClickListener = object : View.OnLongClickListener {
            override fun onLongClick(v: View?): Boolean {
                favoriteAdapter.isEditMode = true
                setEditUIMode(favoriteAdapter.isEditMode)
                return true
            }
        }
    }

    private fun initViewFromXML() {
        titleLayout = findViewById(R.id.favorite_top_title_layout)
        editTextView = findViewById(R.id.favorite_edit_TextView)
        allCheckTextView = findViewById(R.id.favorite_all_check_TextView)
        nodataTextView = findViewById(R.id.favorite_no_data_TextView)
        deleteButton = findViewById(R.id.favorite_delete_Button)
        dataRecyclerView = findViewById(R.id.favorite_data_RecyclerView)
        dataRefreshLayout = findViewById(R.id.favorite_data_RefreshLayout)
    }

    override fun onRefresh(layout: RefreshLayout?) {
        favoriteAdapter.itemOpenPosition = -1
        favoriteAdapter.isEditMode = false
        setEditUIMode(favoriteAdapter.isEditMode)

        lastUpdateTime = 0
        dataRefreshLayout.isEnableLoadMore = true
        favoriteAdapter.clearDataList()
        onLoadMore(dataRefreshLayout)
    }

    override fun onLoadMore(refreshLayout: RefreshLayout) {
        MainScope().launch(ExceptionHandler.coroutineExceptionHandler) {
            if (isFirst) {
                APPUtil.showLoadingDialog(this@FavoriteBookListActivity)
                isFirst = false
            }
            val favoriteList = SDKSingleton.userBl.getFavoriteBookList(lastUpdateTime)?.toMutableList()
            favoriteAdapter.addDataList(favoriteList)
            if (!favoriteAdapter.dataList.isNullOrEmpty()) {
                lastUpdateTime = favoriteAdapter.dataList!![0].lastUpdateTime!!
                for (item in favoriteAdapter.dataList!!) {
                    if (lastUpdateTime > item.lastUpdateTime!!) {
                        lastUpdateTime = item.lastUpdateTime!!
                    }
                }
                KLog.d("lastUpdateTime = $lastUpdateTime")
            }

            if (favoriteList.isNullOrEmpty()) {
                dataRefreshLayout.isEnableLoadMore = false
            }

            if (favoriteAdapter.itemCount == 0) {
                editTextView.visibility = View.GONE
                nodataTextView.visibility = View.VISIBLE
                dataRecyclerView.visibility = View.GONE
            } else {
                nodataTextView.visibility = View.GONE
                editTextView.visibility = View.VISIBLE
                dataRecyclerView.visibility = View.VISIBLE
            }

            APPUtil.dismissLoadingDialog(this@FavoriteBookListActivity)
            dataRefreshLayout.finishLoadMoreAndRefresh()
        }
    }

    fun setEditUIMode(isEditMode: Boolean) {
        if (isEditMode) {
            editTextView.setText(R.string.label_cancel)
            allCheckTextView.setText(R.string.select_all)
            allCheckTextView.visibility = View.VISIBLE
            titleLayout.backImageView.visibility = View.GONE
            deleteButton.visibility = View.VISIBLE
        } else {
            editTextView.setText(R.string.edit)
            allCheckTextView.visibility = View.GONE
            titleLayout.backImageView.visibility = View.VISIBLE
            deleteButton.visibility = View.GONE
        }
    }

//    fun showDeleteBatchData() {
//        CommAlertDialog.with(this)
//            .setTitle(R.string.remove_favorite_title)
//            .setMessage(getString(R.string.comform_remove_favorite))
//            .setStartText(R.string.label_cancel).setEndText(R.string.label_OK)
//            .setOnViewClickListener { dialog, view, i ->
//                if (i == CommAlertDialog.TAG_CLICK_END) {
//                    MainScope().launch(ExceptionHandler.coroutineExceptionHandler) {
//                        for (item in favoriteAdapter.selectItemList) {
//                            SDKSingleton.userBl.removeFavoriteBook(item.productId!!)
//                        }
//                        favoriteAdapter.isEditMode = false
//                        setEditUIMode(favoriteAdapter.isEditMode)
//                        onRefresh(dataRefreshLayout)
//                    }
//                }
//
//            }
//            .create().show()
//    }

    override fun onDestroy() {
        EventBus.getDefault().post(OnFavoriteEvent())
        super.onDestroy()
    }

    override fun onBackPressed() {
        if (favoriteAdapter.isEditMode) {
            favoriteAdapter.isEditMode = false
            setEditUIMode(favoriteAdapter.isEditMode)
        } else {
            super.onBackPressed()
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onFavoriteChangeEvent(event: OnFavoriteEvent) {
        onRefresh(dataRefreshLayout)
    }

    override fun onClick(v: View?) {
        when (v) {
            editTextView -> {
                favoriteAdapter.itemOpenPosition = -1
                favoriteAdapter.isEditMode = !favoriteAdapter.isEditMode
                setEditUIMode(favoriteAdapter.isEditMode)
            }
            allCheckTextView -> {
                favoriteAdapter.setSelectAll(!favoriteAdapter.isAllSelected())
            }
            deleteButton -> {
                if (favoriteAdapter.selectItemList.isNullOrEmpty()) {
                    ToastUtil.showToastShort(getString(R.string.select_book))
                    return
                }

                MainScope().launch(ExceptionHandler.coroutineExceptionHandler) {
                    APPUtil.showLoadingDialog(this@FavoriteBookListActivity)
                    var list = ArrayList<FavoriteBookEntity>().toMutableList()
                    favoriteAdapter.dataList?.let { list.addAll(it) }
                    for (item in favoriteAdapter.selectItemList) {
                        SDKSingleton.userBl.removeFavoriteBook(item.productId!!)
                        list.remove(item)
                    }
                    favoriteAdapter.clearDataList()
                    favoriteAdapter.addDataList(list)
                    setEmptyView()
                    favoriteAdapter.isEditMode = false
                    setEditUIMode(favoriteAdapter.isEditMode)
                    APPUtil.dismissLoadingDialog(this@FavoriteBookListActivity)
                }

//                showDeleteBatchData()
            }
        }
    }

    private fun setEmptyView() {
        if (favoriteAdapter.itemCount == 0) {
            editTextView.visibility = View.GONE
            nodataTextView.visibility = View.VISIBLE
            dataRecyclerView.visibility = View.GONE
        } else {
            nodataTextView.visibility = View.GONE
            editTextView.visibility = View.VISIBLE
            dataRecyclerView.visibility = View.VISIBLE
        }
    }
}

/***
 *@date 创建时间 2022/2/17 20:45
 *<AUTHOR> W.YuLong
 *@description
 */
class FavoriteAdapter : BaseRecycleAdapter<FavoriteBookEntity, FavoriteViewHolder>() {
    var selectItemList: ArrayList<FavoriteBookEntity> = ArrayList()
    var itemOpenPosition = -1
    var onAllDataSelectListener: OnDataChangeSelectListener? = null
    var onLongClickListener: View.OnLongClickListener? = null
    fun isAllSelected(): Boolean {
        return selectItemList.size == itemCount
    }

    fun setSelectAll(isSelectAll: Boolean) {
        selectItemList.clear()
        if (isSelectAll) {
            selectItemList.addAll(dataList!!.toMutableList())
        }
        onAllDataSelectListener?.isAllSelected(isAllSelected())
        notifyDataSetChanged()
    }

    override fun updateDataCount() {
        itemOpenPosition = -1
        onAllDataSelectListener?.isAllSelected(isAllSelected())
        super.updateDataCount()
    }

    var isEditMode = false
        set(value) {
            field = value
            selectItemList.clear()
            notifyDataSetChanged()
        }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): FavoriteViewHolder {
        return FavoriteViewHolder(parent)
    }

    override fun onBindViewHolder(holder: FavoriteViewHolder, @SuppressLint("RecyclerView") position: Int) {
        var dataEntity = dataList?.get(position)!!
        holder.initUIData(dataEntity)
        holder.setEditModeUI(isEditMode)
        holder.setSelectMode(selectItemList.contains(dataEntity))

        (holder.itemView as SwipeRevealLayout).let { swipeLayout ->
            if (itemOpenPosition == position) {
                swipeLayout.open(false)
            } else {
                swipeLayout.close(true)
            }
        }
        holder.itemView.setOnLongClickListener {
            if (!isEditMode) {
                onLongClickListener?.onLongClick(it)
            }
            return@setOnLongClickListener true
        }

        val swipeRevealLayout = holder.itemView as SwipeRevealLayout?
        swipeRevealLayout?.setLockDrag(isEditMode)

        swipeRevealLayout?.setSwipeListener(object : SwipeRevealLayout.SwipeListener {
            override fun onClosed(view: SwipeRevealLayout?) {
            }

            override fun onOpened(view: SwipeRevealLayout?) {
                itemOpenPosition = position
                notifyDataSetChanged()
            }

            override fun onSlide(view: SwipeRevealLayout?, slideOffset: Float) {
            }
        })

        holder.deleteButton.setOnClickListener {
            MainScope().launch(ExceptionHandler.coroutineExceptionHandler) {
                SDKSingleton.userBl.removeFavoriteBook(dataEntity.productId!!)
                deleteItem(dataEntity)
                notifyItemRemoved(position)
                onAllDataSelectListener?.itemCount(itemCount)
            }
        }
        holder.dataContainerLayout.setOnClickListener {
            if (isEditMode) {
                if (selectItemList.contains(dataEntity)) {
                    selectItemList.remove(dataEntity)
                } else {
                    selectItemList.add(dataEntity)
                }
                onAllDataSelectListener?.isAllSelected(isAllSelected())
                notifyDataSetChanged()
            } else {
                BookDetailActivity.gotoBookDetail(it.context, dataEntity.productId!!)
            }
        }
    }
}

/***
 *@date 创建时间 2022/2/17 20:45
 *<AUTHOR> W.YuLong
 *@description
 */
class FavoriteViewHolder(parent: ViewGroup) : BaseViewHolder(parent, R.layout.holder_favorite_item_layout) {

    val dataContainerLayout: ConstraintLayout = itemView.findViewById(R.id.item_favorite_data_container_Layout)
    val deleteButton: Button = itemView.findViewById(R.id.item_favorite_delete_Button)
    val coverImageView: AdaptiveImageView = itemView.findViewById(R.id.item_favorite_cover_ImageView)
    val nameTextView: TextView = itemView.findViewById(R.id.item_favorite_name_TextView)
    val authorTextView: TextView = itemView.findViewById(R.id.item_favorite_author_TextView)
    val originalPriceTextView: TextView = itemView.findViewById(R.id.item_favorite_original_price_TextView)
    val discountPriceTextView: TextView = itemView.findViewById(R.id.item_favorite_discount_price_TextView)
    val checkImageView: ImageView = itemView.findViewById(R.id.item_favorite_check_ImageView)
    val purchaseFlagImageView: ImageView = itemView.findViewById(R.id.item_favorite_purchase_flag_ImageView)

    var entity: FavoriteBookEntity? = null

    override fun <T> initUIData(t: T) {
        entity = t as FavoriteBookEntity
        if (!t.activitiesList.isNullOrEmpty()) {
            entity!!.currentPrice = entity!!.activitiesList!![0]!!.amount.toDouble()
            t.priceCNY = t.activitiesList!![0]!!.amountCNY.toDouble()
            t.discount = t.activitiesList!![0]!!.discount.toDouble()
        }
        PictureUtil.loadImage(coverImageView, t.cover!!)
        nameTextView.setText(t.title)
        authorTextView.setText(t.author)
        originalPriceTextView.text = APPUtil.setOriginalPrice(t.currency, t.originalPrice!!.toFloat())
        discountPriceTextView.text = APPUtil.setCurrentPrice(t.currency, t.currentPrice!!.toFloat(), t.priceCNY!!.toFloat(), t.discount!!.toFloat(), !t.activitiesList.isNullOrEmpty())

        purchaseFlagImageView.visibility = if (t.isPurchased == 1) View.VISIBLE else View.GONE
    }

    fun setEditModeUI(isEditMode: Boolean) {
        checkImageView.visibility = if (isEditMode) View.VISIBLE else View.GONE
    }

    fun setSelectMode(isSelected: Boolean) {
        checkImageView.isSelected = isSelected
    }
}
