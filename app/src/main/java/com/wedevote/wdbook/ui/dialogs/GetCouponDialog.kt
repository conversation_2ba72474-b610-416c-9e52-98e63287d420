package com.wedevote.wdbook.ui.dialogs

import android.content.Context
import android.os.Bundle
import android.view.Gravity
import android.view.View
import android.widget.TextView
import com.aquila.lib.widget.view.CustomRecyclerView
import com.wedevote.wdbook.R
import com.wedevote.wdbook.entity.coupon.CouponEntity
import com.wedevote.wdbook.ui.user.coupon.CouponItemAdapter
import com.wedevote.wdbook.ui.user.coupon.CouponItemType

/***
 * @date 创建时间 2022/6/9 14:40
 * <AUTHOR> W.<PERSON>Long
 * @description
 */
class GetCouponDialog(context: Context) : BaseDialog(context), View.OnClickListener {

    lateinit var titleTextView: TextView
    lateinit var dataRecyclerView: CustomRecyclerView


    lateinit var couponItemAdapter: CouponItemAdapter

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.dialog_get_coupon_layout)
        dataRecyclerView = findViewById(R.id.get_coupon_data_RecyclerView)

        couponItemAdapter = CouponItemAdapter(CouponItemType.GET_COUPON)
        dataRecyclerView.adapter = couponItemAdapter

        configDialog(Gravity.BOTTOM)
    }

    fun initDataList(list: List<CouponEntity>?) {
        couponItemAdapter.dataList = list?.toMutableList()
    }

    override fun onClick(v: View?) {
    }


}