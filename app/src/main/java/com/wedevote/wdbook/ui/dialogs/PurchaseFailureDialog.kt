package com.wedevote.wdbook.ui.dialogs

import android.content.Context
import android.os.Bundle
import android.view.View
import android.view.View.OnClickListener
import android.widget.Button
import android.widget.TextView
import com.aquila.lib.base.OnViewClickListener
import com.wedevote.wdbook.R

/***
 * @date 创建时间 2022/5/13 16:46
 * <AUTHOR> W<PERSON>
 * @description
 */
class PurchaseFailureDialog(context: Context) : BaseDialog(context), OnClickListener {

    lateinit var retryButton: Button
    lateinit var feedbackTextView: TextView

    var onViewClickListener: OnViewClickListener? = null

    companion object {
        const val ACTION_RETRY = "retry"
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.dialog_purchase_failure_layout)
        retryButton = findViewById(R.id.failure_retry_Button)
        feedbackTextView = findViewById(R.id.fauilure_feedback_TextView)

        retryButton.setOnClickListener(this)
        feedbackTextView.setOnClickListener(this)
    }

    override fun onClick(v: View?) {
        when (v) {
            retryButton -> {
                onViewClickListener?.onClickAction(v, ACTION_RETRY, "")
            }
            feedbackTextView -> {
                onViewClickListener?.onClickAction(v, "", "")

            }
        }
        dismiss()

    }
}