package com.wedevote.wdbook.ui.dialogs

import android.content.Context
import android.os.Bundle
import android.view.Gravity
import android.view.View
import android.widget.Button
import android.widget.TextView
import com.aquila.lib.base.OnViewClickListener
import com.wedevote.wdbook.R

/***
 *@date 创建时间  2023.4.17
 *<AUTHOR> <PERSON><PERSON>
 *@description
 */
class RegisterTipDialog(context: Context) : BaseDialog(context), View.OnClickListener {
    lateinit var tipTextView: TextView
    private lateinit var tipTitleTextView: TextView
    private lateinit var cancelButton: Button
    lateinit var okButton: Button
    var onViewClickListener: OnViewClickListener? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.dialog_register_tip_layout)
        tipTextView = findViewById(R.id.tip_content_TextView)
        tipTitleTextView = findViewById(R.id.tip_title_TextView)
        cancelButton = findViewById(R.id.tip_content_cancel_Button)
        okButton = findViewById(R.id.payment_result_OK_Button)

        okButton.setOnClickListener(this)
        cancelButton.setOnClickListener(this)

        configDialog(Gravity.CENTER)
        setCancelable(false)
        setCanceledOnTouchOutside(false)
    }

    override fun onClick(v: View?) {
        when (v) {
            okButton -> {
                onViewClickListener?.onClickAction(v, "", "")
                dismiss()
            }
            cancelButton -> {
                dismiss()
            }
        }
    }

    fun setContentText(content: String) {
        tipTextView.text = content
    }

    fun setTitleText(content: String) {
        tipTitleTextView.text = content
    }

    fun setCancelButtonVisible(isVisible: Boolean) {
        cancelButton.visibility = if (isVisible) View.VISIBLE else View.GONE
    }

    fun setRightButtonText(text: String) {
        okButton.text = text
    }

    fun setLeftButtonText(text: String) {
        cancelButton.text = text
    }
}
