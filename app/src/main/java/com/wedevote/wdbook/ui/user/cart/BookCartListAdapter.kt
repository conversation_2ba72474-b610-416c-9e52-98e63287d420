package com.wedevote.wdbook.ui.user.cart

import android.annotation.SuppressLint
import android.app.Dialog
import android.content.Context
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.ImageView
import android.widget.TextView
import com.aquila.lib.base.BaseRecycleAdapter
import com.aquila.lib.base.BaseViewHolder
import com.aquila.lib.dialog.CommAlertDialog
import com.aquila.lib.dialog.OnDialogViewClickListener
import com.aquila.lib.log.KLog
import com.aquila.lib.widget.view.AdaptiveImageView
import com.chauthai.swipereveallayout.SwipeRevealLayout
import com.wedevote.wdbook.R
import com.wedevote.wdbook.entity.store.CartProductEntity
import com.wedevote.wdbook.tools.util.UnitFormatUtil
import com.wedevote.wdbook.ui.user.OnDataChangeSelectListener

/***
 * @date 创建时间 2022/9/21 15:45
 * <AUTHOR> <PERSON><PERSON>
 * @description
 */
class BookCartListAdapter(var onDataChangeSelectListener: OnDataChangeSelectListener) :
    BaseRecycleAdapter<CartProductEntity, BookCartViewHolder>() {
    
    var onDataRefreshLoadListener: OnDataRefreshLoadListener? = null
    
    var selectItemList: ArrayList<CartProductEntity> = ArrayList<CartProductEntity>()
        set(value) {
            field = value
            notifyDataSetChanged()
        }
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BookCartViewHolder {
        return BookCartViewHolder(parent)
    }
    
    override fun onBindViewHolder(holder: BookCartViewHolder, @SuppressLint("RecyclerView") position: Int) {
        val data = getDataFromPosition(position)
        holder.initUIData(data)
        holder.setItemSelectStatus(selectItemList.contains(data))
        
        if (data!!.productId == PublisherCartListAdapter.openProductId) {
            holder.swipeLayout.open(false)
        } else {
            holder.swipeLayout.close(true)
        }
        
        holder.swipeLayout.setSwipeListener(object : SwipeRevealLayout.SwipeListener {
            override fun onClosed(view: SwipeRevealLayout?) {
            }
            
            override fun onOpened(view: SwipeRevealLayout?) {
                PublisherCartListAdapter.openProductId = data.productId
                onDataRefreshLoadListener?.onRefreshDataLoad()
            }
            
            override fun onSlide(view: SwipeRevealLayout?, slideOffset: Float) {
            }
        })
        
        var onClickListener = object : View.OnClickListener {
            override fun onClick(v: View?) {
                when (v) {
                    holder.dataContainerLayout -> {
                        KLog.d("点击了item")
                        if (selectItemList.contains(data)) {
                            selectItemList.remove(data)
                            PublisherCartListAdapter.centerSelectList.remove(data!!)
                        } else {
                            selectItemList.add(data!!)
                            PublisherCartListAdapter.centerSelectList.add(data!!)
                        }
                        onDataChangeSelectListener.isAllSelected(selectItemList.size == itemCount && itemCount > 0)
                        notifyDataSetChanged()
                    }
                    holder.deleteButton -> {
                        showDeleteDialog(v.context, data, position)
        
                    }
                    holder.favoriteButton -> {}
                }
            }
        }
        holder.dataContainerLayout.setOnClickListener(onClickListener)
        holder.deleteButton.setOnClickListener(onClickListener)
        holder.favoriteButton.setOnClickListener(onClickListener)
    }
    
    fun showDeleteDialog(context: Context, cartProductEntity: CartProductEntity, position: Int) {
        CommAlertDialog.with(context)
            .setMessage("确认删除吗?")
            .setStartText(R.string.label_cancel).setEndText(R.string.label_OK)
            .setOnViewClickListener(object : OnDialogViewClickListener {
                override fun onViewClick(dialog: Dialog, v: View, tag: Int) {
                    if (tag == CommAlertDialog.TAG_CLICK_END) {
                        onDataRefreshLoadListener?.onDataDelete(cartProductEntity)
                    }
                }
            }).showDialog()
    }
    
    fun doSelectAll() {
        selectItemList.clear()
        if (!dataList.isNullOrEmpty()) {
            for (item in dataList!!) {
                selectItemList.add(item)
            }
        }
        onDataChangeSelectListener.isAllSelected(selectItemList.size == itemCount && itemCount > 0)
        notifyDataSetChanged()
    }
    
    fun unSelectAll() {
        selectItemList.clear()
        onDataChangeSelectListener.isAllSelected(selectItemList.size == itemCount && itemCount > 0)
        notifyDataSetChanged()
    }
    
    
}

/***
 * @date 创建时间 2022/9/21 15:45
 * <AUTHOR> W.YuLong
 * @description
 */
class BookCartViewHolder(parent: ViewGroup) : BaseViewHolder(parent, R.layout.item_cart_book_info_layout) {
    val checkImageView: ImageView = itemView.findViewById(R.id.book_cart_check_flag_ImageView)
    val coverImageView: AdaptiveImageView = itemView.findViewById(R.id.book_cart_cover_ImageView)
    val nameTextView: TextView = itemView.findViewById(R.id.book_cart_name_TextView)
    val realPriceTextView: TextView = itemView.findViewById(R.id.book_cart_real_price_TextView)
    val originalTextView: TextView = itemView.findViewById(R.id.book_cart_original_price_TextView)
    val couponTextView: TextView = itemView.findViewById(R.id.book_cart_coupon_TextView)
    val hasRemove: TextView = itemView.findViewById(R.id.book_cart_has_removed_TextView)
    val favoriteButton: Button = itemView.findViewById(R.id.book_cart_move_favorite_Button)
    val deleteButton: Button = itemView.findViewById(R.id.book_cart_delete_Button)
    val dataContainerLayout: ViewGroup = itemView.findViewById(R.id.book_cart_content_container_Layout)
    val swipeLayout: SwipeRevealLayout = itemView as SwipeRevealLayout
    
    override fun <T> initUIData(t: T) {
        t as CartProductEntity
        nameTextView.text = t.title
        realPriceTextView.text = UnitFormatUtil.formatPrice(t.currency, t.putInPrice)
        originalTextView.text = UnitFormatUtil.formatPrice(t.currency, t.originalPrice)
        if (t.status != 1) {
            hasRemove.visibility = View.VISIBLE
        }
        
        couponTextView.visibility = if (t.couponsList.isNullOrEmpty()) View.GONE else View.VISIBLE
        originalTextView.text = UnitFormatUtil.formatPrice(t.currency, t.originalPrice)
        
    }
    
    fun setItemSelectStatus(isSelect: Boolean) {
        checkImageView.isSelected = isSelect
    }
    
    
}