package com.wedevote.wdbook.ui.read

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.fragment.app.DialogFragment
import com.aquila.lib.base.BaseRecycleAdapter
import com.aquila.lib.base.OnViewClickListener
import com.aquila.lib.dialog.CommAlertDialog
import com.aquila.lib.layout.SmartRefreshLayout
import com.aquila.lib.layout.api.RefreshLayout
import com.aquila.lib.layout.listener.OnLoadMoreListener
import com.aquila.lib.layout.listener.OnRefreshListener
import com.aquila.lib.widget.view.CustomRecyclerView
import com.chauthai.swipereveallayout.SwipeRevealLayout
import com.wedevote.wdbook.R
import com.wedevote.wdbook.base.ExceptionHandler
import com.wedevote.wdbook.base.SDKSingleton
import com.wedevote.wdbook.entity.NoteEntity
import com.wedevote.wdbook.tools.event.BookDataChangeEvent
import com.wedevote.wdbook.tools.event.OnSyncNoteFinish
import com.wedevote.wdbook.tools.util.IntentConstants
import com.wedevote.wdbook.tools.util.findString
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus

/***
 *@date 创建时间 1/13/21 5:04 PM
 *<AUTHOR> Qian kai
 *@description 笔记回收站页面
 */
class NoteRecycleBinDialogFragment : DialogFragment(), OnLoadMoreListener, OnRefreshListener, View.OnClickListener {
    lateinit var dataRecycleView: CustomRecyclerView
    lateinit var noDataLinearLayout: LinearLayout
    lateinit var clearButton: Button
    lateinit var notesRecycleBinAdapter: NotesRecycleBinAdapter
    lateinit var refreshLayout: SmartRefreshLayout
    lateinit var maskView: View
    lateinit var rootContainerLayout: ConstraintLayout
    lateinit var closeImageView: ImageView
    lateinit var bottomLineView: View
    lateinit var titleTextView: TextView
    var resourceId: String = ""
    var onDataChangeListener: OnViewClickListener? = null
    var noteList = ArrayList<NoteEntity>()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL, R.style.Dialog_FullScreen)
        resourceId = arguments?.getString(IntentConstants.EXTRA_ResourceId)!!
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        val v = inflater.inflate(R.layout.fragment_book_notes_recycle_layout, container, false)
        dataRecycleView = v.findViewById(R.id.notes_recycle_data_RecyclerView)
        noDataLinearLayout = v.findViewById(R.id.notes_recycle_no_data_LinearLayout)
        clearButton = v.findViewById(R.id.notes_recycle_clear_Button)
        refreshLayout = v.findViewById(R.id.notes_recycle_data_SmartRefreshLayout)
        maskView = v.findViewById(R.id.notes_recycle_mask_View)
        rootContainerLayout = v.findViewById(R.id.notes_recycle_container_layout)
        closeImageView = v.findViewById(R.id.notes_recycle_close_ImageView)
        bottomLineView = v.findViewById(R.id.notes_recycle_bottom_line_View)
        titleTextView = v.findViewById(R.id.notes_recycle_title_TextView)

        resourceId = arguments?.getString(IntentConstants.EXTRA_ResourceId)!!

        notesRecycleBinAdapter = NotesRecycleBinAdapter(object : OnViewClickListener {
            override fun <T> onClickAction(v: View, str: String, t: T?) {
                setCurrentUIShowStatus(notesRecycleBinAdapter.dataList)
                onDataChangeListener?.onClickAction(v, str, t)
            }
        })
        dataRecycleView.adapter = notesRecycleBinAdapter
        reloadNotesData()
        setViewListeners()
        return v
    }

    private fun setViewListeners() {
        clearButton.setOnClickListener(this)
        refreshLayout.setOnRefreshListener(this).setOnLoadMoreListener(this)
        closeImageView.setOnClickListener(this)
    }

    override fun onClick(v: View?) {
        when (v) {
            clearButton -> {
                CommAlertDialog.with(v.context).setMessage(R.string.are_you_sure_clear_note_recycle)
                    .setStartText(R.string.label_cancel).setEndText(findString(R.string.clear))
                    .setOnViewClickListener { _, _, tag ->
                        if (tag == CommAlertDialog.TAG_CLICK_END) {
                            MainScope().launch(ExceptionHandler.coroutineExceptionHandler) {
                                notesRecycleBinAdapter.dataList!!.forEach {
                                    it.dataId?.let {
                                        SDKSingleton.dbWrapBl.deleteNote(it)
                                    }
                                }
                                SDKSingleton.syncBl.syncNoteData()
                                notesRecycleBinAdapter.clearDataList()
                                setCurrentUIShowStatus(notesRecycleBinAdapter.dataList)
                                BookReadActivity.isOperateBookData = true
                                EventBus.getDefault().post(OnSyncNoteFinish())
                            }
                        }
                    }.create().show()
            }
            closeImageView -> {
                dismiss()
            }
        }
    }

//    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
//        super.onViewCreated(view, savedInstanceState)
//        rootContainerLayout.post {
//            ObjectAnimator.ofFloat(rootContainerLayout, "translationY", rootContainerLayout.height.toFloat(), 0f).apply {
//                duration = 300
//                addListener(object : AnimatorListenerAdapter() {
//                    override fun onAnimationEnd(animation: Animator) {
//                        super.onAnimationEnd(animation)
//                        closeImageView.visibility = View.VISIBLE
//                        titleTextView.visibility = View.VISIBLE
//                        reloadNotesData()
//                    }
//                })
//            }.start()
//            ObjectAnimator.ofFloat(maskView, "alpha", 0f, 1f).apply {
//                addListener(object : AnimatorListenerAdapter() {
//                    override fun onAnimationStart(animation: Animator) {
//                        super.onAnimationStart(animation)
//                        maskView.visibility = View.VISIBLE
//                    }
//                })
//                duration = 300
//            }.start()
//        }
//    }

    private fun reloadNotesData() {
        noteList = SDKSingleton.dbWrapBl.getRemovedNoteEntityList(resourceId) as ArrayList<NoteEntity>
        notesRecycleBinAdapter.dataList = noteList
        setCurrentUIShowStatus(noteList)
    }

    fun setCurrentUIShowStatus(noteList: List<NoteEntity>?) {
        if (!noteList.isNullOrEmpty()) {
            noDataLinearLayout.visibility = View.GONE
            dataRecycleView.visibility = View.VISIBLE
            clearButton.visibility = View.VISIBLE
            bottomLineView.visibility = View.VISIBLE
        } else {
            noDataLinearLayout.visibility = View.VISIBLE
            dataRecycleView.visibility = View.GONE
            clearButton.visibility = View.GONE
            bottomLineView.visibility = View.GONE
        }
    }

    override fun onLoadMore(layout: RefreshLayout) {
        refreshLayout.isEnableLoadMore = false
        refreshLayout.finishLoadMoreAndRefresh()
    }

    override fun onRefresh(layout: RefreshLayout) {
        MainScope().launch(ExceptionHandler.coroutineExceptionHandler) {
            SDKSingleton.syncBl.syncNoteData()
            notesRecycleBinAdapter.clearDataList()
            reloadNotesData()
            refreshLayout.finishLoadMoreAndRefresh()
        }
    }

    /***
     *@date 创建时间 1/13/21 5:05 PM
     *<AUTHOR> Qian kai
     *@description
     */
    class NotesRecycleBinAdapter(var onViewClickListener: OnViewClickListener? = null) :
        BaseRecycleAdapter<NoteEntity, NotesRecycleBinViewHolder>() {
        var itemOpenPosition = -1

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): NotesRecycleBinViewHolder {
            return NotesRecycleBinViewHolder(parent)
        }

        override fun updateDataCount() {
            super.updateDataCount()
            itemOpenPosition = -1
        }

        override fun onBindViewHolder(holderRecycleBin: NotesRecycleBinViewHolder, @SuppressLint("RecyclerView") position: Int) {
            val data = dataList!![position]
            holderRecycleBin.initUIData(data)

            val swipeLayout = holderRecycleBin.itemView as SwipeRevealLayout
            swipeLayout.setSwipeListener(object : SwipeRevealLayout.SwipeListener {
                override fun onClosed(view: SwipeRevealLayout?) {
                }

                override fun onOpened(view: SwipeRevealLayout?) {
                    itemOpenPosition = position
                    notifyDataSetChanged()
                }

                override fun onSlide(view: SwipeRevealLayout?, slideOffset: Float) {}
            })

            if (itemOpenPosition == position) {
                swipeLayout.open(false)
            } else {
                swipeLayout.close(true)
            }

            holderRecycleBin.contentLayout.setOnClickListener { v ->
                if (itemOpenPosition != -1) {
                    itemOpenPosition = -1
                    notifyDataSetChanged()
                } else {
                    CommAlertDialog.with(v.context).setTitle(R.string.can_not_open_note).setMessage(R.string.please_restore_note)
                        .setStartText(R.string.label_cancel).setEndText(R.string.restore)
                        .setOnViewClickListener { _, _, tag ->
                            if (tag == CommAlertDialog.TAG_CLICK_END) {
                                doRecover(v, data, position)
                            }
                        }.create().show()
                }
            }

            holderRecycleBin.deleteButton.setOnClickListener { v ->
                CommAlertDialog.with(v.context).setTitle(R.string.label_delete)
                    .setMessage(R.string.delete_note_confirm).setStartText(R.string.label_cancel).setEndText(R.string.label_delete)
                    .setOnViewClickListener { _, _, tag ->
                        if (tag == CommAlertDialog.TAG_CLICK_END) {
                            MainScope().launch(ExceptionHandler.coroutineExceptionHandler) {
                                data.dataId?.let { SDKSingleton.dbWrapBl.deleteNote(it) }
                                itemOpenPosition = -1
                                deleteItemIndex(position)
                                BookReadActivity.isOperateBookData = true
                                EventBus.getDefault().post(OnSyncNoteFinish())
                                SDKSingleton.syncBl.uploadNoteData()
                                onViewClickListener?.onClickAction(v, "Delete", data)
                            }
                        }
                    }.create().show()
            }
            holderRecycleBin.recoverButton.setOnClickListener { v ->
                CommAlertDialog.with(v.context).setTitle(R.string.restore).setMessage(R.string.sure_to_restore_note)
                    .setStartText(R.string.label_cancel).setEndText(R.string.restore)
                    .setOnViewClickListener { _, _, tag ->
                        if (tag == CommAlertDialog.TAG_CLICK_END) {
                            doRecover(v, data, position)
                        }
                    }.create().show()
            }
        }

        private fun doRecover(v: View, data: NoteEntity, position: Int) {
            MainScope().launch(ExceptionHandler.coroutineExceptionHandler) {
                data.dataId?.let { SDKSingleton.dbWrapBl.recoverNote(it) }
                itemOpenPosition = -1
                deleteItemIndex(position)
                BookReadActivity.isOperateBookData = true
                notifyDataSetChanged()
                SDKSingleton.syncBl.syncNoteData()
                EventBus.getDefault().post(OnSyncNoteFinish())
                EventBus.getDefault().post(BookDataChangeEvent())
                onViewClickListener?.onClickAction(v, "Recover", data)
            }
        }
    }

//    override fun dismiss() {
//        val alphaAnimator = ObjectAnimator.ofFloat(maskView, "alpha", 1f, 0f)
//        alphaAnimator.duration = 300
//        alphaAnimator.start()
//        val moveAnimator = ObjectAnimator.ofFloat(rootContainerLayout, "translationY", 0f, rootContainerLayout.height.toFloat())
//        moveAnimator.setDuration(300).addListener(object : AnimatorListenerAdapter() {
//            override fun onAnimationEnd(animation: Animator) {
//            }
//        })
//        moveAnimator.start()
//        Handler().postDelayed({ super.dismiss() }, 300)
//    }
}
