package com.wedevote.wdbook.ui.store

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.ImageView
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.aquila.lib.base.BaseRecycleAdapter
import com.aquila.lib.base.BaseViewHolder
import com.aquila.lib.base.OnViewClickListener
import com.aquila.lib.tools.singleton.SPSingleton
import com.aquila.lib.widget.group.GroupImageTextLayout
import com.aquila.lib.widget.group.GroupSideAlignLayout
import com.aquila.lib.widget.view.AdaptiveImageView
import com.aquila.lib.widget.view.CustomRecyclerView
import com.wedevote.wdbook.R
import com.wedevote.wdbook.base.APPConfig
import com.wedevote.wdbook.base.ExceptionHandler
import com.wedevote.wdbook.base.RootActivity
import com.wedevote.wdbook.base.SDKSingleton
import com.wedevote.wdbook.entity.coupon.CouponEntity
import com.wedevote.wdbook.entity.coupon.CouponStatus
import com.wedevote.wdbook.entity.store.AccountedProductsEntity
import com.wedevote.wdbook.entity.store.PaymentAmountEntity
import com.wedevote.wdbook.network.SystemParamDefine
import com.wedevote.wdbook.tools.payment.OnOrderPayCallback
import com.wedevote.wdbook.tools.payment.OrderPaymentHelper
import com.wedevote.wdbook.tools.util.APPUtil
import com.wedevote.wdbook.tools.util.PayTypeDefine
import com.wedevote.wdbook.tools.util.PictureUtil
import com.wedevote.wdbook.tools.util.SPKeyDefine
import com.wedevote.wdbook.tools.util.UnitFormatUtil
import com.wedevote.wdbook.tools.util.findString
import com.wedevote.wdbook.ui.dialogs.MultipleConfirmItemListDialog
import com.wedevote.wdbook.ui.dialogs.OnClickBuyListener
import com.wedevote.wdbook.ui.dialogs.PartPurchasedItemListDialog
import com.wedevote.wdbook.ui.dialogs.UsableCouponDialog
import com.wedevote.wdbook.ui.service.SyncDataService
import com.wedevote.wdbook.ui.widgets.CommTopTitleLayout
import com.wedevote.wdbook.ui.widgets.WidgetMineItemLayout
import com.wedevote.wdbook.utils.JsonUtility
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch

/***
 * @date 创建时间 2022/6/22 16:24
 * <AUTHOR> W.YuLong
 * @description 活动选择订单确认的支付页
 */
open class OrderConfirmActivity : RootActivity(), View.OnClickListener {

    lateinit var topTitleLayout: CommTopTitleLayout
    lateinit var dataRecyclerView: CustomRecyclerView
    lateinit var totalAmountLayout: GroupSideAlignLayout
    lateinit var needPayTextView: TextView

    lateinit var hasPurchaseLayout: RelativeLayout
    lateinit var purchasedTextView: TextView

    lateinit var useCouponLayout: WidgetMineItemLayout

    lateinit var creditCardLayout: ConstraintLayout
    lateinit var paypalLayout: GroupImageTextLayout
    lateinit var alipayLayout: GroupImageTextLayout
    lateinit var unbindHelperTextView: TextView

    lateinit var cardCheckImageView: ImageView
    lateinit var paypalCheckImageView: ImageView
    lateinit var alipayCheckImageView: ImageView

    lateinit var buyButton: Button

    lateinit var singleBookContainerLayout: ConstraintLayout
    lateinit var singleBookCoverImageView: AdaptiveImageView
    lateinit var singleBookNameTextView: TextView
    lateinit var singleBookPriceTextView: TextView
    lateinit var totalProductCountLayout: GroupImageTextLayout
    lateinit var bottomNeedPayTextView: TextView

    lateinit var productAdapter: ConfirmProductAdapter
    var clickBuyTime = 0L

    lateinit var paymentAmountEntity: PaymentAmountEntity
    var currentCouponEntity: CouponEntity? = null
    var orderId: Long = -1

    // 默认自动结算
    var trialCalType = 0

    var payType: String = SPSingleton.get().getString(SPKeyDefine.SP_LastPaymentStyle, PayTypeDefine.PAY_TYPE_CARD.value)!!

    private lateinit var orderPaymentHelper: OrderPaymentHelper

    companion object {
        var onClickBuyListener: OnClickBuyListener? = null
        fun gotoConfirmOrderActivity(
            context: Context,
            paymentAmountEntity: PaymentAmountEntity?,
            orderId: Long = -1,
            listener: OnClickBuyListener?,
        ) {
            if (!APPConfig.isFastClick()) {
                val intent = Intent(context, OrderConfirmActivity::class.java)
                if (paymentAmountEntity != null) {
                    intent.putExtra("PaymentAmountEntity", JsonUtility.encodeToString(paymentAmountEntity))
                }
                intent.putExtra("orderId", orderId)
                context.startActivity(intent)
                onClickBuyListener = listener
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_active_confirm_order_layout)
        initViewFromXML()
        productAdapter = ConfirmProductAdapter()
        dataRecyclerView.adapter = productAdapter
        intent.getStringExtra("PaymentAmountEntity")?.let {
            paymentAmountEntity = JsonUtility.decodeFromString(it)
        }
        orderId = intent.getLongExtra("orderId", -1)

        if (paymentAmountEntity.couponList.isNullOrEmpty()) { // 是否从活动页面过来
            useCouponLayout.visibility = View.GONE
        } else {
            currentCouponEntity = getMaxCoupon(paymentAmountEntity.couponList)
            useCouponLayout.visibility = View.VISIBLE
        }

        orderPaymentHelper = OrderPaymentHelper(
            this,
            object : OnOrderPayCallback {
                override fun onOrderPayResult(orderStatus: Int) {
                    when (orderStatus) {
                        OrderStatusDefine.ORDER_STATUS_SUCCEED, OrderStatusDefine.ORDER_STATUS_FINISH -> { // 成功
                            val intent = Intent(this@OrderConfirmActivity, SyncDataService::class.java)
                            startService(intent)
                            <EMAIL>()
                        }
                    }
                }

                override fun retryAgain() {
                    buyButton.performClick()
                }
            },
        )

        initDataFromServer()

        setViewListener()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        orderPaymentHelper.onActivityResult(requestCode, resultCode, data)
    }

    fun getMaxCoupon(couponList: ArrayList<CouponEntity>?): CouponEntity? {
        if (couponList.isNullOrEmpty()) {
            return null
        }
        var resultEntity: CouponEntity? = null
        for (item in couponList) {
            if (item.status == CouponStatus.CAN_USE.status) {
                if (resultEntity == null) {
                    resultEntity = item
                } else {
                    if (item.couponAmount > resultEntity.couponAmount) {
                        resultEntity = item
                    }
                }
            }
        }
        return resultEntity
    }

    private fun setViewListener() {
        creditCardLayout.setOnClickListener(this)
        paypalLayout.setOnClickListener(this)
        alipayLayout.setOnClickListener(this)
        unbindHelperTextView.setOnClickListener(this)
        buyButton.setOnClickListener(this)
        hasPurchaseLayout.setOnClickListener(this)
        useCouponLayout.setOnClickListener(this)
        totalProductCountLayout.setOnClickListener(this)
    }

    private fun initViewFromXML() {
        topTitleLayout = findViewById(R.id.order_confirm_title_layout)
        dataRecyclerView = findViewById(R.id.order_confirm_data_RecyclerView)
        totalAmountLayout = findViewById(R.id.order_confirm_total_amount_Layout)
        hasPurchaseLayout = findViewById(R.id.order_confirm_has_brought_Layout)
        purchasedTextView = findViewById(R.id.order_confirm_purchased_TextView)
        needPayTextView = findViewById(R.id.order_confirm_need_pay_TextView)
        creditCardLayout = findViewById(R.id.order_confirm_stripe_layout)
        paypalLayout = findViewById(R.id.order_confirm_paypal_layout)
        alipayLayout = findViewById(R.id.order_confirm_alipay_layout)
        unbindHelperTextView = findViewById(R.id.order_confirm_unbind_help_TextView)
        useCouponLayout = findViewById(R.id.order_confirm_use_coupon_layout)

        bottomNeedPayTextView = findViewById(R.id.confirm_activity_need_pay_price_TextView)
        singleBookPriceTextView = findViewById(R.id.order_confirm_single_book_price_TextView)
        singleBookNameTextView = findViewById(R.id.order_confirm_single_book_name_TextView)
        singleBookCoverImageView = findViewById(R.id.order_confirm_single_book_cover_ImageView)
        singleBookContainerLayout = findViewById(R.id.order_confirm_single_product_layout)
        totalProductCountLayout = findViewById(R.id.order_confirm_total_product_layout)

        cardCheckImageView = findViewById(R.id.order_confirm_stripe_check_status_ImageView)
        paypalCheckImageView = findViewById(R.id.order_confirm_paypal_check_status_ImageView)
        alipayCheckImageView = findViewById(R.id.order_confirm_alipay_check_status_ImageView)

        buyButton = findViewById(R.id.order_confirm_buy_Button)
    }

    private fun initUIShowStatus(isSingleProduct: Boolean) {
        if (isSingleProduct) {
            singleBookContainerLayout.visibility = View.VISIBLE
            totalProductCountLayout.visibility = View.GONE

            dataRecyclerView.visibility = View.GONE
        } else {
            singleBookContainerLayout.visibility = View.GONE
            totalProductCountLayout.visibility = View.VISIBLE
            dataRecyclerView.visibility = View.VISIBLE
        }
    }

    var couponIdList = ArrayList<Long>()
    var couponList = ArrayList<CouponEntity>()

    open fun initDataFromServer() {
        SDKSingleton.appBl.getSystemParam(SystemParamDefine.PAYMETHOD_CARD_DISABLE) {
            if (it?.value.equals("0")) {
                creditCardLayout.visibility = View.VISIBLE
            } else {
                creditCardLayout.visibility = View.GONE
            }
            SDKSingleton.appBl.getSystemParam(SystemParamDefine.PAYMETHOD_ALIPAY_DISABLE) {
                if (it?.value.equals("0")) {
                    alipayLayout.visibility = View.VISIBLE
                } else {
                    alipayLayout.visibility = View.GONE
                }
                SDKSingleton.appBl.getSystemParam(SystemParamDefine.PAYMETHOD_PAYPAL_DISABLE) {
                    if (it?.value.equals("0")) {
                        paypalLayout.visibility = View.VISIBLE
                    } else {
                        paypalLayout.visibility = View.GONE
                    }
                    initCurrentPayTypeUI(payType)
                }
            }
        }

        MainScope().launch(ExceptionHandler.getCoroutineExceptionHandler {
            orderPaymentHelper.doUnpaidInfoException(it, paymentAmountEntity.getProductIdList()) {
                paymentAmountEntity.purchasedProductList?.let { it1 ->
                    orderPaymentHelper.doPayActivityOrder(
                        it1,
                        activityIds = paymentAmountEntity.getActivityIdList(),
                    )
                }
            }
            APPUtil.dismissLoadingDialog(this@OrderConfirmActivity)
        }) {
            APPUtil.showLoadingDialog(this@OrderConfirmActivity)
            var entity: PaymentAmountEntity? = null
            if (orderId != -1L) {
                entity = SDKSingleton.paymentBl.getPayAmountEntityWithOrderId(orderId)
            } else {
                couponIdList = ArrayList()
                couponList = ArrayList()
                if (currentCouponEntity != null) {
                    couponIdList.add(currentCouponEntity!!.couponId)
                    couponList.add(currentCouponEntity!!)
                }
                entity = SDKSingleton.paymentBl.getPayAmountEntity(
                    paymentAmountEntity.getProductIdList(),
                    couponIdList,
                    null,
                    trialCalType,
                )
                entity?.trialCalType = trialCalType
            }

            if (entity != null) {
                paymentAmountEntity = entity
                initUI(entity)
            }

            APPUtil.dismissLoadingDialog(this@OrderConfirmActivity)
        }
    }

    open fun initUI(amountEntity: PaymentAmountEntity) {
        if (paymentAmountEntity.accountedProducts?.size == 1) {
            var entity = paymentAmountEntity.accountedProducts!![0]
            PictureUtil.loadImage(singleBookCoverImageView, entity.cover)
            singleBookNameTextView.text = entity.title
            singleBookPriceTextView.text = UnitFormatUtil.formatPrice(entity.currency, entity.price + entity.activityAmount + entity.couponAmount)
        } else {
            productAdapter.dataList = paymentAmountEntity.accountedProducts
            totalProductCountLayout.setText("共${productAdapter.itemCount}本")
        }
        // 调整当前的UI是否是单本书籍
        initUIShowStatus(productAdapter.itemCount == 0)

        var purchasedAmount = 0f
        if (!amountEntity.purchasedProductList.isNullOrEmpty()) {
            for (item in amountEntity.purchasedProductList!!) {
                purchasedAmount += item.price
            }
            purchasedTextView.setText("-${UnitFormatUtil.formatPrice(amountEntity.currency, purchasedAmount)}")
            hasPurchaseLayout.visibility = View.VISIBLE
        } else {
            hasPurchaseLayout.visibility = View.GONE
        }
        totalAmountLayout.setAppRightText("${UnitFormatUtil.formatPrice(amountEntity.currency, amountEntity.originalPrice - amountEntity.activityAmount)}")
        needPayTextView.text = UnitFormatUtil.formatPrice(amountEntity.currency, amountEntity.actualAmount)
        bottomNeedPayTextView.setText(UnitFormatUtil.formatPrice(amountEntity.currency, amountEntity.actualAmount))

        initSelectCouponUI(currentCouponEntity)
    }

    fun initSelectCouponUI(entity: CouponEntity?) {
        if (entity != null) {
            useCouponLayout.setDataText("-${UnitFormatUtil.formatPrice(paymentAmountEntity!!.currency, entity.couponAmount)}")
            useCouponLayout.setOnClickListener(this)
        } else {
            if (!paymentAmountEntity.couponList.isNullOrEmpty()) {
                useCouponLayout.setDataText(findString(R.string.do_not_use_coupon))
            } else {
                useCouponLayout.setOnClickListener(null)
                useCouponLayout.setDataText(findString(R.string.no_useable_coupon))
            }
        }
    }

    fun initCurrentPayTypeUI(payType: String) {
        when (payType) {
            PayTypeDefine.PAY_TYPE_CARD.value -> {
                if (creditCardLayout.visibility == View.VISIBLE) {
                    alipayCheckImageView.isSelected = false
                    cardCheckImageView.isSelected = true
                    paypalCheckImageView.isSelected = false
                }
            }
            PayTypeDefine.PAY_TYPE_ALIPAY.value -> {
                if (alipayLayout.visibility == View.VISIBLE) {
                    alipayCheckImageView.isSelected = true
                    cardCheckImageView.isSelected = false
                    paypalCheckImageView.isSelected = false
                }
            }
            PayTypeDefine.PAY_TYPE_PAYPAL.value -> {
                if (paypalLayout.visibility == View.VISIBLE) {
                    alipayCheckImageView.isSelected = false
                    cardCheckImageView.isSelected = false
                    paypalCheckImageView.isSelected = true
                }
            }
        }
        setPaymentType(payType)
        if (!alipayCheckImageView.isSelected && !cardCheckImageView.isSelected && !paypalCheckImageView.isSelected) {
            if (creditCardLayout.visibility == View.VISIBLE) {
                initCurrentPayTypeUI(PayTypeDefine.PAY_TYPE_CARD.value)
            } else if (alipayLayout.visibility == View.VISIBLE){
                initCurrentPayTypeUI(PayTypeDefine.PAY_TYPE_ALIPAY.value)
            } else if (paypalLayout.visibility == View.VISIBLE){
                initCurrentPayTypeUI(PayTypeDefine.PAY_TYPE_PAYPAL.value)
            }
        }

    }

    private fun setPaymentType(payType: String) {
        this.payType = payType
        SPSingleton.get().putString(SPKeyDefine.SP_LastPaymentStyle, payType)
    }

    override fun onClick(v: View?) {
        when (v) {
            totalProductCountLayout -> {
                paymentAmountEntity?.let {
                    var listDialog = MultipleConfirmItemListDialog(this)
                    listDialog.show()
                    listDialog.initDataList(it.accountedProducts)
                }
            }
            creditCardLayout -> {
                initCurrentPayTypeUI(PayTypeDefine.PAY_TYPE_CARD.value)
            }
            paypalLayout -> {
                initCurrentPayTypeUI(PayTypeDefine.PAY_TYPE_PAYPAL.value)
            }
            alipayLayout -> {
                initCurrentPayTypeUI(PayTypeDefine.PAY_TYPE_ALIPAY.value)
            }
            unbindHelperTextView -> {
                startActivity(Intent(this, UnbindCardHelperActivity::class.java))
            }
            useCouponLayout -> {
                if (!paymentAmountEntity.couponList.isNullOrEmpty()) {
                    var usableCouponList = ArrayList<CouponEntity>()
                    for (item in paymentAmountEntity.couponList!!) {
                        if (item.status == CouponStatus.CAN_USE.status) {
                            usableCouponList.add(item)
                        }
                    }

                    var useCouponDialog = UsableCouponDialog(this)
                    useCouponDialog.show()

                    useCouponDialog.initDataList(usableCouponList, currentCouponEntity)
                    useCouponDialog.onViewClickListener = object : OnViewClickListener {
                        override fun <T> onClickAction(v: View, str: String, t: T?) {
                            if (t != null) {
                                currentCouponEntity = t as CouponEntity?
                            } else {
                                currentCouponEntity = null
                            }
                            // 用户做了优惠券的操作之后变为手动计算
                            trialCalType = 1
                            initDataFromServer()
                        }
                    }
                }
            }
            hasPurchaseLayout -> {
                paymentAmountEntity?.let {
                    var purchasedItemListDialog = PartPurchasedItemListDialog(this)
                    purchasedItemListDialog.show()
                    purchasedItemListDialog.initDataList(it.purchasedProductList)
                }
            }

            buyButton -> {
                if (System.currentTimeMillis() - clickBuyTime < 3000) {
                    return
                }
                clickBuyTime = System.currentTimeMillis()
                if (orderId == -1L) {
                    couponIdList
                    var tempPaymentAmountEntity: PaymentAmountEntity = PaymentAmountEntity()
                    tempPaymentAmountEntity.actualAmount = paymentAmountEntity.actualAmount
                    tempPaymentAmountEntity.couponAmount = paymentAmountEntity.couponAmount
                    tempPaymentAmountEntity.activityAmount = paymentAmountEntity.activityAmount
                    tempPaymentAmountEntity.couponList = couponList
                    tempPaymentAmountEntity.activityEntityList = paymentAmountEntity.activityEntityList
                    tempPaymentAmountEntity.trialCalType = paymentAmountEntity.trialCalType
                    if (couponIdList.size == 0 && trialCalType == 1) {
                        tempPaymentAmountEntity?.couponList = ArrayList<CouponEntity>()
                    }
                    orderPaymentHelper.createPaymentOrder(payType, paymentAmountEntity.getProductIdList(), tempPaymentAmountEntity) {
                        orderId = it.toLong()
                    }
                } else {
                    orderPaymentHelper.doPayWithOrderId(orderId.toString(), paymentAmountEntity, payType)
                }
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        onClickBuyListener = null
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        setIntent(intent)
        handlePayPalReturn()
    }

    private fun handlePayPalReturn() {
        intent?.let { orderPaymentHelper.handlePayPalReturn(it) }
    }
}

/***
 *@date 创建时间 2022/6/22 17:35
 *<AUTHOR> W.YuLong
 *@description
 */
class ConfirmProductAdapter : BaseRecycleAdapter<AccountedProductsEntity, ConfirmProductViewHolder>() {
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ConfirmProductViewHolder {
        return ConfirmProductViewHolder(parent)
    }
}

/***
 *@date 创建时间 2022/6/22 17:20
 *<AUTHOR> W.YuLong
 *@description
 */
class ConfirmProductViewHolder(parent: ViewGroup) : BaseViewHolder(parent, R.layout.item_holder_confirm_multiple_book_layout) {
    val coverImageView: AdaptiveImageView = itemView.findViewById(R.id.item_confirm_book_cover_ImageView)
    override fun <T> initUIData(t: T) {
        t as AccountedProductsEntity
        PictureUtil.loadImage(coverImageView, t.cover)
        coverImageView.setOnClickListener {
            BookDetailActivity.gotoBookDetail(it.context, t.productId)
        }
    }
}
