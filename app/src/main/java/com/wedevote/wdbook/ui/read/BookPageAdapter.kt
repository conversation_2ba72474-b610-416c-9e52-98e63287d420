package com.wedevote.wdbook.ui.read

import android.annotation.SuppressLint
import android.content.ClipboardManager
import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.Typeface
import android.os.Handler
import android.os.Looper
import android.os.Message
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.FragmentActivity
import androidx.viewpager.widget.PagerAdapter
import com.aquila.lib.base.OnViewClickListener
import com.aquila.lib.log.KLog
import com.aquila.lib.tools.interfaceimpl.OnPageChangeListenerImpl
import com.aquila.lib.tools.singleton.SPSingleton
import com.wedevote.wdbook.tools.util.ImageLoadUtil
import com.aquila.lib.tools.util.ToastUtil
import com.wedevote.wdbook.R
import com.wedevote.wdbook.base.ExceptionHandler
import com.wedevote.wdbook.base.SDKSingleton
import com.wedevote.wdbook.constants.DataStatus
import com.wedevote.wdbook.constants.AnalyticsConstants
import com.wedevote.wdbook.constants.AnalyticsConstants.LOG_V1_PARAM_RESOURCE_ID
import com.wedevote.wdbook.entity.BookmarkEntity
import com.wedevote.wdbook.entity.NoteEntity
import com.wedevote.wdbook.entity.shelf.ReadProgressEntity
import com.wedevote.wdbook.tools.define.TextFontType
import com.wedevote.wdbook.tools.util.DataPathUtil
import com.wedevote.wdbook.tools.util.AnalyticsUtils.logEvent
import com.wedevote.wdbook.tools.util.IntentConstants
import com.wedevote.wdbook.tools.util.ParamsDefine
import com.wedevote.wdbook.tools.util.SPKeyDefine
import com.wedevote.wdbook.tools.util.dp2px
import com.wedevote.wdbook.tools.util.findString
import com.wedevote.wdbook.ui.dialogs.GuideDialog
import com.wedevote.wdbook.ui.read.lib.BookParameters
import com.wedevote.wdbook.ui.read.lib.BookTextControllerHelper
import com.wedevote.wdbook.ui.read.lib.EPubBook
import com.wedevote.wdbook.ui.read.lib.OnCalculateCompleteListener
import com.wedevote.wdbook.ui.read.lib.PageInfoCalculateHelper
import com.wedevote.wdbook.ui.read.widgets.BookReadToolLayout
import com.wedevote.wdbook.ui.read.widgets.OptionClickTag
import com.wedevote.wdbook.utils.JsonUtility
import kotlin.collections.set
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch

/***
 *@date 重构时间 2020/10/28 10:50
 *<AUTHOR> W.YuLong
 *@description
 */
class BookPageAdapter(private val activity: FragmentActivity, val viewPager: BookReadViewPager) :
    PagerAdapter() {

    private var textSizePx: Float
    private var bookPathList: ArrayList<String> = ArrayList()
    private var toolLayout: BookReadToolLayout? = null

    private var currentBookTextHelper: BookTextControllerHelper? = null
    private var leftBookTextHelper: BookTextControllerHelper? = null
    private var rightBookTextHelper: BookTextControllerHelper? = null

    private var currentPosition = Int.MAX_VALUE / 2
    private var indexInChapter = 0
    var currentPathIndex = 0

    private var readProgressEntity: ReadProgressEntity? = null
    private var resourceId: String? = null
    private var fileId: String? = null
    private var isSendComplete: Boolean = false
    private val hashMap = HashMap<Int, BookTextViewHolder>()

    var onViewClickListener: OnViewClickListener? = null

    var pageInfoCalculateHelper = PageInfoCalculateHelper()

    var readyToFlip = false

    var onUserNavigationListener: (() -> Unit)? = null
    var suppressNavigationCallback: Boolean = false

    private fun notifyUserNavigated() {
        if (!suppressNavigationCallback) {
            onUserNavigationListener?.invoke()
        }
    }

    //    var selectPageNumbers = 0
    val MSG_FLIP_NEXT = 0
    val MSG_FLIP_PREV = 1

    var lastReadTime = 0L

    private var isFirst: Boolean = true


    private val handler: Handler = object : Handler(Looper.getMainLooper()) {
        @SuppressLint("HandlerLeak")
        override fun handleMessage(msg: Message) {
            readyToFlip = false
            val bNext = (msg.what == MSG_FLIP_NEXT)
            var position = if (bNext) {
                currentPosition + 1
            } else {
                currentPosition - 1
            }
            if (hasPage(position)) {
                val currentViewHolder = hashMap[currentPosition]
                val prevWord = currentViewHolder?.getSelectedWordPosition(bNext)
                currentViewHolder?.hideTextSelectLayout()
                val nextViewHolder = hashMap[position]
                val index = if (bNext) {
                    indexInChapter + 1
                } else {
                    indexInChapter - 1
                }
                val firstLine = currentBookTextHelper!!.getPageFirstLineIndex(index)
                var lastLine = currentBookTextHelper!!.getPageLastLineIndex(index)
                if (lastLine >= currentBookTextHelper!!.textPageList.size) {
                    lastLine = currentBookTextHelper!!.textPageList.size - 1
                }
                nextViewHolder?.showTextSelectLayout(
                    firstLine,
                    lastLine,
                    0,
                    currentBookTextHelper!!.textPageList[lastLine].size,
                )
                nextViewHolder?.setPreviousSelectedWord(prevWord!!, bNext)
                checkAndLoadText(position)
                viewPager.setCurrentItem(position, true)
                notifyUserNavigated()
            }
        }
    }

    init {
        textSizePx = BookParameters.getTextSizePx()

        pageInfoCalculateHelper.setListener(object : OnCalculateCompleteListener {
            override fun onCalculateComplete() {
                for (bookTextViewHolder in hashMap.values) {
                    bookTextViewHolder.setPageString(
                        pageInfoCalculateHelper.getPageString(
                            bookTextViewHolder.pathIndex,
                            bookTextViewHolder.indexInChapter,
                        ),
                    )
                }
                initBookmarkState()
                setJumpProgress(currentPathIndex, indexInChapter)
            }
        })
        viewPager.addOnPageChangeListener(object : OnPageChangeListenerImpl() {
            override fun onPageSelected(position: Int) {
                checkAndLoadText(position)
                if (pageInfoCalculateHelper.getPercent(currentPathIndex, indexInChapter) >= 99) {
                    if (!isFirst && !isSendComplete) {
                        logEvent(
                            AnalyticsConstants.LOG_V1_BOOK_READ_COMPLETED,
                            LOG_V1_PARAM_RESOURCE_ID,
                            resourceId!!
                        )
                    }
                    isSendComplete = true
                }
                isFirst = false
                initBookmarkState()
                checkNoteFold(position)

                savePageReadDuration()

                notifyUserNavigated()
            }
        })
    }

    fun savePageReadDuration() {
        if (lastReadTime == 0L) {
            lastReadTime = System.currentTimeMillis() / 1000
            return
        }
        var pageReadDuration = System.currentTimeMillis() / 1000 - lastReadTime
        if (pageReadDuration > 3 * 60) {
            pageReadDuration = 3 * 60
        }
        resourceId?.let {
            SPSingleton.get().putLong(
                it,
                SPSingleton.get().getLong(it, 0L) + pageReadDuration,
            )
        }
        lastReadTime = System.currentTimeMillis() / 1000
    }

    fun setBookReadToolLayout(toolLayout: BookReadToolLayout?) {
        this.toolLayout = toolLayout
    }

    fun getTitle(index: Int): String {
        val path = bookPathList[index]
        return EPubBook.getTocTitle(path)
    }

    override fun instantiateItem(container: ViewGroup, position: Int): Any {
        var holderLayout: BookTextViewHolder? = null
        var pathIndex = 0
        var indexInChapter = 0
        if (position == currentPosition) {
            currentBookTextHelper!!.indexInChapter = this.indexInChapter
            holderLayout = BookTextViewHolder(container, currentBookTextHelper!!)
            pathIndex = currentPathIndex
            indexInChapter = this.indexInChapter
        } else if (position > currentPosition) {
            if (this.indexInChapter < currentBookTextHelper!!.getChapterPageCount() - 1) {
                currentBookTextHelper!!.indexInChapter = this.indexInChapter + 1
                holderLayout = BookTextViewHolder(container, currentBookTextHelper!!)
                pathIndex = currentPathIndex
                indexInChapter = this.indexInChapter + 1
            } else if (rightBookTextHelper != null) {
                rightBookTextHelper!!.indexInChapter = 0
                holderLayout = BookTextViewHolder(container, rightBookTextHelper!!)
                pathIndex = currentPathIndex + 1
                indexInChapter = 0
            }
        } else {
            if (this.indexInChapter > 0) {
                currentBookTextHelper!!.indexInChapter = this.indexInChapter - 1
                holderLayout = BookTextViewHolder(container, currentBookTextHelper!!)
                pathIndex = currentPathIndex
                indexInChapter = this.indexInChapter - 1
            } else if (leftBookTextHelper != null) {
                leftBookTextHelper!!.indexInChapter = leftBookTextHelper!!.getChapterPageCount() - 1
                holderLayout = BookTextViewHolder(container, leftBookTextHelper!!)
                pathIndex = currentPathIndex - 1
                indexInChapter = leftBookTextHelper!!.getChapterPageCount() - 1
            }
        }

        currentBookTextHelper?.initCurrentBookmarkData()

        if (holderLayout == null) {
            holderLayout = BookTextViewHolder(container, currentBookTextHelper!!)
        }
        hashMap[position] = holderLayout
        holderLayout.setOnPageListener(onTextOperateListener)
        holderLayout.initDataUI(getTitle(pathIndex), pathIndex, indexInChapter, position)
        holderLayout.onBookmarkActionListener = onBookmarkListener
        container.addView(holderLayout.itemView)

        holderLayout.setPageString(pageInfoCalculateHelper.getPageString(pathIndex, indexInChapter))

        return holderLayout.itemView
    }

    fun checkAndLoadText(position: Int) {
        var isInBoundary = false
        viewPager.enableSlide()
        if (currentBookTextHelper == null) {
            return
        }
        if (position == currentPosition - 1) {
            indexInChapter--
            if (indexInChapter < 0) {
                if (leftBookTextHelper != null) {
                    rightBookTextHelper = currentBookTextHelper
                    currentBookTextHelper = leftBookTextHelper
                    leftBookTextHelper = null
                    indexInChapter = currentBookTextHelper!!.getChapterPageCount() - 1
                    currentPathIndex--
                } else {
                    indexInChapter++ // 到第一页了
                    viewPager.currentItem = currentPosition
                    isInBoundary = true
                }
            }
        } else if (position == currentPosition + 1) {
            indexInChapter++
            if (indexInChapter >= currentBookTextHelper!!.getChapterPageCount()) {
                if (rightBookTextHelper != null) {
                    leftBookTextHelper = currentBookTextHelper
                    currentBookTextHelper = rightBookTextHelper
                    rightBookTextHelper = null
                    indexInChapter = 0
                    currentPathIndex++
                } else {
                    indexInChapter-- // 到最后一页了
                    viewPager.currentItem = currentPosition
                    isInBoundary = true
                }
            }
        }

        if (indexInChapter == 0 && leftBookTextHelper == null) {
            if (currentPathIndex > 0) {
                leftBookTextHelper = buildText(currentPathIndex - 1)
            } else {
                viewPager.lockLeftSlide()
            }
        }

        if (indexInChapter == currentBookTextHelper!!.getChapterPageCount() - 1 && rightBookTextHelper == null) {
            if (currentPathIndex < bookPathList.size - 1) {
                rightBookTextHelper = buildText(currentPathIndex + 1)
            } else {
                viewPager.lockRightSlide()
            }
        }

        if (!isInBoundary) {
            currentPosition = position
        }

        if (hashMap[currentPosition] != null) {
            if (hashMap[currentPosition]?.isTextSelectShow()!!) {
                viewPager.saveAndLockAll()
            }
        }

        if (toolLayout != null && currentBookTextHelper != null) {
            val title = EPubBook.getTocTitle(getCurrentPath())
            toolLayout!!.setJumpTitle(title)
            setJumpProgress(currentPathIndex, indexInChapter)
            toolLayout!!.getScopeSeekBar().let {
                it.max = bookPathList.size - 1
                it.progress = currentPathIndex
            }
        }
    }

    fun getCurrentPageInBook(): Int {
        return pageInfoCalculateHelper.getCurrentPageIndex(
            currentBookTextHelper!!.pathIndex,
            currentBookTextHelper!!.indexInChapter,
        )
    }

    /****
     * 添加或删除书签
     */
    fun addOrRemoveBookmark() {
        var bookmark = getCurrentBookmarkData()
        if (bookmark == null) {
            bookmark = initBookmarkEntity()
            SDKSingleton.dbWrapBl.saveBookmark(bookmark)
        } else {
            val firstWordLocation = currentBookTextHelper!!.getFirstWordOffset(indexInChapter)
            val lastWordLocation = currentBookTextHelper!!.getPageLastWordOffset(indexInChapter)
            SDKSingleton.dbWrapBl.deleteCurrentPageBookmark(
                resourceId!!,
                getCurrentPath(),
                firstWordLocation,
                lastWordLocation,
            )
        }

        BookReadActivity.isOperateBookData = true
        notifyDataSetChanged()
    }

    private fun initBookmarkEntity(): BookmarkEntity {
        val bookmark = BookmarkEntity()
        bookmark.also {
            it.dataId = ""
            it.createTime = System.currentTimeMillis()
            it.resourceId = resourceId ?: ""
            it.pagePath = getCurrentPath()
            it.filePosition = currentPathIndex
            it.tocTitle = EPubBook.getTocTitle(currentPathIndex)
            it.firstWordOffset = currentBookTextHelper!!.getFirstWordOffset(indexInChapter)
            it.summary = currentBookTextHelper!!.getPageSummary(indexInChapter)
            it.dataStatus = DataStatus.NORMAL
        }
        return bookmark
    }

    val onBookmarkListener = object : OnBookmarkActionListener {
        override fun doBookmark(isAdd: Boolean) {
            KLog.d("addOrRemoveBookmark")
            addOrRemoveBookmark()
            initBookmarkState()
        }
    }

    fun initBookmarkState() {
        if (indexInChapter < 0) return
        val bookmark = getCurrentBookmarkData()
        toolLayout!!.bookmarkImageView.isSelected = (bookmark != null)
    }

    private fun getCurrentBookmarkData(): BookmarkEntity? {
        val firstWordLocation = currentBookTextHelper!!.getFirstWordOffset(indexInChapter)
        val lastWordLocation = currentBookTextHelper!!.getPageLastWordOffset(indexInChapter)
        return SDKSingleton.dbWrapBl.getBookmarkEntity(
            resourceId!!,
            getCurrentPath(),
            firstWordLocation,
            lastWordLocation,
        )
    }

    private fun buildText(pathIndex: Int): BookTextControllerHelper {
        val content = EPubBook.getText(pathIndex)
        val showPageInfo = SPSingleton.get().getBoolean(SPKeyDefine.SP_isShowPage, false)

        val controllerHelper = BookTextControllerHelper()
            .also {
                it.pathIndex = pathIndex
                it.setTextSize(textSizePx)
                if (EPubBook.textFont == TextFontType.SERIF.value) {
                    it.setTextFontType(EPubBook.typefaceSerif)
                } else {
                    it.setTextFontType(Typeface.DEFAULT)
                }
                it.parseText(content, !EPubBook.isCopyrightPage(pathIndex), showPageInfo)
                it.resourceId = resourceId
                it.fileId = fileId
            }

        return controllerHelper
    }

    fun setJumpProgress(pathIndex: Int, index: Int) {
        if (index < 0) {
            toolLayout!!.setJumpProgress(pageInfoCalculateHelper.getPageString(pathIndex, 0), 0)
        } else {
            val offset = currentBookTextHelper!!.getFirstWordOffset(index)
            toolLayout!!.setJumpProgress(
                pageInfoCalculateHelper.getPageString(pathIndex, index),
                offset,
            )
        }
    }


    fun preLoadBook(path: String, bookKey: String?, bookFileId: String): Boolean {
        val encryptionKeyUser = SDKSingleton.sessionBl.getEncryptionKeyUser()
        if (bookKey == null || bookKey.isEmpty() || !EPubBook.openBook(
                path,
                bookKey,
                encryptionKeyUser,
                bookFileId
            )
        ) {
            bookPathList.clear()
            return false
        } else {
            return true
        }
    }

    // FIXME: bookKey can be encryptionKey
    fun loadBook(fileId: String, resourceId: String?, jsonNote: String?): Boolean {
        readProgressEntity = SDKSingleton.dbWrapBl.getBookReadProgressEntity(resourceId)
        this.resourceId = resourceId
        this.fileId = fileId
        EPubBook.textFont = SPSingleton.get().getInt(SPKeyDefine.SP_BookTextFont, TextFontType.SERIF.value)
        viewPager.resumeFromLockAll()
        val bookToc = EPubBook.tocItemList
        bookPathList.clear()
        bookPathList.addAll(EPubBook.pathList)
        pageInfoCalculateHelper.calculateBookPageInfo(fileId, textSizePx, EPubBook.textFont)


        if (bookToc.isNotEmpty()) {
            if (!jsonNote.isNullOrEmpty()) {
                val noteEntity: NoteEntity = JsonUtility.decodeFromString(jsonNote)

                // 检查是否为搜索高亮
                if (noteEntity.isSearchHighlight) {
                    val pathIndex = EPubBook.getPathIndex(noteEntity.pagePath)

                    // 尝试精确定位高亮内容
                    if (tryLocateHighlightContent(noteEntity, pathIndex)) {
                        return true
                    }
                }

                // 如果精确定位失败或不是搜索高亮，使用原始偏移量
                gotoPage(EPubBook.getPathIndex(noteEntity.pagePath), noteEntity.wordStartOffset)
            } else {
                val path: String
                val offset: Int
                if (readProgressEntity != null) {
                    path = readProgressEntity?.pagePath ?: bookToc[0].path
                    offset = readProgressEntity!!.firstWordOffset
                } else {
                    path = bookToc[0].path
                    offset = 0
                }
                gotoAddress(path, offset)
            }
        }
        return true
    }

    /**
     * 尝试精确定位高亮内容
     * @return 是否成功定位
     */
    fun tryLocateHighlightContent(noteEntity: NoteEntity, pathIndex: Int): Boolean {
        // 如果有原始内容，尝试在页面中精确定位
        if (!noteEntity.summary.isNullOrEmpty()) {
            val bookTextHelper = buildText(pathIndex)
            if (bookTextHelper != null) {
                val pageText = bookTextHelper.textPageList.originalText

                val contentWithTag = noteEntity.noteText ?: ""
                val sentenceRegex = "\\[\\[\\[(.*?)\\]\\]\\]".toRegex()
                val sentenceMatch = sentenceRegex.find(contentWithTag)

                if (sentenceMatch != null) {
                    val originalSentence = sentenceMatch.groupValues[1]

                    // 查找第一个高亮关键词的位置
                    val highlightRegex = "\\{\\{\\{(.*?)\\}\\}\\}".toRegex()
                    val highlightMatch = highlightRegex.find(contentWithTag)

                    if (highlightMatch != null) {
                        val highlightKeyword = highlightMatch.groupValues[1]

                        val sentenceStartOffset = EPubBook.findSentenceOffsetWithRTLSpaces(pageText, originalSentence)

                        if (sentenceStartOffset >= 0) {
                            // 在原始句子中查找高亮关键词的位置
                            val keywordOffsetInSentence = originalSentence.indexOf(highlightKeyword)

                            if (keywordOffsetInSentence >= 0) {
                                // 计算高亮关键词在整个文本中的精确位置
                                val exactStartOffset = sentenceStartOffset + keywordOffsetInSentence
                                gotoPage(pathIndex, exactStartOffset)
                                return true
                            } else {
                                gotoPage(pathIndex, sentenceStartOffset)
                                return true
                            }
                        }
                    } else {
                        // 如果没有找到高亮标记，则使用整个句子
                        val sentenceStartOffset = EPubBook.findSentenceOffsetWithRTLSpaces(originalSentence, pageText)
                        if (sentenceStartOffset >= 0) {
                            gotoPage(pathIndex, sentenceStartOffset)
                            return true
                        }
                    }
                } else {
                    val content = noteEntity.summary
                    val contentStartOffset = EPubBook.findSentenceOffsetWithRTLSpaces(content, pageText)
                    if (contentStartOffset >= 0) {
                        gotoPage(pathIndex, contentStartOffset)
                        return true
                    }
                }
            }
        }
        return false
    }

    /**
     * 查找最佳匹配位置
     */
    private fun findBestMatch(needle: String, haystack: String): Int {
        if (haystack.contains(needle)) {
            return haystack.indexOf(needle)
        }
        
        for (length in needle.length - 1 downTo minOf(needle.length / 2, 5)) {
            val partialNeedle = needle.substring(0, length)
            if (haystack.contains(partialNeedle)) {
                return haystack.indexOf(partialNeedle)
            }
        }
        
        // 如果前缀匹配失败，尝试使用滑动窗口查找最长公共子串
        if (needle.length > 10) {
            var bestMatchLength = 0
            var bestMatchPos = -1
            
            for (windowSize in minOf(needle.length, 20) downTo 8) {
                for (i in 0..needle.length - windowSize) {
                    val window = needle.substring(i, i + windowSize)
                    if (haystack.contains(window)) {
                        val matchPos = haystack.indexOf(window)
                        if (windowSize > bestMatchLength) {
                            bestMatchLength = windowSize
                            bestMatchPos = matchPos
                        }
                    }
                }
                
                if (bestMatchLength >= 8) {
                    return bestMatchPos
                }
            }
            
            if (bestMatchPos >= 0) {
                return bestMatchPos
            }
        }
        
        return -1
    }

    fun gotoPage(index: Int, offset: Int, force: Boolean = false, allowAdjustment: Boolean = true, markUserNavigation: Boolean = true) {
        val first = currentBookTextHelper?.getFirstWordOffset(indexInChapter) ?: 0
        val last = currentBookTextHelper?.getPageLastWordOffset(indexInChapter) ?: 0
        if (index != currentPathIndex || (offset > last!! || offset < first!!) || force) {
            if (bookPathList.isEmpty() || index < 0 || index >= bookPathList.size) {
                return
            }
            val path = bookPathList[index]
            gotoAddress(path, offset, markUserNavigation)

            // 如果允许调整并且是搜索高亮，检查当前页是否包含目标内容
            if (allowAdjustment) {
                adjustPageForHighlight()
            }
        }
    }

    /**
     * 调整页面以确保高亮内容可见
     */
    private fun adjustPageForHighlight() {
        if (activity.intent.hasExtra(IntentConstants.EXTRA_NoteEntity)) {
            val noteEntityJson = activity.intent.getStringExtra(IntentConstants.EXTRA_NoteEntity)
            if (!noteEntityJson.isNullOrEmpty()) {
                try {
                    val noteEntity: NoteEntity = JsonUtility.decodeFromString(noteEntityJson)
                    if (noteEntity.isSearchHighlight && !noteEntity.summary.isNullOrEmpty()) {
                        val currentHelper = currentBookTextHelper
                        if (currentHelper != null) {
                            val pageText = currentHelper.getPageText(indexInChapter)
                            // 检查当前页是否包含目标内容
                            if (!pageText.contains(noteEntity.summary)) {
                                if (indexInChapter > 0) {
                                    val prevPageText = currentHelper.getPageText(indexInChapter - 1)
                                    if (prevPageText.contains(noteEntity.summary)) {
                                        indexInChapter--
                                        setJumpProgress(currentPathIndex, indexInChapter)
                                        notifyDataSetChanged()
                                        viewPager.currentItem = currentPosition
                                        return
                                    }
                                }

                                if (indexInChapter < currentHelper.getChapterPageCount() - 1) {
                                    val nextPageText = currentHelper.getPageText(indexInChapter + 1)
                                    if (nextPageText.contains(noteEntity.summary)) {
                                        indexInChapter++
                                        setJumpProgress(currentPathIndex, indexInChapter)
                                        notifyDataSetChanged()
                                        viewPager.currentItem = currentPosition
                                        return
                                    }
                                }
                            }
                        }
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
        }
    }

    fun gotoAddress(p: String?, wordOffset: Int, markUserNavigation: Boolean = true) {
        var path = p
        val index = path!!.lastIndexOf('#')
        var anchor: String? = null
        if (index > 0) {
            anchor = path.substring(index + 1)
            path = path.substring(0, index)
        }

        currentPathIndex = EPubBook.getPathIndex(path)
        leftBookTextHelper = null
        rightBookTextHelper = null
        currentBookTextHelper = buildText(currentPathIndex)
        val initPage = currentBookTextHelper!!.findPageFromOffset(wordOffset)
        indexInChapter = if (initPage >= 0) {
            initPage
        } else if (anchor != null) {
            currentBookTextHelper!!.findAnchor(anchor)
        } else {
            0
        }
        checkAndLoadText(currentPosition)
        notifyDataSetChanged()
        viewPager.currentItem = currentPosition
        initBookmarkState()

        if (markUserNavigation) {
            notifyUserNavigated()
        }
    }

    fun getCurrentPath(): String {
        return if (!bookPathList.isNullOrEmpty()) {
            if (currentPathIndex == bookPathList.size) {
                bookPathList[currentPathIndex - 1]
            } else {
                bookPathList[currentPathIndex]
            }
        } else {
            ""
        }
    }

    fun savePosition() {
        if (currentBookTextHelper == null) {
            return
        }
        try {
            if (readProgressEntity == null) {
                readProgressEntity = ReadProgressEntity()
            }
            readProgressEntity?.let {
                it.resourceId = resourceId ?: ""
                it.resourceTypeId = SDKSingleton.dbWrapBl.getResourceTypeId(resourceId!!) ?: ""
                it.pagePath = getCurrentPath()
                it.firstWordOffset = currentBookTextHelper!!.getFirstWordOffset(indexInChapter)
                it.progress = pageInfoCalculateHelper.getPercent(currentPathIndex, indexInChapter)
                it.summary = currentBookTextHelper!!.getSummary(indexInChapter)
            }
            SDKSingleton.dbWrapBl.saveReadProgress(readProgressEntity!!)
        } catch (e: Exception) {
        }

        MainScope().launch(ExceptionHandler.coroutineExceptionHandler) {
            // 将阅读进度同步到后台
            SDKSingleton.syncBl.syncReadProgressData()
        }
    }

    fun setTextSize(textLevel: Int) {
        textSizePx = getTextSizePx(textLevel)
        val fileId = activity.intent.getStringExtra(IntentConstants.EXTRA_fileId) ?: ""
        pageInfoCalculateHelper.calculateBookPageInfo(fileId, textSizePx, EPubBook.textFont)
        val path = getCurrentPath()
        val offset = currentBookTextHelper!!.getFirstWordOffset(indexInChapter)
        gotoAddress(path, offset)
    }

    private fun getTextSizePx(level: Int): Float {
        val dp: Int = if (level < 7) {
            level * 2 + 16
        } else {
            level * 4 + 4
        }
        return dp2px(dp) * 0.8f
    }

    fun checkNoteFold(position: Int) {
        val viewHolder = hashMap[position]
        if (viewHolder == null || !viewHolder.isNoteFolded()) {
            return
        }
        toolLayout?.hideToolsLayout()
        if (SPSingleton.get().getBoolean(SPKeyDefine.SP_FistTimeFoldNote, true)) {
            SPSingleton.get().putBoolean(SPKeyDefine.SP_FistTimeFoldNote, false)
            val dialog = GuideDialog(activity, R.drawable.guide_fold, GuideDialog.BUTTON_LEFT)
            dialog.show()
        }
    }

    override fun getCount(): Int {
        return if (bookPathList.isNullOrEmpty()) {
            0
        } else {
            Int.MAX_VALUE
        }
    }

    override fun getItemPosition(`object`: Any): Int {
        return POSITION_NONE
    }

    override fun isViewFromObject(view: View, any: Any): Boolean {
        return view === any
    }

    override fun destroyItem(container: ViewGroup, position: Int, any: Any) {
        container.removeView(any as View)
        hashMap.remove(position)
    }

    fun hasPage(position: Int): Boolean {
        if (position == currentPosition - 1) {
            return indexInChapter > 0 || leftBookTextHelper != null
        } else if (position == currentPosition + 1) {
            return (indexInChapter < currentBookTextHelper!!.getChapterPageCount() - 1 || rightBookTextHelper != null)
        }
        return true
    }

    fun setTextFontType(font: Int) {
        EPubBook.textFont = font
        val fileId = activity.intent.getStringExtra(IntentConstants.EXTRA_fileId) ?: ""
        pageInfoCalculateHelper.calculateBookPageInfo(fileId, textSizePx, EPubBook.textFont)
        if (currentBookTextHelper == null || bookPathList.isNullOrEmpty()) {
            return
        }
        val path = getCurrentPath()
        val offset = currentBookTextHelper!!.getFirstWordOffset(indexInChapter)
        // FootnoteSpan.resetFootnoteBitmap()
        gotoAddress(path, offset)
    }

    fun goToNextPage() {
        var position = currentPosition + 1
        if (hasPage(position)) {
            checkAndLoadText(position)
            viewPager.setCurrentItem(position, true)
            hashMap[currentPosition]?.hideTextSelectLayout()
            notifyUserNavigated()
        }
    }

    fun goToPrevPage() {
        var position = currentPosition - 1
        if (hasPage(position)) {
            checkAndLoadText(position)
            viewPager.setCurrentItem(position, true)
            hashMap[currentPosition]?.hideTextSelectLayout()
            notifyUserNavigated()
        }
    }

    /**
     * 清除搜索高亮并重新加载所有页面的笔记数据
     */
    fun clearSearchHighlight() {
        activity.intent.removeExtra(IntentConstants.EXTRA_NoteEntity)

        for (holder in hashMap.values) {
            holder.reloadNoteListData()
        }
    }

    /**
     * 重新加载当前页面以显示搜索高亮
     */
    fun reloadCurrentPage() {
        hashMap[currentPosition]?.reloadNoteListData()
    }

    /**
     * 在外部高度变化（如横竖屏切换）后，基于实际可见高度重新计算分页，避免底部文字被截断
     */
    fun reflowForNewHeight(newHeight: Float) {
        if (newHeight <= 0) return
        BookTextControllerHelper.overridePageHeightForAll = newHeight
        currentBookTextHelper?.recalculatePagesForHeight(newHeight)
        leftBookTextHelper?.recalculatePagesForHeight(newHeight)
        rightBookTextHelper?.recalculatePagesForHeight(newHeight)

        // 越界保护
        currentBookTextHelper?.let { helper ->
            val pageCount = helper.getChapterPageCount()
            if (indexInChapter >= pageCount) {
                indexInChapter = if (pageCount > 0) pageCount - 1 else 0
            }
        }
        fileId?.let { id ->
            pageInfoCalculateHelper.calculateBookPageInfo(id, textSizePx, EPubBook.textFont)
        }
        notifyDataSetChanged()
    }

    /**
     * 旋转前捕获当前页锚点（章节索引 + 本页首字全局偏移），用于旋转后恢复定位
     */
    fun getCurrentAnchorPathIndex(): Int {
        return currentPathIndex
    }

    fun getCurrentPageFirstWordOffset(): Int {
        return try {
            currentBookTextHelper?.getFirstWordOffset(indexInChapter) ?: 0
        } catch (e: Exception) {
            0
        }
    }

    private val onTextOperateListener: OnTextOperateListener = object : OnTextOperateListener {
        override fun onBitmapClick(bitmap: Bitmap) {
            if (bitmap != null) {
                ImageLoadUtil.writeBitmapToFile(bitmap, DataPathUtil.getImageTempPath())
                ImageFullSizeActivity.gotoFullImageActivity(activity)
            }
        }

        override fun onLayoutIsShowing(isShowing: Boolean) {
            if (isShowing) {
                viewPager.saveAndLockAll()
            } else {
                viewPager.resumeFromLockAll()
            }
        }

        override fun onFlipPage(position: Int, next: Boolean): Boolean {
            if (toolLayout!!.isVisible() || position != currentPosition) {
                return false
            }
            var position = currentPosition
            if (next) {
                position++
            } else {
                position--
            }

            return if (hasPage(position)) {
                checkAndLoadText(position)
                viewPager.setCurrentItem(position, false)
                notifyUserNavigated()
                true
            } else {
                false
            }
        }

        override fun onCopy(text: String) {
            val cm = activity.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
            cm.text = text
            ToastUtil.showToastShort(R.string.already_copy)
        }

        override fun onShare(text: String) {
            val intent = Intent(Intent.ACTION_SEND)
            if (text != null) {
                intent.type = "text/plain"
                intent.putExtra(Intent.EXTRA_TEXT, text)
            }

            intent.putExtra(Intent.EXTRA_SUBJECT, findString(R.string.select_share_app))
            intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
            activity.startActivity(Intent.createChooser(intent, findString(R.string.select_share_app)))
        }

        override fun onTextClick(positionInViewPager: Int, isLongClick: Boolean) {
            if (positionInViewPager == currentPosition) {
                onViewClickListener?.onClickAction(
                    viewPager,
                    if (isLongClick) ParamsDefine.TAG_HideToolBar else "",
                    null,
                )
            }
        }

        override fun onInnerJump(link: String) {
            onViewClickListener?.onClickAction(viewPager, OptionClickTag.TAG_INNER_JUMP, link)
        }

        override fun onNoteFold(position: Int) {
            if (position == currentPosition) {
                checkNoteFold(position)
            }
        }

        override fun onReloadBookData() {
            BookReadActivity.isOperateBookData = true
            for (holderLayout in hashMap.values) {
                holderLayout.reloadNoteListData()
            }
        }

        override fun onSelectedChanged(line: Int, wordIndex: Int) {
            val lastLine = currentBookTextHelper?.getPageLastLineIndex(indexInChapter)
            val firstLine = currentBookTextHelper?.getPageFirstLineIndex(indexInChapter)

            if (line >= lastLine!!) {
                if (indexInChapter == currentBookTextHelper!!.getChapterPageCount() - 1) {
                    return
                }
                if (line > lastLine || wordIndex == currentBookTextHelper!!.textPageList[line].size) {
                    if (!readyToFlip) {
                        readyToFlip = true
                        handler.sendEmptyMessageDelayed(MSG_FLIP_NEXT, 1000)
                    }
                    return
                }
            } else if (line <= firstLine!! && wordIndex == 0 && indexInChapter > 0) {
                if (!readyToFlip) {
                    readyToFlip = true
                    handler.sendEmptyMessageDelayed(MSG_FLIP_PREV, 1000)
                }
                return
            }

            readyToFlip = false
            handler.removeMessages(MSG_FLIP_NEXT)
            handler.removeMessages(MSG_FLIP_PREV)
        }

        override fun onSelectedStopMoving() {
            handler.removeMessages(MSG_FLIP_NEXT)
            handler.removeMessages(MSG_FLIP_PREV)
        }
    }
}
