package com.wedevote.wdbook.ui.read.lib.span;

import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.Matrix;
import android.graphics.Paint;

import com.wedevote.wdbook.R;
import com.wedevote.wdbook.base.APP;
import com.wedevote.wdbook.tools.define.TextFontType;
import com.wedevote.wdbook.ui.read.lib.EPubBook;

import java.util.HashMap;

public class FootnoteSpan extends WDLinkSpan implements SpecialElementSpan {
    private static final HashMap<Float,Bitmap> footnoteBmpMap = new HashMap<>();
    private static final float fontOffset = 100f;
    public FootnoteSpan(String content,int id) {
        super(content);
        this.id = id;
    }

    static public Bitmap getBitmap(Paint paint) {
        boolean bSystemFont = EPubBook.INSTANCE.getTextFont() == TextFontType.DEFAULT.getValue();
        Bitmap footnoteBmp = footnoteBmpMap.get(paint.getTextSize() + (bSystemFont?fontOffset:0));
        if (footnoteBmp == null) {
            float f = -paint.ascent();
            if(bSystemFont) {
                footnoteBmp = BitmapFactory.decodeResource(APP.get().getResources(), R.drawable.ic_footnote_sys);
            } else {
                footnoteBmp = BitmapFactory.decodeResource(APP.get().getResources(), R.drawable.ic_footnote);
            }
            float ratio = f / footnoteBmp.getHeight() * 0.8f;
            Matrix matrix = new Matrix();
            matrix.postScale(ratio, ratio);
            Bitmap newBmp = Bitmap.createBitmap(footnoteBmp, 0, 0, footnoteBmp.getWidth(), footnoteBmp.getHeight(), matrix, false);
            if (!newBmp.equals(footnoteBmp)) {
                footnoteBmp = newBmp;
            }
            footnoteBmpMap.put(paint.getTextSize() + (bSystemFont?fontOffset:0), footnoteBmp);
        }
        return footnoteBmp;
    }

    public String getContent() {
        return getLink();
    }

    @Override
    public float getElementWidth(Paint paint) {
        return getBitmap(paint).getWidth()*1.2f;
    }

    @Override
    public void drawIt(Canvas canvas,Paint paint,float x,float y, int height, float ascent){
        canvas.drawBitmap(getBitmap(paint), x, y+ascent , paint);
    }
}
