package com.wedevote.wdbook.ui.store

import android.os.Bundle
import com.aquila.lib.layout.SmartRefreshLayout
import com.aquila.lib.layout.api.RefreshLayout
import com.aquila.lib.layout.listener.OnLoadMoreListener
import com.aquila.lib.layout.listener.OnRefreshListener
import com.aquila.lib.tools.util.ToastUtil
import com.aquila.lib.widget.view.CustomRecyclerView
import com.wedevote.wdbook.R
import com.wedevote.wdbook.base.ExceptionHandler
import com.wedevote.wdbook.base.RootActivity
import com.wedevote.wdbook.base.SDKSingleton
import com.wedevote.wdbook.entity.home.NewWidgetDetailEntity
import com.wedevote.wdbook.tools.event.OnSuccessBuyEvent
import com.wedevote.wdbook.tools.util.APPUtil
import com.wedevote.wdbook.tools.util.IntentConstants
import com.wedevote.wdbook.ui.home.microwidget.BookItemRecyclerAdapter
import com.wedevote.wdbook.ui.widgets.CommTopTitleLayout
import com.wedevote.wdbook.utils.JsonUtility
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

/***
 * @date 创建时间 2021/11/25 15:44
 * <AUTHOR> W.YuLong
 * @description 从首页推荐点击查看全部进入的列表布局
 */
class RecommendBookListActivity : RootActivity(), OnRefreshListener, OnLoadMoreListener {

    private var isFirst: Boolean = true
    lateinit var dataRecyclerView: CustomRecyclerView
    lateinit var refreshLayout: SmartRefreshLayout
    lateinit var titleLayout: CommTopTitleLayout

    lateinit var dataAdapter: BookItemRecyclerAdapter
    var page: Int = 1

    lateinit var detailEntity: NewWidgetDetailEntity
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_recommend_book_list_layout)
        initViewFromXML()

        var json = intent.getStringExtra(IntentConstants.EXTRA_WidgetDetailEntityJson) ?: ""
        var title = intent.getStringExtra(IntentConstants.EXTRA_CategoryName) ?: ""
        var entity: NewWidgetDetailEntity? = null
        if (!json.isNullOrEmpty()) {
            entity = JsonUtility.decodeFromString(json)
        }
        if (entity == null) {
            ToastUtil.showToastShort(R.string.no_data)
            onBackPressed()
        }

        titleLayout.setTitle(title)
        detailEntity = entity!!
        detailEntity.widgetViewParam = detailEntity.widgetViewParam

        dataAdapter = BookItemRecyclerAdapter()
        dataRecyclerView.adapter = dataAdapter

        onRefresh(refreshLayout)
        refreshLayout.setOnRefreshListener(this).setOnLoadMoreListener(this)
    }

    private fun initViewFromXML() {
        dataRecyclerView = findViewById(R.id.recommend_book_list_data_RecyclerView)
        refreshLayout = findViewById(R.id.recommend_book_list_SmartRefreshLayout)
        titleLayout = findViewById(R.id.recommend_book_list_top_TitleLayout)
    }

    override fun onRefresh(refreshLayout: RefreshLayout) {
        page = 1
        dataAdapter.clearDataList()
        refreshLayout.isEnableLoadMore = true
        getDataFromServer(page)
    }

    override fun onLoadMore(refreshLayout: RefreshLayout) {
        page++
        getDataFromServer(page)
    }

    private fun getDataFromServer(page: Int) {
        MainScope().launch(ExceptionHandler.getCoroutineExceptionHandler {
            refreshLayout.finishLoadMoreAndRefresh()
            APPUtil.dismissLoadingDialog(this@RecommendBookListActivity)
        }) {
            if (isFirst) {
                APPUtil.showLoadingDialog(this@RecommendBookListActivity)
                isFirst = false
            }
            val paramsStr = detailEntity.paramsMap[NewWidgetDetailEntity.KEY_requestParams]
            var subParamsMap = HashMap<String, String?>()
            if (!paramsStr.isNullOrEmpty()) {
                subParamsMap = JsonUtility.decodeFromString(paramsStr)
                when {
                    subParamsMap.containsKey("page") -> {
                        subParamsMap["page"] = page.toString()
                    }
                }
            }

            SDKSingleton.storeBl.getRecommendDataList(
                "${detailEntity.paramsMap[NewWidgetDetailEntity.KEY_dataSource]}",
                subParamsMap
            ).also { entity ->
                dataAdapter.addDataList(entity.productList)
                refreshLayout.isEnableLoadMore = !entity.productList.isNullOrEmpty()
            }

            APPUtil.dismissLoadingDialog(this@RecommendBookListActivity)
            refreshLayout.finishLoadMoreAndRefresh()
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onReceiveBuyResultEvent(event: OnSuccessBuyEvent) {
        dataAdapter.updateProductPurchaseStatus(event.productId)
    }
}
