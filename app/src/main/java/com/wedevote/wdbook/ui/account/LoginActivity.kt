package com.wedevote.wdbook.ui.account

import android.content.Intent
import android.os.Bundle
import android.text.Editable
import android.text.InputType
import android.text.method.HideReturnsTransformationMethod
import android.text.method.PasswordTransformationMethod
import android.view.View
import android.widget.EditText
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import com.aquila.lib.log.KLog
import com.aquila.lib.tools.interfaceimpl.TextWatcherImpl
import com.aquila.lib.tools.singleton.SPSingleton
import com.aquila.lib.tools.util.ToastUtil
import com.wedevote.wdbook.R
import com.wedevote.wdbook.base.ExceptionHandler
import com.wedevote.wdbook.base.RootActivity
import com.wedevote.wdbook.base.SDKSingleton
import com.wedevote.wdbook.constants.AnalyticsConstants
import com.wedevote.wdbook.tools.event.OnLoginEvent
import com.wedevote.wdbook.tools.util.APPUtil
import com.wedevote.wdbook.tools.util.APPUtil.isAppInstalled
import com.wedevote.wdbook.tools.util.AnalyticsUtils
import com.wedevote.wdbook.tools.util.IntentConstants
import com.wedevote.wdbook.tools.util.IntentUtils
import com.wedevote.wdbook.tools.util.NetWorkUtils
import com.wedevote.wdbook.tools.util.SPKeyDefine
import com.wedevote.wdbook.tools.util.findString
import com.wedevote.wdbook.ui.account.register.CountryCodeChooseActivity
import com.wedevote.wdbook.ui.service.SyncDataService
import com.wedevote.wdbook.ui.widgets.CommTopTitleLayout
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus

/***
 *@date 创建时间 2023/4/19
 *<AUTHOR> John.Qian
 *@description
 */
class LoginActivity : RootActivity(), View.OnClickListener {
    private lateinit var topTitleLayout: CommTopTitleLayout
    private lateinit var countryCodeTextView: TextView
    private lateinit var accountEditText: EditText
    private lateinit var passwordEditText: EditText
    private lateinit var pwdVisibleImageView: ImageView
    private lateinit var clearImageView: ImageView
    private lateinit var forgetPasswordTextView: TextView
    private lateinit var loginTextView: TextView
    private lateinit var loginSwitchTextView: TextView
    private lateinit var accountTitleTextView: TextView
    private lateinit var emailLayout: View
    private lateinit var divideLine: View
    private lateinit var tipTextView: TextView
    private lateinit var tipImageView: ImageView
    private lateinit var phoneLinearLayout: LinearLayout
    private lateinit var ssoLoginButton: LinearLayout
    private lateinit var loginManager: LoginManager
    private var executeNewIntentTime: Long = 0
    private var loginType: Int = 0

    private var lastAccountPhone: String? = null
    private var lastAccountMail: String? = null
    private val LOGIN_TYPE_MOBILE = 0
    private val LOGIN_TYPE_EMAIL = 1
    private var currentLoginType = LOGIN_TYPE_EMAIL

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_login_layout)
        initViewFromXML()
        loginManager = LoginManager(this)
        setViewListeners()

        var account = intent.getStringExtra(IntentConstants.EXTRA_account)
        if (account.isNullOrEmpty()) {
            account = SPSingleton.get().getString(SPKeyDefine.SP_lastUser, "")
        }

        setLoginType(SPSingleton.get().getInt(SPKeyDefine.SP_LoginAccountType, LOGIN_TYPE_EMAIL))

        val lastCode = SPSingleton.get().getString(
            SPKeyDefine.SP_defaultCountry,
            UserAccountManager.getCurrentCountryCode(this)
        )
        countryCodeTextView.text = "$lastCode"
        accountEditText.setText(account)
    }

    override fun onResume() {
        super.onResume()
        if (isAppInstalled(this, SDKSingleton.appBl.androidWDBiblePackage)
            || isAppInstalled(this, SDKSingleton.appBl.androidWDBibleHDPackage)
        ) {
            ssoLoginButton.visibility = View.VISIBLE
        } else {
            ssoLoginButton.visibility = View.GONE
        }
    }

    private fun setPasswordShowState(
        isHidePassword: Boolean,
        viewImageView: ImageView,
        passwordEditText: EditText
    ) {
        if (isHidePassword) {
            passwordEditText.transformationMethod = PasswordTransformationMethod.getInstance()
        } else {
            passwordEditText.transformationMethod = HideReturnsTransformationMethod.getInstance()
        }
        viewImageView.isSelected = !viewImageView.isSelected
        passwordEditText.setSelection(passwordEditText.text.length)
    }

    private fun setViewListeners() {
        loginTextView.setOnClickListener(this)
        emailLayout.setOnClickListener(this)
        pwdVisibleImageView.setOnClickListener(this)
        countryCodeTextView.setOnClickListener(this)
        forgetPasswordTextView.setOnClickListener(this)
        ssoLoginButton.setOnClickListener(this)
        clearImageView.setOnClickListener(this)

        accountEditText.addTextChangedListener(object : TextWatcherImpl() {
            override fun afterTextChanged(s: Editable) {
                if (currentLoginType == LOGIN_TYPE_MOBILE) {
                    lastAccountPhone = s.toString()
                } else {
                    lastAccountMail = s.toString()
                }
                checkNextButtonState()
                setErrorTip(false)
                tipImageView.visibility = View.GONE
                if (accountEditText.text.isNotEmpty()) {
                    clearImageView.visibility = View.VISIBLE
                } else {
                    clearImageView.visibility = View.GONE
                }
            }
        })
        passwordEditText.addTextChangedListener(object : TextWatcherImpl() {
            override fun afterTextChanged(s: Editable) {
                checkNextButtonState()
            }
        })
        accountEditText.onFocusChangeListener = View.OnFocusChangeListener { _, p1 ->
            if (!p1) {
                clearImageView.visibility = View.GONE
            } else if (accountEditText.text.isNotEmpty()) {
                clearImageView.visibility = View.VISIBLE
                setErrorTip(false)
            }
        }
    }

    fun checkNextButtonState(): Boolean {
        if (accountEditText.text.toString().trim()
                .isEmpty() || passwordEditText.text.toString().isEmpty()
        ) {
            loginTextView.isEnabled = false
            return false
        } else {
            loginTextView.isEnabled = true
            return true
        }
    }

    private fun initViewFromXML() {
        topTitleLayout = findViewById(R.id.login_top_title_layout)
        countryCodeTextView = findViewById(R.id.login_country_code_TextView)
        accountEditText = findViewById(R.id.login_account_EditText)
        passwordEditText = findViewById(R.id.login_password_EditText)
        pwdVisibleImageView = findViewById(R.id.login_password_ImageView)
        forgetPasswordTextView = findViewById(R.id.login_forget_password_TextView)
        forgetPasswordTextView.text = getString(R.string.forget_password)
        loginSwitchTextView = findViewById(R.id.login_switch_TextView)
        loginTextView = findViewById(R.id.login_login_TextView)
        emailLayout = findViewById(R.id.login_mail_Layout)
        accountTitleTextView = findViewById(R.id.login_account_title_TextView)
        divideLine = findViewById(R.id.login_country_divide_TextView)
        tipTextView = findViewById(R.id.phone_error_tip_TextView)
        tipImageView = findViewById(R.id.error_tip_ImageView)
        phoneLinearLayout = findViewById(R.id.phone_email_LinearLayout)
        ssoLoginButton = findViewById(R.id.sso_login_LinearLayout)
        clearImageView = findViewById(R.id.clear_text_ImageView)
        pwdVisibleImageView.isSelected = false
        APPUtil.setEditTextInputSpace(passwordEditText)
        APPUtil.setEditTextInputSpace(accountEditText)
    }

    private fun setLoginType(type: Int) {
        currentLoginType = type
        if (type == LOGIN_TYPE_MOBILE) {
            countryCodeTextView.visibility = View.VISIBLE
            divideLine.visibility = View.VISIBLE
            accountEditText.inputType = InputType.TYPE_CLASS_PHONE
            accountEditText.setText(lastAccountPhone)
            loginSwitchTextView.text = getString(R.string.use_email_login)
            accountTitleTextView.text = getString(R.string.phone_number)
            topTitleLayout.setTitle(getString(R.string.use_phone_login))
            accountEditText.hint = getString(R.string.please_input_phone_number)
            emailLayout.isSelected = false
        } else {
            countryCodeTextView.visibility = View.GONE
            divideLine.visibility = View.GONE
            accountEditText.inputType = InputType.TYPE_TEXT_VARIATION_EMAIL_ADDRESS
            accountEditText.setText(lastAccountMail)
            loginSwitchTextView.text = getString(R.string.use_phone_login)
            accountTitleTextView.text = getString(R.string.email_address)
            topTitleLayout.setTitle(getString(R.string.use_email_login))
            accountEditText.hint = getString(R.string.please_input_email_address)
            emailLayout.isSelected = true
        }
        passwordEditText.setText("")
        passwordEditText.hint = getString(R.string.please_input_passward)
        passwordEditText.transformationMethod = PasswordTransformationMethod.getInstance()
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        if (System.currentTimeMillis() - executeNewIntentTime < 1000) {
            return
        }
        executeNewIntentTime = System.currentTimeMillis()

        val authToken = intent.getStringExtra("UserTokenBeanJson")
        KLog.d(authToken)
        if (!authToken.isNullOrEmpty()) {
            SDKSingleton.sessionBl.setUserTokenString(authToken)
            SPSingleton.get().putString(SPKeyDefine.SP_LoginUserId, SDKSingleton.sessionBl.userId)
            KLog.d("SDKSingleton.sessionBl.userId = ${SDKSingleton.sessionBl.userId}, authToken = $authToken")

            MainScope().launch {
                APPUtil.showLoadingDialog(this@LoginActivity)
                try {
                    val i = SDKSingleton.userBl.checkAndAddDevice(SDKSingleton.appBl.deviceId)
                    if (i == 1312) {
                        checkAndGotoDeviceManageActivity()
                    } else {
                        afterLogin()
                    }
                    APPUtil.dismissLoadingDialog(this@LoginActivity)
                } catch (e: Exception) {
                    afterLogin()
                    APPUtil.dismissLoadingDialog(this@LoginActivity)
                    ExceptionHandler.handleException(e)
                }
            }
        } else {
            ToastUtil.showToastShort(R.string.sso_login_unauthorized)
            KLog.e("授权登录失败")
            return
        }
    }

    private fun checkAndGotoDeviceManageActivity() {
        DeviceManagerActivity.gotoDeviceManager(
            this,
            object : OnLoginResultCallBack {
                override fun onLoginResult(isSuccess: Boolean) {
                    if (isSuccess) {
                        afterLogin()
                    }
                }
            }
        )
    }

    private fun afterLogin() {
        EventBus.getDefault().post(OnLoginEvent())
        AnalyticsUtils.updateAnalyticsUserID()
        SSOLoginActivity.onCallBack?.onLoginResult(true)
        AnalyticsUtils.logEvent(
            AnalyticsConstants.LOG_V1_USER_LOGIN_SUCCESS,
            AnalyticsConstants.LOG_V1_PARAM_LOGIN_TYPE,
            loginType.toString()
        )
        val intent = Intent(this@LoginActivity, SyncDataService::class.java)
        startService(intent)
        finish()
    }

    override fun onClick(v: View) {
        when (v) {
            loginTextView -> {
                val userName: String = accountEditText.text.toString()
                if (userName.isEmpty()) {
                    return
                }
                if (!checkInputFormat()) {
                    return
                }
                SPSingleton.get().putString(SPKeyDefine.SP_lastUser, userName)
                SPSingleton.get().putInt(SPKeyDefine.SP_LoginAccountType, currentLoginType)

                if (!NetWorkUtils.isNetworkAvailable()) {
                    NetWorkUtils.showTipDialog(this, getString(R.string.no_network_connect))
                    return
                }
                AnalyticsUtils.logEvent(AnalyticsConstants.LOG_V1_USER_LOGIN)
                val password = passwordEditText.text.toString()
                if (currentLoginType == LOGIN_TYPE_MOBILE) {
                    loginManager.startLogin(
                        this,
                        UserAccountManager.getCountryCode(countryCodeTextView) + userName,
                        password
                    ) {

                    }
                } else {
                    loginManager.startLogin(this, userName, password) {

                    }
                }
            }

            emailLayout -> {
                if (currentLoginType == LOGIN_TYPE_MOBILE) {
                    setLoginType(LOGIN_TYPE_EMAIL)
                } else {
                    setLoginType(LOGIN_TYPE_MOBILE)
                }
            }

            forgetPasswordTextView -> {
                val intent = Intent(this, ResetPasswordActivity::class.java)
                intent.putExtra(
                    IntentConstants.EXTRA_fromEmail,
                    currentLoginType == LOGIN_TYPE_EMAIL
                )
                intent.putExtra(IntentConstants.EXTRA_account, accountEditText.text.toString())
                startActivity(intent)
            }

            pwdVisibleImageView -> {
                setPasswordShowState(
                    pwdVisibleImageView.isSelected,
                    pwdVisibleImageView,
                    passwordEditText
                )
            }

            countryCodeTextView -> {
                val intent = Intent(this, CountryCodeChooseActivity::class.java)
                startActivityForResult(intent, IntentConstants.INTENT_RESULT_COUNTRY_CODE)
            }

            ssoLoginButton -> {
                if (!NetWorkUtils.isNetworkAvailable()) {
                    NetWorkUtils.showTipDialog(this, getString(R.string.no_network_connect))
                    return
                }
                AnalyticsUtils.logEvent(AnalyticsConstants.LOG_V1_USER_LOGIN)

                val wdBiblePackage = SDKSingleton.appBl.androidWDBiblePackage
                val wdBibleHDPackage = SDKSingleton.appBl.androidWDBibleHDPackage
                val isBiblePackageInstalled = isAppInstalled(this, wdBiblePackage)
                val isBibleHDPackageInstalled = isAppInstalled(this, wdBibleHDPackage)
                val thisClassName = "com.wedevote.wdbook.ui.account.LoginActivity"
                val bibleIntent =
                    IntentUtils.createBibleInvocationIntent(wdBiblePackage, thisClassName)
                val bibleHDIntent =
                    IntentUtils.createBibleInvocationIntent(wdBibleHDPackage, thisClassName)

                if (isBiblePackageInstalled && isBibleHDPackageInstalled) {
                    // 比较极端的情况，用户同时安装了 Bible 和 Bible HD 这两个 App
                    val chooserIntent =
                        Intent.createChooser(bibleIntent, findString(R.string.choose_application))
                    chooserIntent.putExtra(Intent.EXTRA_INITIAL_INTENTS, arrayOf(bibleHDIntent))
                    try {
                        loginType = 1
                        startActivity(chooserIntent)
                    } catch (e: Exception) {
                        e.printStackTrace()
                    }
                } else if (isBiblePackageInstalled) {
                    // 只安装了 Bible App
                    try {
                        loginType = 1
                        startActivity(bibleIntent)
                    } catch (e: Exception) {
                        e.printStackTrace()
                    }
                } else if (isBibleHDPackageInstalled) {
                    // 只安装了 Bible HD App
                    try {
                        loginType = 1
                        startActivity(bibleHDIntent)
                    } catch (e: Exception) {
                        e.printStackTrace()
                    }
                }
            }

            clearImageView -> {
                accountEditText.setText("")
            }
        }
    }

    private fun setErrorTip(isError: Boolean, text: String = "") {
        if (isError) {
            tipImageView.visibility = View.VISIBLE
            clearImageView.visibility = View.GONE
            tipTextView.visibility = View.VISIBLE
            tipTextView.text = text
        } else {
            tipTextView.visibility = View.GONE
        }
        tipImageView.isSelected = isError
        phoneLinearLayout.isSelected = isError
    }

    private fun checkInputFormat(): Boolean {
        val account = accountEditText.text.toString()
        if (currentLoginType == LOGIN_TYPE_MOBILE) {
            if (!UserAccountManager.isMobileNO(
                    UserAccountManager.getCountryCode(countryCodeTextView),
                    account
                )
            ) {
                setErrorTip(true, getString(R.string.invalid_phone_number))
                return false
            }
        } else {
            if (!UserAccountManager.isEmail(account)) {
                setErrorTip(true, getString(R.string.invalid_email_number))
                return false
            }
        }
        setErrorTip(false)
        return true
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == IntentConstants.INTENT_RESULT_COUNTRY_CODE && data != null) {
            val countryCode = data.getStringExtra(IntentConstants.Extra_countryCode)
            countryCodeTextView.text = countryCode
            SPSingleton.get().putString(SPKeyDefine.SP_defaultCountry, countryCode)
        }
    }
}
