package com.wedevote.wdbook.ui.shelf.search

import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.KeyEvent
import android.view.View
import android.view.inputmethod.EditorInfo
import android.widget.EditText
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.aquila.lib.dialog.CommAlertDialog
import com.aquila.lib.layout.SmartRefreshLayout
import com.aquila.lib.layout.api.RefreshLayout
import com.aquila.lib.layout.listener.OnLoadMoreListener
import com.aquila.lib.layout.listener.OnRefreshListener
import com.aquila.lib.layout.util.DensityUtil
import com.aquila.lib.tools.util.ToastUtil
import com.aquila.lib.widget.view.CustomRecyclerView
import com.wedevote.wdbook.R
import com.wedevote.wdbook.base.APPConfig
import com.wedevote.wdbook.base.RootActivity
import com.wedevote.wdbook.base.SDKSingleton
import com.wedevote.wdbook.entity.store.SearchItemEntity
import com.wedevote.wdbook.tools.interfaces.OnItemClickListener
import com.wedevote.wdbook.tools.util.IntentConstants
import com.wedevote.wdbook.ui.dialogs.LoadingProgressDialog
import com.wedevote.wdbook.ui.read.lib.EPubBook
import com.wedevote.wdbook.ui.store.FlexboxDataAdapter
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import com.wedevote.wdbook.entity.shelf.ShelfDataType
import com.wedevote.wdbook.exception.ApiException
import com.wedevote.wdbook.exception.SDKException
import com.wedevote.wdbook.tools.event.HomeShelfDataReloadEvent
import com.wedevote.wdbook.tools.util.NetWorkUtils
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import com.wedevote.wdbook.entity.store.ProductEntity
import com.wedevote.wdbook.constants.Constants

/***
 *@date 创建时间 2025/4/25 14:18
 *<AUTHOR> John.Qian
 *@description  书架搜索页面
 */
class ShelfSearchActivity : RootActivity(), View.OnClickListener, OnLoadMoreListener, OnRefreshListener {
    lateinit var searchEditText: EditText
    lateinit var clearInputImageView: ImageView
    lateinit var cancelTextView: TextView
    lateinit var historyCommendLayout: ConstraintLayout
    lateinit var historyLayout: ConstraintLayout
    lateinit var deleteHistoryImageView: ImageView
    lateinit var historyDataRecyclerView: CustomRecyclerView

    lateinit var noResultLayout: LinearLayout
    lateinit var resultDataRecyclerView: CustomRecyclerView
    lateinit var resultRefreshLayout: SmartRefreshLayout
    lateinit var noHistoryLayout: LinearLayout

    lateinit var historyFlexAdapter: FlexboxDataAdapter<SearchItemEntity>

    lateinit var resultDataAdapter: ShelfSearchResultAdapter

    var entity = SearchItemEntity()

    private var storeCurrentPage = 1 // 书城数据页码
    private var isShelfSearchComplete = false // 标记书架数据是否加载完毕
    private var isStoreSearchComplete = false // 标记书城数据是否加载完毕
    private var isNetworkAvailable = true

    var currentSearchEntity = SearchItemEntity()

    /*加载搜索历史记录的条数*/
    val maxSearchHistoryCount = 8L

    private lateinit var loadingDialog: LoadingProgressDialog

    enum class SearchModeUI(val value: Int) {
        HISTORY(0),
        RESULT_DATA(1),
        NO_DATA(2),
    }

    private var isTriggeredByFilterChange = false

    private fun mapPriceDOsToProducts(productList: ArrayList<ProductEntity>) {
        if (productList.isEmpty()) return
        for (product in productList) {
            val priceDO = product.priceDOs?.firstOrNull() ?: continue
            product.price = priceDO.price
            product.originalPrice = priceDO.originalPrice
            product.priceCNY = priceDO.priceCNY
            product.discount = priceDO.discount
        }
    }

    private var currentMode = SearchModeUI.HISTORY

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_shelf_search_layout)
        initViewFromXML()
        initCurrentModeShow(currentMode)
        loadingDialog = LoadingProgressDialog(this)
        initAdapters()
        loadSearchHistoryData()
        setViewListeners()
        searchEditText.post {
            var keywords = intent.getStringExtra(IntentConstants.EXTRA_Keywords)
            if (!keywords.isNullOrEmpty()) {
                searchEditText.setText(keywords)
                searchEditText.setSelection(keywords.length)
                userDoSearchAction()
            } else {
                searchEditText.requestFocus()
            }
        }
    }

    private fun initViewFromXML() {
        searchEditText = findViewById(R.id.search_input_EditText)
        clearInputImageView = findViewById(R.id.search_clear_input_ImageView)
        cancelTextView = findViewById(R.id.search_cancel_TextView)
        historyCommendLayout = findViewById(R.id.search_history_and_recommend_container_layout)
        historyLayout = findViewById(R.id.search_history_container_layout)
        deleteHistoryImageView = findViewById(R.id.search_delete_history_ImageView)
        historyDataRecyclerView = findViewById(R.id.search_history_RecyclerView)
        noResultLayout = findViewById(R.id.search_no_result_Layout)
        resultDataRecyclerView = findViewById(R.id.search_result_data_RecyclerView)
        resultRefreshLayout = findViewById(R.id.search_data_RefreshLayout)
        noHistoryLayout = findViewById(R.id.search_no_history_Layout)
    }

    private fun initAdapters() {
        historyFlexAdapter = FlexboxDataAdapter(FlexboxDataAdapter.FlexboxViewType.SEARCH_HISTORY)
        historyFlexAdapter.enableSelectItem = false
        historyDataRecyclerView.adapter = historyFlexAdapter

        resultDataAdapter = ShelfSearchResultAdapter()
        resultDataRecyclerView.adapter = resultDataAdapter

        resultDataAdapter.onFilterChangedListener = object : com.wedevote.wdbook.ui.store.StoreFilterBarView.OnFilterChangedListener {
            override fun onOrderFieldChanged(orderField: com.wedevote.wdbook.constants.OrderField) {
                storeCurrentPage = 1
                isStoreSearchComplete = false
                resultDataAdapter.clearStoreMessage()
                isTriggeredByFilterChange = true
                refreshStoreOnly()
            }

            override fun onLanguageChanged(language: String) {
                storeCurrentPage = 1
                isStoreSearchComplete = false
                resultDataAdapter.clearStoreMessage()
                isTriggeredByFilterChange = true
                refreshStoreOnly()
            }
        }
    }

    private fun setViewListeners() {
        clearInputImageView.setOnClickListener(this)
        cancelTextView.setOnClickListener(this)
        deleteHistoryImageView.setOnClickListener(this)
        resultRefreshLayout.setOnRefreshListener(this).setOnLoadMoreListener(this)
        val onFlexItemSelectListener = object : OnItemClickListener {
            override fun <T> onItemClick(obj: Any, tag: String, t: T) {
                if (t is SearchItemEntity) {
                    searchEditText.setText(t.keyWords)
                    searchEditText.setSelection(t.keyWords.length)
                    userDoSearchAction(t)
                } else if (t is String) {
                    searchEditText.setText(t)
                    searchEditText.setSelection(t.length)
                    userDoSearchAction(null)
                }
            }
        }
        historyFlexAdapter.onItemClickListener = onFlexItemSelectListener

        searchEditText.setOnEditorActionListener(object : TextView.OnEditorActionListener {
            override fun onEditorAction(v: TextView?, actionId: Int, event: KeyEvent?): Boolean {
                if (actionId == EditorInfo.IME_ACTION_SEARCH) {
                    userDoSearchAction()
                    return true
                }
                return false
            }
        })
        searchEditText.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
            }

            override fun afterTextChanged(s: Editable?) {
                clearInputImageView.visibility = if (s!!.length > 0) View.VISIBLE else View.GONE
            }
        })
    }

    /*用户手动点击搜索操作*/
    fun userDoSearchAction(entity: SearchItemEntity? = null) {
        if (APPConfig.isFastClick()) return
        if (entity == null) {
            var searchEntity = SearchItemEntity()
            searchEntity.keyWords = EPubBook.removeRTLControlChars(searchEditText.text.toString().trim())
            currentSearchEntity = searchEntity
        } else {
            currentSearchEntity = entity
            currentSearchEntity.updateTime = System.currentTimeMillis()
        }
        if (currentSearchEntity.keyWords.isNullOrBlank()) {
            ToastUtil.showToastShort(R.string.input_search_keyword)
            return
        }
        val length = currentSearchEntity.keyWords.length
        if (length > 32) {
            currentSearchEntity.keyWords = currentSearchEntity.keyWords.subSequence(length - 32, length).toString()
        }

        SDKSingleton.userBl.saveShelfSearchData(currentSearchEntity.keyWords)

        // 重置状态并开始加载
        storeCurrentPage = 1
        isShelfSearchComplete = false
        isStoreSearchComplete = false
        resultRefreshLayout.setEnableLoadMore(true) // 确保启用加载更多
        resultDataAdapter.clearStoreMessage()
        resultDataAdapter.clearShelfMessage()
        resultDataAdapter.resetFilterLanguageToAll(getString(R.string.all_language), Constants.ALL_LANGUAGE_CODE)
        resultDataAdapter.setShowStoreFilterBar(false)
        isTriggeredByFilterChange = false

        if (NetWorkUtils.isNetworkAvailable()) {
            loadingDialog.show()
            loadingDialog.setTitleText(getString(R.string.loading))
        }
        onRefresh(resultRefreshLayout)
        resultDataRecyclerView.scrollToPosition(0)
    }

    private fun loadSearchHistoryData() {
        historyFlexAdapter.dataList = SDKSingleton.userBl.getShelfSearchDataList(maxSearchHistoryCount)
        historyLayout.visibility = if (historyFlexAdapter.itemCount > 0) View.VISIBLE else View.GONE
        if (historyFlexAdapter.itemCount > 0) {
            noHistoryLayout.visibility = View.GONE
        } else {
            noHistoryLayout.visibility = View.VISIBLE
        }
    }

    override fun onRefresh(refreshLayout: RefreshLayout?) {
        storeCurrentPage = 1
        isShelfSearchComplete = false
        isStoreSearchComplete = false
        isNetworkAvailable = true
        resultRefreshLayout.isEnableLoadMore = true
        resultDataAdapter.clearDataList()
        resultDataAdapter.addTitleHeader(getString(R.string.search_in_shelf_title, currentSearchEntity.keyWords, 0))
        resultDataAdapter.addTitleHeader(getString(R.string.search_in_store_title, currentSearchEntity.keyWords, 0))
        onLoadMore(resultRefreshLayout)
    }

    private val pageSize = 10

    override fun onLoadMore(refreshLayout: RefreshLayout) {
        if (isShelfSearchComplete && isStoreSearchComplete) {
            resultRefreshLayout.finishLoadMoreAndRefresh()
            return
        }

        MainScope().launch {
            if (!isShelfSearchComplete) {
                val shelfResult = SDKSingleton.userBl.searchAllShelfEntityListByKeyword(
                    currentSearchEntity.keyWords
                )

                if (shelfResult.items.isNotEmpty()) {
                    val itemsToAdd = shelfResult.items.map { combineEntity ->
                        when (combineEntity.dataType) {
                            ShelfDataType.RESOURCE -> SearchResultItem.ShelfBook(combineEntity.bookItemEntity!!, currentSearchEntity.keyWords)
                            ShelfDataType.ARCHIVE -> SearchResultItem.ShelfArchive(combineEntity.archiveEntity!!, currentSearchEntity.keyWords)
                            else -> null
                        }
                    }.filterNotNull()
                    resultDataAdapter.addShelfData(itemsToAdd)
                    resultDataAdapter.updateHeaderTitle(0, getString(R.string.search_in_shelf_title, currentSearchEntity.keyWords, shelfResult.total))
                } else {
                    if (resultDataAdapter.getShelfItemCount() == 0) {
                        resultDataAdapter.showEmptyShelfState(this@ShelfSearchActivity)
                    }
                    resultDataAdapter.updateHeaderTitle(0, getString(R.string.search_in_shelf_title, currentSearchEntity.keyWords, 0))
                }
                isShelfSearchComplete = true
            }

            if (isShelfSearchComplete && !isStoreSearchComplete) {
                if (!NetWorkUtils.isNetworkAvailable()) {
                    isNetworkAvailable = false
                    isStoreSearchComplete = true
                    resultDataAdapter.showNoNetworkForStoreState(this@ShelfSearchActivity)
                    resultDataAdapter.updateHeaderTitle(1, getString(R.string.search_in_store_title, currentSearchEntity.keyWords, 0))
                    resultRefreshLayout.setEnableLoadMore(false)
                } else {
                    isNetworkAvailable = true
                    try {
                        val storeResult = SDKSingleton.storeBl.searchProduct(
                            storeCurrentPage,
                            pageSize,
                            currentSearchEntity.keyWords,
                            resultDataAdapter.selectedLanguage,
                            resultDataAdapter.selectedOrderField.value,
                            null
                        )
                        applyStoreData(
                            storeResult?.productList,
                            storeResult?.total ?: 0,
                            storeResult?.totalPage ?: 1,
                            false
                        )
                    } catch (e: ApiException) {
                        resultRefreshLayout.setEnableLoadMore(false)
                    } catch (e: SDKException) {
                        resultRefreshLayout.setEnableLoadMore(false)
                    } catch (e: Exception) {
                        resultRefreshLayout.setEnableLoadMore(false)
                        isNetworkAvailable = false
                        resultDataAdapter.showNoNetworkForStoreState(this@ShelfSearchActivity)
                        resultDataAdapter.updateHeaderTitle(1, getString(R.string.search_in_store_title, currentSearchEntity.keyWords, 0))
                    } finally {
                        if (loadingDialog.isShowing) {
                            loadingDialog.dismiss()
                        }
                    }
                }
            }

            resultRefreshLayout.finishLoadMoreAndRefresh()

            if (isShelfSearchComplete && isStoreSearchComplete) {
                resultRefreshLayout.setEnableLoadMore(false)
            }

            checkAndShowEmptyState()
            isTriggeredByFilterChange = false

            loadSearchHistoryData()
        }
    }

    private fun checkAndShowEmptyState(){
        initCurrentModeShow(SearchModeUI.RESULT_DATA)
        if(resultDataAdapter.getStoreItemCount() == 0 && isNetworkAvailable) {
            resultDataAdapter.showEmptyStoreState(this)
            resultDataAdapter.updateHeaderTitle(1, getString(R.string.search_in_store_title, currentSearchEntity.keyWords, 0))
            resultDataAdapter.setShowStoreFilterBar(isTriggeredByFilterChange)
            resultRefreshLayout.finishLoadMoreAndRefresh()
            resultRefreshLayout.setEnableLoadMore(false)
        } else {
            resultDataAdapter.setShowStoreFilterBar(true)
        }
    }

    private fun refreshStoreOnly() {
        isShelfSearchComplete = true
        if (!NetWorkUtils.isNetworkAvailable()) {
            isNetworkAvailable = false
            isStoreSearchComplete = true
            resultDataAdapter.showNoNetworkForStoreState(this@ShelfSearchActivity)
            resultDataAdapter.updateHeaderTitle(1, getString(R.string.search_in_store_title, currentSearchEntity.keyWords, 0))
            resultDataAdapter.setShowStoreFilterBar(true)
            resultRefreshLayout.setEnableLoadMore(false)
            return
        }

        if (!loadingDialog.isShowing) {
            loadingDialog.show()
            loadingDialog.setTitleText(getString(R.string.loading))
        }

        MainScope().launch {
            try {
                val storeResult = SDKSingleton.storeBl.searchProduct(
                    1,
                    pageSize,
                    currentSearchEntity.keyWords,
                    resultDataAdapter.selectedLanguage,
                    resultDataAdapter.selectedOrderField.value,
                    null
                )
                applyStoreData(
                    storeResult?.productList,
                    storeResult?.total ?: 0,
                    storeResult?.totalPage ?: 1,
                    true
                )
            } catch (e: Exception) {
                resultRefreshLayout.setEnableLoadMore(false)
                isNetworkAvailable = false
                resultDataAdapter.showNoNetworkForStoreState(this@ShelfSearchActivity)
                resultDataAdapter.updateHeaderTitle(1, getString(R.string.search_in_store_title, currentSearchEntity.keyWords, 0))
            } finally {
                if (loadingDialog.isShowing) {
                    loadingDialog.dismiss()
                }
            }
        }
    }

    private fun applyStoreData(
        productList: List<ProductEntity>?,
        total: Int,
        totalPage: Int,
        isRefreshStoreOnly: Boolean
    ) {
        if (productList != null && productList.isNotEmpty()) {
            mapPriceDOsToProducts(ArrayList(productList))
            val itemsToAdd = productList.map { product ->
                SearchResultItem.StoreBook(product, currentSearchEntity.keyWords)
            }
            if (isRefreshStoreOnly) {
                resultDataAdapter.setStoreData(itemsToAdd)
            } else {
                resultDataAdapter.addStoreData(itemsToAdd)
            }
            resultDataAdapter.setShowStoreFilterBar(true)
            resultDataAdapter.updateHeaderTitle(1, getString(R.string.search_in_store_title, currentSearchEntity.keyWords, total))
            if (isRefreshStoreOnly) {
                storeCurrentPage = if (totalPage > 1) 2 else 1
                isStoreSearchComplete = totalPage <= 1
                resultRefreshLayout.setEnableLoadMore(!isStoreSearchComplete)
            } else {
                if (storeCurrentPage >= totalPage) {
                    isStoreSearchComplete = true
                } else {
                    storeCurrentPage++
                }
            }
        } else {
            if (isRefreshStoreOnly) {
                resultDataAdapter.setStoreData(emptyList())
                resultDataAdapter.setShowStoreFilterBar(true)
                resultDataAdapter.showEmptyStoreState(this@ShelfSearchActivity)
                resultDataAdapter.updateHeaderTitle(1, getString(R.string.search_in_store_title, currentSearchEntity.keyWords, 0))
                isStoreSearchComplete = true
                resultRefreshLayout.setEnableLoadMore(false)
            } else {
                isStoreSearchComplete = true
            }
        }
    }

    private fun initCurrentModeShow(mode: SearchModeUI) {
        when (mode) {
            SearchModeUI.HISTORY -> {
                historyCommendLayout.visibility = View.VISIBLE
                resultRefreshLayout.visibility = View.GONE
                noResultLayout.visibility = View.GONE
            }
            SearchModeUI.RESULT_DATA -> {
                historyCommendLayout.visibility = View.GONE
                resultRefreshLayout.visibility = View.VISIBLE
                noResultLayout.visibility = View.GONE
                val inputMethodManager = getSystemService(INPUT_METHOD_SERVICE) as android.view.inputmethod.InputMethodManager
                inputMethodManager.hideSoftInputFromWindow(searchEditText.windowToken, 0)
                searchEditText.clearFocus()
            }
            SearchModeUI.NO_DATA -> {
                historyCommendLayout.visibility = View.GONE
                resultRefreshLayout.visibility = View.GONE
                noResultLayout.visibility = View.VISIBLE
            }
        }
        currentMode = mode
    }

    override fun onClick(v: View?) {
        when (v) {
            cancelTextView -> {
                onBackPressed()
            }
            clearInputImageView -> {
                searchEditText.setText("")
                resultDataAdapter.clearDataList()
                initCurrentModeShow(SearchModeUI.HISTORY)
            }
            deleteHistoryImageView -> {
                showClearSearchHistoryDialog()
            }
        }
    }

    fun showClearSearchHistoryDialog() {
        CommAlertDialog.with(this)
            .setTitle(R.string.are_you_sure_delete_all_history)
            .setTitleTextTextSize(16)
            .setDialogHeight(DensityUtil.dp2px(110f))
            .setDialogWidth(DensityUtil.dp2px(325f))
            .setStartText(R.string.label_cancel).setEndText(R.string.label_delete)
            .setLeftColorRes(R.color.text_color_blue_007AFF)
            .setRightColorRes(R.color.color_red_FF342A)
            .setOnViewClickListener { dialog, view, i ->
                if (i == CommAlertDialog.TAG_CLICK_END) {
                    SDKSingleton.userBl.clearShelfSearchHistory()
                    loadSearchHistoryData()
                    ToastUtil.showToastShort(R.string.has_clear_search_data)
                }
            }.showDialog()
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun doOnBookItemDownloadFinish(event: HomeShelfDataReloadEvent) {
        if (event.isRefresh) {
            onRefresh(resultRefreshLayout)
            resultDataRecyclerView.scrollToPosition(0)
        }

    }
}
