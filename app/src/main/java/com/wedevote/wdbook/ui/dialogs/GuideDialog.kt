package com.wedevote.wdbook.ui.dialogs

import android.content.Context
import android.graphics.Color
import android.os.Bundle
import android.view.Gravity
import android.view.WindowManager
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.RelativeLayout
import android.widget.TextView
import com.wedevote.wdbook.R
import com.wedevote.wdbook.base.APPConfig

class GuideDialog(context: Context, val imageId: Int, val transparentPosition: Int) : BaseDialog(context) {
    lateinit var imageView: ImageView
    companion object {
        val BUTTON_LEFT = 0
        val BUTTON_MIDDLE = 1
        val BUTTON_RIGHT = 2
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.dialog_note_guide_layout)
        imageView = findViewById(R.id.dialog_note_guide_imageView)
        imageView.setImageResource(imageId)
        val mainLayout: RelativeLayout = findViewById(R.id.dialog_note_guide_main)
        mainLayout.setOnClickListener { dismiss() }
        val bottomLayout: LinearLayout = findViewById(R.id.dialog_note_guide_bottom)
        bottomLayout.setOnClickListener { dismiss() }
        val transparentView: TextView = if (transparentPosition == BUTTON_LEFT) {
            findViewById(R.id.dialog_note_position_left)
        } else if (transparentPosition == BUTTON_MIDDLE) {
            findViewById(R.id.dialog_note_position_middle)
        } else {
            findViewById(R.id.dialog_note_position_right)
        }
        if (APPConfig.isCurrentThemeLight()) {
            transparentView.setBackgroundColor(Color.TRANSPARENT)
        } else {
            transparentView.setBackgroundColor(context.resources.getColor(R.color.transparent_white))
        }
        configDialog(Gravity.BOTTOM)
        setCancelable(true)
    }

    override fun configDialog(gravity: Int) {
        val wl = window!!.attributes
        wl.gravity = gravity
        window!!.setWindowAnimations(com.aquila.lib.dialog.R.style.bottomDialogWindowAnim)
        wl.width = WindowManager.LayoutParams.MATCH_PARENT
        wl.height = WindowManager.LayoutParams.MATCH_PARENT
        wl.dimAmount = 0f
        window!!.attributes = wl
        window!!.setBackgroundDrawableResource(android.R.color.transparent)
    }
}
