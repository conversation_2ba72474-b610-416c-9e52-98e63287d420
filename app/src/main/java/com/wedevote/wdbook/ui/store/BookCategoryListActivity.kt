package com.wedevote.wdbook.ui.store

import android.content.Intent
import android.os.Bundle
import android.view.ViewGroup
import android.widget.TextView
import com.aquila.lib.base.BaseRecycleAdapter
import com.aquila.lib.base.BaseViewHolder
import com.aquila.lib.layout.SmartRefreshLayout
import com.aquila.lib.layout.api.RefreshLayout
import com.aquila.lib.layout.listener.OnRefreshListener
import com.aquila.lib.widget.view.CustomRecyclerView
import com.wedevote.wdbook.R
import com.wedevote.wdbook.base.ExceptionHandler
import com.wedevote.wdbook.base.RootActivity
import com.wedevote.wdbook.base.SDKSingleton
import com.wedevote.wdbook.entity.store.StoreCategoryEntity
import com.wedevote.wdbook.tools.util.APPUtil
import com.wedevote.wdbook.tools.util.IntentConstants
import com.wedevote.wdbook.tools.util.findString
import com.wedevote.wdbook.tools.util.getFunctionInfo
import com.wedevote.wdbook.ui.widgets.CommTopTitleLayout
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch

/***
 * @date 创建时间 2020/8/12 15:01
 * <AUTHOR> W.YuLong
 * @description 书籍所有分类的页面
 */
class BookCategoryListActivity : RootActivity(), OnRefreshListener {
    lateinit var topTitleLayout: CommTopTitleLayout
    lateinit var dataRecyclerView: CustomRecyclerView
    lateinit var categoryAdapter: BookCategoryAdapter
    lateinit var refreshLayout: SmartRefreshLayout
    var mIsRefresh: Boolean = true

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_book_category_list_layout)
        topTitleLayout = findViewById(R.id.book_category_top_title_Layout)
        dataRecyclerView = findViewById(R.id.book_category_data_RecyclerView)
        refreshLayout = findViewById(R.id.book_category_RefreshLayout)

        refreshLayout.setOnRefreshListener(this)

        categoryAdapter = BookCategoryAdapter()
        dataRecyclerView.adapter = categoryAdapter
        onRefresh(refreshLayout)
        mIsRefresh = false
    }

    override fun onRefresh(layout: RefreshLayout?) {
        loadBookCategoryList()
        MainScope().launch(ExceptionHandler.coroutineExceptionHandler) {
            val bookCategorySyncResult = SDKSingleton.syncBl.syncBookCategoryData(APPUtil.getLanguageMode())
            val bookCategoryProductCountSyncResult = SDKSingleton.syncBl.syncBookCategoryProductCount()
            if (bookCategorySyncResult.count > 0 || bookCategoryProductCountSyncResult.count > 0) {
                loadBookCategoryList()
            }
            refreshLayout.finishLoadMoreAndRefresh()
        }
    }

    private fun loadBookCategoryList() {
        try {
            val dataList = SDKSingleton.storeBl.getLocalStoreCategoryEntityList()
            val allEntity = StoreCategoryEntity().apply {
                categoryName = findString(R.string.all_books)
                productCount = SDKSingleton.storeBl.getAllStoreCount()
            }
            categoryAdapter.dataList = dataList?.toMutableList()?.apply { add(0, allEntity) }
        } catch (e: Exception) {
            ExceptionHandler.handleException(e)
        }
    }
}

/***
 *@date 创建时间 2020/8/20 16:49
 *<AUTHOR> W.YuLong
 *@description
 */
class BookCategoryAdapter : BaseRecycleAdapter<StoreCategoryEntity, BookCategoryViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BookCategoryViewHolder {
        return BookCategoryViewHolder(parent)
    }

    override fun onBindViewHolder(holder: BookCategoryViewHolder, position: Int) {
        val data = dataList!![position]
        holder.initUIData(data)

        holder.itemView.setOnClickListener { v ->
            val intent = Intent(v.context, NewBookListActivity::class.java)
            intent.putExtra(IntentConstants.EXTRA_CategoryId, data.categoryId)
            intent.putExtra(IntentConstants.EXTRA_CategoryName, data.categoryName)
            v.context.startActivity(intent)
        }
    }
}

/***
 *@date 创建时间 2020/8/13 09:50
 *<AUTHOR> W.YuLong
 *@description
 */
class BookCategoryViewHolder(parent: ViewGroup) : BaseViewHolder(parent, R.layout.holder_item_category_layout) {
    val titleTextView: TextView = itemView.findViewById(R.id.item_category_title_TextView)
    private val countTextView: TextView = itemView.findViewById(R.id.item_category_count_TextView)

    override fun <T> initUIData(t: T) {
        t as StoreCategoryEntity
        titleTextView.text = t.categoryName
        countTextView.text = countTextView.context.getString(R.string.product_count, t.productCount)
    }
}
