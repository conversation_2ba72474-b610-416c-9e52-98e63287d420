package com.wedevote.wdbook.ui.widgets

import android.content.Context
import android.util.AttributeSet

/***
 * @date 创建时间 2022/4/20 18:40
 * <AUTHOR> <PERSON><PERSON>
 * @description
 */
class SquaredTextView(context: Context, attr: AttributeSet? = null) : androidx.appcompat.widget.AppCompatTextView(context, attr) {

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec)
        setMeasuredDimension(measuredWidth, measuredWidth)
    }
}
