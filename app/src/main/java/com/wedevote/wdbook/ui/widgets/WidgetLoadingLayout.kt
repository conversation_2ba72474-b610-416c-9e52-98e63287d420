package com.wedevote.wdbook.ui.widgets

import android.content.Context
import android.util.AttributeSet
import android.util.TypedValue
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import android.widget.ProgressBar
import android.widget.RelativeLayout
import android.widget.TextView
import com.wedevote.wdbook.R

class WidgetLoadingLayout(context: Context, attrs: AttributeSet? = null) : LinearLayout(context, attrs) {
    val progressBar: ProgressBar
    val textView: TextView
    
    init {
        View.inflate(context, R.layout.widget_loading_api_layout, this)
        progressBar = findViewById(R.id.loading_ProgressBar)
        textView = findViewById(R.id.loading_title_TextView)
        
        val a = context.obtainStyledAttributes(attrs, com.aquila.lib.widget.R.styleable.WidgetLoadingLayout)
        var width = a.getDimensionPixelOffset(com.aquila.lib.widget.R.styleable.WidgetLoadingLayout_attr_progress_width, -1)
        var height = a.getDimensionPixelOffset(com.aquila.lib.widget.R.styleable.WidgetLoadingLayout_attr_progress_height, -1)
        var marginSize = a.getDimension(com.aquila.lib.widget.R.styleable.WidgetLoadingLayout_attr_inner_margin_size, -1f)
        var nameText = a.getText(com.aquila.lib.widget.R.styleable.WidgetLoadingLayout_attr_name)
        val textSize = a.getDimensionPixelSize(com.aquila.lib.widget.R.styleable.WidgetLoadingLayout_attr_text_size, 16)
        val textColor = a.getColorStateList(com.aquila.lib.widget.R.styleable.WidgetLoadingLayout_attr_text_color)
        
        
        val progressParams = progressBar.layoutParams as LinearLayout.LayoutParams
        if (width > 0) {
            progressParams.width = width
        }
        if (height > 0) {
            progressParams.height = height
        }
        progressBar.layoutParams = progressParams
        
        if (textColor != null) {
            textView.setTextColor(textColor)
        }
        textView.setTextSize(TypedValue.COMPLEX_UNIT_SP, textSize.toFloat())
    }
    
    
}