package com.wedevote.wdbook.ui.read.lib

import android.content.Context
import android.util.AttributeSet
import android.view.View
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.aquila.lib.tools.singleton.SPSingleton
import com.aquila.lib.tools.util.ToastUtil
import com.wedevote.wdbook.R
import com.wedevote.wdbook.base.ExceptionHandler
import com.wedevote.wdbook.base.SDKSingleton
import com.wedevote.wdbook.constants.EntranceType
import com.wedevote.wdbook.constants.HighlightColorType
import com.wedevote.wdbook.entity.NoteEntity
import com.wedevote.wdbook.tools.util.SPKeyDefine
import com.wedevote.wdbook.tools.util.dp2px
import com.wedevote.wdbook.tools.util.inValueArea
import com.wedevote.wdbook.tools.util.parseColor
import com.wedevote.wdbook.ui.dialogs.CorrectionReadBookDialog
import com.wedevote.wdbook.ui.read.OnBookNoteActionListener
import com.wedevote.wdbook.ui.read.OnTextOperateListener
import com.wedevote.wdbook.ui.widgets.CircleBGImageView
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import androidx.fragment.app.FragmentActivity
import com.wedevote.wdbook.constants.LanguageMode
import com.wedevote.wdbook.tools.util.APPUtil
import com.wedevote.wdbook.ui.read.widgets.BookSearchResultLayout

/***
 * @date 创建时间 2020/11/16 19:18
 * <AUTHOR> W.YuLong
 * @description
 */
class TextSelectOperateLayout(context: Context, attrs: AttributeSet? = null) : ConstraintLayout(context, attrs), View.OnClickListener {
    lateinit var firstCopyTextView: TextView
    lateinit var secondCopyTextView: TextView
    lateinit var drawLineTextView: TextView
    lateinit var noteTextView: TextView
    lateinit var shareTextView: TextView
    lateinit var correctionTextView: TextView

    lateinit var deleteLineTextView: TextView
    lateinit var colorCombineImageView: ImageView

    lateinit var menuContainerLayout: LinearLayout
    lateinit var highlightContainerLayout: LinearLayout
    lateinit var topTriangleImageView: ImageView
    lateinit var bottomTriangleImageView: ImageView
    lateinit var topSummeryTextView: TextView
    lateinit var bottomSummeryTextView: TextView
    lateinit var mainContainerView: View
    lateinit var redBGImageView: CircleBGImageView
    lateinit var blueBGImageView: CircleBGImageView
    lateinit var yellowBGImageView: CircleBGImageView
    lateinit var orangeBGImageView: CircleBGImageView
    lateinit var lineBGImageView: CircleBGImageView

    lateinit var firstStepContainerLayout: LinearLayout
    lateinit var secondStepContainerLayout: LinearLayout

    var onTextOperateListener: OnTextOperateListener? = null
    var onBookNoteActionListener: OnBookNoteActionListener? = null
    var isInSelect = false
    lateinit var iGetSelectTextListener: IGetSelectTextListener

    var noteEntity: NoteEntity? = null

    init {
        inflate(context, R.layout.item_text_select_menu_layout, this)
        findView()
        initBgImageViewColor()
        setViewListeners()
    }

    private fun findView() {
        menuContainerLayout = findViewById(R.id.text_select_option_menu_layout)
        highlightContainerLayout = findViewById(R.id.text_select_option_highlight_container_layout)
        firstStepContainerLayout = findViewById(R.id.text_select_option_first_menu_layout)
        secondStepContainerLayout = findViewById(R.id.text_select_option_second_layout)

        topTriangleImageView = findViewById(R.id.text_select_triangle_flag_top_ImageView)
        bottomTriangleImageView = findViewById(R.id.text_select_triangle_flag_bottom_ImageView)

        mainContainerView = findViewById(R.id.text_select_option_main_container_layout)
        topSummeryTextView = findViewById(R.id.text_select_summery_top_TextView)
        bottomSummeryTextView = findViewById(R.id.text_select_summery_bottom_TextView)

        firstCopyTextView = findViewById(R.id.text_select_option_first_copy_TextView)
        secondCopyTextView = findViewById(R.id.text_select_option_second_copy_TextView)
        drawLineTextView = findViewById(R.id.text_select_option_draw_line_TextView)
        noteTextView = findViewById(R.id.text_select_option_note_textView)
        shareTextView = findViewById(R.id.text_select_option_share_TextView)
        correctionTextView = findViewById(R.id.text_select_option_correction_TextView)

        deleteLineTextView = findViewById(R.id.text_select_option_delete_line_TextView)
        colorCombineImageView = findViewById(R.id.text_select_option_color_combine_ImageView)

        redBGImageView = findViewById(R.id.highlight_color_red_View)
        blueBGImageView = findViewById(R.id.highlight_color_blue_View)
        yellowBGImageView = findViewById(R.id.highlight_color_yellow_View)
        orangeBGImageView = findViewById(R.id.highlight_color_orange_View)
        lineBGImageView = findViewById(R.id.highlight_color_draw_line_View)
    }

    private fun initBgImageViewColor() {
        redBGImageView.initBgColor(parseColor(SDKSingleton.appBl.getHighlightColor(HighlightColorType.COLOR_RED)))
        blueBGImageView.initBgColor(parseColor(SDKSingleton.appBl.getHighlightColor(HighlightColorType.COLOR_BLUE)))
        yellowBGImageView.initBgColor(parseColor(SDKSingleton.appBl.getHighlightColor(HighlightColorType.COLOR_YELLOW)))
        orangeBGImageView.initBgColor(parseColor(SDKSingleton.appBl.getHighlightColor(HighlightColorType.COLOR_ORANGE)))

        lineBGImageView.initBgColor(parseColor(SDKSingleton.appBl.getHighlightStokeColor()))
        lineBGImageView.initStokeColor(parseColor(SDKSingleton.appBl.getHighlightColor(HighlightColorType.LINE_ORANGE)))
    }

    companion object {
        val STEP_FIRST = 1
        val STEP_SECOND = 2
        val STEP_THIRD = 3
    }

    var currentStep = STEP_FIRST
    private fun showCurrentStepUI(step: Int) {
        when (step) {
            STEP_FIRST -> {
                menuContainerLayout.visibility = VISIBLE
                highlightContainerLayout.visibility = GONE
                firstStepContainerLayout.visibility = VISIBLE
                secondStepContainerLayout.visibility = GONE
                bottomSummeryTextView.visibility = GONE
                topSummeryTextView.visibility = GONE
                secondCopyTextView.visibility = GONE
                correctionTextView.visibility = VISIBLE
            }
            STEP_SECOND -> {
                menuContainerLayout.visibility = VISIBLE
                highlightContainerLayout.visibility = GONE
                firstStepContainerLayout.visibility = GONE
                secondStepContainerLayout.visibility = VISIBLE
                secondCopyTextView.visibility = VISIBLE
                correctionTextView.visibility = GONE
                val left = (ContentUtils.contentWidth.toInt() - width) / 2
                val params = layoutParams as MarginLayoutParams
                params.leftMargin = left
                layoutParams = params
            }
            STEP_THIRD -> {
                menuContainerLayout.visibility = GONE
                highlightContainerLayout.visibility = VISIBLE
                bottomSummeryTextView.visibility = GONE
                topSummeryTextView.visibility = GONE
                correctionTextView.visibility = GONE
                if (noteEntity != null) {
                    initHighlightColorSelect(noteEntity!!.highlightColorType)
                }
            }
        }
        currentStep = step
    }

    fun initSelectNoteUI(note: NoteEntity?, isOnlySelect: Boolean = false) {
        if (note == null || isOnlySelect) {
            showCurrentStepUI(STEP_FIRST)
        } else {
            this.noteEntity = note
            showCurrentStepUI(STEP_SECOND)
        }
    }

    private fun initHighlightColorSelect(colorType: HighlightColorType) {
        redBGImageView.isSelected = false
        blueBGImageView.isSelected = false
        yellowBGImageView.isSelected = false
        orangeBGImageView.isSelected = false
        lineBGImageView.isSelected = false
        when (colorType) {
            HighlightColorType.COLOR_RED -> redBGImageView.isSelected = true
            HighlightColorType.COLOR_BLUE -> blueBGImageView.isSelected = true
            HighlightColorType.COLOR_YELLOW -> yellowBGImageView.isSelected = true
            HighlightColorType.COLOR_ORANGE -> orangeBGImageView.isSelected = true
            HighlightColorType.LINE_ORANGE -> lineBGImageView.isSelected = true
        }
    }

    private fun getSuffixNoteSummary(): String {
        return if (noteEntity!!.summary.length > 10) {
            noteEntity!!.summary.trim() + SDKSingleton.dbWrapBl.getCopySuffix(noteEntity!!.resourceId)
        } else {
            noteEntity!!.summary
        }
    }

    override fun onClick(v: View) {
        when (v) {
            firstCopyTextView -> {
                val selectText = iGetSelectTextListener.getSelectText()
                if (!selectText.isNullOrEmpty()) {
                    onTextOperateListener?.onCopy(selectText)
                }
                onBookNoteActionListener?.onCancelSelect()
            }
            secondCopyTextView -> {
                if (noteEntity == null) {
                    val selectText = iGetSelectTextListener.getSelectText()
                    if (!selectText.isNullOrEmpty()) {
                        onTextOperateListener?.onCopy(selectText)
                    }
                } else {
                    onTextOperateListener?.onCopy(getSuffixNoteSummary())
                }
                onBookNoteActionListener?.onCancelSelect()
            }
            correctionTextView -> {

                MainScope().launch {
                    try {
                        SDKSingleton.sessionBl.setMarket(
                            LanguageMode.contentOf(APPUtil.getLanguage())
                        )
                        val tagList = SDKSingleton.userBl.getFeedbackTagList(EntranceType.FROM_CORRECTION.type)
                        val dialog = CorrectionReadBookDialog(context)
                        dialog.show()
                        dialog.setDataList(tagList)
                        dialog.correctionParamsEntity = onBookNoteActionListener?.initCorrectionEntity(iGetSelectTextListener.getSelectText())

                        dialog.setOnDismissListener {
                            setTextOperateViewShowState(false)
                            onBookNoteActionListener?.onCancelSelect()
                        }
                    } catch (e: Exception) {
                        ToastUtil.showToastShort(R.string.loading_failure_try_again)
                        ExceptionHandler.handleException(e)
                    }
                }
            }
            shareTextView -> {
                if (noteEntity == null) {
                    val selectText = iGetSelectTextListener.getSelectText()
                    if (!selectText.isNullOrEmpty()) {
                        onTextOperateListener?.onShare(selectText)
                    }
                } else {
                    onTextOperateListener?.onShare(getSuffixNoteSummary())
                }
                onBookNoteActionListener?.onCancelSelect()
            }
            colorCombineImageView -> {
                showCurrentStepUI(STEP_THIRD)
            }
            deleteLineTextView -> {
                onBookNoteActionListener?.onDeleteNote(noteEntity)
            }
            noteTextView -> {
                onBookNoteActionListener?.onNoteAdd(noteEntity)
            }
            topSummeryTextView, bottomSummeryTextView -> {
                onBookNoteActionListener?.onNoteShow(noteEntity)
            }
            drawLineTextView -> {
                if (onBookNoteActionListener?.onHighlightAction(
                        HighlightColorType.contentOf(
                            SPSingleton.get().getString(
                                SPKeyDefine.SP_LastMarkType,
                                HighlightColorType.COLOR_YELLOW.value
                            )
                        ),
                        noteEntity
                    )!!
                ) {
                    showCurrentStepUI(STEP_SECOND)
                }
            }
            redBGImageView -> {
                onBookNoteActionListener?.onHighlightAction(HighlightColorType.COLOR_RED, noteEntity)
                initHighlightColorSelect(HighlightColorType.COLOR_RED)
            }
            blueBGImageView -> {
                onBookNoteActionListener?.onHighlightAction(HighlightColorType.COLOR_BLUE, noteEntity)
                initHighlightColorSelect(HighlightColorType.COLOR_BLUE)
            }
            yellowBGImageView -> {
                onBookNoteActionListener?.onHighlightAction(HighlightColorType.COLOR_YELLOW, noteEntity)
                initHighlightColorSelect(HighlightColorType.COLOR_YELLOW)
            }
            orangeBGImageView -> {
                onBookNoteActionListener?.onHighlightAction(HighlightColorType.COLOR_ORANGE, noteEntity)
                initHighlightColorSelect(HighlightColorType.COLOR_ORANGE)
            }
            lineBGImageView -> {
                onBookNoteActionListener?.onHighlightAction(HighlightColorType.LINE_ORANGE, noteEntity)
                initHighlightColorSelect(HighlightColorType.LINE_ORANGE)
            }
        }
    }

    private fun setViewListeners() {
        firstCopyTextView.setOnClickListener(this)
        secondCopyTextView.setOnClickListener(this)
        drawLineTextView.setOnClickListener(this)
        noteTextView.setOnClickListener(this)
        topSummeryTextView.setOnClickListener(this)
        bottomSummeryTextView.setOnClickListener(this)
        shareTextView.setOnClickListener(this)
        deleteLineTextView.setOnClickListener(this)
        colorCombineImageView.setOnClickListener(this)
        correctionTextView.setOnClickListener(this)

        redBGImageView.setOnClickListener(this)
        blueBGImageView.setOnClickListener(this)
        yellowBGImageView.setOnClickListener(this)
        orangeBGImageView.setOnClickListener(this)
        lineBGImageView.setOnClickListener(this)
    }

    fun isShowing(): Boolean {
        return this.visibility == View.VISIBLE
    }

    var isFirstShow = true

    fun setSelectedContainerLayoutPosition(x: Float, lineBottomY: Float, lineHeight: Float) {
        if (isFirstShow) {
            post {
                setShowSelectedContainerLayoutPosition(x, lineBottomY, lineHeight)
                isFirstShow = false
            }
        } else {
            setShowSelectedContainerLayoutPosition(x, lineBottomY, lineHeight)
        }
    }

    fun setShowSelectedContainerLayoutPosition(x: Float, lineBottomY: Float, lineHeight: Float) {
        var summeryHeight = 0
        var showSummery = false
        if (currentStep == STEP_SECOND && noteEntity != null && !noteEntity?.noteText.isNullOrEmpty()) {
            val width = mainContainerView.width
            bottomSummeryTextView.width = width
            topSummeryTextView.width = width
            summeryHeight = dp2px(50)
            showSummery = true
        }

        // 检查顶部是否有搜索结果布局
        val activity = context as? FragmentActivity
        val searchResultLayout = activity?.findViewById<View>(R.id.book_search_result_layout)
        val searchResultLayoutVisible = if (searchResultLayout != null && searchResultLayout is BookSearchResultLayout) {
            searchResultLayout.isVisible()
        } else {
            searchResultLayout?.visibility == View.VISIBLE
        }

        val topSpace = lineBottomY - lineHeight
        val minTopSpace = if (searchResultLayoutVisible) {
            dp2px(70)
        } else {
            dp2px(20)
        }

        val preliminaryTopMargin = lineBottomY - lineHeight - mainContainerView.height - summeryHeight + dp2px(40)
        val shouldShowBelow = preliminaryTopMargin <= 0 || topSpace < minTopSpace

        if (shouldShowBelow) {
            topTriangleImageView.visibility = VISIBLE
            bottomTriangleImageView.visibility = INVISIBLE
        } else {
            bottomTriangleImageView.visibility = VISIBLE
            topTriangleImageView.visibility = INVISIBLE
        }
        
        // 计算顶部边距
        val marginTop = if (shouldShowBelow) {
            lineBottomY + dp2px(100)
        } else {
            val calculatedTop = lineBottomY - lineHeight - mainContainerView.height - summeryHeight + dp2px(60)
            calculatedTop
        }

        if (showSummery) {
            if (topTriangleImageView.visibility == VISIBLE) {
                topSummeryTextView.visibility = GONE
                bottomSummeryTextView.visibility = VISIBLE
                bottomSummeryTextView.text = noteEntity?.noteText?.trim()
            } else {
                bottomSummeryTextView.visibility = GONE
                topSummeryTextView.visibility = VISIBLE
                topSummeryTextView.text = noteEntity?.noteText?.trim()
            }
        }

        var left = if (currentStep != STEP_SECOND) {
            (x - width / 2).toInt().inValueArea(
                0,
                (ContentUtils.contentWidth.toInt() - width + ContentUtils.contentHorizonMargin * 2).toInt()
            )
        } else {
            (ContentUtils.contentWidth.toInt() - width) / 2
        }

        if (left < 0) {
            left = 0
        }
        val params = layoutParams as MarginLayoutParams
        params.leftMargin = left
        params.topMargin = marginTop.toInt()
        layoutParams = params
    }

    fun setSelectState(isSelected: Boolean) {
        isInSelect = isSelected
        onTextOperateListener?.onLayoutIsShowing(isInSelect)
    }

    fun setTextOperateViewShowState(isShow: Boolean) {
        visibility = if (isShow) View.VISIBLE else View.GONE
        if (!isShow) {
            noteEntity = null
            topSummeryTextView.visibility = GONE
        }
        showCurrentStepUI(STEP_FIRST)
    }
}

/***
 *@date 创建时间 2020/12/2 17:40
 *<AUTHOR> W.YuLong
 *@description
 */
interface IGetSelectTextListener {
    fun getSelectText(): String
}
