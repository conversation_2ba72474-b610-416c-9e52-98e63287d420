package com.wedevote.wdbook.ui.read

import android.content.ClipboardManager
import android.content.Context
import android.graphics.Typeface
import android.os.Bundle
import android.text.Html
import android.view.Gravity
import android.widget.ImageView
import android.widget.TextView
import com.aquila.lib.tools.util.ToastUtil
import com.wedevote.wdbook.R
import com.wedevote.wdbook.tools.define.TextFontType
import com.wedevote.wdbook.ui.dialogs.BaseDialog
import com.wedevote.wdbook.ui.read.lib.EPubBook

/***
 *@date 重构时间 2020/10/28 15:29
 *<AUTHOR> W.YuLong
 *@description 显示脚注的的对话框页面
 */
class FootnoteDialog(context: Context) : BaseDialog(context) {
    lateinit var contentTextView: TextView
    lateinit var titleTextView: TextView
    lateinit var closeImageView: ImageView

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.dialog_show_footnote_layout)
        contentTextView = findViewById(R.id.dialog_footnote_content_TextView)
        titleTextView = findViewById(R.id.dialog_footnote_title_TextView)
        closeImageView = findViewById(R.id.dialog_footnote_close_ImageView)

        configDialog(Gravity.BOTTOM)
        closeImageView.setOnClickListener { dismiss() }
        contentTextView.setOnLongClickListener {
            val cm: ClipboardManager =
                context.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
            cm.text = contentTextView.text.toString()
            ToastUtil.showToastShort(R.string.foot_note_copied)
            false
        }
        setTextFont()
    }

    private fun setTextFont() {
        if (EPubBook.textFont == TextFontType.SERIF.value) {
            titleTextView.typeface = EPubBook.typefaceSerif
            contentTextView.typeface = EPubBook.typefaceSerif
        } else {
            titleTextView.typeface = Typeface.DEFAULT
            contentTextView.typeface = Typeface.DEFAULT
        }
    }

    fun initFootnoteText(text: String?, id: Int) {
        if (text.isNullOrEmpty()) {
            contentTextView.text = ""
        } else {
            contentTextView.text = Html.fromHtml(text)
        }
        titleTextView.text = context.getString(R.string.footnote) + id
    }
}
