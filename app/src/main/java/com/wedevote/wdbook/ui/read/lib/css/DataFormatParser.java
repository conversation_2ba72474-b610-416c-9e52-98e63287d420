package com.wedevote.wdbook.ui.read.lib.css;

import android.graphics.Color;

import com.wedevote.wdbook.base.SDKSingleton;

public class DataFormatParser {
    static public HeightUnit parseFloat(String paramString) {
        HeightUnit heightUnit = new HeightUnit();
        try {
            if (paramString.endsWith("em")) {
                paramString = paramString.replaceAll("em", "");
                heightUnit.em = Float.parseFloat(paramString);
            } else if (paramString.endsWith("px")) {
                paramString = paramString.replaceAll("px", "");
                heightUnit.px = Float.parseFloat(paramString);
            }
        }catch (Exception e){
            e.printStackTrace();
        }
        return heightUnit;
    }

    static public int parseColor(String paramString) {
        try{
            if (paramString.charAt(0) == '#' && paramString.length() < 7) {
                StringBuilder sb = new StringBuilder("#");
                for(int i=1;i<paramString.length();i++){
                    sb.append(paramString.charAt(i));
                    sb.append(paramString.charAt(i));
                }
                paramString = sb.toString();
            }
            return Color.parseColor(paramString);
        }catch (Exception e){
            return 0;
        }
    }
}
