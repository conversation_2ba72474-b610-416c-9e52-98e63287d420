package com.wedevote.wdbook.ui.read.widgets

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.ObjectAnimator
import android.app.Activity
import android.content.Context
import android.util.AttributeSet
import android.view.View
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.SeekBar
import androidx.constraintlayout.widget.ConstraintLayout
import com.aquila.lib.base.OnViewClickListener
import com.gyf.immersionbar.BarHide
import com.gyf.immersionbar.ImmersionBar
import com.wedevote.wdbook.R
import com.wedevote.wdbook.base.APPConfig
import com.wedevote.wdbook.tools.util.APPUtil
import com.wedevote.wdbook.tools.util.WindowInsetsUtils
import com.wedevote.wdbook.ui.widgets.CommTopTitleLayout

/***
 * @date 创建时间 2020/6/15 16:27
 * <AUTHOR> W<PERSON>
 * @description
 */
class BookReadToolLayout @JvmOverloads constructor(context: Context, attrs: AttributeSet? = null) :
    ConstraintLayout(
        context, attrs
    ),
    View.OnClickListener {
    val rootLayout: ConstraintLayout
    val commTitleLayout: CommTopTitleLayout
    val bottomContainerLayout: LinearLayout
    val optionContainerLayout: FrameLayout
    val menuLayout: ConstraintLayout
    val contentsImageView: ImageView
    val scopeImageView: ImageView
    val fontImageView: ImageView
    val brightImageView: ImageView
    val bookNoteImageView: ImageView
    val bookSearchImageView: ImageView
    val bookmarkImageView: ImageView

    val animDuration = 300L

    val optionBrightnessLayout: OptionBrightnessLayout
    val optionFontLayout: OptionFontLayout
    val optionScopeLayout: OptionScopeLayout

    val TAB_SCOPE = 1
    val TAB_FONT = 2
    val TAB_BRIGHTNESS = 3

    var currentTab = -1
    private var statusBarHeight = 0

    var onViewClickListener: OnViewClickListener? = null
        set(value) {
            field = value
            optionFontLayout.onViewClickListener = value
            optionScopeLayout.onViewClickListener = value
            optionBrightnessLayout.onViewClickListener = value
        }

    init {
        View.inflate(context, R.layout.book_read_tool_layout, this)
        commTitleLayout = findViewById(R.id.book_read_tool_title_Layout)
        if (APPUtil.isAboveAndroid15()) {
            WindowInsetsUtils.applyTopMarginAfterInsets(commTitleLayout)
        }
        rootLayout = findViewById(R.id.book_read_tool_ConstraintLayout)
        bookNoteImageView = findViewById(R.id.book_note_ImageView)
        bookSearchImageView = findViewById(R.id.book_search_ImageView)
        bookmarkImageView = findViewById(R.id.book_mark_ImageView)
        bottomContainerLayout = findViewById(R.id.book_read_tool_bottom_container_layout)

        menuLayout = findViewById(R.id.book_read_tool_menu_layout)
        contentsImageView = findViewById(R.id.book_read_tool_contents_ImageView)

        scopeImageView = findViewById(R.id.book_read_tool_scope_ImageView)
        fontImageView = findViewById(R.id.book_read_tool_font_ImageView)
        brightImageView = findViewById(R.id.book_read_tool_bright_ImageView)

        optionContainerLayout = findViewById(R.id.book_read_tool_option_container_layout)
        optionFontLayout = findViewById(R.id.book_read_tool_read_jump_Layout)
        optionScopeLayout = findViewById(R.id.book_read_tool_scope_Layout)
        optionBrightnessLayout = findViewById(R.id.book_read_tool_brightness_layout)
        optionBrightnessLayout.initUI()

        if (!isInEditMode) {
            statusBarHeight = if (APPUtil.isAboveAndroid15()) {
                0
            } else {
                APPUtil.getStatusBarHeight(context)
            }
            // 为顶部工具栏添加状态栏高度的padding
            rootLayout.setPadding(
                commTitleLayout.paddingLeft,
                statusBarHeight,
                commTitleLayout.paddingRight,
                commTitleLayout.paddingBottom
            )
            setViewListeners()
        }
    }

    private fun setViewListeners() {
        bottomContainerLayout.setOnClickListener(this)
        optionContainerLayout.setOnClickListener(this)
        contentsImageView.setOnClickListener(this)
        scopeImageView.setOnClickListener(this)
        fontImageView.setOnClickListener(this)
        brightImageView.setOnClickListener(this)

        commTitleLayout.setOnClickListener(this)
        bookNoteImageView.setOnClickListener(this)
        bookmarkImageView.setOnClickListener(this)
        bookSearchImageView.setOnClickListener(this)
    }

    fun isVisible(): Boolean {
        return commTitleLayout.visibility == View.VISIBLE
    }

    fun showToolsLayout() {
        val topAnimator = ObjectAnimator.ofFloat(commTitleLayout, "translationY", -commTitleLayout.height.toFloat(), 0f)
        topAnimator.duration = animDuration
        topAnimator.addListener(object : AnimatorListenerAdapter() {
            override fun onAnimationStart(animation: Animator) {
                commTitleLayout.visibility = View.VISIBLE
            }
        })
        topAnimator.start()
        val bottomAnimator = ObjectAnimator.ofFloat(
            bottomContainerLayout, "translationY",
            bottomContainerLayout.height.toFloat(), 0f
        )
        bottomAnimator.duration = animDuration
        bottomAnimator.addListener(object : AnimatorListenerAdapter() {
            override fun onAnimationStart(animation: Animator) {
                bottomContainerLayout.visibility = View.VISIBLE
            }
        })
        bottomAnimator.start()
    }

    fun hideToolsLayout() {
        val topAnimator = ObjectAnimator.ofFloat(commTitleLayout, "translationY", 0f, -commTitleLayout.height.toFloat())
        topAnimator.duration = animDuration
        topAnimator.addListener(object : AnimatorListenerAdapter() {
            override fun onAnimationEnd(animation: Animator) {
                commTitleLayout.visibility = View.GONE
            }
        })
        topAnimator.start()

        optionContainerLayout.visibility = View.GONE
        val bottomAnimator = ObjectAnimator.ofFloat(
            bottomContainerLayout, "translationY", 0f,
            bottomContainerLayout.height.toFloat()
        )
        bottomAnimator.duration = animDuration
        bottomAnimator.addListener(object : AnimatorListenerAdapter() {
            override fun onAnimationEnd(animation: Animator) {
                bottomContainerLayout.visibility = View.GONE
            }
        })
        bottomAnimator.start()
    }

    fun hideToolsLayoutWithoutAnim() {
        commTitleLayout.visibility = View.GONE
        optionContainerLayout.visibility = View.GONE
        bottomContainerLayout.visibility = View.GONE
    }

    fun setStatusBarShowState(activity: Activity, isShow: Boolean) {
        val bar = ImmersionBar.with(activity).fitsSystemWindows(false).autoDarkModeEnable(true)/*.fullScreen(true)*/
        if (APPConfig.isCurrentThemeLight()) {
            bar.barColor(R.color.white)
        } else {
            bar.barColor(R.color.color_dark_1E1E1E)
        }
        if (isShow) {
            bar.hideBar(BarHide.FLAG_SHOW_BAR)
        } else {
            bar.hideBar(BarHide.FLAG_HIDE_BAR)
        }
        bar.init()
    }

    fun getScopeSeekBar(): SeekBar {
        return optionScopeLayout.seekBar
    }

    fun setJumpTitle(title: String) {
        optionScopeLayout.setTitle(title)
    }

    fun setJumpProgress(pageString: String, offset: Int) {
        optionScopeLayout.setProgressText(pageString, offset)
    }

    fun saveHistory() {
        optionScopeLayout.saveCurrentHistory()
    }

    fun getLastHistory(): ReadProcess {
        return optionScopeLayout.readProcess
    }

    fun setCurrentTabShow(tab: Int) {
        if (tab == currentTab) {
            if (optionContainerLayout.visibility == View.VISIBLE) {
                optionContainerLayout.visibility = View.GONE
                optionScopeLayout.showTitle(false)
            } else {
                optionContainerLayout.visibility = View.VISIBLE
                optionScopeLayout.showTitle(true)
            }
        } else {
            optionContainerLayout.visibility = View.VISIBLE
            when (tab) {
                TAB_SCOPE -> {
                    optionScopeLayout.visibility = View.VISIBLE
                    optionScopeLayout.showTitle(true)

                    optionBrightnessLayout.visibility = View.GONE
                    optionFontLayout.visibility = View.GONE
                }
                TAB_FONT -> {
                    optionScopeLayout.visibility = View.GONE
                    optionBrightnessLayout.visibility = View.GONE
                    optionFontLayout.visibility = View.VISIBLE
                }
                TAB_BRIGHTNESS -> {
                    optionScopeLayout.visibility = View.GONE
                    optionBrightnessLayout.visibility = View.VISIBLE
                    optionFontLayout.visibility = View.GONE
                }
            }
            currentTab = tab
        }
        onViewClickListener?.onClickAction(contentsImageView, OptionClickTag.TAG_CLICK_TOOL_LAYOUT, 0)
    }

    override fun onClick(v: View?) {
        when (v) {
            commTitleLayout -> {
                onViewClickListener?.onClickAction(contentsImageView, OptionClickTag.TAG_CLICK_TOOL_TITLE, 0)
            }
            contentsImageView -> {
                onViewClickListener?.onClickAction(contentsImageView, OptionClickTag.TAG_CONTENT, 0)
            }
            fontImageView -> {
                setCurrentTabShow(TAB_FONT)
            }
            scopeImageView -> {
                setCurrentTabShow(TAB_SCOPE)
            }
            brightImageView -> {
                setCurrentTabShow(TAB_BRIGHTNESS)
            }
            bottomContainerLayout -> {
                onViewClickListener?.onClickAction(contentsImageView, OptionClickTag.TAG_CLICK_TOOL_LAYOUT, 0)
            }
            bookNoteImageView -> { // 点击笔记
                onViewClickListener?.onClickAction(contentsImageView, OptionClickTag.TAG_BOOK_NOTE, 0)
            }
            bookSearchImageView -> { // 点击搜索
                onViewClickListener?.onClickAction(contentsImageView, OptionClickTag.TAG_BOOK_SEARCH, 0)
            }
            bookmarkImageView -> { // 点击书签
                bookmarkImageView.isSelected = !bookmarkImageView.isSelected
                onViewClickListener?.onClickAction(contentsImageView, OptionClickTag.TAG_BOOK_MARK, 0)
            }
        }
    }
}
