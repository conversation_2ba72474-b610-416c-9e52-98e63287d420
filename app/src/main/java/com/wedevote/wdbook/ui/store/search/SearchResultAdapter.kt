package com.wedevote.wdbook.ui.store.search

import android.text.Html
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import com.aquila.lib.base.BaseRecycleAdapter
import com.aquila.lib.base.BaseViewHolder
import com.wedevote.wdbook.tools.util.ImageLoadUtil
import com.aquila.lib.widget.view.AdaptiveImageView
import com.wedevote.wdbook.R
import com.wedevote.wdbook.entity.store.ProductEntity
import com.wedevote.wdbook.tools.util.PictureUtil
import com.wedevote.wdbook.tools.util.initAuthorsName
import com.wedevote.wdbook.tools.util.parseHtmlTag
import com.wedevote.wdbook.ui.store.BookDetailActivity
import com.wedevote.wdbook.ui.home.microwidget.BookProductViewHolder

/***
 * @date 创建时间 2022/1/19 14:11
 * <AUTHOR> <PERSON><PERSON>
 * @description
 */
class SearchResultAdapter : BaseRecycleAdapter<ProductEntity, BookProductViewHolder>() {
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BookProductViewHolder {
        return BookProductViewHolder(parent, BookProductViewHolder.TYPE_PRODUCT_LIST)
    }
}
