package com.wedevote.wdbook.ui.read.lib.span;

import com.wedevote.wdbook.base.SDKSingleton;

/**
 * Created by theophilus on 4/10/18.
 */

public class TableDataSpan {
    private int line;
    private int rowIndex;
    private int maxRow;

    public TableDataSpan(String info) {
        String[] params = info.split("#");
        if (params.length == 3) {
            try {
                rowIndex = Integer.parseInt(params[0]);
                maxRow = Integer.parseInt(params[1]);
                line = Integer.parseInt(params[2]);
            } catch (RuntimeException e) {
                e.printStackTrace();
            }
        }
    }

    public int getLine() {
        return line;
    }

    public int getRowIndex() {
        return rowIndex;
    }

    public int getMaxRow() {
        return maxRow;
    }

    public int getBackgroundColor() {
        if (line % 2 == 0 && SDKSingleton.INSTANCE.getAppBl().isCurrentThemeLight()) {
            return 0xfff5f5f5;
        } else {
            return -1;
        }
    }
}
