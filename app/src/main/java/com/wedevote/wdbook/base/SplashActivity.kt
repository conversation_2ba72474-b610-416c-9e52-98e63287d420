package com.wedevote.wdbook.base

import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.widget.ImageView
import android.widget.TextView
import com.wedevote.wdbook.R
import com.wedevote.wdbook.tools.util.DataPathUtil
import com.wedevote.wdbook.tools.util.NetWorkUtils
import com.wedevote.wdbook.ui.home.HomeMainActivity
import com.wedevote.wdbook.ui.service.SyncDataService
import java.io.File
import java.util.*

/***
 * @date 创建时间 2020/4/7 17:38
 * <AUTHOR> <PERSON><PERSON>
 * @description APP 的启动页面
 */
class SplashActivity : RootActivity() {
    private lateinit var logoImageView: ImageView
    private lateinit var appNameTextView: TextView
    val handler = Handler()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_splash_layout)

        logoImageView = findViewById(R.id.splash_logo_ImageView)
        appNameTextView = findViewById(R.id.splash_app_name_TextView)

        setSwipeBackEnable(false)
        initTokenInfo()
        clearExpiredCacheFile()
    }

    private fun initTokenInfo() {
        if (NetWorkUtils.isNetworkAvailable()) {
            val intent = Intent(this@SplashActivity, SyncDataService::class.java)
            startService(intent)
        }
        handler.postDelayed({ gotoMainActivity() }, 1500)
    }

    private fun gotoMainActivity() {
        val intent = Intent(this, HomeMainActivity::class.java)
        startActivity(intent)
        finish()
        overridePendingTransition(
            com.aquila.lib.dialog.R.anim.anim_alpha_in,
            com.aquila.lib.dialog.R.anim.anim_alpha_out
        )
    }

    /*清除过期的缓存文件*/
    private fun clearExpiredCacheFile() {
        val dirFile = File(DataPathUtil.getPictureCachePath())
        val listFile = dirFile.listFiles()
        if (!listFile.isNullOrEmpty()) {
            Arrays.sort(
                listFile
            ) { o1, o2 -> if (o1!!.lastModified() > o2!!.lastModified()) 1 else -1 }
            var i = 0
            for (f in listFile) {
                if (f.isFile) {
                    if (i >= 10) {
                        f.delete()
                    }
                    i++
                }
            }
        }
    }
}
