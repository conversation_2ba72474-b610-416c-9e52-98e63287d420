package com.wedevote.wdbook.base

import com.wedevote.wdbook.bl.AppBl
import com.wedevote.wdbook.constants.LanguageMode
import com.wedevote.wdbook.tools.util.APPUtil
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch

object WrapCoroutineHelper {

    private fun currentLanguageMode(): LanguageMode {
        return APPUtil.getLanguageMode()
    }

    fun getBookAgreementURL(language: LanguageMode? = null, callBack: (url: String) -> Unit) {
        val langMode = language ?: currentLanguageMode()
        MainScope().launch(ExceptionHandler.getCoroutineExceptionHandler {
            val url = SDKSingleton.appBl.buildWebUrl(AppBl.DEFAULT_BASE_WEB, AppBl.PATH_AGREEMENT, langMode)
            callBack(SDKSingleton.appBl.appendThemeParam(url, APPConfig.isCurrentThemeLight()))
        }) {
            callBack(SDKSingleton.appBl.getBookAgreementURL(langMode, !APPConfig.isCurrentThemeLight()))
        }
    }

    fun getBookPrivacyURL(language: LanguageMode? = null, callBack: (url: String) -> Unit) {
        val langMode = language ?: currentLanguageMode()
        MainScope().launch(ExceptionHandler.getCoroutineExceptionHandler {
            val url = SDKSingleton.appBl.buildWebUrl(AppBl.DEFAULT_BASE_WEB, AppBl.PATH_PRIVACY, langMode)
            callBack(SDKSingleton.appBl.appendThemeParam(url, APPConfig.isCurrentThemeLight()))
        }) {
            callBack(SDKSingleton.appBl.getBookPrivacyURL(langMode, !APPConfig.isCurrentThemeLight()))
        }
    }
}