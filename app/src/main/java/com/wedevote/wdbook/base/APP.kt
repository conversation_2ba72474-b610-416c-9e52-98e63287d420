package com.wedevote.wdbook.base

import android.app.Application
import android.content.Context
import android.net.ConnectivityManager
import android.net.Network
import android.os.Build
import com.aquila.lib.log.KLog
import com.aquila.lib.tools.ToolsLibAPP
import com.stripe.android.PaymentConfiguration
import com.sunchen.netbus.NetStatusBus
import com.wedevote.wdbook.constants.AppEnvironment
import com.wedevote.wdbook.tools.util.APPUtil
import com.wedevote.wdbook.tools.util.AnalyticsUtils
import com.wedevote.wdbook.tools.util.CftUtils
import com.wedevote.wdbook.tools.util.ThirdPartyActivityAdapter
import com.aquila.lib.tools.singleton.SPSingleton
import com.wedevote.wdbook.tools.util.SPKeyDefine

/***
 * @date 创建时间 2020/4/7 17:35
 * <AUTHOR> W.YuLong
 * @description
 */
class APP : Application() {

    companion object {
        const val TAG = "APP"

        var app: APP? = null

        var firstRun = true

        @JvmStatic
        fun get(): APP {
            return app!!
        }
    }

    override fun onCreate() {
        super.onCreate()
        app = this
        ToolsLibAPP.init(this)

        listenNetworkConnectivity(this)

        AndroidBusinessPatch.init(this)
        SDKSingleton.initializeSdk(this)
        KLog.setIsShowLog(APPUtil.isApkInDebug || APPConfig.env == AppEnvironment.TEST)
        PaymentConfiguration.init(this, SDKSingleton.appBl.getStripeKey())
        AnalyticsUtils.init(this)
        NetStatusBus.getInstance().init(this)

        CftUtils.appStartCFTLogic(this)
        firstRun = false

        if (APPUtil.isAboveAndroid15()) {
            // 第三方页面 Android15以上全面屏适配器
            registerActivityLifecycleCallbacks(ThirdPartyActivityAdapter(APPConfig.env == AppEnvironment.TEST))
        }

        // 若检测到已更新到新版本，清除强制升级标记
        try {
            val currentVersion = APPUtil.getVersionCode()
            val lastVersion = SPSingleton.get().getInt(SPKeyDefine.SP_LastAppVersionCode, -1)
            if (lastVersion != -1 && currentVersion > lastVersion) {
                SPSingleton.get().putBoolean(SPKeyDefine.SP_ForceUpgrade, false)
            }
            SPSingleton.get().putInt(SPKeyDefine.SP_LastAppVersionCode, currentVersion)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun listenNetworkConnectivity(app: APP) {
        // https://developer.android.com/develop/connectivity/network-ops/reading-network-state#listening-events
        val connectivityManager =
            getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            connectivityManager.registerDefaultNetworkCallback(object :
                ConnectivityManager.NetworkCallback() {
                override fun onAvailable(network: Network) {
                    // When a new network becomes the default
                    if (!firstRun) {
                        CftUtils.networkChangedCFTLogic(app)
                    }
                }
            })
        }
    }
}
