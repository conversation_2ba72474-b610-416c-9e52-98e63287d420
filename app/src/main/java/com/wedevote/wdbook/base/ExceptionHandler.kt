package com.wedevote.wdbook.base

import android.util.Log
import android.database.sqlite.SQLiteFullException
import com.aquila.lib.log.KLog
import com.aquila.lib.tools.singleton.SPSingleton
import com.aquila.lib.tools.util.ToastUtil
import com.wedevote.wdbook.R
import com.wedevote.wdbook.constants.Constants
import com.wedevote.wdbook.entity.ExceptionType
import com.wedevote.wdbook.exception.ApiException
import com.wedevote.wdbook.exception.ErrorInfo
import com.wedevote.wdbook.exception.SDKException
import com.wedevote.wdbook.network.ApiErrorCode
import com.wedevote.wdbook.tools.event.LogoutEvent
import com.wedevote.wdbook.tools.util.CftUtils
import com.wedevote.wdbook.tools.util.SPKeyDefine
import com.wedevote.wdbook.tools.util.getFunctionInfo
import com.wedevote.wdbook.ui.account.ShowDeviceDisableDialogActivity
import com.wedevote.wdbook.ui.home.HomeMainActivity
import com.wedevote.wdbook.ui.home.HomeTab
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus

/***
 * Created by Stephen on 2021/5/24 14:33
 *
 * @description
 */
object ExceptionHandler {

    fun getCoroutineExceptionHandler(block: (t: Throwable) -> Unit): CoroutineExceptionHandler {
        return CoroutineExceptionHandler { _, exception ->
            handleException(exception)
            block(exception)
        }
    }

    val coroutineExceptionHandler = CoroutineExceptionHandler { _, exception ->
        handleException(exception)
    }

    /**
     * 如果开启协程，需要调用 handleException
     */
    fun handleException(t: Throwable) {
        if (isDbFull(t)) {
            ToastUtil.showToastShortOnce(R.string.tip_operation_too_frequent)
            KLog.e("SQLite FULL: ${t.message}")
            return
        }

        var code = 1
        val errorType: ExceptionType
        when (t) {
            is SDKException -> {
                code = handleSDKException(t)
                errorType = ExceptionType.SDKException
            }

            is ApiException -> {
                code = handleApiException(t)
                errorType = ExceptionType.APIException
            }

            else -> {
                if (t.toString().contains(Constants.SOCKS_HOST_UNREACHABLE_ERROR)) {
                    CftUtils.recordAPIFailed()
                }

                t.printStackTrace()
                errorType = ExceptionType.JavaException
                KLog.e("OtherException occurred" + t.message)
            }
        }
        SDKSingleton.loggerBl.handleThrowable(code, errorType, t, getFunctionInfo())
    }

    fun <T> wrap(valueOnException: T?, block: () -> T?): T? {
        return try {
            block()
        } catch (t: Throwable) {
            handleException(t)
            valueOnException
        }
    }

    fun wrapUnit(block: () -> Unit) {
        try {
            block()
        } catch (t: Throwable) {
            handleException(t)
        }
    }

    private fun handleSDKException(exception: SDKException): Int {
        when (exception.code) {
            ErrorInfo.USER_NOT_LOGIN.code -> {
                KLog.e("未检测到用户登录")
            }

            ErrorInfo.Invalid_token.code,
            ErrorInfo.TOKEN_IS_EXPIRE.code,
            -> {
                if (exception.errDetail != null && exception.errDetail!!.errno == ApiErrorCode.DeviceExceedLimit_1312) {
                    ShowDeviceDisableDialogActivity.showDeviceFullDialog(APP.get())
                    MainScope().launch {
                        try {
                            SPSingleton.get().removeKey(SPKeyDefine.SP_LoginUserId)
                            SDKSingleton.sessionBl.logout()
                            EventBus.getDefault().post(LogoutEvent())
                        } catch (e: Exception) {
                            KLog.e("Exception when trying to logout : $e")
                        }
                    }
                } else {
                    KLog.e("用户已经退出登录")
                    if (!SPSingleton.get().getString(SPKeyDefine.SP_LoginUserId, null)
                            .isNullOrEmpty()
                    ) {
                        MainScope().launch {
                            try {
                                SDKSingleton.sessionBl.logout()
                                ToastUtil.showToastShortOnce(R.string.user_already_logout)
                                EventBus.getDefault().post(LogoutEvent(true))
                                HomeMainActivity.gotoHomeActivity(APP.get(), HomeTab.STORE)
                            } catch (e: Exception) {
                                KLog.e("Exception when trying to logout and go to STORE home tab : $e")
                            }
                        }
                    }
                }
            }

            else -> {
                KLog.e("Unhandled sdk exception: $exception")
                exception.printStackTrace()
            }
        }
        return exception.code

    }

    private fun handleApiException(exception: ApiException): Int {
        when (exception.code) {
            ErrorInfo.Invalid_token.code -> {
                if (exception.errDetail != null && exception.errDetail!!.errno == ApiErrorCode.DeviceExceedLimit_1312) {
                    ShowDeviceDisableDialogActivity.showDeviceFullDialog(APP.get())
                    MainScope().launch {
                        try {
                            SPSingleton.get().removeKey(SPKeyDefine.SP_LoginUserId)
                            SDKSingleton.sessionBl.logout()
                        } catch (e: Exception) {
                            KLog.e("Exception when trying to logout : $e")
                        }
                    }
                } else {
                    if (!SPSingleton.get().getString(SPKeyDefine.SP_LoginUserId, null)
                            .isNullOrEmpty()
                    ) {
                        MainScope().launch {
                            try {
                                SDKSingleton.sessionBl.logout()
                                ToastUtil.showToastShortOnce(R.string.user_already_logout)
                                HomeMainActivity.gotoHomeActivity(APP.get(), HomeTab.STORE)
                            } catch (e: Exception) {
                                KLog.e("Exception when trying to logout and go to STORE home tab : $e")
                            }
                        }
                    }
                }
            }

            ErrorInfo.REPEAT_PURCHASE.code -> { // 重复购买
                ToastUtil.showToastLong("您已购买此商品，请刷新查看")
            }
        }
        KLog.e("ApiException occurred $exception")
        exception.printStackTrace()
        return exception.code
    }

    private fun isDbFull(t: Throwable): Boolean {
        return t is SQLiteFullException
    }
}
