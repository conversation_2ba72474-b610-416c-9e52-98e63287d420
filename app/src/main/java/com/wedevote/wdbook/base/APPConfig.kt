package com.wedevote.wdbook.base

import com.wedevote.wdbook.BuildConfig
import com.wedevote.wdbook.R
import com.wedevote.wdbook.constants.AppEnvironment

/***
 * @date 创建时间 2020/7/29 15:06
 * <AUTHOR> <PERSON><PERSON>
 * @description
 */
object APPConfig {
    val env = when (BuildConfig.FLAVOR_app) {
        "wdbookprod" -> AppEnvironment.PROD
        "wdbookinner" -> AppEnvironment.INNER
        else -> AppEnvironment.TEST
    }
    var isChangeThemeClicked: Boolean = false

    // 是否禁用购买
    var isDisablePayment = false

    var lastClickTime = 0L
    fun isFastClick(): Boolean {
        return if (System.currentTimeMillis() - lastClickTime > 1000) {
            lastClickTime = System.currentTimeMillis()
            false
        } else true
    }

    fun getCurrentThemeStyle(): Int {
        return if (SDKSingleton.appBl.isCurrentThemeLight()) R.style.AppTheme_light else R.style.AppTheme_Dark_dark
    }

    fun isCurrentThemeLight(): Boolean {
        return SDKSingleton.appBl.isCurrentThemeLight()
    }
}
