package com.wedevote.wdbook.base

import android.os.Bundle
import android.view.ViewGroup
import android.widget.EditText
import android.widget.TextView
import com.aquila.lib.base.BaseRecycleAdapter
import com.aquila.lib.base.BaseViewHolder
import com.aquila.lib.widget.view.CustomRecyclerView
import com.wedevote.wdbook.R
import com.wedevote.wdbook.entity.base.DeeplinkEntity
import com.wedevote.wdbook.tools.util.FilePathUtil
import com.wedevote.wdbook.ui.home.microwidget.DeepLinkUtils
import com.wedevote.wdbook.utils.JsonUtility

/***
 * @date 创建时间 2022/4/15 16:59
 * <AUTHOR> W.<PERSON>
 * @description
 */
class TestDeeplinkActivity : RootActivity() {
    lateinit var dataRecyclerView: CustomRecyclerView
    lateinit var linkAdapter: DeeplinkAdapter

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_test_deep_link_layout)
        dataRecyclerView = findViewById(R.id.deeplink_data_RecyclerView)
        linkAdapter = DeeplinkAdapter()
        dataRecyclerView.adapter = linkAdapter

        val json = FilePathUtil.getTextFromAssets("deeplink.json")
        val dataList = JsonUtility.decodeFromString<ArrayList<DeeplinkEntity>>(json)
        linkAdapter.dataList = dataList
    }
}

/***
 *@date 创建时间 2022/4/15 17:12
 *<AUTHOR> W.YuLong
 *@description
 */

class DeeplinkAdapter : BaseRecycleAdapter<DeeplinkEntity, DeeplinkViewHolder>() {
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): DeeplinkViewHolder {
        return DeeplinkViewHolder(parent)
    }
}

/***
 *@date 创建时间 2022/4/15 17:08
 *<AUTHOR> W.YuLong
 *@description
 */
class DeeplinkViewHolder(parent: ViewGroup) : BaseViewHolder(parent, R.layout.holder_item_test_deeplink_layout) {
    val nameTextView: TextView = itemView.findViewById(R.id.deeplink_name_TextView)
    val urlEditText: EditText = itemView.findViewById(R.id.deeplink_url_EditText)

    override fun <T> initUIData(t: T) {
        t as DeeplinkEntity
        nameTextView.text = t.name
        urlEditText.setText(t.deeplink)
        itemView.setOnClickListener {
            DeepLinkUtils.parseDeepLink(itemView.context, urlEditText.text.toString())
        }
    }
}
