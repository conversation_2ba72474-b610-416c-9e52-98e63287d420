package com.wedevote.wdbook.base

import com.wedevote.wdbook.SDK
import com.wedevote.wdbook.bl.DownloadBl
import com.wedevote.wdbook.bl.UserBl
import com.wedevote.wdbook.constants.DownloadStatus
import com.wedevote.wdbook.entity.BookNoteCountEntity
import com.wedevote.wdbook.entity.BookmarkEntity
import com.wedevote.wdbook.entity.DownloadDataEntity
import com.wedevote.wdbook.entity.NoteEntity
import com.wedevote.wdbook.entity.PurchasedResourceEntity
import com.wedevote.wdbook.entity.resource.ResourceDownloadInfo
import com.wedevote.wdbook.entity.shelf.HomeShelfItemCombineEntity
import com.wedevote.wdbook.entity.shelf.ReadProgressEntity
import com.wedevote.wdbook.entity.shelf.ShelfArchiveItemEntity
import com.wedevote.wdbook.entity.shelf.ShelfBookEntity
import com.wedevote.wdbook.entity.shelf.ShelfBookItemEntity

/***
 * @date 创建时间 2021/12/27 11:01
 * <AUTHOR> <PERSON><PERSON>
 * @description
 */
class LocalDBWrapBl(sdk: SDK) {
    val userBl: UserBl
    val downloadBl: DownloadBl

    init {
        userBl = sdk.userBl
        downloadBl = sdk.downloadBl
    }

    fun transactionSaveShelfArchiveData(archiveName: String): ShelfArchiveItemEntity {
        return ExceptionHandler.wrap(ShelfArchiveItemEntity(), {
            userBl.transactionSaveShelfArchiveData(archiveName)
        }) ?: ShelfArchiveItemEntity()
    }

    fun saveShelfItemData(entity: ShelfBookEntity) {
        ExceptionHandler.wrapUnit {
            userBl.saveShelfItemData(entity)
        }
    }

    fun getAllNoteCount(): Long {
        return ExceptionHandler.wrap(0L, {
            userBl.getAllNoteCount()
        }) ?: 0L
    }

    fun getBookNoteCountList(offset: Long, limit: Long = 20): List<BookNoteCountEntity> {
        return ExceptionHandler.wrap(emptyList(), {
            userBl.getBookNoteCountList(offset, limit)
        }) ?: emptyList()
    }

    fun getBookNoteCountEntity(resourceId: String): BookNoteCountEntity? {
        return ExceptionHandler.wrap(null, {
            userBl.getBookNoteCountEntity(resourceId)
        })
    }

    fun getLocalEncryptionKey(fileId: String): String? {
        return ExceptionHandler.wrap("", {
            userBl.getLocalEncryptionKey(fileId)
        })
    }

    fun getCopySuffix(resourceId: String): String {
        return ExceptionHandler.wrap("", {
            userBl.getCopySuffix(resourceId)
        })!!
    }

    fun updateBookLastVisitTime(resourceId: String) {
        ExceptionHandler.wrapUnit {
            userBl.updateBookLastVisitTime(resourceId)
        }
    }

    fun deleteArchiveItem(originalArchiveId: String) {
        ExceptionHandler.wrapUnit {
            userBl.deleteArchiveItem(originalArchiveId)
        }
    }

    fun moveShelfBookToArchive(bookList: List<ShelfBookItemEntity>, clientArchiveId: String?) {
        ExceptionHandler.wrapUnit {
            userBl.moveShelfBookToArchive(bookList, clientArchiveId)
        }
    }

    fun getBookListByClientArchiveId(clientArchiveId: String, offset: Long = 0, limit: Long = 20): List<ShelfBookItemEntity>? {
        return ExceptionHandler.wrap(null, {
            userBl.getBookListByClientArchiveId(clientArchiveId, offset, limit)
        })
    }

    fun renameArchiveName(archiveName: String?, clientArchiveId: String) {
        ExceptionHandler.wrapUnit {
            userBl.renameArchiveName(archiveName, clientArchiveId)
        }
    }

    fun getShelfEntityList(offset: Long, limit: Long): List<HomeShelfItemCombineEntity> {
        return ExceptionHandler.wrap(emptyList(), {
            userBl.getShelfEntityList(offset, limit)
        })!!
    }

    fun getArchiveDataList(): List<ShelfArchiveItemEntity>? {
        return ExceptionHandler.wrap(emptyList(), {
            userBl.getArchiveDataList()
        })
    }

    fun getShelfArchiveUrlList(clientArchiveId: String?, maxItem: Long = 4L): MutableList<String>? {
        return ExceptionHandler.wrap(arrayListOf(), {
            userBl.getShelfArchiveUrlList(clientArchiveId, maxItem)
        })
    }

    fun saveFilePageInfo(fileId: String, fontSize: Int, pageInfo: String) {
        ExceptionHandler.wrapUnit {
            userBl.saveFilePageInfo(fileId, fontSize, pageInfo)
        }
    }

    fun getFilePageInfo(fileId: String, fontSize: Int): String? {
        return ExceptionHandler.wrap("", {
            userBl.getFilePageInfo(fileId, fontSize)
        })
    }

    fun saveBookXmlData(fileId: String, xmlData: String) {
        ExceptionHandler.wrapUnit {
            userBl.saveBookXmlData(fileId, xmlData)
        }
    }

    fun getBookXmlData(fileId: String): String? {
        return ExceptionHandler.wrap("", {
            userBl.getBookXmlData(fileId)
        })
    }

    fun saveReadProgress(entity: ReadProgressEntity) {
        ExceptionHandler.wrapUnit {
            userBl.saveReadProgress(entity)
        }
    }

    fun saveBookmark(bookmarkEntity: BookmarkEntity) {
        ExceptionHandler.wrapUnit {
            userBl.saveBookmark(bookmarkEntity)
        }
    }

    fun deleteBookmark(dataId: String) {
        ExceptionHandler.wrapUnit {
            userBl.deleteBookmark(dataId)
        }
    }

    fun deleteCurrentPageBookmark(resourceId: String, pagePath: String, firstWordLocation: Int, lastWordLocation: Int) {
        ExceptionHandler.wrapUnit {
            userBl.deleteCurrentPageBookmark(resourceId, pagePath, firstWordLocation, lastWordLocation)
        }
    }

    fun getBookmarkEntity(resourceId: String, pagePath: String, firstWordLocation: Int, lastWordLocation: Int): BookmarkEntity? {
        return ExceptionHandler.wrap(null, {
            userBl.getBookmarkEntity(resourceId, pagePath, firstWordLocation, lastWordLocation)
        })
    }

    fun getBookmarkEntityList(resourceId: String, offset: Long, limit: Long): ArrayList<BookmarkEntity> {
        return ExceptionHandler.wrap(arrayListOf(), {
            userBl.getBookmarkEntityList(resourceId, offset, limit)
        })!!
    }

    fun saveNote(noteEntity: NoteEntity) {
        ExceptionHandler.wrapUnit {
            userBl.saveNote(noteEntity)
        }
    }

    fun solvedConflictData(remoteId: String, dataId: String) {
        ExceptionHandler.wrapUnit {
            userBl.solvedConflictData(remoteId, dataId)
        }
    }

    fun deleteNote(dataId: String) {
        ExceptionHandler.wrapUnit {
            userBl.deleteNote(dataId)
        }
    }

    fun deleteNoteList(dataIdList: List<String>) {
        ExceptionHandler.wrapUnit {
            userBl.deleteNoteList(dataIdList)
        }
    }

    fun removeNote(dataId: String) {
        ExceptionHandler.wrapUnit {
            userBl.removeNote(dataId)
        }
    }

    fun recoverNote(dataId: String) {
        ExceptionHandler.wrapUnit {
            userBl.recoverNote(dataId)
        }
    }

    fun getPageNoteEntityList(resourceId: String, pagePath: String, firstWordPosition: Int, lastWordPosition: Int): List<NoteEntity> {
        return ExceptionHandler.wrap(emptyList(), {
            userBl.getPageNoteEntityList(resourceId, pagePath, firstWordPosition, lastWordPosition)
        })!!
    }

    fun mergeNote(
        noteEntity: NoteEntity,
        resourceId: String,
        path: String,
        force: Boolean
    ): NoteEntity? {
        return ExceptionHandler.wrap(null, {
            userBl.mergeNote(noteEntity, resourceId, path, force)
        })
    }

    fun matchNoteInContent(noteEntity: NoteEntity, content: String): NoteEntity {
        return ExceptionHandler.wrap(null, {
            userBl.matchNoteInContent(noteEntity, content)
        })!!
    }

    fun getNoteEntityListByPagePath(resourceId: String, pagePath: String): List<NoteEntity> {
        return ExceptionHandler.wrap(emptyList(), {
            userBl.getNoteEntityListByPagePath(resourceId, pagePath)
        })!!
    }

    fun getNoteEntityByDataId(dataId: String, dataStatus: Int): NoteEntity? {
        return ExceptionHandler.wrap(null, {
            userBl.getNoteEntityByDataId(dataId, dataStatus)
        })
    }

    fun getNoteEntityList(resourceId: String, offset: Int, limit: Int): List<NoteEntity> {
        return ExceptionHandler.wrap(arrayListOf(), {
            userBl.getNoteEntityList(resourceId, offset, limit)
        })!!
    }

    fun getRemovedNoteEntityList(resourceId: String): List<NoteEntity> {
        return ExceptionHandler.wrap(arrayListOf(), {
            userBl.getRemovedNoteEntityList(resourceId)
        })!!
    }

    fun getResourceTypeId(resourceId: String): String? {
        return ExceptionHandler.wrap("", {
            userBl.getResourceTypeId(resourceId)
        })
    }

    fun getRecycleBinNoteCount(resourceId: String): Long {
        return ExceptionHandler.wrap(0L, {
            userBl.getRecycleBinNoteCount(resourceId)
        }) ?: 0L
    }

    fun getPurchasedCount(): Int {
        return ExceptionHandler.wrap(0, {
            userBl.getPurchasedCount()
        }) ?: 0
    }

    fun getPurchasedResourceEntityList(position: Int, limit: Int): List<PurchasedResourceEntity> {
        return ExceptionHandler.wrap(emptyList(), {
            userBl.getPurchasedResourceEntityList(position, limit)
        })!!
    }

    fun getBookReadProgressEntity(resourceId: String?): ReadProgressEntity? {
        return ExceptionHandler.wrap(null, {
            userBl.getBookReadProgressEntity(resourceId)
        })
    }

    fun updateDownloadStatus(fileId: String, status: DownloadStatus) {
        ExceptionHandler.wrapUnit {
            downloadBl.updateDownloadStatus(fileId, status)
        }
    }

    fun updateDownloadFileSize(fileId: String, downloadSize: Long) {
        ExceptionHandler.wrapUnit {
            downloadBl.updateDownloadFileSize(fileId, downloadSize)
        }
    }

    fun updateDownloadingProgressStatus(fileId: String, downloadSize: Long) {
        ExceptionHandler.wrapUnit {
            downloadBl.updateDownloadingProgressStatus(fileId, downloadSize)
        }
    }

    fun getDownloadDataByDownloadStatus(status: DownloadStatus): List<DownloadDataEntity> {
        return ExceptionHandler.wrap(emptyList(), {
            downloadBl.getDownloadDataByDownloadStatus(status)
        })!!
    }

    fun updateActualFileName(fileId: String, actualFileName: String) {
        ExceptionHandler.wrapUnit {
            downloadBl.updateActualFileName(fileId, actualFileName)
        }
    }

    fun deleteDownloadItem(downloadDataEntity: DownloadDataEntity) {
        ExceptionHandler.wrapUnit {
            downloadBl.deleteDownloadItem(downloadDataEntity)
        }
    }

    fun getResourceDownloadInfo(resourceId: String): ResourceDownloadInfo? {
        return ExceptionHandler.wrap(null, {
            downloadBl.getResourceDownloadInfo(resourceId)
        })
    }

    fun getFileDownloadDataEntity(fileId: String?): DownloadDataEntity? {
        return ExceptionHandler.wrap(null, {
            downloadBl.getFileDownloadDataEntity(fileId)
        })
    }

    fun fetchDownloadFileEntity(fileId: String, dirPath: String): DownloadDataEntity? {
        return ExceptionHandler.wrap(null, {
            downloadBl.fetchDownloadFileEntity(fileId, dirPath)
        })
    }

    fun saveDownloadData(entity: DownloadDataEntity?) {
        ExceptionHandler.wrapUnit {
            downloadBl.saveDownloadData(entity)
        }
    }
}
