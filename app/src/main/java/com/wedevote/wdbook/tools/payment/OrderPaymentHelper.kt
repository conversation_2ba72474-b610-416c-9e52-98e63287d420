package com.wedevote.wdbook.tools.payment

import android.content.Intent
import android.os.Handler
import android.os.Looper
import android.view.View
import androidx.appcompat.app.AppCompatActivity
import com.aquila.lib.base.OnViewClickListener
import com.aquila.lib.dialog.CommAlertDialog
import com.aquila.lib.dialog.CommProgressDialog
import com.aquila.lib.log.KLog
import com.aquila.lib.tools.singleton.SPSingleton
import com.aquila.lib.tools.util.ToastUtil
import com.wedevote.wdbook.R
import com.wedevote.wdbook.base.APPConfig
import com.wedevote.wdbook.base.ExceptionHandler
import com.wedevote.wdbook.base.SDKSingleton
import com.wedevote.wdbook.constants.AppEnvironment
import com.wedevote.wdbook.constants.AnalyticsConstants
import com.wedevote.wdbook.constants.AnalyticsConstants.LOG_V1_PARAM_ORDER_ID
import com.wedevote.wdbook.entity.ExceptionDataEntity
import com.wedevote.wdbook.entity.ExceptionType
import com.wedevote.wdbook.entity.store.PaymentAmountEntity
import com.wedevote.wdbook.entity.store.ProductDetailEntity
import com.wedevote.wdbook.entity.user.InPaymentOrderEntity
import com.wedevote.wdbook.exception.ApiException
import com.wedevote.wdbook.tools.download.LifecycleFragment
import com.wedevote.wdbook.tools.event.OnRefreshAfterPay
import com.wedevote.wdbook.tools.payment.stripe.OnPaymentCallback
import com.wedevote.wdbook.tools.payment.stripe.StripePaymentUtil
import com.wedevote.wdbook.tools.util.AnalyticsUtils
import com.wedevote.wdbook.tools.util.AnalyticsUtils.logEvent
import com.wedevote.wdbook.tools.util.GsonUtil
import com.wedevote.wdbook.tools.util.PayTypeDefine
import com.wedevote.wdbook.tools.util.SPKeyDefine
import com.wedevote.wdbook.tools.util.findString
import com.wedevote.wdbook.tools.util.toJsonStr
import com.wedevote.wdbook.ui.account.OnLoginResultCallBack
import com.wedevote.wdbook.ui.account.SSOLoginActivity
import com.wedevote.wdbook.ui.dialogs.OnClickBuyListener
import com.wedevote.wdbook.ui.dialogs.PayResultDialog
import com.wedevote.wdbook.ui.dialogs.PurchaseFailureDialog
import com.wedevote.wdbook.ui.dialogs.UnpaidOrderDialog
import com.wedevote.wdbook.ui.service.SyncDataService
import com.wedevote.wdbook.ui.store.OrderConfirmActivity
import com.wedevote.wdbook.ui.store.OrderStatusDefine
import com.wedevote.wdbook.ui.store.PayPalPaymentHelper
import com.wedevote.wdbook.ui.user.feedback.EditFeedbackActivity
import com.wedevote.wdbook.utils.JsonUtility
import com.yuansfer.pay.YSAppPay
import com.yuansfer.pay.aliwx.AliWxPayMgr
import com.yuansfer.pay.util.ErrStatus
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus

/***
 * @date 创建时间 2020/9/21 12:01
 * <AUTHOR> W.YuLong
 * @description 订单和支付相关的逻辑封装
 */
class OrderPaymentHelper(private val activity: AppCompatActivity, var onOrderPayCallback: OnOrderPayCallback) :
    LifecycleFragment.LifecycleListener {
    var stripePayUtil: StripePaymentUtil
    var payPalPaymentHelper: PayPalPaymentHelper
    val progressDialog: CommProgressDialog
    lateinit var loadingDialog: CommProgressDialog

    var onPaymentCallback = object : OnPaymentCallback {
        override fun onPayResult(orderId: String, paymentResultEnum: OrderPaymentResultEnum) {
            when (paymentResultEnum) {
                OrderPaymentResultEnum.SUCCESS, OrderPaymentResultEnum.SUCCESS_ALT -> {
                    KLog.d("成功扣款，查询订单状态")
                    if (!activity.isFinishing && !loadingDialog.isShowing) {
                        loadingDialog.show()
                        loadingDialog.setTitleText(findString(R.string.getting_order_payment_results))
                    }
                    firstRequestTime = System.currentTimeMillis()
                    getPaymentStatus(orderId)
                }
                OrderPaymentResultEnum.CANCEL -> {
                    ToastUtil.showToastLong(R.string.pay_canceled)
                }
                OrderPaymentResultEnum.EXCEPTION -> {
                    ToastUtil.showToastLong(R.string.pay_error)
                }
                OrderPaymentResultEnum.FAILURE -> {
                    if (!activity.isFinishing) {
                        val failureDialog = PurchaseFailureDialog(activity)
                        failureDialog.onViewClickListener = object : OnViewClickListener {
                            override fun <T> onClickAction(v: View, str: String, t: T?) {
                                when (str) {
                                    PurchaseFailureDialog.ACTION_RETRY -> {
                                        onOrderPayCallback.retryAgain()
                                    }
                                    else -> {
                                        EditFeedbackActivity.gotoEditFeedback(activity)
                                    }
                                }
                            }
                        }
                        failureDialog.show()
                    }
                }
            }
        }

        override fun executePayAction() {
        }
    }

    init {
        activity.supportFragmentManager.beginTransaction().add(LifecycleFragment(this), LifecycleFragment::class.java.name)
            .commitAllowingStateLoss()

        loadingDialog = CommProgressDialog.with(activity).create()

        progressDialog = CommProgressDialog.with(activity)
            .setTitle(findString(R.string.loading)).create()
        stripePayUtil = StripePaymentUtil(activity, onPaymentCallback)
        payPalPaymentHelper = PayPalPaymentHelper(activity, onPaymentCallback)
    }

    private var resultOrderId: String = ""
    val payCallback = object : AliWxPayMgr.IAliWxPayCallback {
        override fun onPaySuccess(payType: Int) {
            onPaymentCallback.onPayResult(resultOrderId, OrderPaymentResultEnum.SUCCESS)
            KLog.d("onPaySuccess")
        }

        override fun onPayFail(payType: Int, errStatus: ErrStatus?) {
            onPaymentCallback.onPayResult(resultOrderId, OrderPaymentResultEnum.FAILURE)
            KLog.d("onPayFail，errStatus = ${errStatus?.toJsonStr()}")

            SDKSingleton.loggerBl.saveAndReportExceptionLog(ExceptionDataEntity().apply {
                errorType = ExceptionType.DEFAULT.value
                errorCode = 0
                errorMessage = "onPayFail，resultOrderId = $resultOrderId, errStatus = ${errStatus?.toJsonStr()}"
            })
        }

        override fun onPayCancel(payType: Int) {
            onPaymentCallback.onPayResult(resultOrderId, OrderPaymentResultEnum.CANCEL)
        }
    }

    override fun onLifecycleCreate() {
        YSAppPay.registerAliWxPayCallback(payCallback)
        if (APPConfig.env == AppEnvironment.TEST) {
            YSAppPay.setAliEnv(false)
        }
    }

    override fun onLifecycleDestroy() {
        YSAppPay.unregisterAliWxPayCallback(payCallback)
    }


    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        KLog.d("requestCode= $requestCode, resultCode = $resultCode")
        stripePayUtil.dealOnActivityResult(requestCode, resultCode, data)
    }

    override fun onLifecycleStop() {
        super.onLifecycleStop()
        if (progressDialog.isShowing) {
            progressDialog.dismiss()
        }
    }

    fun getOnClickBuyListener(productIdList: ArrayList<Long>): OnClickBuyListener? {
        return object : OnClickBuyListener {
            override fun onClickBuy(payType: String, entity: PaymentAmountEntity?) {
                if (entity != null) {
                    createPaymentOrder(payType, productIdList, entity)
                }
            }
        }
    }

    /*获取支付信息*/
    fun singlePayAmountAction(
        detailEntity: ProductDetailEntity,
        couponIds: List<Long>? = null,
        activityIds: List<Long>? = null,
        trialCalType: Int = 0
    ) {
        val productIdList = ArrayList<Long>()
        productIdList.add(detailEntity.productId)
        MainScope().launch {
            progressDialog.show()
            try {
                SDKSingleton.paymentBl.getPayAmountEntity(productIdList, couponIds, null, trialCalType).also { entity ->
                    OrderConfirmActivity.gotoConfirmOrderActivity(activity, entity, -1, listener = getOnClickBuyListener(productIdList))
                    progressDialog.dismiss()
                }
            } catch (e: Exception) {
                progressDialog.dismiss()
                doUnpaidInfoException(e, productIdList) {
                    singlePayAmountAction(detailEntity, couponIds, activityIds, trialCalType)
                }
            }
        }
    }


    /***
     *@date 创建时间 2022/7/18 17:48
     *<AUTHOR> W.YuLong
     *@description
     */
    fun doUnpaidInfoException(exception: Throwable, productIdList: ArrayList<Long>, block: (t: Throwable) -> Unit) {
        if (exception is ApiException && exception.code == 2005) {
            KLog.d(JsonUtility.encodeToString(exception.message))
            if (!exception.message.isNullOrEmpty()) {
                var inPaymentOrderEntity: InPaymentOrderEntity =
                    JsonUtility.decodeFromString(exception.message!!)
                val repeatPurchaseDialog = UnpaidOrderDialog(activity, inPaymentOrderEntity,
                    object : OnClickBuyListener {
                        override fun onClickBuy(type: String, payAmontEntity: PaymentAmountEntity?) {
                            doPayWithOrderId(inPaymentOrderEntity.orderId.toString(), payAmontEntity!!, type)
                        }
                    })
                repeatPurchaseDialog.show()
            }
        } else {
            if (!SDKSingleton.sessionBl.isLogin() && SPSingleton.get().getString(SPKeyDefine.SP_LoginUserId, null).isNullOrEmpty()) {
                SSOLoginActivity.checkAndGotoLogin(
                    activity, callBack = object : OnLoginResultCallBack {
                        override fun onLoginResult(isSuccess: Boolean) {
                            if (isSuccess) {
                                block
                            }
                        }
                    }
                )
            }
        }
    }

    /*优惠活动的订单确认页*/
    fun doPayActivityOrder(
        itemList: ArrayList<ProductDetailEntity>,
        couponIds: List<Long>? = null,
        activityIds: List<Long>? = null,
        trialCalType: Int = 0
    ) {
        var idList = ArrayList<Long>()
        for (item in itemList) {
            idList.add(item.productId)
        }
        MainScope().launch {
            try {
                progressDialog.show()
                SDKSingleton.paymentBl.getPayAmountEntity(idList, couponIds, null, trialCalType)?.also { entity ->
                    OrderConfirmActivity.gotoConfirmOrderActivity(activity, entity, listener = getOnClickBuyListener(idList))
                    progressDialog.dismiss()
                }
            } catch (e: Exception) {
                progressDialog.dismiss()
                doUnpaidInfoException(e, idList) {
                    doPayActivityOrder(itemList, couponIds, activityIds, trialCalType)
                }
                ExceptionHandler.handleException(e)
            }
        }
    }

    fun createPaymentOrder(payType: String, productIdList: ArrayList<Long>, amountEntity: PaymentAmountEntity, onSuccess: ((text: String) -> Unit)? = null,) {
        MainScope().launch {
            try {
                SDKSingleton.paymentBl.createPaymentOrder(productIdList, amountEntity)?.also { orderId ->
                    if (orderId != null) {
                        doPayWithOrderId(orderId, amountEntity, payType)
                        onSuccess?.invoke(orderId)
                    }
                }
            } catch (e: Throwable) {
                doUnpaidInfoException(e, productIdList) {}
                progressDialog.dismiss()
                ExceptionHandler.handleException(e)
            }
        }
    }

    fun doPayWithOrderId(orderId: String, amountEntity: PaymentAmountEntity, payType: String) {
        logEvent(AnalyticsConstants.LOG_V1_PRODUCT_CLICK_PAY, LOG_V1_PARAM_ORDER_ID, orderId)
        KLog.d("订单创建成功，订单号： $orderId")
        // 0元的商品，创建订单就直接返回成功
        if (amountEntity.actualAmount == 0f) {
            onPaymentCallback.onPayResult(orderId, OrderPaymentResultEnum.SUCCESS)
        } else {
            when (payType) {
                PayTypeDefine.PAY_TYPE_ALIPAY.value -> {
                    MainScope().launch() {
                        try {
                            loadingDialog.show()
                            val prepayResponse = SDKSingleton.storeBl.postYuansferPrepay(orderId, "alipay")
                            loadingDialog.dismiss()
                            resultOrderId = orderId
                            YSAppPay.getInstance().requestAliPayment(activity, prepayResponse!!.result!!.payInfo)
                        } catch (e: Exception) {
                            ExceptionHandler.handleException(e)
                            e.printStackTrace()
                        }
                    }
                }
                PayTypeDefine.PAY_TYPE_PAYPAL.value -> {
                    payPalPaymentHelper.initPayPalToken(orderId, amountEntity.actualAmount.toString())
                }
                PayTypeDefine.PAY_TYPE_CARD.value -> {
                    stripePayUtil.readyToPay(payType, orderId, amountEntity.actualAmount)
                }
            }
        }
    }


    var firstRequestTime: Long = 0
    var isPaySucceeded = false
    private var handler = Handler(Looper.getMainLooper())
    fun getPaymentStatus(orderId: String) {
        MainScope().launch() {
            try {
                SDKSingleton.paymentBl.getPaymentStatusEntity(orderId).also { entity ->
                    if (entity != null) {
                        KLog.d(GsonUtil.objectToJson(entity))
                        when (entity.status) {
                            OrderStatusDefine.ORDER_STATUS_PENDING, OrderStatusDefine.ORDER_STATUS_SUCCEED -> { // 待付款,继续调用查询支付结果的API
                                // 连续请求10秒的时间，如果一直是0 待支付状态就循环刷新10秒
                                if (System.currentTimeMillis() - firstRequestTime < 10_000L) {
                                    if (entity.status == OrderStatusDefine.ORDER_STATUS_SUCCEED) { // 如果在10秒内出现支付成功，状态标记为支付成功
                                        isPaySucceeded = true
                                    }
                                    handler.postDelayed({ getPaymentStatus(entity.orderId) }, 500)
                                } else {
                                    // 10秒之后还没有出现订单完成的状态，如果之前有支付成功就提示成功
                                    if (isPaySucceeded) {
                                        showPayResultDialog(entity.status, entity.orderId)
                                        AnalyticsUtils.logEvent(
                                            AnalyticsConstants.LOG_V1_PRODUCT_PAY_SUCCESS,
                                            AnalyticsConstants.LOG_V1_PARAM_ORDER_ID,
                                            entity.orderId
                                        )
                                    } else {
                                        if (!activity.isFinishing) {
                                            CommAlertDialog.with(activity).setMessage(R.string.order_info_processed)
                                                .setStartText(R.string.got_it).create().show()
                                        }
                                    }
                                    loadingDialog.dismiss()
                                }
                            }
                            OrderStatusDefine.ORDER_STATUS_FINISH -> { // 订单完成，最后所有流程走通显示成功
                                loadingDialog.dismiss()
                                showPayResultDialog(entity.status, entity.orderId)
                                AnalyticsUtils.logEvent(
                                    AnalyticsConstants.LOG_V1_PRODUCT_PAY_SUCCESS,
                                    AnalyticsConstants.LOG_V1_PARAM_ORDER_ID,
                                    entity.orderId
                                )
                            }
                            else -> { // 相当支付失败
                                loadingDialog.dismiss()
                                showPayResultDialog(entity.status, entity.orderId)
                            }
                        }
                    }
                }
            } catch (e: Exception) {
                ExceptionHandler.handleException(e)
                e.printStackTrace()
            }
        }
    }


    private fun showPayResultDialog(status: Int, orderId: String) {
        if (!activity.isFinishing) {
            val payResultDialog = PayResultDialog(activity)
            payResultDialog.show()
            payResultDialog.initResultUI(status)
            payResultDialog.onViewClickListener = object : OnViewClickListener {
                override fun <T> onClickAction(v: View, str: String, t: T?) {
                    t as Int
                    when (t) {
                        OrderStatusDefine.ORDER_STATUS_SUCCEED, OrderStatusDefine.ORDER_STATUS_FINISH -> { // 成功
                            val intent = Intent(activity, SyncDataService::class.java)
                            activity.startService(intent)

                        }
                    }
                    EventBus.getDefault().post(OnRefreshAfterPay(status, orderId))
                    onOrderPayCallback.onOrderPayResult(t)
                }
            }
        }
    }

    fun handlePayPalReturn(intent: Intent) {
        payPalPaymentHelper.handlePayPalReturn(intent)
    }
}


/***
 * @date 创建时间 2022/11/30 13:59
 * <AUTHOR> W.YuLong
 * @description
 */
interface OnOrderPayCallback {
    fun onOrderPayResult(orderStatus: Int)

    fun retryAgain() {}
}
