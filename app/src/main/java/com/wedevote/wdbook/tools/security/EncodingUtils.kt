package com.wdbible.app.wedevotebible.tools.security

import com.soywiz.krypto.encoding.Base64
import java.io.ByteArrayOutputStream
import java.security.KeyFactory
import java.security.MessageDigest
import java.security.spec.X509EncodedKeySpec
import javax.crypto.Cipher
import javax.crypto.spec.SecretKeySpec

/***
 * @date 创建时间 2019-10-18 16:04
 * <AUTHOR> <PERSON><PERSON>
 * @description 编码转换的工具类
 */
object EncodingUtils {
    /**
     * md5加密字符串
     * md5使用后转成16进制变成32个字节
     */
    @JvmStatic
    fun md5(str: String): String {
        val digest = MessageDigest.getInstance("MD5")
        val result = digest.digest(str.toByteArray())
        // 没转16进制之前是16位
        // 转成16进制后是32字节
        return toHex(result)
    }

    @JvmStatic
    fun toHex(byteArray: ByteArray): String {
        val result = with(StringBuilder()) {
            byteArray.forEach {
                val hex = it.toInt() and (0xFF)
                val hexStr = Integer.toHexString(hex)
                if (hexStr.length == 1) {
                    this.append("0").append(hexStr)
                } else {
                    this.append(hexStr)
                }
            }
            this.toString()
        }
        // 转成16进制后是32字节
        return result
    }

    @JvmStatic
    fun sha1(str: String): String {
        val digest = MessageDigest.getInstance("SHA-1")
        val result = digest.digest(str.toByteArray())
        return toHex(result)
    }

    @JvmStatic
    fun sha256(str: String): String {
        val digest = MessageDigest.getInstance("SHA-256")
        val result = digest.digest(str.toByteArray())
        return toHex(result)
    }

    @JvmStatic
    fun sha512(str: String): String {
        val digest = MessageDigest.getInstance("SHA-512")
        val result = digest.digest(str.toByteArray())
        return toHex(result)
    }

    /**
     * RSA 公钥加密
     * @param input 原文
     * @param publicKey 公钥
     */
    fun rsaEncryptByPublicKey(input: String, publicKey: String): String {
        val maxEncryptBlock = 64
        val keyFactory = KeyFactory.getInstance("RSA")
        val publicKey = keyFactory.generatePublic(X509EncodedKeySpec(Base64.decode(publicKey)))
        val cipher = Cipher.getInstance("RSA/ECB/PKCS1Padding")
        cipher.init(Cipher.ENCRYPT_MODE, publicKey)
        val data = input.toByteArray()
        val inputLen: Int = data.size
        val out = ByteArrayOutputStream()
        var offSet = 0
        var cache: ByteArray
        var i = 0

        // 对数据分段加密
        while (inputLen - offSet > 0) {
            cache = if (inputLen - offSet > maxEncryptBlock) {
                cipher.doFinal(data, offSet, maxEncryptBlock)
            } else {
                cipher.doFinal(data, offSet, inputLen - offSet)
            }
            out.write(cache, 0, cache.size)
            i++
            offSet = i * maxEncryptBlock
        }

        val encryptedData: ByteArray = out.toByteArray()

        out.close()

        return Base64.encode(encryptedData)
    }

    fun encryptData(data: ByteArray, secret: String): String {
        val cipher = Cipher.getInstance("AES/ECB/NoPadding")
        val secretKey = SecretKeySpec(secret.substring(0, 16).toByteArray(), "AES")
        cipher.init(Cipher.ENCRYPT_MODE, secretKey)
        val finalBytes = cipher.doFinal(padData(data, 16))
        return Base64.encode(finalBytes)
    }

    fun padData(data: ByteArray, blockSize: Int): ByteArray {
        val paddedLength = ((data.size) / blockSize + 1) * blockSize
        val paddedData = ByteArray(paddedLength)
        System.arraycopy(data, 0, paddedData, 0, data.size)
        return paddedData
    }

    fun getMD5(data: ByteArray): String {
        val md5Digest = MessageDigest.getInstance("MD5")
        val md5Bytes = md5Digest.digest(data)
        val bytesToHexString = bytesToHexString(md5Bytes)
        return if (bytesToHexString.isNotEmpty()) {
            bytesToHexString.toLowerCase()
        } else {
            ""
        }
    }

    fun bytesToHexString(bytes: ByteArray): String {
        val hexChars = CharArray(bytes.size * 2)
        for (i in bytes.indices) {
            val v = bytes[i].toInt() and 0xFF
            hexChars[i * 2] = "0123456789abcdef"[v ushr 4]
            hexChars[i * 2 + 1] = "0123456789abcdef"[v and 0x0F]
        }
        return String(hexChars)
    }

}
