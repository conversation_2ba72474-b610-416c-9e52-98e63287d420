package com.wedevote.wdbook.tools.payment.stripe

import android.app.Activity
import android.content.Intent
import android.view.WindowManager
import androidx.activity.ComponentActivity
import com.aquila.lib.dialog.CommAlertDialog
import com.aquila.lib.dialog.CommProgressDialog
import com.aquila.lib.log.KLog
import com.stripe.android.*
import com.stripe.android.model.ConfirmPaymentIntentParams
import com.stripe.android.model.PaymentMethod
import com.stripe.android.model.StripeIntent.Status
import com.stripe.android.view.PaymentMethodsActivityStarter
import com.wedevote.wdbook.R
import com.wedevote.wdbook.base.APP
import com.wedevote.wdbook.base.ExceptionHandler
import com.wedevote.wdbook.base.SDKSingleton
import com.wedevote.wdbook.exception.ApiException
import com.wedevote.wdbook.exception.ErrorInfo
import com.wedevote.wdbook.tools.payment.OrderPaymentResultEnum
import com.wedevote.wdbook.tools.util.GsonUtil
import com.wedevote.wdbook.tools.util.PayTypeDefine
import com.wedevote.wdbook.tools.util.findString
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch

/***
 * @date 创建时间 2020/9/18 17:15
 * <AUTHOR> W.YuLong
 * @description
 */
class StripePaymentUtil(private val activity: ComponentActivity, var callback: OnPaymentCallback) {
    private val stripe: Stripe by lazy {
        Stripe(APP.get(), PaymentConfiguration.getInstance(APP.get()).publishableKey)
    }

    private var paymentSession: PaymentSession
    private var orderId: String = ""
    private var amount: Float = 0f
    private var loadingDialog: CommProgressDialog

    init {
        CustomerSession.initCustomerSession(activity, EphemeralKeyProviderImpl(activity))
        paymentSession = PaymentSession(activity, createPaymentSessionConfig())
        setupPaymentSession()

        loadingDialog = CommProgressDialog.with(activity).apply {
            setTouchOutsideCancel(false)
            setTitle(findString(R.string.getting_order_payment_results))
        }.create()
    }

    private fun setupPaymentSession() {
        paymentSession.init(object : PaymentSession.PaymentSessionListener {
            override fun onCommunicatingStateChanged(isCommunicating: Boolean) {
            }

            override fun onError(errorCode: Int, errorMessage: String) {
                if (SDKSingleton.sessionBl.isLogin()) {
//                    CommAlertDialog.with(activity).setMessage(errorMessage).setMiddleText(
//                        findString(R.string.got_it)
//                    ).create().show()
                }
            }

            override fun onPaymentSessionDataChanged(data: PaymentSessionData) {
                KLog.d("onPaymentSessionDataChanged = ${GsonUtil.objectToJson(data)}")
            }
        })
    }

    fun readyToPay(payType: String, orderId: String, amount: Float) {
        this.orderId = orderId
        this.amount = amount
        when (payType) {
            PayTypeDefine.PAY_TYPE_CARD.value -> {
                paymentSession.presentPaymentMethodSelection()
            }
        }
    }

    fun dealOnActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        KLog.d("requestCode = $requestCode ,resultCode = $resultCode")
        if (data != null) {
            if (resultCode == Activity.RESULT_OK) {
                when (requestCode) {
                    PaymentMethodsActivityStarter.REQUEST_CODE -> {
                        val result = PaymentMethodsActivityStarter.Result.fromIntent(data)
                        KLog.d(GsonUtil.objectToJson(result))
                        callback.executePayAction()
                        executePaymentAction(result!!.paymentMethod!!.id!!)
                    }
                    50000 -> { // resultCode = 50000 这个是确认支付后的结果，通过调试看源码得到的数值
                        stripe.onPaymentResult(
                            requestCode, data,
                            object : ApiResultCallback<PaymentIntentResult> {
                                override fun onSuccess(result: PaymentIntentResult) {
                                    val paymentIntent = result.intent
                                    KLog.d(GsonUtil.objectToJson(paymentIntent))
                                    when (paymentIntent.status) {
                                        Status.Succeeded -> {
                                            callback.onPayResult(orderId, OrderPaymentResultEnum.SUCCESS)
                                        }
                                        Status.RequiresAction, Status.Canceled -> {
                                            callback.onPayResult(orderId, OrderPaymentResultEnum.CANCEL)
                                        }
                                        Status.Processing -> {
                                            callback.onPayResult(orderId, OrderPaymentResultEnum.FAILURE)
                                        }
                                        else -> {
                                            callback.onPayResult(orderId, OrderPaymentResultEnum.EXCEPTION)
                                        }
                                    }

                                    if (loadingDialog.isShowing) {
                                        loadingDialog.dismiss()
                                    }
                                }

                                override fun onError(e: Exception) {
                                    KLog.e(e.message)
                                    callback.onPayResult(orderId, OrderPaymentResultEnum.EXCEPTION)

                                    if (loadingDialog.isShowing) {
                                        loadingDialog.dismiss()
                                    }
                                }
                            }
                        )
                    }
                }
            }
            paymentSession.handlePaymentData(requestCode, resultCode, data)
        }
    }

    /*请求后台执行支付操作*/
    private fun executePaymentAction(payMethodId: String) {
        if (!loadingDialog.isShowing) {
            loadingDialog.show()
        }
        MainScope().launch {
            try {
                SDKSingleton.paymentBl.onPay(orderId, amount).also { result ->
                    if (!result.isNullOrEmpty()) {
                        stripe.confirmPayment(activity, ConfirmPaymentIntentParams.createWithPaymentMethodId(payMethodId, result, ""))
                    }
                }
            } catch (e: ApiException) {
                val alertMessage = if (e.code == ErrorInfo.PAYMENT_AMOUNT_CHANGED.code) {
                    findString(R.string.amount_changed)
                } else {
                    activity.getString(R.string.no_network_connect)
                }
                CommAlertDialog.with(activity).setMessage(alertMessage).setStartText(
                    findString(R.string.got_it)
                ).setOnViewClickListener { dialog, view, i ->
                }.create().show()
            } catch (e: java.io.IOException) {
                CommAlertDialog.with(activity)
                    .setMessage(findString(R.string.pay_error))
                    .setStartText(findString(R.string.got_it))
                    .setOnViewClickListener { dialog, view, i ->
                        callback.onPayResult(orderId, OrderPaymentResultEnum.FAILURE)
                    }.create().show()
            } catch (t: Throwable) {
                ExceptionHandler.handleException(t)
            }
        }
    }
}

private fun createPaymentSessionConfig(): PaymentSessionConfig {
    return PaymentSessionConfig.Builder().setShippingMethodsRequired(false).setWindowFlags(
        WindowManager.LayoutParams.FLAG_SECURE
    ).setShippingInfoRequired(false).setPaymentMethodTypes(
        listOf(PaymentMethod.Type.Card)
    ).setShouldShowGooglePay(false).setCanDeletePaymentMethods(true)
        .build()
}

interface OnPaymentCallback {
    // 支付结果的回调 0-取消支付， 1-支付成功, 2-支付异常, 3-正在支付
    fun onPayResult(orderId: String, payResult: OrderPaymentResultEnum)
    fun executePayAction()
}
