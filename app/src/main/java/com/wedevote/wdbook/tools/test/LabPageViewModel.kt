package com.wedevote.wdbook.tools.test

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.wedevote.wdbook.base.SDKSingleton

/**
 * @date 2024/6/12
 * <AUTHOR>
 * @description Lab page 的页面（供 QA 测试用）的 ViewModel
 * Copyright © 2024 WD Bible Team. All rights reserved.
 */
class LabPageViewModel : ViewModel() {
    // 是否使用测试公网地址的参数
    private val _useTestCftParamConfig = MutableLiveData(false)
    val useTestCftParamConfig: LiveData<Boolean> = _useTestCftParamConfig

    // 是否打开 CFT 日志（对 Prod）
    private val _showCftLog = MutableLiveData(false)
    val showCftLog: LiveData<Boolean> = _showCftLog

    fun syncData() {
        _useTestCftParamConfig.value = SDKSingleton.sessionBl.useTestCftParamConfig
        _showCftLog.value = SDKSingleton.sessionBl.showCftLog
    }

    fun setUseTestCftParamConfig(useTest: Boolean) {
        _useTestCftParamConfig.value = useTest
        SDKSingleton.sessionBl.useTestCftParamConfig = useTest
    }

    fun setShowCftLog(value: Boolean) {
        _showCftLog.value = value
        SDKSingleton.sessionBl.showCftLog = value
    }

    companion object {
        const val TAG = "LabPageViewModel"
    }
}