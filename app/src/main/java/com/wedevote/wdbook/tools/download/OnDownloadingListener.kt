package com.wedevote.wdbook.tools.download

import com.wedevote.wdbook.entity.store.BookFileDownloadEntity

/***
 * @date 创建时间 2020/4/16 13:38
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON>
 * @description
 */
interface OnDownloadingListener {

    /*在任务队列等待中,
    * isAdded 是否添加到队列，false表示移出等待队列
    * */
    fun onWait(entity: BookFileDownloadEntity, isAdded: Boolean) {}

    /*
    * 开始下载
    * */
    fun onBeginning(entity: BookFileDownloadEntity) {}

    /*
    * 正在下载中
    * */
    fun onDownloadingProgress(entity: BookFileDownloadEntity, downloadSize: Long, totalSize: Long) {}

    /*
    * 下载暂停
    * */
    fun onPause(entity: BookFileDownloadEntity) {}

    /*
    *
    * 下载错误
    * */
    fun onError(entity: BookFileDownloadEntity, errorDesc: String) {}

    /*
    * 下载完成
    * */
    fun onComplete(entity: BookFileDownloadEntity) {}

    fun onCancel(entity: BookFileDownloadEntity) {}
}
