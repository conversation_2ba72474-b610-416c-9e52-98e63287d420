package com.wedevote.wdbook.tools.upgrade

import com.liulishuo.filedownloader.BaseDownloadTask
import com.liulishuo.filedownloader.FileDownloadListener

/***
 * @date 创建时间 2018/5/23 10:09
 * <AUTHOR> <PERSON><PERSON><PERSON>
 * @description
 */
open class FileDownloadListenerImpl : FileDownloadListener() {
    override fun pending(task: BaseDownloadTask, soFarBytes: Int, totalBytes: Int) {}
    override fun progress(task: BaseDownloadTask, soFarBytes: Int, totalBytes: Int) {}
    override fun completed(task: BaseDownloadTask) {}
    override fun paused(task: BaseDownloadTask, soFarBytes: Int, totalBytes: Int) {}
    override fun error(task: BaseDownloadTask, e: Throwable) {}
    override fun warn(task: BaseDownloadTask) {}
}
