package com.wedevote.wdbook.tools.util

/***
 * Created by <PERSON> on 2023/3/20 15:18
 *
 * @description
 */
import android.util.Log
import com.wedevote.wdbook.base.ExceptionHandler
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import okhttp3.OkHttpClient
import okhttp3.Request
import org.json.JSONObject
import java.io.IOException

suspend fun isDomainSpoofed(domain: String): Boolean = withContext(Dispatchers.IO) {
    val dnsServer = "*******"
    val url = "https://$dnsServer/resolve?name=$domain"
    val client = OkHttpClient()
    val request = Request.Builder()
        .url(url)
        .build()
    val response = try {
        client.newCall(request).execute()
    } catch (e: IOException) {
        // 请求异常，可能是网络连接问题
        e.printStackTrace()
        return@withContext true
    }
    if (response.isSuccessful) {
        val jsonResponse = JSONObject(response.body?.string())
        val answerArray = jsonResponse.optJSONArray("Answer")
        answerArray == null
    } else {
        // 请求失败，可能是域名不存在或者服务端异常
        response.close()
        return@withContext true
    }
}

object DomainCheckUtils {
    const val DNS_SERVER = "*******"
    const val DNS_RESOLVE_URL = "https://$DNS_SERVER/resolve?name="

    fun checkDomainSpoofing(domain: String) {
        CoroutineScope(Dispatchers.IO).launch(ExceptionHandler.coroutineExceptionHandler) {
            val isSpoofed = isDomainSpoofed(domain)
            if (isSpoofed) {
                // 域名被污染
                Log.e("DomainCheckUtils", "$domain is spoofed")
            } else {
                // 域名正常
                Log.i("DomainCheckUtils", "$domain is not spoofed")
            }
        }
    }
}
