package com.wedevote.wdbook.tools.util;

import java.lang.ref.WeakReference;

import android.os.Handler;

/**
 * Created by OneTree on 2016/12/19.
 */

public class WeakHandler<T> extends Handler {

    private WeakReference<T> weakReference ;

    private T instance;

    protected T getInstance(){
        if (instance == null){
            instance = weakReference.get() ;
        }
        return instance ;
    }

    public WeakHandler(T t){
        weakReference = new WeakReference<>(t) ;
    }

}
