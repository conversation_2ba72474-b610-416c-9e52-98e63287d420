package com.wedevote.wdbook.tools.util

import android.app.Activity
import android.app.Application
import android.graphics.Color
import android.os.Build
import android.os.Bundle
import com.aquila.lib.log.KLog
import com.wedevote.wdbook.R
import com.wedevote.wdbook.base.APPConfig

/**
 * 第三方页面 全面屏适配器
 * 专门用于处理 第三方页面 Activity 的 Android 15 全面屏适配
 */
class ThirdPartyActivityAdapter(
    private val debugMode: Boolean = false
) : Application.ActivityLifecycleCallbacks {
    
        companion object {
        private const val TAG = "StripeActivityAdapter"
        
        // 需要适配的 第三方 Activity 类名列表
        private val THIRD_PARTY_ACTIVITIES = setOf(
            "com.stripe.android.view.PaymentMethodsActivity",
            "com.stripe.android.view.PaymentFlowActivity", 
            "com.stripe.android.view.StripeActivity",
            "com.stripe.android.PaymentAuthWebViewActivity",
            "com.stripe.android.view.AddPaymentMethodActivity",
            "com.stripe.android.payments.StripeBrowserLauncherActivity",
            "com.stripe.android.payments.paymentlauncher.PaymentLauncherConfirmationActivity",
            "com.stripe.android.googlepaylauncher.GooglePayLauncherActivity"
        )
    }
    
    override fun onActivityCreated(activity: Activity, savedInstanceState: Bundle?) {
        if (Build.VERSION.SDK_INT <= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
            return
        }
        
        val activityClassName = activity.javaClass.name
        val packageName = activity.packageName
        
        // 检查是否为需要适配的 第三方 Activity
        if (shouldAdaptActivity(activityClassName, packageName)) {
            KLog.d(TAG, "Adapting Stripe activity: $activityClassName")
            applyEdgeToEdgeForThirdPartyActivity(activity)
        }
    }
    
    /**
     * 判断是否需要适配该 Activity
     */
    private fun shouldAdaptActivity(className: String, packageName: String): Boolean {
        if (THIRD_PARTY_ACTIVITIES.contains(className)) {
            return true
        }
        
        return className.startsWith("com.stripe")
    }
    
    private fun applyEdgeToEdgeForThirdPartyActivity(activity: Activity) {
        try {
            // 设置全面屏显示
            WindowInsetsUtils.setEdgeToEdgeDisplay(activity)
            
            val isLightTheme = APPConfig.isCurrentThemeLight()
            val statusBarColor = activity.getColor(R.color.stripe_toolbar_color_default)

            WindowInsetsUtils.setStatusBarColor(
                activity,
                statusBarColor,
                needTopPadding = true,
                needBottomPadding = true,
                transparentStatusBar = false,
                transparentNavigationBar = true
            )
            
            WindowInsetsUtils.setSystemBarsColor(
                activity,
                statusBarColor = Color.TRANSPARENT,
                navigationBarColor = Color.TRANSPARENT,
                isLightStatusBar = isLightTheme,
                isLightNavigationBar = isLightTheme
            )
            
            if (debugMode) {
                KLog.d(TAG, """
                    |Applied edge-to-edge for: ${activity.javaClass.simpleName}
                    |Theme: ${if (isLightTheme) "Light" else "Dark"}
                    |StatusBar Color: ${Integer.toHexString(statusBarColor)}
                """.trimMargin())
            } else {
                KLog.d(TAG, "Successfully applied edge-to-edge for: ${activity.javaClass.simpleName}")
            }
            
        } catch (e: Exception) {
            KLog.e(TAG, "Failed to apply edge-to-edge for Stripe activity: ${activity.javaClass.simpleName}", e.message)
        }
    }
    
    override fun onActivityStarted(activity: Activity) {
    }
    
    override fun onActivityResumed(activity: Activity) {
    }
    
    override fun onActivityPaused(activity: Activity) {
    }
    
    override fun onActivityStopped(activity: Activity) {
    }
    
    override fun onActivitySaveInstanceState(activity: Activity, outState: Bundle) {
    }
    
    override fun onActivityDestroyed(activity: Activity) {
    }
} 