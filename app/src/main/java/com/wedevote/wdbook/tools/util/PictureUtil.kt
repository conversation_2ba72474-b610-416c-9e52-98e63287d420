package com.wedevote.wdbook.tools.util

import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Matrix
import android.graphics.Paint
import android.graphics.RectF
import android.widget.ImageView
import com.bumptech.glide.load.model.GlideUrl
import com.wedevote.wdbook.R
import com.wedevote.wdbook.base.APP
import com.wedevote.wdbook.base.SDKSingleton
import p67.fox.Client

/***
 * @date 创建时间 2020/6/4 16:17
 * <AUTHOR> <PERSON>.<PERSON>ong
 * @description
 */
object PictureUtil {
    val paint = Paint(Paint.ANTI_ALIAS_FLAG)
    fun appendUrlToStr(urlList: List<String>): String {
        var sb = StringBuilder()
        for (i in urlList.indices) {
            if (!urlList[i].isNullOrEmpty()) {
                sb.append(urlList[i])
            } else { // 有的书籍没有图片，是空字符，这时候给个索引，来确定合成图片的显示位置
                sb.append(i.toString())
            }
        }
        return sb.toString()
    }

    fun formatCellUrlBitmap(urlList: List<GlideUrl?>?, builder: Builder): Bitmap? {
        var resultBitmap: Bitmap? = null
        urlList?.let {
            val bitmapList = ArrayList<Bitmap>()
            var i = 0
            for (url in it) {
                var bitmap = ImageLoadUtil.getBitmapUrlAsync(url, Client.proxy())
                if (bitmap == null) {
                    bitmap = BitmapFactory.decodeResource(APP.get().resources, R.drawable.ic_default_book_cover)
                }
                bitmapList.add(bitmap!!)
                i++
                if (i >= 4) {
                    break
                }
            }
            resultBitmap = formatBookShelfBitmap(bitmapList, builder)
        }
        return resultBitmap
    }

    /**/
    fun formatBookShelfBitmap(bitmapList: List<Bitmap>, builder: Builder): Bitmap? {
        if (bitmapList == null || bitmapList.size == 0) {
            return null
        }
        // 最多显示4张
        var length = Math.min(bitmapList.size, 4)
        val outWidth: Int = builder.outWidth
        val outHeight: Int = builder.outHeight
        // 图片画板的内间距
        val paddingSize: Int = builder.paddingSize
        // 每张图片之间的间距
        val itemMargin: Int = builder.itemMargin
        // 每张需要绘制图片的宽高
        val cellWidth: Int = (outWidth - paddingSize * 2 - itemMargin) / 2
        var cellHeight: Int = (outHeight - paddingSize * 2 - itemMargin) / 2
        // 画布
        val outBitmap = Bitmap.createBitmap(outWidth, outHeight, Bitmap.Config.ARGB_8888)
        val canvas = Canvas(outBitmap)
        // 先画合成之后的背景颜色，默认是白色
        if (SDKSingleton.appBl.isCurrentThemeLight()) {
            canvas.drawColor(builder.backgroundColor)
        } else {
            canvas.drawColor(builder.backgroundColorDark)
        }

        // 这个主要是用来计算绘制图片的起始位置
        var left = paddingSize
        var top = paddingSize
        val moveWidth = cellWidth + itemMargin
        val moveHeight = cellHeight + itemMargin
        val paint = Paint(Paint.ANTI_ALIAS_FLAG or Paint.DITHER_FLAG)

        for (i in 0 until length) {
            val dealBitmap: Bitmap = scaleAndFitCenterBitmap(
                bitmapList[i],
                cellWidth,
                cellHeight,
                builder.roundSize // 传递圆角半径
            )
            left = paddingSize + moveWidth * (i % 2)
            top = paddingSize + moveHeight * (i / 2)
            canvas.drawBitmap(dealBitmap, left.toFloat(), top.toFloat(), paint)
            // 回收dealBitmap，因为它只在循环内使用
            if (!dealBitmap.isRecycled) {
                dealBitmap.recycle()
            }
        }
        return outBitmap
    }

    // 将图片缩放换成指定宽高，
    private fun scaleAndFitCenterBitmap(sourceBitmap: Bitmap, width: Int, height: Int, roundSize: Float): Bitmap {
        // 先缩放图片并保持比例
        val widthScale = width / sourceBitmap.width.toFloat()
        val heightScale = height / sourceBitmap.height.toFloat()
        val scale = Math.min(widthScale, heightScale)
        val matrix = Matrix().apply { setScale(scale, scale) }
        val resizeBitmap = Bitmap.createBitmap(sourceBitmap, 0, 0, sourceBitmap.width, sourceBitmap.height, matrix, true)
        // 计算绘制偏移，使图片居中
        val left = (width - resizeBitmap.width) / 2f
        val top = (height - resizeBitmap.height) / 2f
        // 创建目标bitmap
        val outBitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
        val canvas = Canvas(outBitmap)
        // 设置圆角剪裁路径
        val path = android.graphics.Path().apply {
            addRoundRect(
                RectF(left, top, left + resizeBitmap.width, top + resizeBitmap.height), // 关键：路径基于scaledBitmap的绘制位置和尺寸
                roundSize,
                roundSize,
                android.graphics.Path.Direction.CW
            )
        }
        canvas.save()
        canvas.clipPath(path)
        // 绘制缩放后的图片
        canvas.drawBitmap(resizeBitmap, left, top, null)
        canvas.restore()
        // 回收中间bitmap
        if (!resizeBitmap.isRecycled) {
            resizeBitmap.recycle()
        }
        return outBitmap
    }

    data class Builder(
        var outWidth: Int = 300,
        var outHeight: Int = 300,
        var paddingSize: Int = 10,
        var itemMargin: Int = 15,
        var backgroundColor: Int = APP.get().resources.getColor(R.color.colorPrimary),
        var backgroundColorDark: Int = APP.get().resources.getColor(R.color.color_25272C),
        var roundSize: Float = 0f // 移动到构造函数，保持一致性
    )
    fun loadImage(coverImageView: ImageView, url: String?) {
        loadImage(coverImageView, url, R.drawable.ic_default_book_cover)
    }
    fun loadImage(coverImageView: ImageView, url: String?, defaultId: Int) {
        ImageLoadUtil.loadImageWithDefault(coverImageView, getPictureRemotePath(url), defaultId, Client.proxy())
    }
    fun loadImageWithRemotePath(coverImageView: ImageView, path: String, defaultId: Int) {
        ImageLoadUtil.loadImageWithDefault(coverImageView, path, defaultId, Client.proxy())
    }
    fun loadCircleImageWithDefault(imageView: ImageView, url: String?, defaultId: Int) {
        ImageLoadUtil.loadCircleImageWithDefault(imageView, getPictureRemotePath(url), defaultId, Client.proxy())
    }

    fun loadImageWithError(coverImageView: ImageView, url: String?, defaultId: Int) {
        ImageLoadUtil.loadImageWithError(coverImageView, getPictureRemotePath(url), defaultId, Client.proxy())
    }
    fun loadCircleImage(imageView: ImageView, defaultId: Int) {
        ImageLoadUtil.loadCircleImage(imageView,defaultId, Client.proxy())
    }
    fun <T> loadRoundImage(imageView: ImageView, t: T?, roundSize: Int) {
        return ImageLoadUtil.loadRoundImage(imageView, t, roundSize,Client.proxy())
    }
    fun <T> loadRoundImageWithDefault(imageView: ImageView, t: T?, roundSize: Int) {
        return ImageLoadUtil.loadRoundImage(imageView, t, roundSize, R.drawable.ic_default_book_cover ,Client.proxy())
    }
}
