package com.wedevote.wdbook.tools.util

import android.annotation.SuppressLint
import android.content.Context
import android.net.ConnectivityManager
import android.net.NetworkInfo
import android.text.TextUtils
import com.aquila.lib.dialog.CommAlertDialog
import com.wedevote.wdbook.R
import com.wedevote.wdbook.base.APP
import java.io.BufferedReader
import java.io.IOException
import java.io.InputStreamReader
import java.util.*

/***
 * @date 创建时间 2018/5/8 09:52
 * <AUTHOR> YuLong
 * @description 网络相关的工具类
 */
object NetWorkUtils {
    private const val TAG = "NetworkUtils"

    /**
     * 手机网络类型
     */
    const val NET_TYPE_WIFI = 0x01
    const val NET_TYPE_WAP = 0x02
    const val NET_TYPE_NET = 0x03

    @JvmStatic
    fun isNetworkAvailable(): Boolean {
        val cm = APP.get().getSystemService(Context.CONNECTIVITY_SERVICE) as? ConnectivityManager
        if (cm != null) {
            @SuppressLint("MissingPermission")
            val info = cm.allNetworkInfo
            if (info != null) {
                for (i in info.indices) {
                    if (info[i].state == NetworkInfo.State.CONNECTED) {
                        return true
                    }
                }
            }
        }
        return false
    }

    /**
     * 获取当前网络类型
     *
     * @return 0：没有网络 1：WIFI网络 2：WAP网络 3：NET网络
     */
    fun getNetworkType(): Int {
        var netType = 0
        val connectivityManager =
            APP.get().getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        val networkInfo = connectivityManager.activeNetworkInfo ?: return netType
        val nType = networkInfo.type
        if (nType == ConnectivityManager.TYPE_MOBILE) {
            val extraInfo = networkInfo.extraInfo
            if (!TextUtils.isEmpty(extraInfo)) {
                netType = if ("cmnet" == extraInfo.lowercase(Locale.getDefault())) {
                    NET_TYPE_NET
                } else {
                    NET_TYPE_WAP
                }
            }
        } else if (nType == ConnectivityManager.TYPE_WIFI) {
            netType = NET_TYPE_WIFI
        }
        return netType
    }

    fun showTipDialog(context: Context, tip: String) {
        CommAlertDialog.with(context).setTitle("提示")
            .setMessage(tip).setCancelAble(false)
            .setMiddleText(R.string.label_OK).setAllButtonColor(0xff528FFF.toInt())
            .setOnViewClickListener { _, _, _ ->
            }.create().show()
    }

    fun getServerLossRate(ip: String, logString: StringBuilder = StringBuilder()): Int {
        var serverIp = ip
        val headIndex = serverIp.indexOf("://")
        if (headIndex >= 0) {
            serverIp = serverIp.substring(headIndex + 3)
        }
        val endIndex = serverIp.indexOf("/")
        if (endIndex >= 0) {
            serverIp = serverIp.substring(0, endIndex)
        }

        logString.append("\n\nCheck server: $serverIp\n")

        // 每隔 0.5 秒发一次 ICMP 请求；超时为 3 秒
        val cmd = "ping -c 5 -i 0.5 -W 3 $serverIp"

        var lossRate = 100
        try {
            val process = Runtime.getRuntime().exec(cmd)
            val bufferInput = BufferedReader(InputStreamReader(process.inputStream))
            var line = bufferInput.readLine()
            while (line != null) {
                logString.append(line)
                val rateIndex = line.indexOf("% packet loss")
                if (rateIndex > 0) {
                    lossRate = getRateFromLine(line, rateIndex - 1)
                    break
                }
                line = bufferInput.readLine()
            }
            logString.append("\nloss rate = $lossRate%")
            bufferInput.close()
        } catch (e: IOException) {
            e.printStackTrace()
        }
        return lossRate
    }

    private fun getRateFromLine(line: String, index: Int): Int {
        var startIndex = index
        while (startIndex >= 0 && line[startIndex].isDigit()) {
            startIndex--
        }
        startIndex++
        val rateString = line.substring(startIndex, index + 1)
        return rateString.toIntOrNull() ?: 0
    }
}
