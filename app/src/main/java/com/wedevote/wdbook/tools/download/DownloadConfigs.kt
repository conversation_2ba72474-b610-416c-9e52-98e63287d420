package com.wedevote.wdbook.tools.download

import com.wedevote.wdbook.base.SDKSingleton
import okhttp3.Interceptor
import okhttp3.OkHttpClient
import okhttp3.Request
import p67.fox.Client
import java.util.concurrent.TimeUnit

/***
 * @date 创建时间 2020/10/1 10:05
 * <AUTHOR> <PERSON><PERSON>
 * @description
 */
object DownloadConfigs {

    var MAX_TASK_COUNT = 50

    var okHttpClient: OkHttpClient? = null
    val TIME_OUT = 60L

    fun getHttpClient(): OkHttpClient {
        if (okHttpClient == null) {
            synchronized(DownloadService::class.java) {
                if (okHttpClient == null) {
                    val okHttpClientBuilder = OkHttpClient.Builder()
                        .addInterceptor(
                            Interceptor { chain ->
                                val request: Request = chain.request()
                                    .newBuilder()
                                    .header("User-Agent", SDKSingleton.sessionBl.userAgent)
                                    .build()
                                chain.proceed(request)
                            },
                        )
                        .connectTimeout(TIME_OUT, TimeUnit.SECONDS)
                        .readTimeout(TIME_OUT, TimeUnit.SECONDS)
                        .writeTimeout(TIME_OUT, TimeUnit.SECONDS)
                    Client.proxy()?.let { okHttpClientBuilder.proxy(it) }
                    okHttpClient = okHttpClientBuilder.build()
                }
            }
        }
        return okHttpClient!!
    }
}
