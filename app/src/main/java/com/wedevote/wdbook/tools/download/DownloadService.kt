package com.wedevote.wdbook.tools.download

import android.app.Service
import android.content.Intent
import android.os.Binder
import android.os.Handler
import android.os.IBinder
import android.os.Looper
import com.aquila.lib.log.KLog
import com.aquila.lib.tools.singleton.ThreadPoolSingleton
import com.aquila.lib.tools.util.ToastUtil
import com.wedevote.wdbook.R
import com.wedevote.wdbook.base.SDKSingleton
import com.wedevote.wdbook.constants.AnalyticsConstants
import com.wedevote.wdbook.constants.AnalyticsConstants.LOG_V1_PARAM_FILE_ID
import com.wedevote.wdbook.constants.DownloadStatus
import com.wedevote.wdbook.entity.DownloadDataEntity
import com.wedevote.wdbook.entity.store.BookFileDownloadEntity
import com.wedevote.wdbook.tools.util.AnalyticsUtils.logEvent
import com.wedevote.wdbook.tools.util.NetWorkUtils
import java.io.File
import java.util.*

/***
 * @date 创建时间 2020/8/28 18:32
 * <AUTHOR> W<PERSON><PERSON>
 * @description
 */
class DownloadService : Service() {

    var binder = DownloadBinder()
    override fun onBind(intent: Intent?): IBinder? {
        return binder
    }

    private val handler = Handler(Looper.getMainLooper())
    val downloadTaskMap = HashMap<String, DownloadTask>()
    val downloadListenerList = ArrayList<OnDownloadingListener>()
    val pendingTaskList = LinkedList<DownloadTask>()
    override fun onCreate() {
        super.onCreate()
        KLog.d("创建服务")
    }

    /***
     *@date 创建时间 2020/4/16 16:02
     *<AUTHOR> W.YuLong
     *@description
     */
    inner class DownloadBinder : Binder(), OnDownloadingListener {

        fun addDownloadListener(downloadingListener: OnDownloadingListener) {
            downloadListenerList.add(downloadingListener)
        }

        fun removeDownloadListener(downloadingListener: OnDownloadingListener) {
            downloadListenerList.remove(downloadingListener)
        }

        fun isInDownloadQueue(fileId: String?): Boolean {
            return downloadTaskMap.contains(fileId)
        }

        fun startOrPauseDownloadTask(fileDownloadEntity: BookFileDownloadEntity, downloadDataEntity: DownloadDataEntity) {
            /*将下载任务插入到数据库中*/
            SDKSingleton.dbWrapBl.saveDownloadData(downloadDataEntity)

            if (downloadTaskMap.size >= DownloadConfigs.MAX_TASK_COUNT) {
                addPendingTask(fileDownloadEntity, downloadDataEntity)
            } else {
                var downloadTask = downloadTaskMap.get(fileDownloadEntity.fileId)
                if (downloadTask == null) {
                    downloadTask = DownloadTask(fileDownloadEntity, downloadDataEntity, this)
                    downloadTaskMap.put(fileDownloadEntity.fileId, downloadTask)
                    logEvent(AnalyticsConstants.LOG_V1_START_DOWNLOAD, LOG_V1_PARAM_FILE_ID, fileDownloadEntity.fileId)
                    ThreadPoolSingleton.executeTask(downloadTask)
                }
//                else {
//                    downloadTask.isDownloadPaused = true
//                }
            }
        }

        private fun addPendingTask(fileDownloadEntity: BookFileDownloadEntity, downloadDataEntity: DownloadDataEntity) {
            var isContainTask = false
            for (task in pendingTaskList) {
                if (task.getFileId() == fileDownloadEntity.fileId) {
                    isContainTask = true
                    break
                }
            }
            var pendingTask = DownloadTask(fileDownloadEntity, downloadDataEntity, this)
            if (isContainTask) {
                pendingTaskList.remove(pendingTask)
                downloadDataEntity.downloadStatus = DownloadStatus.WAIT
                SDKSingleton.dbWrapBl.deleteDownloadItem(downloadDataEntity)
                onWait(fileDownloadEntity, false)
            } else {
                pendingTaskList.addLast(pendingTask)
                downloadDataEntity.downloadStatus = DownloadStatus.WAIT
                SDKSingleton.dbWrapBl.updateDownloadStatus(downloadDataEntity.fileId!!, downloadDataEntity.downloadStatus)
                onWait(fileDownloadEntity, true)
            }
        }

        fun executeNextPendingTask() {
            if (!pendingTaskList.isNullOrEmpty()) {
                var task = pendingTaskList.poll()
                downloadTaskMap[task.getFileId()] = task
                ThreadPoolSingleton.executeTask(task)
            }
        }
        var toasShowTime = 0L
/***********************************************************************************/
        override fun onWait(entity: BookFileDownloadEntity, isAdded: Boolean) {
            handler.post {
                for (listener in downloadListenerList) {
                    listener.onWait(entity, isAdded)
                }
            }
        }

        override fun onBeginning(entity: BookFileDownloadEntity) {
            handler.post {
                for (listener in downloadListenerList) {
                    listener.onBeginning(entity)
                }
            }
        }

        override fun onDownloadingProgress(entity: BookFileDownloadEntity, downloadSize: Long, totalSize: Long) {
            handler.post {
                for (listener in downloadListenerList) {
                    listener.onDownloadingProgress(entity, downloadSize, totalSize)
                }
            }
        }

        override fun onPause(entity: BookFileDownloadEntity) {
            handler.post {
                for (listener in downloadListenerList) {
                    listener.onPause(entity)
                }
                downloadTaskMap.remove(entity.fileId)
                executeNextPendingTask()
            }
        }

        override fun onError(entity: BookFileDownloadEntity, errorDesc: String) {
            handler.post {
                for (listener in downloadListenerList) {
                    listener.onError(entity, errorDesc)
                }

                if (!NetWorkUtils.isNetworkAvailable() && System.currentTimeMillis() - toasShowTime > 30_000) {
                    ToastUtil.showToastLong(R.string.no_network_connect)
                    toasShowTime = System.currentTimeMillis()
                }

                var task = downloadTaskMap.remove(entity.fileId)
                if (NetWorkUtils.isNetworkAvailable()) {
                    task?.let {
                        val tmpFile = File(it.downloadEntity.getTempDownloadFilePath())
                        if (tmpFile.exists()) {
                            tmpFile.delete()
                            KLog.d("删除下载错误的临时文件")
                        }
                    }
                }
                executeNextPendingTask()
            }
        }

        override fun onComplete(entity: BookFileDownloadEntity) {
            handler.post {
                for (listener in downloadListenerList) {
                    listener.onComplete(entity)
                }
                logEvent(AnalyticsConstants.LOG_V1_FINISH_DOWNLOAD, LOG_V1_PARAM_FILE_ID, entity.fileId)
                downloadTaskMap.remove(entity.fileId)
                executeNextPendingTask()
            }
        }
    }
}
