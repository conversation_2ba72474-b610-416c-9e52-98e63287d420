package com.wedevote.wdbook.tools.util

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Intent
import com.aquila.lib.dialog.R
import com.wedevote.wdbook.base.SDKSingleton
import com.wedevote.wdbook.constants.ThemeStyle
import com.wedevote.wdbook.ui.home.HomeMainActivity
import com.wedevote.wdbook.ui.home.HomeTab

@SuppressLint("StaticFieldLeak")
object ThemeUtils {
    var mainActivity: HomeMainActivity? = null
    private var secondActivity: Activity? = null

    fun setSecondActivity(activity: Activity?) {
        secondActivity = activity
    }

    var themeChanging: Boolean = false
    var themeChangingOutside: Boolean = false

    /**
     * Set the theme of the Activity, and restart it by creating a new Activity
     * of the same type.
     */
    private fun changeToTheme(activity: Activity, outside: Boolean) {
        themeChanging = true
        themeChangingOutside = outside
        if (activity === mainActivity) {
            mainActivity = null
        } else if (activity === secondActivity) {
            secondActivity = null
        }
        activity.finish()
        val intent = activity.intent
        intent.setFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP)
        intent.putExtra(
            IntentConstants.EXTRA_HomeTab,
            if (outside) HomeTab.MINE.tab else HomeTab.EMPTY
        )
        activity.startActivity(intent)
        activity.overridePendingTransition(R.anim.anim_alpha_in, R.anim.anim_alpha_out)
    }

    fun setCurrentTheme(activity: Activity?, theme: ThemeStyle, outside: Boolean) {
        SDKSingleton.appBl.setCurrentThemeStyle(theme)
        if (mainActivity != null) {
            changeToTheme(mainActivity!!, outside)
        }
        if (secondActivity != null) {
            changeToTheme(secondActivity!!, outside)
        }
        if (activity != null) {
            changeToTheme(activity, outside)
        }
    }
}
