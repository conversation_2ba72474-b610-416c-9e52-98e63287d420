package com.wedevote.wdbook.tools.upgrade

import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.os.IBinder
import androidx.fragment.app.FragmentActivity
import com.aquila.lib.download.ADownloadEntity
import com.aquila.lib.download.ADownloadingListener
import com.aquila.lib.download.ALifecycleFragment
import com.aquila.lib.download.AOnDownloadServiceConnectedListener
import com.aquila.lib.log.KLog

/***
 * @date 创建时间 2020/10/23 14:18
 * <AUTHOR> W.YuLong
 * @description
 */
class APKDownloadEngine(val activity: FragmentActivity) : ALifecycleFragment.ALifecycleListener {

    init {
        activity.supportFragmentManager.beginTransaction()
            .add(ALifecycleFragment(this), ALifecycleFragment::class.java.name)
            .commitAllowingStateLoss()
    }

    private var isServiceConnected = false

    lateinit var binder: APKDownloadService.APKDownloadBinder
    private var onServiceConnectedListener: AOnDownloadServiceConnectedListener? = null

    protected var aDownloadingListener: ADownloadingListener? = null
    fun setDownloadingListener(listener: ADownloadingListener?) {
        aDownloadingListener = listener
        if (isServiceConnected) {
            binder.remoteDownloadListener = aDownloadingListener
        }
    }

    val serviceConnection = object : ServiceConnection {
        override fun onServiceConnected(name: ComponentName?, service: IBinder?) {
            isServiceConnected = true
            binder = service as APKDownloadService.APKDownloadBinder
            aDownloadingListener?.let {
                binder.remoteDownloadListener = it
            }
            onServiceConnectedListener?.onServiceConnected()
        }

        override fun onServiceDisconnected(name: ComponentName?) {
            isServiceConnected = false
            KLog.d("解绑下载服务")
        }
    }

    override fun onLifecycleCreate() {
        val intent = Intent(activity, APKDownloadService::class.java)
        activity.bindService(intent, serviceConnection, Context.BIND_AUTO_CREATE)
    }

    override fun onLifecycleDestroy() {
        binder.remoteDownloadListener = null
        activity.unbindService(serviceConnection)
    }

    fun startOrPauseDownload(aDownloadEntity: ADownloadEntity) {
//        val intent = Intent(activity, APKDownloadService::class.java)
//        intent.putExtra("ADownloadEntity", aDownloadEntity)
//        activity.startService(intent)
        binder.startDownloadAPK(aDownloadEntity)
    }
}
