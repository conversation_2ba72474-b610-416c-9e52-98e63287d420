package com.wedevote.wdbook.tools.util

import android.content.Context
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.drawable.BitmapDrawable
import android.graphics.drawable.Drawable
import android.text.TextUtils
import android.util.Log
import android.widget.ImageView
import com.aquila.lib.tools.ToolsLibAPP
import com.aquila.lib.tools.util.ScreenUtil
import com.bumptech.glide.Glide
import com.bumptech.glide.integration.okhttp3.OkHttpUrlLoader
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.load.model.GlideUrl
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.bumptech.glide.load.resource.bitmap.CircleCrop
import com.bumptech.glide.load.resource.bitmap.RoundedCorners
import com.bumptech.glide.load.resource.drawable.DrawableTransitionOptions
import com.bumptech.glide.request.RequestOptions
import com.bumptech.glide.request.RequestOptions.bitmapTransform
import com.bumptech.glide.request.target.SimpleTarget
import com.bumptech.glide.request.transition.Transition
import com.wedevote.wdbook.GlideApp
import jp.wasabeef.glide.transformations.RoundedCornersTransformation
import okhttp3.OkHttpClient
import java.io.ByteArrayOutputStream
import java.io.File
import java.io.FileOutputStream
import java.io.InputStream
import java.net.Proxy
import java.util.concurrent.ExecutionException

/***
 * @date 创建时间 2018/3/23 11:02
 * <AUTHOR> W.YuLong
 * @description 图片的加载工具类
 */
object ImageLoadUtil {
    private const val TAG = "ImageLoadUtil"

    @JvmStatic
    fun <T> loadImageWithOption(imageView: ImageView, url: T, options: RequestOptions) {
        Glide.with(imageView.context).load(url)
            .transition(DrawableTransitionOptions.withCrossFade())
            .apply(options).into(imageView)
    }

    @JvmStatic
    fun <T> loadImageWithError(imageView: ImageView, imgUrl: T?, defaultId: Int, proxy: Proxy? = null) {
        setupProxy(imageView.context, proxy)
        var requestOptions = RequestOptions()
        requestOptions = requestOptions.error(defaultId)
        GlideApp.with(imageView.context).load(imgUrl)
            .diskCacheStrategy(DiskCacheStrategy.ALL)
            .transition(DrawableTransitionOptions.withCrossFade())
            .apply(requestOptions)
            .into(imageView)
    }

    @JvmStatic
    fun <T> loadImageWithDefault(imageView: ImageView, imgUrl: T?, defaultId: Int, proxy: Proxy? = null) {
        setupProxy(imageView.context, proxy)
        var requestOptions = RequestOptions()
        requestOptions = requestOptions.error(defaultId).placeholder(defaultId)
        GlideApp.with(imageView.context).load(imgUrl)
            .diskCacheStrategy(DiskCacheStrategy.ALL)
            .transition(DrawableTransitionOptions.withCrossFade())
            .apply(requestOptions)
            .into(imageView)
    }

    @JvmStatic
    fun <T> loadImageWithDefaultConfig(imageView: ImageView?, url: T?, proxy: Proxy? = null) {
        if (imageView != null) {
            setupProxy(imageView.context, proxy)
            var requestOptions = RequestOptions()
            requestOptions = requestOptions
                .placeholder(com.aquila.lib.widget.R.drawable.ic_spinner_image_load)
                .error(com.aquila.lib.widget.R.drawable.ic_image_load_error)
                .centerCrop()
                .override(452, 256)
            Glide.with(imageView.context).load(url).apply(requestOptions).into(imageView)
        }
    }

    @JvmStatic
    fun <T> loadImage(imageView: ImageView, t: T?, proxy: Proxy? = null) {
        setupProxy(imageView.context, proxy)
        Glide.with(imageView.context).load(t)
            .transition(DrawableTransitionOptions.withCrossFade())
            .into(imageView)
    }

    @JvmStatic
    fun loadBitMapImage(imageView: ImageView, bitmap: Bitmap) {
        val baos = ByteArrayOutputStream()
        bitmap.compress(Bitmap.CompressFormat.PNG, 100, baos)
        val bytes = baos.toByteArray()
        Glide.with(imageView.context).load(bytes)
            .transition(DrawableTransitionOptions.withCrossFade())
            .into(imageView)
    }

    @JvmStatic
    fun loadImageByUrl(imageView: ImageView, imgUrl: String?) {
        if (!TextUtils.isEmpty(imgUrl)) {
            Glide.with(imageView.context).load(imgUrl)
                .transition(DrawableTransitionOptions.withCrossFade())
                .into(imageView)
        }
    }

    @JvmStatic
    fun loadCircularImageByUrl(imageView: ImageView, imgUrl: String?) {
        if (!TextUtils.isEmpty(imgUrl)) {
            val roundedCorners = RoundedCorners(10)
            val options = RequestOptions.bitmapTransform(roundedCorners).override(300, 300)
            Glide.with(imageView.context).load(imgUrl).apply(options)
                .transition(DrawableTransitionOptions.withCrossFade())
                .into(imageView)
        }
    }

    @JvmStatic
    fun <T> loadRoundImage(imageView: ImageView, t: T?, roundSize: Int, proxy: Proxy? = null) {
        setupProxy(imageView.context, proxy)
        Glide.with(imageView.context).load(t)
            .apply(bitmapTransform(RoundedCornersTransformation(roundSize, 0)))
            .into(imageView)
    }

    @JvmStatic
    fun <T> loadRoundImage(imageView: ImageView, t: T?, roundSize: Int, defaultId: Int, proxy: Proxy? = null) {
        setupProxy(imageView.context, proxy)

        var requestOptions = RequestOptions()
        requestOptions = requestOptions.error(defaultId)
        Glide.with(imageView.context).load(t)
            .diskCacheStrategy(DiskCacheStrategy.ALL)
            .transition(DrawableTransitionOptions.withCrossFade())
            .apply(requestOptions)
            .apply(bitmapTransform(RoundedCornersTransformation(roundSize, 0)))
            .into(imageView)
    }

    @JvmStatic
    fun loadAttachmentImage(imageView: ImageView, imageUrl: String?) {
        var requestOptions = RequestOptions()
        val width = ScreenUtil.getScreenWidth()
        requestOptions = requestOptions
            .placeholder(com.aquila.lib.widget.R.drawable.ic_spinner_image_load)
            .error(com.aquila.lib.widget.R.drawable.ic_image_load_error)
            .centerCrop()
            .override((width - 128) / 3 - 110, 80)
        Glide.with(imageView.context).load(imageUrl).apply(requestOptions).into(imageView)
    }

    @JvmStatic
    fun loadImageByUrl(imageView: ImageView, imgUrl: String?, defaultResId: Int) {
        var requestOptions = RequestOptions()
        requestOptions = requestOptions.placeholder(defaultResId)
        Glide.with(imageView.context).load(imgUrl)
            .transition(DrawableTransitionOptions.withCrossFade()).apply(requestOptions)
            .into(imageView)
    }

    @JvmStatic
    fun <T> loadCircleImageWithDefault(imageView: ImageView, imgUrl: T, defaultResId: Int, proxy: Proxy? = null) {
        setupProxy(imageView.context, proxy)
        var requestOptions = RequestOptions()
        requestOptions = requestOptions.transform(CenterCrop())
        Glide.with(imageView.context).load(imgUrl).apply(requestOptions).into(object : SimpleTarget<Drawable>() {
            override fun onResourceReady(resource: Drawable, transition: Transition<in Drawable>?) {
                loadCircleImage(imageView, imgUrl)
            }

            override fun onLoadFailed(errorDrawable: Drawable?) {
                super.onLoadFailed(errorDrawable)
                loadCircleImage(imageView, defaultResId)
            }
        })
    }

    /*加载图片转换为圆形*/
    @JvmStatic
    fun <T> loadCircleImage(imageView: ImageView, path: T?, proxy: Proxy? = null) {
        setupProxy(imageView.context, proxy)
        val options = bitmapTransform(CircleCrop())
        Glide.with(imageView.context).load(path).apply(options).into(imageView)
    }

    @JvmStatic
    @JvmOverloads
    fun <T> getBitmapFromUrl(url: T?, simpleTarget: SimpleTarget<Drawable?>, defaultResId: Int = 0) {
        if (defaultResId != 0) {
            var requestOptions = RequestOptions()
            requestOptions = requestOptions.placeholder(defaultResId)
            Glide.with(ToolsLibAPP.get()).load(url).apply(requestOptions).into(simpleTarget)
        } else {
            Glide.with(ToolsLibAPP.get()).load(url).into(simpleTarget)
        }
    }

    @JvmStatic
    fun drawableToBitmap(drawable: Drawable?): Bitmap? {
        if (drawable == null) {
            return null
        }

        val bitmap: Bitmap?
        if (drawable is BitmapDrawable) {
            if (drawable.bitmap != null) {
                return drawable.bitmap
            }
        }

        if (drawable.intrinsicWidth <= 0 || drawable.intrinsicHeight <= 0) {
            bitmap = Bitmap.createBitmap(1, 1, Bitmap.Config.ARGB_8888) // Single color bitmap will be created of 1x1 pixel
        } else {
            bitmap = Bitmap.createBitmap(drawable.intrinsicWidth, drawable.intrinsicHeight, Bitmap.Config.ARGB_8888)
        }

        val canvas = Canvas(bitmap!!)
        drawable.setBounds(0, 0, canvas.width, canvas.height)
        drawable.draw(canvas)
        return bitmap
    }

    @JvmStatic
    fun <T> getBitmapUrlAsync(url: T?, proxy: Proxy? = null): Bitmap? {
        if (url != null) {
            try {
                val context = ToolsLibAPP.get()
                setupProxy(context, proxy)
                return Glide.with(context).asBitmap()
                    .load(url).submit().get()
            } catch (e: InterruptedException) {
                e.printStackTrace()
            } catch (e: ExecutionException) {
                e.printStackTrace()
            }
        }
        return null
    }

    @JvmStatic
    fun bmpToByteArray(bmp: Bitmap, needRecycle: Boolean): ByteArray {
        val output = ByteArrayOutputStream()
        bmp.compress(Bitmap.CompressFormat.PNG, 100, output)
        if (needRecycle) {
            bmp.recycle()
        }

        val result = output.toByteArray()
        try {
            output.close()
        } catch (e: Exception) {
//            e.printStackTrace()
        }

        return result
    }

    /*将Bitmap写入到文件中*/
    @JvmStatic
    fun writeBitmapToFile(bitmap: Bitmap?, path: String): File? {
        var file: File? = null
        bitmap?.let {
            file = File(path)
            try {
                val baos = ByteArrayOutputStream()

                if (path.endsWith(".jpg", true)) {
                    bitmap.compress(Bitmap.CompressFormat.JPEG, 100, baos)
                } else {
                    bitmap.compress(Bitmap.CompressFormat.PNG, 100, baos)
                }
                val fos = FileOutputStream(file)
                fos.write(baos.toByteArray())
                fos.flush()
                fos.close()
                if (bitmap.isRecycled) {
                    bitmap.recycle()
                }
            } catch (e: Exception) {
//                e.printStackTrace()
                return null
            }
        }
        return file
    }

    @JvmStatic
    fun loadGif(resourceId: Int, progressImageView: ImageView) {
        Glide.with(ToolsLibAPP.get()).load(resourceId).into(progressImageView)
    }

    private fun setupProxy(context: Context, proxy: Proxy? = null) {
        if (proxy != null) {
            Log.d(TAG, "Setup proxy")
            val client = OkHttpClient.Builder().proxy(proxy).build()
            Glide.get(context).registry.replace(
                GlideUrl::class.java,
                InputStream::class.java,
                OkHttpUrlLoader.Factory(client),
            )
        } else {
            Log.d(TAG, "Reset to no proxy")
            val client = OkHttpClient.Builder().build()
            Glide.get(context).registry.replace(
                GlideUrl::class.java,
                InputStream::class.java,
                OkHttpUrlLoader.Factory(client),
            )
        }
    }
}
