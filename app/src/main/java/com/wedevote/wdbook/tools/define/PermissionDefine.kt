package com.wedevote.wdbook.tools.define

import android.Manifest

/***
 * @date 创建时间 2020/4/20 15:22
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @description
 */
object PermissionDefine {

    val Permission_SDCard = arrayOf(Manifest.permission.READ_EXTERNAL_STORAGE, Manifest.permission.WRITE_EXTERNAL_STORAGE)

    val Permission_FIRST_LAUNCHER = arrayOf(
        Manifest.permission.READ_EXTERNAL_STORAGE, Manifest.permission.WRITE_EXTERNAL_STORAGE,
        Manifest.permission.INTERNET
    )
}
