package com.wedevote.wdbook.tools.download

import com.aquila.lib.log.KLog
import com.wedevote.wdbook.base.SDKSingleton
import com.wedevote.wdbook.constants.Constants
import com.wedevote.wdbook.constants.DownloadStatus
import com.wedevote.wdbook.entity.DownloadDataEntity
import com.wedevote.wdbook.entity.store.BookFileDownloadEntity
import com.wedevote.wdbook.network.ApiHeader
import com.wedevote.wdbook.tools.util.FilePathUtil
import com.wedevote.wdbook.tools.util.MD5EncryptUtil
import com.wedevote.wdbook.tools.util.UnitFormatUtil
import com.wedevote.wdbook.tools.util.getFunctionInfo
import okhttp3.Request
import okhttp3.Response
import java.io.File
import java.io.InputStream
import java.io.RandomAccessFile

/***
 * @date 创建时间 2020/4/16 11:22
 * <AUTHOR> <PERSON><PERSON>
 * @description 执行具体的下载任务封装类
 */
class DownloadTask(
    val fileDownloadEntity: BookFileDownloadEntity,
    var downloadEntity: DownloadDataEntity,
    val downloadListener: OnDownloadingListener?,
) : Runnable {
    var isDownloadPaused = false
    
    val downloadBL = SDKSingleton.downloadBl
    override fun toString(): String {
        return getFileId()
    }
    
    override fun equals(other: Any?): Boolean {
        return other != null && toString().equals(other.toString())
    }
    
    fun getFileId(): String {
        return fileDownloadEntity.fileId
    }
    
    override fun run() {
        isDownloadPaused = false
        
        KLog.d("下载开始")
        downloadEntity.downloadStatus = DownloadStatus.BEGIN
        downloadBL.updateDownloadStatus(downloadEntity.fileId!!, DownloadStatus.BEGIN)
        downloadListener?.onBeginning(fileDownloadEntity)
        val file = File(downloadEntity.getTempDownloadFilePath())
        if (!file.exists()) {
            file.createNewFile()
        }
        
        var tempFileLength = file.length()
        val request: Request = Request.Builder()
            .addHeader("Range", "bytes=$tempFileLength-")
            .addHeader(ApiHeader.REFERER, Constants.WD_BOOK_HTTP_REFERER)
            .url(fileDownloadEntity.downloadUrl).build()
        
        var inputStream: InputStream? = null
        var response: Response? = null
        
        try {
            response = DownloadConfigs.getHttpClient().newCall(request).execute()
            if (response.isSuccessful) {
                val total: Long = response.body!!.contentLength()
                inputStream = response.body!!.byteStream()
                val randomAccessFile = RandomAccessFile(file, "rwd")
                randomAccessFile.seek(tempFileLength)
                val bytes = ByteArray(1024 * 8)
                var len: Int = 0
                var lastTime = 0L
                var currentTime = 0L
                updateDownloadStatus(DownloadStatus.DOWNLOADING)
                while (inputStream.read(bytes).also({ len = it }) != -1) {
                    randomAccessFile.write(bytes, 0, len)
                    tempFileLength += len.toLong()
                    currentTime = System.currentTimeMillis()
    
                    if (currentTime - lastTime > 500L) {
                        downloadListener?.onDownloadingProgress(fileDownloadEntity, tempFileLength, total)
                        lastTime = currentTime
                        KLog.d(
                            "total = ${UnitFormatUtil.formatFileSize(total)}, tempFileLength = ${
                                UnitFormatUtil.formatFileSize(
                                    tempFileLength
                                )
                            }"
                        )
                    }
    
                    if (isDownloadPaused) {
                        updateDownloadStatus(DownloadStatus.PAUSE)
                        downloadListener?.onPause(fileDownloadEntity)
                        break
                    }
                }
                downloadListener?.onDownloadingProgress(fileDownloadEntity, tempFileLength, total)
    
                if (tempFileLength >= total) {
                    var isError = false
                    var md5 = MD5EncryptUtil.md5File(File(downloadEntity.getTempDownloadFilePath()))
                    if (md5 == downloadEntity.md5) {
                        var resultFileName = FilePathUtil.unzipFile(downloadEntity.getTempDownloadFilePath())
                        if (!resultFileName.isNullOrEmpty()) {
                            downloadEntity.actualFileName = resultFileName
                            updateDownloadStatus(DownloadStatus.COMPLETE)
                            SDKSingleton.dbWrapBl.updateActualFileName(downloadEntity.fileId!!, downloadEntity.actualFileName!!)
                            downloadListener?.onComplete(fileDownloadEntity)
                            isError = false
                        }
                    }
                    if (isError) {
                        updateDownloadStatus(DownloadStatus.ERROR)
                        downloadListener?.onError(fileDownloadEntity, "MD5不匹配")
                    }
                }
            } else {
                updateDownloadStatus(DownloadStatus.ERROR)
                SDKSingleton.dbWrapBl.deleteDownloadItem(downloadEntity)
                downloadListener?.onError(fileDownloadEntity, "${response.code}  ${response.message}")
            }
        } catch (e: Exception) {
            e.printStackTrace()
            SDKSingleton.dbWrapBl.deleteDownloadItem(downloadEntity)
            downloadListener?.onError(fileDownloadEntity, "下载出现了异常：" + e.message)
            SDKSingleton.loggerBl.handleThrowable(code = 0, t = e, funcName = getFunctionInfo())
        } finally {
            inputStream?.close()
            response?.body?.close()
        }
    }
    
    private fun updateDownloadStatus(status: DownloadStatus) {
        try {
            downloadEntity.downloadStatus = status
            downloadBL.updateDownloadStatus(downloadEntity.fileId!!, status)
        } catch (e: Exception) {
        }
    }
}
