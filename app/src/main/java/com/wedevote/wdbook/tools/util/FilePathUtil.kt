package com.wedevote.wdbook.tools.util

import android.content.Context
import android.net.Uri
import android.os.Build
import android.os.Environment
import android.text.TextUtils
import androidx.core.content.FileProvider
import com.aquila.lib.log.KLog
import com.wedevote.wdbook.base.APP
import com.wedevote.wdbook.base.SDKSingleton
import java.io.BufferedInputStream
import java.io.BufferedOutputStream
import java.io.BufferedWriter
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream
import java.io.FileWriter
import java.io.IOException
import java.io.InputStream
import java.util.zip.ZipEntry
import java.util.zip.ZipInputStream

/***
 * @date 创建时间 2020/4/20 15:26
 * <AUTHOR> W.YuLong
 * @description
 */
object FilePathUtil {

    private var rootPath: String = Environment.getExternalStorageDirectory().absolutePath + "/.WDBook/"

    private var multipleMediaPath = ""
    fun getMultipleMediaPath(): String {
        if (multipleMediaPath.isNullOrEmpty()) {
            synchronized(FilePathUtil::class.java) {
                multipleMediaPath = rootPath + "MultipleMedia/"
                checkDir(multipleMediaPath)
            }
        }
        return multipleMediaPath
    }

    private fun checkDir(dirPath: String, needMakeDir: Boolean = true): Boolean {
        val file = File(dirPath)
        if (!file.exists() && needMakeDir) {
            return file.mkdirs()
        }
        return true
    }

    fun copyRawFileToPath(path: String, resource: Int): Boolean {
        val inputStream = APP.get().resources.openRawResource(resource)
        try {
            val os = FileOutputStream(path)
            val buffer = ByteArray(4096)
            var count = 0
            while (inputStream.read(buffer).also { count = it } > 0) {
                os.write(buffer, 0, count)
            }
            os.close()
            inputStream.close()
        } catch (e: Exception) {
            e.printStackTrace()
            return false
        }
        return true
    }

    fun copyAssetFileToMemory(assetFileName: String, outFilePath: String, needOverride: Boolean = true): File? {
        KLog.d("拷贝数据库")
        var checkFile = File(outFilePath)
        if (!needOverride && checkFile.exists()) {
            return checkFile
        }

        var outFile: File?
        try {
            val data = ByteArray(4096)

            if (checkFile.exists()) {
                checkFile.delete()
            }
            outFile = File(outFilePath)
            val outputStream = FileOutputStream(outFile)
            // 获得原文件流
            val inputStream = APP.get().assets.open(assetFileName)
            // 开始处理流
            while (inputStream.read(data) != -1) {
                outputStream.write(data)
            }
            inputStream.close()
            outputStream.close()
        } catch (e: Exception) {
            e.printStackTrace()
            SDKSingleton.loggerBl.handleThrowable(e, getFunctionInfo())
            return null
        }
        return outFile
    }

    @JvmStatic
    fun readTextFromFile(path: String): String? {
        val file = File(path)
        if (!file.exists()) {
            KLog.e("文件不存在:$path")
            return ""
        }
        var text: String? = null
        var inputStream = FileInputStream(file)
        try {
            text = readStringFromInputStream(inputStream)
            // 关闭输入流
        } catch (e: IOException) {
            e.printStackTrace()
            SDKSingleton.loggerBl.handleThrowable(e, getFunctionInfo())
        } finally {
            try {
                inputStream.close()
            } catch (e: IOException) {
                e.printStackTrace()
                SDKSingleton.loggerBl.handleThrowable(e, getFunctionInfo())
            }
        }
        return text
    }

    /*读取asset中的文件内容*/
    fun getTextFromAssets(fileName: String): String {
        var text: String = ""
        val inputStream = APP.get().assets.open(fileName)
        try {
            text = readStringFromInputStream(inputStream)
        } catch (e: Exception) {
            e.printStackTrace()
        } finally {
            try {
                inputStream.close()
            } catch (e: IOException) {
                e.printStackTrace()
                SDKSingleton.loggerBl.handleThrowable(e, getFunctionInfo())
            }
        }
        return text
    }

    fun readStringFromInputStream(inputStream: InputStream): String {
        val sb = StringBuilder()
        val temp = ByteArray(8192)
        var len: Int
        // 读取文件内容:
        while ((inputStream.read(temp).also { len = it }) > 0) {
            sb.append(String(temp, 0, len))
        }
        return sb.toString()
    }

    @JvmStatic
    fun copyFile(oldFilePath: String, newFilePath: String): File? { // 如果原文件不存在
        if (!File(oldFilePath).exists()) {
            return null
        }
        var outFile: File?
        // 获得原文件流
        var inputStream: FileInputStream?
        try {
            inputStream = FileInputStream(File(oldFilePath))
            val data = ByteArray(4096)
            // 输出流
            outFile = File(newFilePath)
            val outputStream = FileOutputStream(outFile)
            // 开始处理流
            while (inputStream.read(data) != -1) {
                outputStream.write(data)
            }
            inputStream.close()
            outputStream.close()
        } catch (e: java.lang.Exception) {
            e.printStackTrace()
            SDKSingleton.loggerBl.handleThrowable(e, getFunctionInfo())
            return null
        }
        return outFile
    }

    fun deleteFolderFile(file: File, isDeleteFolder: Boolean = false) {
        if (file.isDirectory) {
            val files = file.listFiles()
            for (f in files) {
                deleteFolderFile(f)
            }
            // 是否文件夹也要删除
            if (isDeleteFolder) {
                file.delete()
            }
        } else if (file.exists()) {
            file.delete()
        }
    }

    @JvmStatic
    fun writeStringToFile(path: String, text: String) {
        if (TextUtils.isEmpty(text)) {
            return
        }
        try {
            val file = File(path)
            if (!file.exists()) {
                file.createNewFile()
            }
            // 后面这个参数代表是不是要接上文件中原来的数据，不进行覆盖
            val filerWriter = FileWriter(path, true)
            val bufWriter = BufferedWriter(filerWriter)
            bufWriter.write(text)
            bufWriter.close()
            filerWriter.close()
        } catch (e: IOException) {
            e.printStackTrace()
        }
    }

    fun unzipFile(zipFilePath: String, needDeleteSource: Boolean = true): String {
        var destFileName: String = ""
        if (zipFilePath.isNullOrEmpty()) {
            return ""
        }
        var zipFile = File(zipFilePath)
        if (!zipFile.exists()) {
            return ""
        }
        var targetDir = zipFile.parent
        val buffer = 8192 // 这里缓冲区我们使用4KB，
        var strEntry: String // 保存每个zip的条目名称
        try {
            var dest: BufferedOutputStream? // 缓冲输出流
            val fis = FileInputStream(zipFile)
            val zis = ZipInputStream(BufferedInputStream(fis))
            var entry: ZipEntry? // 每个zip条目的实例

            while (true) {
                entry = zis.nextEntry
                if (entry == null) {
                    break
                }
                try {
                    var count: Int
                    val data = ByteArray(buffer)
                    strEntry = entry.name
                    if (strEntry.contains("__MACOSX")) {
                        continue
                    }

                    val entryFile = File(targetDir, strEntry)
                    val entryDir = File(entryFile.parent)
                    if (!entryDir.exists()) {
                        entryDir.mkdirs()
                    }
                    // 如果存在原来的文件，先删除，后重新创建新文件
                    if (entryFile.exists()) {
                        entryFile.delete()
                        entryFile.createNewFile()
                    }

                    val fos = FileOutputStream(entryFile)
                    dest = BufferedOutputStream(fos, buffer)
                    while (true) {
                        count = zis.read(data, 0, buffer)
                        if (count != -1) {
                            dest.write(data, 0, count)
                        } else {
                            break
                        }
                    }
                    dest.flush()
                    dest.close()
                    destFileName = entryFile.name
                } catch (ex: Exception) {
                    ex.printStackTrace()
                    SDKSingleton.loggerBl.handleThrowable(ex, getFunctionInfo())
                }
            }
            zis.close()

            if (needDeleteSource) {
                zipFile.delete()
            }
        } catch (cwj: Exception) {
            cwj.printStackTrace()
            return ""
        }
        return destFileName
    }

    /**/
    fun unzipFile(zipFile: String, targetDir: String): Boolean {
        val buffer = 8192 // 这里缓冲区我们使用4KB，
        var strEntry: String // 保存每个zip的条目名称
        var flag = false
        try {
            var dest: BufferedOutputStream? // 缓冲输出流
            val fis = FileInputStream(zipFile)
            val zis = ZipInputStream(
                BufferedInputStream(fis)
            )
            var entry: ZipEntry? // 每个zip条目的实例

            while (true) {
                entry = zis.nextEntry
                if (entry == null) {
                    break
                }
                try {
                    var count: Int
                    val data = ByteArray(buffer)
                    strEntry = entry.name
                    if (strEntry.contains("__MACOSX")) {
                        continue
                    }
                    val entryFile = File(targetDir + strEntry)
                    val entryDir = File(entryFile.parent)
                    if (!entryDir.exists()) {
                        entryDir.mkdirs()
                    }
                    val fos = FileOutputStream(entryFile)
                    dest = BufferedOutputStream(fos, buffer)
                    while (true) {
                        count = zis.read(data, 0, buffer)
                        if (count != -1) {
                            dest.write(data, 0, count)
                        } else {
                            break
                        }
                    }
                    dest.flush()
                    dest.close()
                } catch (ex: Exception) {
                    ex.printStackTrace()
                    SDKSingleton.loggerBl.handleThrowable(ex, getFunctionInfo())
                }
            }
            zis.close()
            flag = true
        } catch (cwj: Exception) {
            cwj.printStackTrace()
        }
        return flag
    }

    @JvmStatic
    fun parseUri(context: Context, cameraFile: File): Uri {
        val imageUri: Uri
        val authority = context.packageName + ".fileprovider"
        if (Build.VERSION.SDK_INT > Build.VERSION_CODES.M) {
            imageUri = FileProvider.getUriForFile(context, authority, cameraFile)
        } else {
            imageUri = Uri.fromFile(cameraFile)
        }
        return imageUri
    }
}
