package com.wedevote.wdbook.tools.util

import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Matrix
import com.google.zxing.BarcodeFormat
import com.google.zxing.EncodeHintType
import com.google.zxing.WriterException
import com.google.zxing.qrcode.QRCodeWriter
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel
import java.util.*

/***
 * @date 创建时间 2019-11-08 13:24
 * <AUTHOR> <PERSON>.<PERSON>ong
 * @description 二维码生成的工具类
 */
object QRCodeUtil {
    private var IMAGE_HALFWIDTH = 50 // 宽度值，影响中间图片大小

    /**
     * 生成二维码
     * @param text 需要生成二维码的文字、网址等
     * @param size 需要生成二维码的大小（）
     * @return bitmap
     */
    @JvmStatic
    fun createQRCode(text: String, size: Int): Bitmap? {
        try {
            val hints = Hashtable<EncodeHintType, String>()
            hints[EncodeHintType.CHARACTER_SET] = "utf-8"
            val bitMatrix = QRCodeWriter().encode(
                text,
                BarcodeFormat.QR_CODE, size, size, hints
            )
            val pixels = IntArray(size * size)
            for (y in 0 until size) {
                for (x in 0 until size) {
                    if (bitMatrix.get(x, y)) {
                        pixels[y * size + x] = -0x1000000
                    } else {
                        pixels[y * size + x] = -0x1
                    }
                }
            }
            val bitmap = Bitmap.createBitmap(
                size, size,
                Bitmap.Config.ARGB_8888
            )
            bitmap.setPixels(pixels, 0, size, 0, 0, size, size)
            return bitmap
        } catch (e: WriterException) {
            e.printStackTrace()
            return null
        }
    }

    /**
     * 生成带logo的二维码，logo默认为二维码的1/5
     *
     * @param text       需要生成二维码的文字、网址等
     * @param size       需要生成二维码的大小（）
     * @param logoBitmap logo文件
     * @return bitmap
     */
    @JvmStatic
    fun createQRCodeWithLogo(text: String, size: Int, color: Int, logoBitmap: Bitmap): Bitmap? {
        try {

            val hints = Hashtable<EncodeHintType, Any>()
            hints[EncodeHintType.CHARACTER_SET] = "utf-8"
            /*
             * 设置容错级别，默认为ErrorCorrectionLevel.L
             * 因为中间加入logo所以建议你把容错级别调至H,否则可能会出现识别不了
             */
            hints[EncodeHintType.ERROR_CORRECTION] = ErrorCorrectionLevel.H
            hints[EncodeHintType.MARGIN] = 1
            val bitMatrix = QRCodeWriter().encode(text, BarcodeFormat.QR_CODE, size, size, hints)
            val width = bitMatrix.width // 矩阵高度
            val height = bitMatrix.height // 矩阵宽度
            val halfW = width / 2
            val halfH = height / 2

            val IMAGE_HALFWIDTH = size / 12
            val m = Matrix()
            val sx = 2.toFloat() * IMAGE_HALFWIDTH / logoBitmap.width
            val sy = 2.toFloat() * IMAGE_HALFWIDTH / logoBitmap.height
            m.setScale(sx, sy)
            // 设置缩放信息
            // 将logo图片按martix设置的信息缩放
            val zoomOutBitmap =
                Bitmap.createBitmap(logoBitmap, 0, 0, logoBitmap.width, logoBitmap.height, m, false)
            val space = (zoomOutBitmap.width * 0.15).toInt()
            val realBitmap = Bitmap.createBitmap(
                zoomOutBitmap.width + space * 2,
                zoomOutBitmap.height + space * 2,
                Bitmap.Config.ARGB_8888
            )
            val canvas = Canvas(realBitmap)
            canvas.drawColor(Color.WHITE)
            canvas.drawBitmap(zoomOutBitmap, space.toFloat(), space.toFloat(), null)
            val pixels = IntArray(size * size)
            for (y in 0 until size) {
                for (x in 0 until size) {
                    if (x > halfW - realBitmap.width / 2 && x < halfW + realBitmap.width / 2 &&
                        y > halfH - realBitmap.width / 2 &&
                        y < halfH + realBitmap.width / 2
                    ) {
                        // 该位置用于存放图片信息
                        // 记录图片每个像素信息
                        pixels[y * width + x] = realBitmap.getPixel(
                            x - halfW + realBitmap.width / 2,
                            y - halfH + realBitmap.width / 2
                        )
                    } else {
                        if (bitMatrix.get(x, y)) {
                            //                            pixels[y * size + x] = 0xff000000;
                            pixels[y * size + x] = color
                        } else {
                            pixels[y * size + x] = Color.TRANSPARENT
                        }
                    }
                }
            }
            val bitmap = Bitmap.createBitmap(size, size, Bitmap.Config.ARGB_8888)
            bitmap.setPixels(pixels, 0, size, 0, 0, size, size)
            return bitmap
        } catch (e: WriterException) {
            e.printStackTrace()
            return null
        }
    }
}
