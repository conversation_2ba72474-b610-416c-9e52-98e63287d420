package com.wedevote.wdbook.tools.event

import com.wedevote.wdbook.ui.store.OrderStatusDefine
import com.wedevote.wdbook.entity.NoteEntity

/***
 * @date 创建时间 2021/6/30 18:37
 * <AUTHOR> <PERSON><PERSON><PERSON>
 * @description
 */
class RestartAllActivityEvent(var isLanguageSetting: Boolean = false)

class BookDataChangeEvent

class LogoutEvent(var isTokenExpire: Boolean = false)

class OnLoginEvent

class OnSuccessBuyEvent(var productId: Long) : HomeShelfDataReloadEvent()

class OnBookReadEvent : HomeShelfDataReloadEvent()

class OnBookItemDownloadFinish : HomeShelfDataReloadEvent()

class DialogDismissEvent

// 笔记合并的事件
class OnNoteMergedEvent

// 首页书架数据reload的事件
open class HomeShelfDataReloadEvent(isRefresh: Boolean = false) {
    var isRefresh: Boolean = isRefresh
        private set
}

class OnFavoriteEvent

/*deeplink跳转到书架编辑的事件*/
class EditShelfEvent

/*显示活动推荐对话框的Event*/
class ShowActivityNotifyEvent

/*反馈已读标记的Event*/
class OnFeedbackReadEvent(feedbackId: Long)

/*消息通知已读标记的Event*/
class OnNotificationReadEvent()

/*书籍自动下载事件*/
class AutoDownloadEvent

/*购买成功后刷新页面*/
class OnRefreshAfterPay(
    var status: Int = OrderStatusDefine.ORDER_STATUS_FAILURE,
    var orderId: String = ""
)

/*修改用户资料成功*/
class OnChangeAccountSuccess(var isResetPassword: Boolean = false)

/*点击了书籍内搜索跳转事件*/
class OnBookSearchJumpClickEvent(
    val noteEntity: NoteEntity? = null,
    val resourceId: String = "",
    val filePath: String = "",
    val fileId: String? = "",
    val keywords: String = ""
)

/*搜索结果导航事件，用于请求上一条或下一条搜索结果*/
class OnBookSearchNavigateEvent(val isNext: Boolean)

/*搜索结果导航响应事件，包含导航结果*/
class OnBookSearchNavigateResultEvent(
    val success: Boolean,
    val noteEntity: NoteEntity? = null,
    val resourceId: String = "",
    val filePath: String = "",
    val fileId: String = ""
)

/**
 * 清理搜索高亮和隐藏搜索菜单的事件
 */
class OnBookSearchClearEvent

/**
 * 调用销毁书籍内搜索Activity事件
 */
class OnBookSearchCallFinishEvent