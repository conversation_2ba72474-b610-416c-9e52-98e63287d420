package com.wedevote.wdbook.tools.util

import com.aquila.lib.log.KLog
import com.wedevote.wdbook.base.APP
import java.io.File

/***
 * @date 创建时间 2020/7/15 14:17
 * <AUTHOR> <PERSON><PERSON>
 * @description APP内部的路径工具类
 */
object DataPathUtil {

    fun getAPPDataPath(): String {
        return APP.get().filesDir.absolutePath
    }

    private var APKPath: String? = null
    fun getAPkDirPath(): String {
        if (APKPath.isNullOrEmpty()) {
            synchronized(DataPathUtil::class.java) {
                if (APKPath.isNullOrEmpty()) {
                    APKPath = getDownloadPath() + "/APK/"
                    checkOrMakeDir(APKPath!!)
                }
            }
        }
        return APKPath!!
    }

    private var downloadPath: String? = null
    fun getDownloadPath(): String {
        if (downloadPath.isNullOrEmpty()) {
            synchronized(DataPathUtil::class.java) {
                if (downloadPath.isNullOrEmpty()) {
                    downloadPath = getAPPDataPath() + "/download/"
                    checkOrMakeDir(downloadPath!!)
                }
            }
        }
        return downloadPath!!
    }

    private var imageTempPath: String? = null

    @JvmStatic
    fun getImageTempPath(): String {
        if (imageTempPath.isNullOrEmpty()) {
            synchronized(DataPathUtil::class.java) {
                if (imageTempPath.isNullOrEmpty()) {
                    imageTempPath = getAPPDataPath() + "temp_image.png"
                }
            }
        }
        return imageTempPath!!
    }

    private var cachePath: String = ""
    fun getCachePath(): String {
        if (cachePath.isNullOrEmpty()) {
            synchronized(DataPathUtil::class.java) {
                cachePath = getAPPDataPath() + "/Cache/"
                checkOrMakeDir(cachePath)
            }
        }
        return cachePath
    }

    private var webCachePath: String = ""

    @JvmStatic
    fun getWebCachePath(): String {
        if (webCachePath.isNullOrEmpty()) {
            synchronized(DataPathUtil::class.java) {
                webCachePath = getCachePath() + "web/"
                checkOrMakeDir(webCachePath)
            }
        }
        return webCachePath
    }

    fun getPictureCachePath(archiveId: String? = null): String {
        var pictureCachePath = ""
        synchronized(DataPathUtil::class.java) {
            pictureCachePath = getCachePath() + "Pictures/" + if (!archiveId.isNullOrEmpty()) "$archiveId/" else ""
            checkOrMakeDir(pictureCachePath)
        }
        return pictureCachePath
    }

    fun checkOrMakeDir(dirPath: String): Boolean {
        val file = File(dirPath)
        if (!file.exists()) {
            val flag = file.mkdirs()
            if (!flag) {
                KLog.e("新建文件夹失败::$dirPath")
            }
            return file.exists()
        }
        return true
    }

    private var appDBPath: String? = null

    @Synchronized
    fun getAPPDBPath(): String {
        if (appDBPath == null) {
            if (appDBPath == null) {
                appDBPath = getAPPDataPath() + "/database/"
                checkOrMakeDir(appDBPath!!)
            }
        }
        return appDBPath!!
    }

    /*用户相关的数据库*/
    fun getUserDataDBFilePath(userId: String): String {
        var dirPath = getAPPDBPath() + userId + if (APPUtil.isApkInDebug) {
            "/debug/"
        } else {
            "/release/"
        }
        DataPathUtil.checkOrMakeDir(dirPath)
        return dirPath + "UserData.db"
    }

    /*笔记相关的数据库*/
    fun getBookDataDBFilePath(userId: String): String {
        var dirPath = getAPPDBPath() + userId + if (APPUtil.isApkInDebug) {
            "/debug/"
        } else {
            "/release/"
        }
        DataPathUtil.checkOrMakeDir(dirPath)
        return dirPath + "BookData.db"
    }

    //    /*书签相关的数据库*/
    //    fun getBookMarkDataDBFilePath(userId: String): String {
    //        var dirPath = getAPPDBPath() + userId + if (APPUtil.isApkInDebug) {
    //            "/debug/"
    //        } else {
    //            "/release/"
    //        }
    //        DataPathUtil.checkOrMakeDir(dirPath!!)
    //        return dirPath + "BookData.db"
    //    }

    private var bibleDBPath: String? = null
    fun getBibleDBPath(): String {
        if (bibleDBPath.isNullOrEmpty()) {

//            bibleDBPath = APP.get().getDatabasePath(GlobalConfig.DB_BIBLE_FILE_NAME).absolutePath
            bibleDBPath = APP.get().getDatabasePath("bible_cunps.db").absolutePath
//                getAPPDBPath() + "bible.db"
        }
        return bibleDBPath!!
    }
}
