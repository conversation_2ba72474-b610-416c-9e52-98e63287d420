package com.wedevote.wdbook.tools.define

/***
 * @date 创建时间 2021/7/5 13:49
 * <AUTHOR> <PERSON><PERSON>
 * @description 商品的购买状态，已购或者未购买
 */
enum class PurchaseStatus(var value: Int) {
    UNDONE(0),

    PURCHASED(1)
}

/*商品的上架状态*/
enum class PutawayStatus(var value: Int) {
    // 正常
    Normal(1),
    // 还未上架
    UnGrounding(2),
}

/*阅读字体类型*/
enum class TextFontType(var value: Int) {
    // 思源宋体
    SERIF(0),
    // 系统默认字体
    DEFAULT(1)
}
