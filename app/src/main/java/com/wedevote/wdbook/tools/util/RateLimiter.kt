package com.wedevote.wdbook.tools.util

import android.os.SystemClock

/**
 * @date 2024/5/13
 * <AUTHOR>
 * @description
 * Copyright © 2024 WD Bible Team. All rights reserved.
 */
class RateLimiter(private val minIntervalMillis: Long) {
    private var lastActionTime = 0L

    @Synchronized
    fun shouldAllowAction(): Bo<PERSON>an {
        val currentTime = SystemClock.elapsedRealtime()
        if (currentTime - lastActionTime > minIntervalMillis) {
            lastActionTime = currentTime
            return true
        }
        return false
    }
}
