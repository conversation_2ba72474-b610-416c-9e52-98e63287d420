package com.wedevote.wdbook.tools.upgrade

import com.aquila.lib.log.KLog
import okhttp3.Headers
import okhttp3.Interceptor
import okhttp3.MultipartBody
import okhttp3.RequestBody
import okhttp3.Response
import okhttp3.internal.http.promisesBody
import okio.Buffer
import java.io.EOFException
import java.io.IOException
import java.nio.charset.Charset
import java.nio.charset.UnsupportedCharsetException
import java.util.Locale
import java.util.concurrent.TimeUnit
import kotlin.collections.ArrayList

/***
 * @date 创建时间 2019-05-07 16:00
 * <AUTHOR> W.YuLong
 * @description 请求日志的打印
 */
class HttpLogInterceptor : Interceptor {
    @Throws(IOException::class)
    @Synchronized
    override fun intercept(chain: Interceptor.Chain): Response {
        /*测试用*/
        if (chain.request().url.toString().startsWith("https://run.mocky.io")) {
            return chain.proceed(chain.request())
        }

        val logList = ArrayList<String>()
        val request = chain.request()
        val requestBody = request.body
        logList.add("【${request.method}】 【${request.url}】")

        initHeaders(logList, request.headers)

        if (requestBody != null) {
            val sb = StringBuilder()
            if (requestBody is MultipartBody) {
                for (part in requestBody.parts) {

                    part.headers?.let {
                        for (i in 0 until it.size) {
                            sb.append("【%s = %s】\n", it.name(i), it.value(i))
                        }
                    }

                    val partBody = part.body.contentType()
                    if (partBody != null &&
                        when (partBody.type.lowercase(Locale.getDefault())) {
                            "video", "image", "file" -> true
                            else -> false
                        }
                    ) {
                        sb.append("*******************************************************************\n")
                        sb.append("***************这里是文件的数据，省略输出**************************\n")
                        sb.append("*******************************************************************\n\n")
                    } else {
                        sb.append(String.format("【MultipartBody = %s】", formatBodyToString(part.body)))
                    }
                    sb.append("\n\n")
                }
            } else {
                sb.append(formatBodyToString(requestBody))
            }

            logList.add("【Request Body】: " + sb.toString())
        }
        logList.add(String.format("***************** END 【%s】Request ************************************************\n\n", request.method))

        val startNs = System.nanoTime()
        val response: Response
        try {
            response = chain.proceed(request)
        } catch (e: Exception) {
            logList.add("<-- HTTP FAILED: $e")
            KLog.httpLog(logList)
            e.printStackTrace()
            throw e
        }

        val tookMs = TimeUnit.NANOSECONDS.toMillis(System.nanoTime() - startNs)

        val responseBody = response.body
        val contentLength = responseBody!!.contentLength()

        logList.add("【code = ${response.code}】,【url = ${response.request.url}】,【${tookMs}ms】")

        initHeaders(logList, response.headers)

        if (!response.promisesBody()) {
            logList.add("<-- END HTTP Without Body")
        } else if (bodyEncoded(response.headers)) {
            logList.add("<-- END HTTP (encoded body omitted)")
        } else {
            val source = responseBody.source()
            source.request(Long.MAX_VALUE) // Buffer the entire body.
            val buffer = source.buffer

            var charset: Charset? = UTF8
            val contentType = responseBody.contentType()
            if (contentType != null) {
                try {
                    charset = contentType.charset(UTF8)
                } catch (e: UnsupportedCharsetException) {
                    logList.add("Couldn't decode the response body; charset is likely malformed.")
                    logList.add("<-- END HTTP")
                    return response
                }
            }

            if (!isPlaintext(buffer)) {
                logList.add("<-- END HTTP (binary " + buffer.size + "-byte body omitted)")
                return response
            }
            if (contentLength != 0L) {
                logList.add("↓↓↓↓↓↓↓↓↓↓↓↓【Response Data】↓↓↓↓↓↓↓↓↓↓↓")
                logList.add(buffer.clone().readString(charset!!))
            }
            logList.add("<-- END HTTP (" + buffer.size + " -byte body)")
        }
        KLog.httpLog(logList)
        return response
    }

    private fun formatBodyToString(requestBody: RequestBody): String {
        var result = ""
        try {
            val buffer = Buffer()
            requestBody.writeTo(buffer)
            var charset: Charset? = UTF8
            val contentType = requestBody.contentType()
            if (contentType != null) {
                charset = contentType.charset(UTF8)
            }

            if (isPlaintext(buffer)) {
                result = buffer.readString(charset!!)
            }
        } catch (e: IOException) {
            e.printStackTrace()
            result = "解析错误"
        }

        return result
    }

    private fun initHeaders(logList: ArrayList<String>, headers: Headers) {
        for (i in 0 until headers.size) {
            val name = headers.name(i)
            logList.add(String.format("【%s = %s】", name, headers.value(i)))
        }
    }

    private fun bodyEncoded(headers: Headers): Boolean {
        val contentEncoding = headers.get("Content-Encoding")
        return contentEncoding != null && !contentEncoding.equals("identity", ignoreCase = true)
    }

    companion object {
        private val UTF8 = Charset.forName("UTF-8")

        /**
         * Returns true if the body in question probably contains human readable text. Uses a small sample
         * of code points to detect unicode control characters commonly used in binary file signatures.
         */
        internal fun isPlaintext(buffer: Buffer): Boolean {
            try {
                val prefix = Buffer()
                val byteCount = if (buffer.size < 64) buffer.size else 64
                buffer.copyTo(prefix, 0, byteCount)
                for (i in 0..15) {
                    if (prefix.exhausted()) {
                        break
                    }
                    val codePoint = prefix.readUtf8CodePoint()
                    if (Character.isISOControl(codePoint) && !Character.isWhitespace(codePoint)) {
                        return false
                    }
                }
                return true
            } catch (e: EOFException) {
                return false // Truncated UTF-8 sequence.
            }
        }
    }
}
