package com.wedevote.wdbook.tools.upgrade

import android.content.Context
import android.os.Bundle
import android.view.Gravity
import android.widget.ProgressBar
import android.widget.TextView
import com.wedevote.wdbook.R
import com.wedevote.wdbook.ui.dialogs.BaseDialog

/***
 * @date 创建时间 2020/9/30 21:17
 * <AUTHOR> <PERSON><PERSON>
 * @description
 */
class APKDownloadProgressDialog(context: Context) : BaseDialog(context) {
    lateinit var progressBar: ProgressBar
    lateinit var percentTextView: TextView
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.dialog_apk_download_progress_layout)
        progressBar = findViewById(R.id.apk_download_progress_ProgressBar)
        percentTextView = findViewById(R.id.apk_download_percent_TextView)

        configDialog(Gravity.CENTER)
        setCanceledOnTouchOutside(false)
        setCancelable(false)
    }

    fun updateProgressInfo(percent: Int) {
        progressBar.progress = percent
        percentTextView.text = "$percent%"
    }
}
