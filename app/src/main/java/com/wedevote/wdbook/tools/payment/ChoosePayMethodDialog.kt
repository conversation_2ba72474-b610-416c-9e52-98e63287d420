package com.wedevote.wdbook.tools.payment

import android.app.Dialog
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.Gravity
import android.view.View
import android.view.WindowManager
import android.widget.Button
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.aquila.lib.base.OnViewClickListener
import com.aquila.lib.tools.singleton.SPSingleton
import com.aquila.lib.widget.group.GroupImageTextLayout
import com.wedevote.wdbook.R
import com.wedevote.wdbook.entity.store.PaymentAmountEntity
import com.wedevote.wdbook.tools.util.PayTypeDefine
import com.wedevote.wdbook.tools.util.SPKeyDefine
import com.wedevote.wdbook.tools.util.UnitFormatUtil
import com.wedevote.wdbook.tools.util.findString
import com.wedevote.wdbook.ui.store.UnbindCardHelperActivity

/***
 * @date 创建时间 2020/9/21 17:26
 * <AUTHOR> W<PERSON>
 * @description
 */
class ChoosePayMethodDialog(context: Context, var currency: String, var paymentAmountEntity: PaymentAmountEntity) :
    Dialog(context),
    View.OnClickListener {
    lateinit var closeImageView: ImageView
    lateinit var creditCardLayout: ConstraintLayout
    lateinit var unbindHelperTextView: TextView

    lateinit var cardStatusImageView: ImageView
    lateinit var paypalLayout: GroupImageTextLayout
    lateinit var alipayLayout: GroupImageTextLayout
    lateinit var paypalStatusImageView: ImageView
    lateinit var alipayStatusImageView: ImageView
    lateinit var payButton: Button
    var payType = SPSingleton.get().getString(SPKeyDefine.SP_LastPaymentStyle, PayTypeDefine.PAY_TYPE_CARD.value)



    var onViewClickListener: OnViewClickListener? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.dialog_choose_pay_method_layout)
        initViewFromXML()
        setViewListeners()
        val payType = SPSingleton.get().getString(SPKeyDefine.SP_LastPaymentStyle, PayTypeDefine.PAY_TYPE_PAYPAL.value)
        setCurrentPayTypeUI(payType)
        payButton.text = findString(R.string.ensure_pay) + " ${
        UnitFormatUtil.formatPrice(
            UnitFormatUtil.getCurrencySymbol(currency),
            paymentAmountEntity.actualAmount
        )
        }"
        configDialog()
    }

    private fun initViewFromXML() {
        closeImageView = findViewById(R.id.pay_method_close_ImageView)
        cardStatusImageView = findViewById(R.id.pay_method_stripe_check_status_ImageView)
        paypalLayout = findViewById(R.id.pay_method_paypal_layout)
        alipayLayout = findViewById(R.id.pay_method_alipay_layout)
        payButton = findViewById(R.id.pay_method_pay_action_Button)

        alipayStatusImageView = findViewById(R.id.pay_method_alipay_check_status_ImageView)
        paypalStatusImageView = findViewById(R.id.pay_method_paypal_check_status_ImageView)
        unbindHelperTextView = findViewById(R.id.pay_method_unbind_help_TextView)
        creditCardLayout = findViewById(R.id.pay_method_stripe_layout)
    }

    private fun setViewListeners() {
        closeImageView.setOnClickListener(this)
        creditCardLayout.setOnClickListener(this)
        unbindHelperTextView.setOnClickListener(this)
        paypalLayout.setOnClickListener(this)
        alipayLayout.setOnClickListener(this)
        payButton.setOnClickListener(this)
    }

    private fun setCurrentPayTypeUI(payType: String?) {

        when (payType) {
            PayTypeDefine.PAY_TYPE_CARD.value -> {
                alipayStatusImageView.isSelected = false
                cardStatusImageView.isSelected = true
                paypalStatusImageView.isSelected = false
            }
            PayTypeDefine.PAY_TYPE_ALIPAY.value -> {
                alipayStatusImageView.isSelected = true
                cardStatusImageView.isSelected = false
                paypalStatusImageView.isSelected = false
            }
            PayTypeDefine.PAY_TYPE_PAYPAL.value -> {
                alipayStatusImageView.isSelected = false
                cardStatusImageView.isSelected = false
                paypalStatusImageView.isSelected = true
            }
        }
        this.payType = payType
        SPSingleton.get().putString(SPKeyDefine.SP_LastPaymentStyle, payType)
    }

    protected fun configDialog() {
        val wl = window!!.attributes
        wl.gravity = Gravity.BOTTOM // 设置重力
        wl.width = WindowManager.LayoutParams.MATCH_PARENT
        wl.height = WindowManager.LayoutParams.WRAP_CONTENT
        window!!.setWindowAnimations(com.aquila.lib.dialog.R.style.bottomDialogWindowAnim)
        window!!.attributes = wl
        window!!.setBackgroundDrawableResource(android.R.color.transparent)
    }

    override fun onClick(v: View?) {
        when (v) {
            closeImageView -> {
                dismiss()
            }
            payButton -> {
                onViewClickListener?.onClickAction(v, payType ?: "", "")
                dismiss()
            }
            unbindHelperTextView -> {
                context.startActivity(Intent(context, UnbindCardHelperActivity::class.java))
            }
            creditCardLayout -> {
                setCurrentPayTypeUI(PayTypeDefine.PAY_TYPE_CARD.value)
            }
            paypalLayout -> {
                setCurrentPayTypeUI(PayTypeDefine.PAY_TYPE_PAYPAL.value)
            }
            alipayLayout -> {
                setCurrentPayTypeUI(PayTypeDefine.PAY_TYPE_ALIPAY.value)
            }
        }
    }
}
