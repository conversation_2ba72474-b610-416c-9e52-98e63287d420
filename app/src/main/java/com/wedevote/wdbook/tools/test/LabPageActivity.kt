package com.wedevote.wdbook.tools.test

import android.os.Bundle
import androidx.activity.viewModels
import com.aquila.lib.log.KLog
import com.wedevote.wdbook.R
import com.wedevote.wdbook.base.RootActivity
import com.wedevote.wdbook.ui.widgets.SlideSwitch

/**
 * @date 2024/6/12
 * <AUTHOR>
 * @description Lab page 的页面（供 QA 测试用）
 * Copyright © 2024 WD Bible Team. All rights reserved.
 */
class LabPageActivity : RootActivity() {
    private val viewModel: LabPageViewModel by viewModels()

    private lateinit var useTestCftConfigSwitch: SlideSwitch
    private lateinit var showCftLogSwitch: SlideSwitch

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_lab_page)
        syncData()
        initUI()
    }

    private fun initUI() {
        useTestCftConfigSwitch = findViewById(R.id.lab_page_use_test_cft_config_Switch)
        showCftLogSwitch = findViewById(R.id.lab_page_show_cft_log_Switch)

        useTestCftConfigSwitch.state = viewModel.useTestCftParamConfig.value ?: false
        showCftLogSwitch.state = viewModel.showCftLog.value ?: false

        useTestCftConfigSwitch.setSlideListener { _, isOpen ->
            viewModel.setUseTestCftParamConfig(isOpen)
        }

        showCftLogSwitch.setSlideListener { _, isOpen ->
            viewModel.setShowCftLog(isOpen)
        }
    }

    private fun syncData() {
        try {
            viewModel.syncData()
        } catch (e: Exception) {
            KLog.e(TAG, "Failed to sync data : $e")
        }
    }

    companion object {
        private const val TAG = "LabPageActivity"
    }
}