package com.wedevote.wdbook.tools.util

import com.aquila.lib.tools.singleton.SPSingleton
import java.text.SimpleDateFormat
import java.util.*

/**
 * @date 2024/3/6
 * <AUTHOR> XIE
 * @description 日期和时间的统一处理类
 * Copyright © 2024 WD Bible Team. All rights reserved.
 */
object DateTimeUtils {
    /**
     * 判断是不是当天首次进入
     */
    @JvmStatic
    fun isFirstIntoToday(sharedPreferencesKey: String): Boolean {
        val lastDateString = SPSingleton.get().getString(sharedPreferencesKey, "")
        val dateFormatter = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
        val todayString = dateFormatter.format(Date())
        SPSingleton.get().putString(sharedPreferencesKey, todayString)
        return todayString != lastDateString
    }

    /**
     * 重置当天首次进入
     */
    @JvmStatic
    fun resetFirstIntoToday(sharedPreferencesKey: String) {
        SPSingleton.get().putString(sharedPreferencesKey, "")
    }
}
