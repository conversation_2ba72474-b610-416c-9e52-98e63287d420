package com.wedevote.wdbook.tools.util

import android.Manifest
import android.app.Activity
import android.content.Intent
import android.net.Uri
import android.provider.MediaStore
import com.aquila.lib.tools.permission.PermissionUtil.Companion.requestPermission
import com.aquila.lib.tools.permission.PermissionUtil.PermissionCallBack
import com.aquila.lib.tools.util.ToastUtil.showToastShort
import com.wedevote.wdbook.R
import com.wedevote.wdbook.tools.util.crop.CropImage
import com.wedevote.wdbook.tools.util.crop.CropImageView
import java.io.File

/***
 *@date 创建时间 2023/5/15
 *<AUTHOR> <PERSON><PERSON>
 *@description
 */
class SelectImageUtility(private val activity: Activity) {
    fun gotoTakePhoto(filePath: String?) {
        requestPermission(
            activity,
            arrayOf(Manifest.permission.CAMERA),
            object : PermissionCallBack {
                override fun onFailure(permission: Array<String>) {
                    showToastShort(R.string.open_camera_failed)
                }

                override fun onSuccess(permission: Array<String>) {
                    val intentFromCapture = Intent(MediaStore.ACTION_IMAGE_CAPTURE)
                    intentFromCapture.putExtra(
                        MediaStore.EXTRA_OUTPUT, FilePathUtil.parseUri(
                            activity, File(filePath)
                        )
                    )
                    activity.startActivityForResult(intentFromCapture, REQUEST_CODE_TAKE_PHOTO)
                }
            })
    }

    fun getAvatarFilePath(): String {
        return PathUtils.usersPath + "head.png"
    }

    fun gotoPhoneAlbum() {
        val intentFromGallery = Intent()
        intentFromGallery.type = "image/*" // 设置文件类型
        intentFromGallery.action = Intent.ACTION_GET_CONTENT
        activity.startActivityForResult(intentFromGallery, REQUEST_CODE_LOCAL_IMAGE)
    }

    fun startCrop(imageUri: Uri, activity: Activity, bSquare: Boolean) {
        val activityBuilder: CropImage.ActivityBuilder = CropImage.activity(imageUri)
        activityBuilder.setGuidelines(CropImageView.Guidelines.ON)
        activityBuilder.setMultiTouchEnabled(true)
        if (bSquare) {
            activityBuilder.setAspectRatio(1, 1)
            activityBuilder.setFixedSize(320, 320)
        }
        activityBuilder.start(activity)
    }

    companion object {
        const val REQUEST_CODE_LOCAL_IMAGE = 12310 // 请求码 本地图片
        const val REQUEST_CODE_TAKE_PHOTO = 12311 // 拍照
        val avatarFilePath: String
            get() = PathUtils.usersPath + "head.png"
        val shareFilePath: String
            get() = PathUtils.usersPath + "share.png"
    }
}