package com.wedevote.wdbook.tools.download

import android.content.Intent
import android.os.Bundle
import androidx.fragment.app.Fragment

/**
 * 创建一个LifecycleFragment，用于绑定Activity的生命周期
 * 并通过LifecycleListener接口将监听结果返回给调用者
 */
class LifecycleFragment(var lifecycleListener: LifecycleListener? = null) : Fragment() {
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        lifecycleListener?.onLifecycleCreate()
    }
    
    override fun onStart() {
        super.onStart()
        lifecycleListener?.onLifecycleStart()
    }
    
    override fun onStop() {
        super.onStop()
        lifecycleListener?.onLifecycleStop()
    }
    
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
//        super.onActivityResult(requestCode, resultCode, data)
        lifecycleListener?.onActivityResult(requestCode, resultCode, data)
    }
    
    override fun onDestroy() {
        super.onDestroy()
        lifecycleListener?.onLifecycleDestroy()
    }
    
    /**
     * 监听LifecycleFragment生命周期的接口
     */
    interface LifecycleListener {
        fun onLifecycleCreate() {}
        fun onLifecycleStart() {}
        fun onLifecycleStop() {}
        fun onLifecycleDestroy() {}
        
        fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {}
    }
}
