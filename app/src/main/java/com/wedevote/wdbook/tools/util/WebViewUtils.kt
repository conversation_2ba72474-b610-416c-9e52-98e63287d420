package com.wedevote.wdbook.tools.util

import android.util.Log
import androidx.webkit.ProxyConfig
import androidx.webkit.ProxyController
import androidx.webkit.WebViewFeature
import com.facebook.stetho.common.LogUtil
import com.wedevote.wdbook.constants.Constants
import p67.fox.Client

/**
 * @date 2025/4/23
 * <AUTHOR>
 * @description WebView 的 Utils
 * Copyright © 2025 WD Bible Team. All rights reserved.
 */
object WebViewUtils {
    /**
     * 全局设置 WebView 的 Proxy
     */
    @JvmStatic
    fun setWebViewProxy() {
        if (WebViewFeature.isFeatureSupported(WebViewFeature.PROXY_OVERRIDE) && Client.isConnected()) {
            val proxyConfig = ProxyConfig.Builder()
                .addProxyRule("${Constants.SOCKS_SCHEME}${Client.host()}:${Client.port()}")
                .addDirect()
                .build()

            ProxyController.getInstance().setProxyOverride(proxyConfig, {
                Log.i("WebViewUtils", "WebView 代理设置完成")
            }, {
                Log.w("WebViewUtils", "WebView 代理 改变")
            })
        }
    }

    /**
     * 全局取消 WebView 的 Proxy
     */
    @JvmStatic
    fun clearWebViewProxy() {
        if (WebViewFeature.isFeatureSupported(WebViewFeature.PROXY_OVERRIDE)) {
            ProxyController.getInstance().clearProxyOverride({
                LogUtil.d("取消 WebView 代理")
            }, {
                LogUtil.d("取消 WebView 代理成功")
            })
        }
    }
}