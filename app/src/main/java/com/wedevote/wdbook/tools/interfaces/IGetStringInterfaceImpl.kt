package com.wedevote.wdbook.tools.interfaces

import com.aquila.lib.base.IGetStringInterface

/***
 * @date 创建时间 2020/4/25 17:16
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON>
 * @description
 */
// interface IGetStringInterface {
//    fun getString(): String
// }

/***
 *@date 创建时间 2020/4/25 17:17
 *<AUTHOR> <PERSON><PERSON><PERSON>
 *@description
 */
open abstract class IGetStringInterfaceImpl<T>(var entity: T) : IGetStringInterface
