package com.wedevote.wdbook.tools.upgrade

import android.app.ActivityManager
import android.app.Service
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Binder
import android.os.Build
import android.os.Handler
import android.os.IBinder
import androidx.core.content.FileProvider
import com.aquila.lib.download.ADownloadEntity
import com.aquila.lib.download.ADownloadTask
import com.aquila.lib.download.ADownloadingListener
import com.aquila.lib.download.DealDownloadFileEntity
import com.aquila.lib.log.KLog
import com.aquila.lib.tools.singleton.ThreadPoolSingleton
import com.aquila.lib.tools.util.ToastUtil
import com.wedevote.wdbook.R
import com.wedevote.wdbook.tools.util.GsonUtil
import okhttp3.Interceptor
import okhttp3.OkHttpClient
import java.io.File
import java.util.concurrent.TimeUnit

/***
 * @date 创建时间 2020/10/23 11:13
 * <AUTHOR> <PERSON><PERSON>
 * @description
 */
class APKDownloadService : Service() {

    var binder = APKDownloadBinder()

    override fun onBind(intent: Intent?): IBinder? {
        return binder
    }

    override fun onStartCommand(intent: Intent, flags: Int, startId: Int): Int {

        var entity: ADownloadEntity? = intent.getSerializableExtra("ADownloadEntity") as ADownloadEntity?
        binder.startDownloadAPK(entity)

        KLog.d(GsonUtil.objectToJson(entity))
        return super.onStartCommand(intent, flags, startId)
    }

    /***
     *@date 创建时间 2020/10/23 13:44
     *<AUTHOR> W.YuLong
     *@description
     */
    inner class APKDownloadBinder : Binder() {
        var remoteDownloadListener: ADownloadingListener? = null

        private var downloadTask: ADownloadTask? = null
        val handler = Handler()

        var aDownloadingListener = object : ADownloadingListener {
            override fun onWait(entity: ADownloadEntity) {
                handler.post {
                    remoteDownloadListener?.onWait(entity)
                }
            }

            override fun onCancelWait(entity: ADownloadEntity) {

                handler.post {
                    remoteDownloadListener?.onCancelWait(entity)
                }
            }

            override fun onBegin(entity: ADownloadEntity) {
                handler.post {
                    remoteDownloadListener?.onBegin(entity)
                }
            }

            override fun onDownloading(entity: ADownloadEntity, downloadSize: Long, totalSize: Long) {
                handler.post {
                    remoteDownloadListener?.onDownloading(entity, downloadSize, totalSize)
                }
            }

            override fun onPause(entity: ADownloadEntity) {
                handler.post {
                    remoteDownloadListener?.onPause(entity)
                }
            }

            override fun onComplete(entity: ADownloadEntity) {
                handler.post {
                    remoteDownloadListener?.onComplete(entity)
                    installAPK(entity.resultPath!!)
                }
            }

            override fun onException(entity: ADownloadEntity, errorCode: Int, errorMsg: String) {
                handler.post {
                    remoteDownloadListener?.onException(entity, errorCode, errorMsg)
                }
            }

            override fun dealCompleteFile(entity: ADownloadEntity, file: File): DealDownloadFileEntity {
                return remoteDownloadListener?.dealCompleteFile(entity, file)!!
            }

            override fun isInterceptToDealFile(entity: ADownloadEntity): Boolean {
                return remoteDownloadListener?.isInterceptToDealFile(entity)!!
            }
        }

        private var okHttpClient: OkHttpClient? = null
        fun getOkHttpClient(): OkHttpClient {
            if (okHttpClient == null) {
                synchronized(APKDownloadService::class.java) {
                    if (okHttpClient == null) {
                        val TIME_OUT = 60L
                        val builder = OkHttpClient.Builder().connectTimeout(TIME_OUT, TimeUnit.SECONDS).readTimeout(
                            TIME_OUT, TimeUnit.SECONDS
                        ).writeTimeout(TIME_OUT, TimeUnit.SECONDS).addNetworkInterceptor(
                            HttpLogInterceptor()
                        ).addInterceptor(object : Interceptor {
                            override fun intercept(chain: Interceptor.Chain): okhttp3.Response {
                                val original = chain.request()
                                val request = original.newBuilder().apply {
                                }.method(original.method, original.body).build()
                                return chain.proceed(request)
                            }
                        })
                        okHttpClient = builder.build()
                    }
                }
            }
            return okHttpClient!!
        }

        fun startDownloadAPK(entity: ADownloadEntity?) {
            KLog.d(GsonUtil.objectToJson(entity))
            entity?.let {
                if (downloadTask == null) {
                    downloadTask = ADownloadTask(getOkHttpClient(), it, aDownloadingListener)
                }
//                if (downloadTask!!.isRunning) {
//                    ToastUtil.showToastShort("任务已经在下载中...")
//                    downloadTask!!.pauseDownloadTask()
//                } else {
//                    KLog.d("启动下载")
//                }
                ThreadPoolSingleton.executeTask(downloadTask!!)
            }
        }

        private fun installAPK(apkPath: String) {
            if (!isForeground()) {
                return
            }
            val apkFile = File(apkPath)
            if (apkFile == null || !apkFile.exists()) {
                ToastUtil.showToastLong(R.string.package_not_exist)
                return
            } else if (!apkFile.exists()) {
                ToastUtil.showToastLong(R.string.package_error)
                return
            }
            val installIntent = Intent(Intent.ACTION_VIEW)
            installIntent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
            val apkUri: Uri
            // 判读版本是否在7.0以上,解决7.0系统以上的安装apk问题
            apkUri = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                installIntent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
                FileProvider.getUriForFile(this@APKDownloadService, packageName + ".fileprovider", apkFile)
            } else {
                Uri.fromFile(apkFile)
            }
            installIntent.setDataAndType(apkUri, "application/vnd.android.package-archive")
            installIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            startActivity(installIntent)
        }

        fun isForeground(): Boolean {
            val am = getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
            val list = am.getRunningTasks(1)
            if (list != null && list.size > 0) {
                val cpn = list[0].topActivity
                if (packageName == cpn!!.packageName) {
                    return true
                }
            }
            return false
        }
    }
}
