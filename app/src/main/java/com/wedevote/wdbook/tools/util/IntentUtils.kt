package com.wedevote.wdbook.tools.util

import android.content.Intent
import com.wedevote.wdbook.R
import com.wedevote.wdbook.base.SDKSingleton

/**
 * @date 2025/4/9
 * <AUTHOR>
 * @description Intent 的 Utils
 * Copyright © 2025 WD Bible Team. All rights reserved.
 */
object IntentUtils {
    fun createBibleInvocationIntent(wdBiblePackage: String, className: String): Intent {
        val intent = Intent()
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
        intent.setClassName(
            wdBiblePackage,
            "com.wdbible.app.wedevotebible.user.V1AuthorizeLoginActivity"
        )
        intent.putExtra("APPKey", SDKSingleton.sessionBl.appKey)
        intent.putExtra("deviceInfo", SDKSingleton.sessionBl.deviceId)
        intent.putExtra("packageName", SDKSingleton.appBl.wdBookPackage)
        intent.putExtra("APPName", findString(R.string.app_name))
        intent.putExtra("ClassName", className)
        return intent
    }
}