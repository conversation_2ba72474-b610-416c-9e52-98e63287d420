package com.wedevote.wdbook.tools.payment.stripe

import androidx.activity.ComponentActivity
import com.aquila.lib.log.KLog
import com.stripe.android.EphemeralKeyProvider
import com.stripe.android.EphemeralKeyUpdateListener
import com.wedevote.wdbook.base.ExceptionHandler
import com.wedevote.wdbook.base.SDKSingleton
import com.wedevote.wdbook.exception.SDKException
import com.wedevote.wdbook.tools.util.GsonUtil
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch

/***
 * @date 创建时间 2020/9/22 15:49
 * <AUTHOR> <PERSON><PERSON>
 * @description
 */
class EphemeralKeyProviderImpl(val activity: ComponentActivity) : EphemeralKeyProvider {

    override fun createEphemeralKey(apiVersion: String, keyUpdateListener: EphemeralKeyUpdateListener) {
        MainScope().launch {
            try {
                if (SDKSingleton.sessionBl.isLogin()) {
                    SDKSingleton.paymentBl.getEphemeralKeyEntity()?.also { data ->
                        KLog.d(GsonUtil.objectToJson(data))
                        keyUpdateListener.onKeyUpdate(GsonUtil.objectToJson(data))
                    }
                }
            } catch (t: Throwable) {
                if (t is SDKException) {
                    keyUpdateListener.onKeyUpdateFailure(t.code, t.message ?: "")
                }
                ExceptionHandler.handleException(t)
            }
        }
    }
}
