package com.wedevote.wdbook.tools.util

import android.text.TextUtils
import com.wedevote.wdbook.base.APP.Companion.get
import java.io.File

object PathUtils {
    val aPPPath: String
        get() = get().filesDir.absolutePath
    var rootPath: String? = null
        get() {
            if (TextUtils.isEmpty(field)) {
                field = aPPPath
                field = aPPPath + "/"
                checkOrMakeDir(field)
            }
            return field
        }
        private set

    private fun checkOrMakeDir(dirPath: String?): Boolean {
        val file = File(dirPath)
        if (!file.exists()) {
            file.mkdirs()
            return file.exists()
        }
        return true
    }

    var usersPath: String? = null
        get() {
            if (field == null) {
                field = rootPath + "Users/"
                checkOrMakeDir(field)
            }
            return field
        }
        private set
}