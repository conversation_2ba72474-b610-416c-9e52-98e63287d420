package com.wedevote.wdbook.tools.util

import java.text.SimpleDateFormat
import java.util.*

/***
 * @date 创建时间 2019-11-01 15:54
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON>
 * @description 单位换算格式化的工具类
 */
object UnitFormatUtil {
    const val SIZE_KB = 1024L
    const val SIZE_MB = SIZE_KB * 1024L
    const val SIZE_GB = SIZE_MB * 1024L

    val sdf_ymdhm = SimpleDateFormat("yyyy/MM/dd HH:mm")
    val sdf_ymd = SimpleDateFormat("yyyy/MM/dd")
    val sdfNotificationTime = SimpleDateFormat("yyyy年MM月dd日 a HH:mm")
    
    
    fun formatExpireTime(time: Long): String {
        var timeSeconds = time / 1000 % 60
        var timeMinutes = time / 60_000
        return "${timeMinutes}分${timeSeconds}秒"
    }
    
    /*时间戳转时间*/
    fun formatDate_ymdhm(time: Long): String {
        return SimpleDateFormat("yyyy/MM/dd HH:mm").format(Date(time))
    }

    fun getYearFromDate(date: Date): Int {
        val calendar = Calendar.getInstance()
        calendar.time = date
        return calendar.get(Calendar.YEAR)
    }

    /*时间戳转时间*/
    fun formatDate_ymd(time: Long): String {
        return sdf_ymd.format(Date(time))
    }
    
    /*格式化文件大小的单位的方法*/
    fun formatFileSize(byteSize: Long): String {
        return when (byteSize) {
            in 0..SIZE_KB -> byteSize.toString() + "B"
            
            in SIZE_KB..SIZE_MB -> trimDotEndZero(String.format("%.1f", byteSize.toDouble() / SIZE_KB)) + "K"
            
            in SIZE_MB..SIZE_GB -> trimDotEndZero(String.format("%.1f", byteSize.toDouble() / SIZE_MB)) + "M"

            else -> trimDotEndZero(String.format("%.1f", byteSize.toDouble() / SIZE_GB)) + "G"
        }
    }

    fun formatMediaPlayTime(timeMillisecond: Int): String {
        val t = timeMillisecond / 1000
        return if (t < 60) {
            "00:%02d".format(t)
        } else if (t < 3600) {
            "%02d:%02d".format(t / 60, t % 60)
        } else {
            "%02d:%02d:%02d".format(t / 3600, (t % 3600) / 60, t % 60)
        }
    }

    /*将分转换为元*/
    fun formatCentToYuan(cent: Int): String {
        return trimDotEndZero("%.1f".format(cent.toFloat() / 100f))
    }

    /*获取货币的符号*/
    fun getCurrencySymbol(unit: String?): String {
        return when (unit) {
            "USD" -> "$"
            "GBP" -> "￡"
            "HKD" -> "HK$"
            "RMB" -> "￥"
            "TWD" -> "NT$"
            else -> "$"
        }
    }

    fun formatDiscountText(discount: Float, formatStyle: String = "%.2f"): String {
        if (discount >= 1f || discount <= 0f) {
            return ""
        }
        return "${trimDotEndZero(formatStyle.format(discount * 10))}折"
    }

    /*格式化价格的显示*/
    fun formatPrice(currency: String?, price: Float): String {
        return getCurrencySymbol(currency) + String.format("%.2f", price)
    }

    fun trimDotEndZero(oriStr: String): String {
        var oriStr = oriStr
        if (oriStr.indexOf(".") > 0) {
            // 去掉多余的0
            oriStr = oriStr.replace("0+?$".toRegex(), "")
            // 如最后一位是.则去掉
            oriStr = oriStr.replace("[.]$".toRegex(), "")
        }
        return oriStr
    }

    fun maskPhoneNumber(phoneNumber: String): String {
        if (phoneNumber.length < 7) return phoneNumber // 手机号长度至少为7位
        val startIndex = (phoneNumber.length - 7) / 2 + 3
        val endIndex = startIndex + 3
        return phoneNumber.replaceRange(startIndex..endIndex, "****")
    }
}

fun main() {
    println(UnitFormatUtil.formatMediaPlayTime(5000))
    println(UnitFormatUtil.formatMediaPlayTime(15000))
    println(UnitFormatUtil.formatMediaPlayTime(65_000))
    println(UnitFormatUtil.formatMediaPlayTime(365_000))
    println(UnitFormatUtil.formatMediaPlayTime(3650_000))
    println(UnitFormatUtil.formatMediaPlayTime(36543_000))
}
