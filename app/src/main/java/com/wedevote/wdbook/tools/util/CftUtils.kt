package com.wedevote.wdbook.tools.util

import com.aquila.lib.log.KLog
import com.aquila.lib.tools.singleton.SPSingleton.Companion.get
import com.wedevote.wdbook.base.APP
import com.wedevote.wdbook.base.ExceptionHandler
import com.wedevote.wdbook.base.SDKSingleton
import com.wedevote.wdbook.constants.Constants
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.Response
import okhttp3.internal.EMPTY_REQUEST
import org.json.JSONObject
import p67.fox.Client
import java.io.IOException
import java.util.concurrent.TimeUnit

/***
 * Created by <PERSON> on 2023/6/20 11:54
 *
 * @description
 */
object CftUtils {
    private const val TAG = "Cft"

    private var cftConfig = ""
    private var cftStarted = false
    private var listener: ConnectListener? = null

    enum class CFTStatus {
        FAILED,
        CONNECTED,
        CONNECTING,
        DIRECT // 直连
    }

    var status: CFTStatus = CFTStatus.DIRECT

    /**
     * 检查是否可以直连
     */
    private suspend fun checkDirectConnection(app: APP): Boolean {
        val endpoint = SDKSingleton.sessionBl.wdBookEndpoint()
        if (canPing(endpoint)) {
            // 如果可以 ping 通，还需要进一步检测 canConnect 的原因是：
            // 如果域名被污染了，可能是可以 ping 通的，但是指向的可能不是我们自己的服务
            if (canConnect("$endpoint/testapi?r=${System.currentTimeMillis()}", false)) {
                // Reset the http client on KMM
                if (SDKSingleton.isConnectWithProxy) {
                    printCFTLog("Reset the http client on KMM to connect directly")
                    SDKSingleton.initializeSdk(app)
                }
                status = CFTStatus.DIRECT
                listener?.onConnectUpdate()
                get().putBoolean(SPKeyDefine.SP_LastDirectConnectionSuccess, true)
                return true
            }
        }
        get().putBoolean(SPKeyDefine.SP_LastDirectConnectionSuccess, false)
        return false
    }

    fun isValidConfig(server: String): Boolean {
        val cftConfig = SDKSingleton.apiServerBl.cftConfig
        return Client.isValidConfig(cftConfig, server)
    }

    private fun cftStartOrStopLogic(app: APP, onDirectConnectionAvailable: Boolean) {
        if (onDirectConnectionAvailable) {
            if (cftStarted) {
                printCFTLog("可以直连。CFT 已经启动，停止 CFT")
                stopProxyServer()
            } else {
                printCFTLog("可以直连。CFT 没有启动。不做后续操作")
            }
        } else {
            if (cftStarted) {
                printCFTLog("不能直连。CFT 已经启动。不做后续操作")
            } else {
                printCFTLog("不能直连。CFT 没有启动，启动 CFT")
                cftStarted = startProxyServer(app)
            }
        }
    }

    private fun launchOnIODispatcher(logic: suspend () -> Unit) {
        CoroutineScope(Dispatchers.IO).launch(ExceptionHandler.coroutineExceptionHandler) {
            logic()
        }
    }

    /**
     * App 启动（onCreate）后的 CFT 逻辑
     *
     * App 启动的时候，读取上次检测直连的结果（最初是默认不能直连）：
     * 1. 如果上次可以直连，则不启动 CFT。在后台检测是否能直连，如果能直连，不做操作；如果不能直连，启动 CFT
     * 2. 如果上次不能直连，则先启动 CFT。在后台检测是否能直连，如果能直连，停止 CFT；如果不能直连，不做操作
     */
    fun appStartCFTLogic(app: APP) {
        if (get().getBoolean(SPKeyDefine.SP_LastDirectConnectionSuccess, false)) {
            // 上次可以直连
            printCFTLog("App 启动时。上次可以直连，先不启动 CFT。再测试是否可以直连")
            launchOnIODispatcher {
                if (checkDirectConnection(app)) {
                    printCFTLog("App 启动时。上次可以直连。这次也可以直连。不做后续的操作")
                } else {
                    printCFTLog("App 启动时。上次可以直连。这次不能直连。启动 CFT")
                    startCFTWhenAppStarts(app)
                }
            }
        } else {
            // 上次（或者是最初的时候默认）不能直连
            printCFTLog("App 启动时。上次不能直连，先启动 CFT。再测试是否可以直连")
            launchOnIODispatcher {
                // 先启动 CFT
                startCFTWhenAppStarts(app)

                // 再检测能否直连
                printCFTLog("App 启动时。上次不能直连，先启动了 CFT。现在测试是否可以直连")
                if (checkDirectConnection(app)) {
                    printCFTLog("App 启动时。上次不能直连。这次可以直连。停止 CFT")
                    cftStartOrStopLogic(app, true)
                } else {
                    printCFTLog("App 启动时。上次不能直连。这次也不能直连。不做后续的操作")
                }
            }
        }
    }

    /**
     * App 启动（onCreate）后，不能（或因为上次不能）直连，启动 CFT
     */
    private suspend fun startCFTWhenAppStarts(app: APP) {
        printCFTLog("App 启动时。不能（或因为上次不能）直连。重新加载 CFT 参数")
        val reloadResult = SDKSingleton.apiServerBl.reloadCftConfig()
        printCFTLog("App 启动时。不能（或因为上次不能）直连。重新加载 CFT 参数的结果是：$reloadResult")
        if (!reloadResult.isSuccess()) {
            printCFTLog(
                "App 启动时。不能（或因为上次不能）直连。重新加载 CFT 参数失败，重置当天首次进入的时间为空",
                isError = true
            )
            DateTimeUtils.resetFirstIntoToday(SPKeyDefine.SP_LastCFTConfigReloadingTime)
        }
        cftStartOrStopLogic(app, false)
    }

    /**
     * App 从后台切到前台的 CFT 逻辑
     */
    fun backgroundToForegroundCFTLogic(app: APP) {
        printCFTLog("App 从后台切到前台时。先测试是否可以直连")
        launchOnIODispatcher {
            if (!checkDirectConnection(app)) {
                if (DateTimeUtils.isFirstIntoToday(SPKeyDefine.SP_LastCFTConfigReloadingTime)) {
                    printCFTLog("App 从后台切到前台时。不能直连。是当天的首次尝试重新加载 CFT 参数，重新加载 CFT 参数")
                    val reloadResult = SDKSingleton.apiServerBl.reloadCftConfig()
                    if (reloadResult.isUpdated()) {
                        printCFTLog("App 从后台切到前台时。不能直连。CFT 参数更新成功。重新启动 CFT")
                        stopProxyServer()
                        cftStarted = startProxyServer(app)
                    } else {
                        printCFTLog(
                            "App 从后台切到前台时。不能直连。重新加载 CFT 参数的结果是：$reloadResult",
                            isError = true
                        )
                        cftStartOrStopLogic(app, false)
                    }
                } else {
                    printCFTLog("App 从后台切到前台时。不能直连。不是当天的首次尝试重新加载 CFT 参数，不做后续的操作")
                }
            } else {
                printCFTLog("App 从后台切到前台时。可以直连")
                cftStartOrStopLogic(app, true)
            }
        }
    }

    /**
     * App 网络变化（有新的网络成为当前使用的网络）时的 CFT 逻辑
     */
    fun networkChangedCFTLogic(app: APP) {
        printCFTLog("App 网络变化（有新的网络成为当前使用的网络）时。先测试是否可以直连")
        launchOnIODispatcher {
            if (!checkDirectConnection(app)) {
                printCFTLog("App 网络变化（有新的网络成为当前使用的网络）时。不能直连")
                cftStartOrStopLogic(app, false)
            } else {
                printCFTLog("App 网络变化（有新的网络成为当前使用的网络）时。可以直连")
                cftStartOrStopLogic(app, true)
            }
        }
    }

    fun setListener(l: ConnectListener?) {
        listener = l
    }

    fun updateCode() {
        if (status == CFTStatus.CONNECTING || status == CFTStatus.DIRECT) {
            printCFTLog("使用二维码或切换二维码。${if (status == CFTStatus.CONNECTING) "CFT 正在启动" else "是直连模式"}，不做后续操作")
            listener?.onConnectUpdate()
            return
        }

        if (cftStarted) {
            printCFTLog("使用二维码或切换二维码。CFT 已启动，先停止 CFT")
            stopProxyServer()
        }

        listener?.onConnectUpdate()

        CoroutineScope(Dispatchers.IO).launch(ExceptionHandler.coroutineExceptionHandler) {
            printCFTLog("使用二维码或切换二维码。启动 CFT")
            cftStarted = startProxyServer(APP.get())
        }
    }

    private fun startProxyServer(app: APP): Boolean {
        status = CFTStatus.CONNECTING
        listener?.onConnectUpdate()
        var isSuccess = false
        val server = get().getString(SPKeyDefine.SP_SpeedUpServer) ?: ""
        try {
            val cftConfig = SDKSingleton.apiServerBl.cftConfig
            this.cftConfig = cftConfig
            printCFTLog("Start proxy server with CFT config = $cftConfig${if (server.isNotEmpty()) ", server from QR Code = $server" else ""}")
            if (server.isEmpty()) {
                Client.start(cftConfig)
            } else {
                Client.startWithOverride(cftConfig, server)
            }
            printCFTLog("The server started successfully, reset SDKSingleton to use proxy")
            SDKSingleton.initializeSdk(app)
            isSuccess = true
            status = CFTStatus.CONNECTED
            listener?.onConnectUpdate()

            // 设置 WebView 使用 Proxy
            WebViewUtils.setWebViewProxy()
        } catch (t: Throwable) {
            t.printStackTrace()
            printCFTLog("Failed to start proxy server, error : $t", isError = true)
            status = CFTStatus.FAILED
            listener?.onConnectUpdate()
        }

        setKMMCFTStarted(isSuccess)

        return isSuccess
    }

    private fun stopProxyServer() {
        Client.stop()
        SDKSingleton.initializeSdk(APP.get())
        cftStarted = false
        setKMMCFTStarted(false)

        // 取消 WebView 的 Proxy
        WebViewUtils.clearWebViewProxy()

        printCFTLog("Stop proxy server")
    }

    private fun canPing(endpoint: String): Boolean {
        printCFTLog("Check $endpoint can ping or not")
        val logBuf = StringBuilder()
        val lossRate = NetWorkUtils.getServerLossRate(endpoint, logBuf)
        val canPing = lossRate < 100
        printCFTLog("The loss rate for $endpoint is $lossRate%, $endpoint CAN${if (canPing) "" else " NOT"} ping")
        return canPing
    }

    private suspend fun canConnect(url: String, withProxy: Boolean): Boolean =
        withContext(Dispatchers.IO) {
            printCFTLog("Check url $url can connect ${if (withProxy) "with proxy" else "directly"} or not")

            val clientBuilder = OkHttpClient.Builder()
                .connectTimeout(10, TimeUnit.SECONDS)
                .readTimeout(20, TimeUnit.SECONDS)

            if (withProxy) {
                clientBuilder.proxy(Client.proxy())
            }

            val client = clientBuilder.build()
            val request = Request.Builder()
                .post(EMPTY_REQUEST)
                .url(url)
                .header("X-WS-Sign", Constants.TEST_API_SIGN)
                .build()

            var response: Response? = null
            try {
                response = client.newCall(request).execute()
                if (response.isSuccessful) {
                    val responseBody = response.body?.string() ?: ""
                    val json = JSONObject(responseBody)
                    val errno = json.optInt("errno", -1)
                    if (errno == 200) {
                        printCFTLog("Successfully connected to $url with correct response")
                        return@withContext true
                    } else {
                        printCFTLog(
                            "Failed to connect to $url. Response code: ${response.code}",
                            isError = true
                        )
                        return@withContext false
                    }
                } else {
                    printCFTLog(
                        "Failed to connect to $url. Response code: ${response.code}",
                        isError = true
                    )
                    return@withContext false
                }
            } catch (e: IOException) {
                printCFTLog(
                    "IOException when trying to connect to $url, exception $e",
                    isError = true
                )
                return@withContext false
            } finally {
                response?.close() // Ensure the response is closed to avoid resource leaks
            }
        }

    fun recordAPIFailed() {
        // CFT 模式下的统计，非 CFT 模式下的不统计
        if (cftStarted) {
            val cftAPIFailedCount = getCFTAPIFailedCount() + 1
            setCFTAPIFailedCount(cftAPIFailedCount)
            printCFTLog(
                "CFT 模式下 API 请求发生特定《SOCKS: Host unreachable》错误，失败次数加 1。当前失败次数为 $cftAPIFailedCount 次",
                isError = true
            )
        }
    }

    private fun setKMMCFTStarted(value: Boolean) {
        SDKSingleton.apiServerBl.cftStarted = value
    }

    private fun getCFTAPISuccessCount(): Int {
        return SDKSingleton.apiServerBl.cftSuccessAPICount
    }

    private fun resetCFTAPISuccessCount() {
        SDKSingleton.apiServerBl.cftSuccessAPICount = 0
    }

    private fun getCFTAPIFailedCount(): Int {
        return get().getInt(SPKeyDefine.SP_CFTFailedAPICount, 0)
    }

    private fun setCFTAPIFailedCount(count: Int) {
        if (count >= 0) {
            get().putInt(
                SPKeyDefine.SP_CFTFailedAPICount,
                count
            )
        }
    }

    /**
     * 上次的网络请求成功总数和网络请求失败总数（CFT 模式下的统计）
     * 并重置 CFT 模式下的 API 请求失败次数和成功次数为 0
     */
    fun reportCFTSuccessFailedCount() {
        val successCount = getCFTAPISuccessCount()
        val failedCount = getCFTAPIFailedCount()

        if (successCount > 0 || failedCount > 0) {
            printCFTLog("CFT 模式下的 API 请求失败次数和成功次数：成功次数为 $successCount 次，失败次数为 $failedCount 次")
            resetAPIFailSuccessCount()
        } else {
            printCFTLog("CFT 模式下的 API 请求的失败次数和成功次数都为 0")
        }
    }

    private fun resetAPIFailSuccessCount() {
        resetCFTAPISuccessCount()
        setCFTAPIFailedCount(0)
        printCFTLog("重置 CFT 模式下的 API 请求失败次数和成功次数为 0")
    }

    private fun printCFTLog(message: String, isError: Boolean = false) {
        val packageName = APP.get().packageName

        if (packageName == Constants.WD_BOOK_PACKAGE) {
            if (SDKSingleton.sessionBl.showCftLog) {
                println("$TAG ${if (isError) "ERROR " else ""}: $message")
            }
        } else if (packageName == Constants.WD_BOOK_PACKAGE_TEST) {
            if (isError) {
                KLog.e(message)
            } else {
                KLog.i(message)
            }
        }
    }

    interface ConnectListener {
        fun onConnectUpdate()
    }
}
