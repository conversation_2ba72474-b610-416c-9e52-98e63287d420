package com.wedevote.wdbook.tools.util

import android.animation.AnimatorListenerAdapter
import android.animation.ObjectAnimator
import android.animation.ValueAnimator
import android.view.View
import android.view.animation.LinearInterpolator
import com.aquila.lib.log.KLog

/***
 * @date 创建时间 2020/7/4 11:32
 * <AUTHOR> <PERSON><PERSON>
 * @description
 */
object AnimatorUtil {

    /*旋转动画*/
    fun rotateAnimator(
        view: View,
        startDegree: Float,
        endDegrees: Float,
        duration: Long = 300L,
        animatorListenerAdapter: AnimatorListenerAdapter? = null
    ) {
        val rotateAnim = ObjectAnimator.ofFloat(view, "rotation", startDegree, endDegrees)
        rotateAnim.duration = duration
        if (animatorListenerAdapter != null) {
            rotateAnim.addListener(animatorListenerAdapter)
        }
        rotateAnim.interpolator = LinearInterpolator()
        rotateAnim.start()
    }

    fun valueHeightAnimator(view: View, startHeight: Int, endHeight: Int) {
        val valueAnim = ValueAnimator.ofInt(startHeight, endHeight)
        valueAnim.duration = 300
        valueAnim.addUpdateListener { animation ->
            val params = view.layoutParams
            params.height = animation.animatedValue as Int
            view.layoutParams = params
            KLog.e("ValueAnimator = ${animation.animatedValue}")
        }
        valueAnim.start()
    }
}
