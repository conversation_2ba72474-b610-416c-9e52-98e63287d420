package com.wedevote.wdbook.tools.upgrade

import android.os.Bundle
import android.view.Gravity
import android.view.View
import android.view.WindowManager
import android.widget.Button
import android.widget.TextView
import androidx.fragment.app.FragmentActivity
import com.aquila.lib.base.OnViewClickListener
import com.aquila.lib.tools.singleton.SPSingleton
import com.aquila.lib.tools.util.ScreenUtil
import com.wedevote.wdbook.R
import com.wedevote.wdbook.entity.UpgradeEntity
import com.wedevote.wdbook.tools.util.APPUtil
import com.wedevote.wdbook.tools.util.SPKeyDefine
import com.wedevote.wdbook.ui.dialogs.BaseDialog

/***
 * @date 创建时间 2020/9/30 16:12
 * <AUTHOR> W.YuLong
 * @description
 */
class NewVersionDialog(val activity: FragmentActivity) : BaseDialog(activity), View.OnClickListener {
    lateinit var descTextView: TextView
    lateinit var cancelButton: Button
    lateinit var ignoreButton: Button
    lateinit var okButton: Button

    companion object {
        fun getAppUpgradeInfo(entity: UpgradeEntity): String {
            return APPUtil.getVersionName() + APPUtil.getVersionCode().toString() + entity.getUpgradeInfo()
        }
    }

    var onViewClickListener: OnViewClickListener? = null
    var upgradeEntity: UpgradeEntity? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.dialog_new_version_layout)
        descTextView = findViewById(R.id.new_version_describute_TextView)
        cancelButton = findViewById(R.id.new_version_cancel_Button)
        okButton = findViewById(R.id.new_version_ok_Button)
        ignoreButton = findViewById(R.id.new_version_ignore_Button)

        cancelButton.setOnClickListener(this)
        okButton.setOnClickListener(this)

        configDialog(Gravity.CENTER)
    }

    override fun configDialog(gravity: Int) {
        val wl = window!!.attributes
        wl.gravity = gravity
        when (gravity) {
            Gravity.BOTTOM -> {
                window!!.setWindowAnimations(com.aquila.lib.dialog.R.style.bottomDialogWindowAnim)
            }
            Gravity.CENTER -> {
                window!!.setWindowAnimations(com.aquila.lib.dialog.R.style.centerDialogWindowAnim)
            }
        }
        wl.width = (ScreenUtil.getScreenWidth() * 0.8f).toInt()
        wl.height = WindowManager.LayoutParams.WRAP_CONTENT
        window!!.attributes = wl
        window!!.setBackgroundDrawableResource(android.R.color.transparent)
    }

    override fun onClick(v: View?) {
        when (v) {
            cancelButton -> {
                dismiss()
            }
            okButton -> {
                upgradeEntity?.let {
                    onViewClickListener?.onClickAction(v, "ok", upgradeEntity)

                    if (it.updateType != 1) {
                        dismiss()
                    }
                }
            }
        }
    }

    fun initUI(entity: UpgradeEntity, hideIgnore: Boolean = false) {
        upgradeEntity = entity
        descTextView.text = entity.versionDescription
        when (entity.updateType) {
            1 -> {
                ignoreButton.visibility = View.GONE
                cancelButton.visibility = View.GONE
                setCancelable(false)
                setCanceledOnTouchOutside(false)
            }
            2 -> {
                if (!hideIgnore) {
                    ignoreButton.visibility = View.VISIBLE
                    ignoreButton.setOnClickListener {
                        SPSingleton.get().putString(SPKeyDefine.SP_IgnoreUpdateVersion, getAppUpgradeInfo(entity))
                        dismiss()
                    }
                }
            }
        }
    }
}
