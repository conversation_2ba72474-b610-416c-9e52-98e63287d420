package com.wedevote.wdbook.tools

import android.util.Base64
import java.io.ByteArrayOutputStream
import java.io.File
import java.nio.charset.StandardCharsets
import java.security.MessageDigest
import java.security.NoSuchAlgorithmException
import java.util.zip.ZipFile
import javax.crypto.Cipher
import javax.crypto.spec.SecretKeySpec


object BookDecode {
    private var bookFilePath = ""
    private var epubKey = ""
    fun initBook(bookPath: String, code: String, user: String): Int {
        if (File(bookPath).exists()) {
            bookFilePath = bookPath
            epubKey = try {
                getEncryptionKey(user, code)
            } catch (e: Exception) {
                ""
            }
        }
        return 0
    }

    private fun decryptEpub(original: ByteArray, key: String): String {
        try {
            val cipher = Cipher.getInstance("AES/ECB/NoPadding")
            val keySpec = SecretKeySpec(key.toByteArray(StandardCharsets.UTF_8), "AES")
            cipher.init(Cipher.DECRYPT_MODE, keySpec)
            var decryptedBytes = cipher.doFinal(original)
            // 将字节数组尾部的 0 去掉
            var endIndex = decryptedBytes.size
            while (endIndex > 0 && decryptedBytes[endIndex - 1].toInt() == 0) {
                endIndex--
            }
            decryptedBytes = decryptedBytes.copyOf(endIndex)
            return String(decryptedBytes, StandardCharsets.UTF_8)
        } catch (e: Exception) {
            return ""
        }
    }

    private fun getEncryptionKey(userCode: String, bookCode: String): String {
        val keyForKey = generateKey(userCode)
        val keySpec = SecretKeySpec(keyForKey.toByteArray(StandardCharsets.UTF_8), "AES")

        try {
            val cipher = Cipher.getInstance("AES/ECB/NoPadding")
            cipher.init(Cipher.DECRYPT_MODE, keySpec)
            var decryptedBytes = cipher.doFinal(Base64.decode(bookCode, Base64.DEFAULT))
            // 将字节数组尾部的 0 去掉
            var endIndex = decryptedBytes.size
            while (endIndex > 0 && decryptedBytes[endIndex - 1].toInt() == 0) {
                endIndex--
            }
            decryptedBytes = decryptedBytes.copyOf(endIndex)
            return String(decryptedBytes, StandardCharsets.UTF_8)
        } catch (e: Exception) {
            return ""
        }
    }

    private fun getFileBytes(fileName: String): ByteArray? {
        var name: String
        var fileContent: ByteArray? = null
        if (!(File(bookFilePath).exists())) {
            return null
        }
        try {
            val zipFile = ZipFile(bookFilePath)
            val entries = zipFile.entries()

            while (entries.hasMoreElements()) {
                val entry = entries.nextElement()
                name = entry.name
                if (!name.endsWith(fileName)) {
                    continue
                }
                val ins = zipFile.getInputStream(entry)
                val byteArrayOutputStream = ByteArrayOutputStream()
                val buffer = ByteArray(1024)
                var len: Int
                while ((ins.read(buffer).also { len = it }) != -1) {
                    byteArrayOutputStream.write(buffer, 0, len)
                }
                fileContent = byteArrayOutputStream.toByteArray()
                byteArrayOutputStream.close()
                ins.close()
            }
            zipFile.close()
        } catch (e: Exception) {
            return null
        }
        return fileContent
    }

    fun getBookText(pagePath: String): String {
        val encodedBytes = getFileBytes(pagePath) ?: return ""
        val last = pagePath.lastIndexOf('/')
        val name = if (last > 0) {
            pagePath.substring(last + 1)
        } else {
            pagePath
        }
        val fileKey = generateKey(epubKey + name)
        return decryptEpub(encodedBytes, fileKey)
    }

    fun getFileText(fileName: String): String {
        val encodedBytes = getFileBytes(fileName)
        return if (encodedBytes == null) {
            ""
        } else {
            String(encodedBytes)
        }
    }

    private fun generateKey(text: String): String {
        try {
            val md = MessageDigest.getInstance("MD5")
            md.update(text.toByteArray())
            val hash = md.digest()

            // 对生成的16字节数组进行补零操作
            val hex = StringBuilder(hash.size * 2)
            for (b in hash) {
                var i = b.toInt()
                if (i < 0) {
                    i += 256
                } else if (i < 16) {
                    hex.append("0")
                }
                hex.append(Integer.toHexString(i))
            }
            var md5Str = hex.toString()
            if (md5Str.length > 16) {
                md5Str = md5Str.substring(0, 16)
            }
            return md5Str
        } catch (e: NoSuchAlgorithmException) {
            return ""
        }
    }
}