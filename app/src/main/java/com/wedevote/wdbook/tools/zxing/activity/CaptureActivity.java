package com.wedevote.wdbook.tools.zxing.activity;

import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Rect;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.text.TextUtils;
import android.view.SurfaceHolder;
import android.view.SurfaceHolder.Callback;
import android.view.SurfaceView;
import android.view.View;

import com.aquila.lib.tools.util.ToastUtil;
import com.google.zxing.BarcodeFormat;
import com.google.zxing.BinaryBitmap;
import com.google.zxing.DecodeHintType;
import com.google.zxing.Result;
import com.google.zxing.common.HybridBinarizer;
import com.google.zxing.qrcode.QRCodeReader;
import com.wedevote.wdbook.R;
import com.wedevote.wdbook.base.RootActivity;
import com.wedevote.wdbook.tools.util.IntentConstants;
import com.wedevote.wdbook.tools.zxing.camera.CameraManager;
import com.wedevote.wdbook.tools.zxing.decoding.CaptureActivityHandler;
import com.wedevote.wdbook.tools.zxing.decoding.RGBLuminanceSource;
import com.wedevote.wdbook.tools.zxing.view.ViewfinderView;
import com.wedevote.wdbook.ui.dialogs.LoadingProgressDialog;

import java.io.IOException;
import java.io.InputStream;
import java.util.Hashtable;
import java.util.Vector;

/**
 * Initial the camera
 *
 * <AUTHOR>
 */
public class CaptureActivity extends RootActivity implements Callback, View.OnClickListener {

    private static final int REQUEST_CODE_SCAN_GALLERY = 100;

    private CaptureActivityHandler handler;
    private ViewfinderView viewfinderView;
    private View albumView; // 相册
    private boolean isFlashOn = false;
    private boolean hasSurface;
    private Vector<BarcodeFormat> decodeFormats;
    private String characterSet;
    private LoadingProgressDialog mProgress;
    private String photo_path;

    /**
     * Called when the activity is first created.
     */
    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_scanner);
        CameraManager.init(getApplication());
        viewfinderView = (ViewfinderView) findViewById(R.id.viewfinder_content);

        albumView = findViewById(R.id.scan_album_TextView);
        albumView.setOnClickListener(this);

        hasSurface = false;
    }

    @Override
    public void onClick(View v) {
        if (v == albumView) {
            Intent innerIntent = new Intent(Intent.ACTION_GET_CONTENT);
            innerIntent.setType("image/*");
            startActivityForResult(innerIntent, REQUEST_CODE_SCAN_GALLERY);
        }
    }

    @Override
    protected void onActivityResult(final int requestCode, int resultCode, Intent data) {
        if (resultCode == RESULT_OK && requestCode == REQUEST_CODE_SCAN_GALLERY) {
            handleAlbumPic(data);
        }
        super.onActivityResult(requestCode, resultCode, data);
    }

    private void handleAlbumPic(Intent data) {
        //获取选中图片的路径
        mProgress = new LoadingProgressDialog(this);
        mProgress.setCancelable(false);
        mProgress.show();
        mProgress.setTitleText(getString(R.string.toast_waiting_progress));

        new Thread(() -> {
            final Result result = scanningImage(data.getData());
            runOnUiThread(() -> {
                if (mProgress.isShowing()) {
                    mProgress.dismiss();
                }
                if (result != null) {
                    Intent resultIntent = new Intent();
                    Bundle bundle = new Bundle();
                    bundle.putString(IntentConstants.EXTRA_qrCode, result.getText());

                    resultIntent.putExtras(bundle);
                    setResult(RESULT_OK, resultIntent);
                    onBackPressed();
                } else {
                    ToastUtil.showToastShort(getString(R.string.toast_progress_failed));
                }
            });
        }).start();
    }

    /**
     * 扫描二维码图片的方法
     *
     * @param path
     * @return
     */
    public Result scanningImage(Uri uri) {
        if (uri == null) {
            return null;
        }

        Hashtable<DecodeHintType, String> hints = new Hashtable<>();
        hints.put(DecodeHintType.CHARACTER_SET, "UTF8"); //设置二维码内容的编码

        BitmapFactory.Options options = new BitmapFactory.Options();

        Rect emptyRect = new Rect();
        InputStream stream;
        Bitmap scanBitmap;
        try {
            stream = getContentResolver().openInputStream(uri);
            scanBitmap = BitmapFactory.decodeStream(stream, emptyRect, options);
            if (stream != null) {
                stream.close();
            }
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }

        if (scanBitmap == null) {
            return null;
        }

        RGBLuminanceSource source = new RGBLuminanceSource(scanBitmap);
        BinaryBitmap bbm = new BinaryBitmap(new HybridBinarizer(source));
        QRCodeReader reader = new QRCodeReader();
        try {
            return reader.decode(bbm, hints);
        } catch (Exception e) {
            e.printStackTrace();
        }

        return null;
    }

    @Override
    protected void onResume() {
        super.onResume();
        SurfaceView surfaceView = (SurfaceView) findViewById(R.id.scanner_view);
        SurfaceHolder surfaceHolder = surfaceView.getHolder();
        if (hasSurface) {
            initCamera(surfaceHolder);
        } else {
            surfaceHolder.addCallback(this);
            surfaceHolder.setType(SurfaceHolder.SURFACE_TYPE_PUSH_BUFFERS);
        }
        decodeFormats = null;
        characterSet = null;
    }

    @Override
    protected void onPause() {
        super.onPause();
        if (handler != null) {
            handler.quitSynchronously();
            handler = null;
        }
        CameraManager.get().closeDriver();
    }

    /**
     * Handler scan result
     *
     * @param result
     */
    public void handleDecode(Result result) {
        String resultString = result.getText();
        if (TextUtils.isEmpty(resultString)) {
            ToastUtil.showToastShort(getString(R.string.toast_progress_failed));
        } else {
            Intent resultIntent = new Intent();
            Bundle bundle = new Bundle();
            bundle.putString(IntentConstants.EXTRA_qrCode, resultString);
            resultIntent.putExtras(bundle);
            setResult(RESULT_OK, resultIntent);
        }
        onBackPressed();
    }

    private void initCamera(SurfaceHolder surfaceHolder) {
        try {
            CameraManager.get().openDriver(surfaceHolder);
        } catch (IOException ioe) {
            return;
        } catch (RuntimeException e) {
            return;
        }
        if (handler == null) {
            handler = new CaptureActivityHandler(this, decodeFormats,
                    characterSet);
        }
    }

    @Override
    public void surfaceChanged(SurfaceHolder holder, int format, int width,
                               int height) {

    }

    @Override
    public void surfaceCreated(SurfaceHolder holder) {
        if (!hasSurface) {
            hasSurface = true;
            initCamera(holder);
        }
    }

    @Override
    public void surfaceDestroyed(SurfaceHolder holder) {
        hasSurface = false;
    }

    public Handler getHandler() {
        return handler;
    }

    public void drawViewfinder() {
        viewfinderView.invalidate();
    }
}