package com.wedevote.wdbook;


import static androidx.test.espresso.Espresso.onView;
import static androidx.test.espresso.action.ViewActions.click;
import static androidx.test.espresso.action.ViewActions.closeSoftKeyboard;
import static androidx.test.espresso.action.ViewActions.replaceText;
import static androidx.test.espresso.matcher.ViewMatchers.withId;

import androidx.test.espresso.contrib.RecyclerViewActions;
import androidx.test.rule.ActivityTestRule;

import com.wedevote.wdbook.ui.home.HomeMainActivity;

import org.junit.FixMethodOrder;
import org.junit.Rule;
import org.junit.Test;
import org.junit.runners.MethodSorters;

/***
 * @date 创建时间 2020-02-26 16:34
 * <AUTHOR> <PERSON>.<PERSON>
 * @description
 */
@FixMethodOrder(MethodSorters.NAME_ASCENDING)
public class HomeActivityTest {


    @Rule
    public ActivityTestRule<HomeMainActivity> mActivityTestRule = new ActivityTestRule(HomeMainActivity.class);


    @Test
    public void homeTest01() throws InterruptedException {
//        onView(withId(R.id.bottom_tab_read_Layout)).perform(click());
//        onView(withId(R.id.bottom_tab_book_store_Layout)).perform(click());
//        onView(withId(R.id.bottom_tab_mine_Layout)).perform(click());
        onView(withId(R.id.bottom_tab_book_shelf_Layout)).perform(click());
        onView(withId(R.id.book_shelf_data_RecyclerView)).perform(RecyclerViewActions.scrollToPosition(8), click());
    }


    @Test
    public void bookShelfEdit() {
        onView(withId(R.id.bottom_tab_book_shelf_Layout)).perform(click());
        onView(withId(R.id.book_shelf_edit_TextView)).perform(click());
        onView(withId(R.id.book_group_cancel_Button)).perform(click());
        onView(withId(R.id.book_shelf_edit_TextView)).perform(click());
        onView(withId(R.id.book_group_data_RecyclerView)).perform(RecyclerViewActions.actionOnItemAtPosition(0, click()));
        onView(withId(R.id.create_book_group_input_EditText))
                .perform(replaceText("内容测试"), closeSoftKeyboard());
        onView(withId(R.id.create_book_group_ok_Button)).perform(click());
        onView(withId(R.id.book_group_cancel_Button)).perform(click());
        onView(withId(R.id.book_group_data_RecyclerView)).perform(RecyclerViewActions.actionOnItemAtPosition(1, click()));
    }





}
