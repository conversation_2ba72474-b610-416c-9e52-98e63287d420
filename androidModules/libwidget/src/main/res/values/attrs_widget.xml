<?xml version="1.0" encoding="utf-8"?>
<resources>

    <attr name="attr_padding" format="dimension" />
    <attr name="attr_padding_left" format="dimension" />
    <attr name="attr_padding_right" format="dimension" />
    <attr name="attr_padding_top" format="dimension" />
    <attr name="attr_padding_bottom" format="dimension" />


    <attr name="attr_dot_radius" format="dimension" />
    <attr name="attr_dot_color" format="color" />


    <attr name="attr_text" format="string|reference" />
    <attr name="attr_text_background" format="reference" />
    <attr name="attr_text_color" format="color|reference" />
    <attr name="attr_text_size" format="dimension|reference" />
    <attr name="attr_text_single_line" format="boolean" />
    <attr name="attr_text_max_line" format="integer" />
    <attr name="attr_text_max_width" format="dimension" />
    <attr name="attr_text_style_is_bold" format="boolean" />
    <attr name="attr_text_line_spacing_multiplier" format="float" />
    <attr name="attr_text_line_spacing_extra" format="dimension" />
    <attr name="attr_child_view_margin_start" format="dimension" />
    <attr name="attr_inner_margin_size" format="dimension" />

    <attr name="attr_layout_orientation" format="enum">
        <enum name="HORIZONTAL" value="0" />
        <enum name="VERTICAL" value="1" />
    </attr>

    <attr name="attr_fit_type" format="enum">
        <enum name="FIT_NONE" value="0" />
        <enum name="FIT_WIDTH" value="1" />
        <enum name="FIT_HEIGHT" value="2" />
        <enum name="FIT_IMG_SIZE" value="3" />
        <enum name="FIT_SQUARE" value="4" />
    </attr>
    <attr name="attr_aspect_ratio" format="string" />
    <attr name="attr_max_width" format="dimension" />
    <attr name="attr_max_height" format="dimension" />
    <attr name="attr_min_width" format="dimension" />
    <attr name="attr_min_height" format="dimension" />

    <!--******************************************************************************************** -->


    <declare-styleable name="StokeTextView">
        <attr name="attr_border_color" format="color" />
        <attr name="attr_border_size" format="dimension" />
        <attr name="attr_border_enable" format="boolean" />
    </declare-styleable>

    <!--TabLinearLayout-->
    <declare-styleable name="TabLinearLayout">
        <attr name="app_indicator_height" format="dimension" />
        <attr name="app_indicator_width" format="dimension" />
        <attr name="app_indicator_color" format="color" />
        <attr name="app_indicator_position" format="integer" />
        <attr name="app_indicator_margin_bottom" format="dimension" />
    </declare-styleable>


    <declare-styleable name="AdaptiveImageView">
        <attr name="attr_fit_type" />
        <attr name="attr_round_width" format="dimension" />
        <attr name="attr_round_height" format="dimension" />
        <attr name="attr_aspect_ratio" />
        <attr name="attr_max_width" />
        <attr name="attr_max_height" />
        <attr name="attr_min_width" />
        <attr name="attr_min_height" />
        <attr name="attr_mask_color" format="color" />
    </declare-styleable>


    <declare-styleable name="DotView">
        <attr name="attr_dot_radius" />
        <attr name="attr_dot_color" />
    </declare-styleable>


    <declare-styleable name="DotImageView">
        <attr name="attr_is_show_dot" format="boolean" />
        <attr name="attr_dot_radius" />
        <attr name="attr_dot_color" />
        <attr name="attr_dot_position" format="enum">
            <enum name="DOT_TOP_RIGHT" value="0" />
            <enum name="DOT_BOTTOM_RIGHT" value="1" />
            <enum name="DOT_BOTTOM_LEFT" value="2" />
            <enum name="DOT_TOP_LEFT" value="4" />
            <enum name="DOT_CENTER" value="8" />
        </attr>

        <attr name="attr_dot_margin_left" format="dimension" />
        <attr name="attr_dot_margin_right" format="dimension" />
        <attr name="attr_dot_margin_top" format="dimension" />
        <attr name="attr_dot_margin_bottom" format="dimension" />
        <attr name="attr_dot_margin" format="dimension" />

    </declare-styleable>


    <!--倾斜45度显示的textview-->
    <declare-styleable name="TriangleTextView">
        <attr name="attr_triangle_bg_color" format="color|reference"></attr>
        <attr name="attr_ratio" format="float"></attr>
    </declare-styleable>


    <declare-styleable name="CustomViewPager">
        <attr name="attr_layout_orientation" />
        <attr name="attr_is_slide_able" format="boolean"></attr>
    </declare-styleable>


    <declare-styleable name="GroupSideAlignLayout">
        <attr name="attr_left_text_size" format="dimension" />
        <attr name="attr_left_text" format="string|reference" />
        <attr name="attr_left_text_color" format="color" />
        <attr name="attr_left_max_width" format="dimension" />
        <attr name="attr_left_single_line" format="boolean" />
        <attr name="attr_left_ellipsize" format="enum">
            <enum name="start" value="0" />
            <enum name="middle" value="1" />
            <enum name="end" value="2" />
            <enum name="marquee" value="3" />
        </attr>

        <attr name="attr_right_text" format="string|reference" />
        <attr name="attr_right_text_size" format="dimension" />
        <attr name="attr_right_text_color" format="color" />
        <attr name="attr_right_max_width" format="dimension" />
        <attr name="attr_right_single_line" format="boolean" />
        <attr name="attr_left_layout_gravity" format="enum">
            <enum name="center_vertical" value="0" />
            <enum name="bottom" value="1" />
            <enum name="top" value="2" />
        </attr>

        <attr name="attr_right_ellipsize" format="enum">
            <enum name="end" value="0" />
            <enum name="start" value="1" />

        </attr>

        <attr name="attr_right_layout_gravity" format="enum">
            <enum name="center_vertical" value="0" />
            <enum name="bottom" value="1" />
            <enum name="top" value="2" />

        </attr>
    </declare-styleable>


    <!--新版自定义的RecyclerView-->
    <declare-styleable name="CustomRecyclerView">
        <attr name="attr_span_count" format="integer"></attr>
        <attr name="attr_recycler_view_max_height" format="dimension"></attr>
        <attr name="attr_item_divide_line_color" format="color" />
        <attr name="attr_item_divide_line_size" format="dimension" />

        <attr name="attr_item_margin" format="dimension" />
        <attr name="attr_is_expanded" format="boolean" />
        <attr name="attr_start_item_margin" format="dimension" />
        <attr name="attr_end_item_margin" format="dimension" />

        <attr name="attr_divide_left_margin" format="dimension" />
        <attr name="attr_divide_right_margin" format="dimension" />
        <attr name="attr_divide_top_margin" format="dimension" />
        <attr name="attr_divide_bottom_margin" format="dimension" />


        <attr name="attr_item_decoration_type" format="enum">
            <enum name="NONE" value="0"></enum>
            <enum name="ITEM_DIVIDE_LNE" value="1"></enum>
            <enum name="ITEM_SPACE" value="2"></enum>
        </attr>

        <attr name="attr_flex_direction" format="enum">
            <enum name="row" value="0"></enum>
            <enum name="row_reverse" value="1"></enum>
            <enum name="column" value="2"></enum>
            <enum name="column_reverse" value="3"></enum>
        </attr>

        <attr name="attr_flex_wrap" format="enum">
            <enum name="nowrap" value="0" />
            <enum name="wrap" value="1" />
            <enum name="wrap_reverse" value="2" />
        </attr>

        <attr name="attr_layout_style" format="enum">
            <enum name="list_vertical" value="0"></enum>
            <enum name="list_horizontal" value="1"></enum>
            <enum name="grid_vertical" value="2"></enum>
            <enum name="grid_horizontal" value="3"></enum>
            <enum name="staggered_vertical" value="4"></enum>
            <enum name="staggered_horizontal" value="5"></enum>
            <enum name="flexbox" value="6"></enum>
            <enum name="other" value="99"></enum>
        </attr>

    </declare-styleable>


    <declare-styleable name="GroupTwoTextLayout">
        <attr name="attr_first_text" format="string|reference" />
        <attr name="attr_first_text_size" format="dimension" />
        <attr name="attr_first_text_color" format="color" />
        <attr name="attr_first_text_bold" format="boolean" />


        <attr name="attr_first_text_gravity" format="enum">
            <enum name="center_horizontal" value="0" />
            <enum name="left" value="1" />
            <enum name="right" value="2" />
            <enum name="center" value="3" />
            <enum name="center_vertical" value="4" />
        </attr>

        <attr name="attr_first_text_width" format="dimension" />
        <attr name="attr_first_text_height" format="dimension" />


        <attr name="attr_layout_orientation" />

        <attr name="attr_is_linkage" format="boolean" />

        <attr name="attr_inner_margin_size" />

        <attr name="attr_second_text_width" format="dimension" />
        <attr name="attr_second_text_height" format="dimension" />

        <attr name="attr_second_text_gravity" format="enum">
            <enum name="center_horizontal" value="0" />
            <enum name="left" value="1" />
            <enum name="right" value="2" />
            <enum name="center" value="3" />
            <enum name="center_vertical" value="4" />
        </attr>

        <attr name="attr_second_text" format="string|reference" />
        <attr name="attr_second_text_bold" format="boolean" />
        <attr name="attr_second_hint_text" format="string" />
        <attr name="attr_second_text_size" format="dimension" />
        <attr name="attr_second_text_color" format="color" />
    </declare-styleable>


    <!--GroupImageTextLayout的自定义相关属性-->
    <declare-styleable name="GroupImageTextLayout">
        <attr name="attr_image_src" format="reference" />
        <attr name="attr_image_width" format="dimension" />
        <attr name="attr_image_height" format="dimension" />
        <attr name="attr_image_background" format="color|reference" />
        <attr name="attr_image_src_tint" format="color|reference" />
        <attr name="attr_scale_type" format="enum">
            <enum name="MATRIX" value="0" />
            <enum name="FIT_XY" value="1" />
            <enum name="FIT_START" value="2" />
            <enum name="FIT_CENTER" value="3" />
            <enum name="FIT_END" value="4" />
            <enum name="CENTER" value="5" />
            <enum name="CENTER_CROP" value="6" />
            <enum name="CENTER_INSIDE" value="7" />
        </attr>
        <attr name="attr_text_margin_image_size" format="dimension|reference" />

        <attr name="attr_child_view_margin_start" />
        <attr name="attr_text" />
        <attr name="attr_text_color" />
        <attr name="attr_text_size" />
        <attr name="attr_text_background" />
        <attr name="attr_text_single_line" />
        <attr name="attr_text_max_line" />
        <attr name="attr_text_max_width" />
        <attr name="attr_text_style_is_bold" />
        <attr name="attr_text_line_spacing_multiplier" />
        <attr name="attr_text_line_spacing_extra" />

        <attr name="attr_is_selected" format="boolean" />

        <attr name="attr_text_gravity" format="enum">
            <enum name="center_horizontal" value="0" />
            <enum name="left" value="1" />
            <enum name="right" value="2" />
            <enum name="center" value="3" />
            <enum name="center_vertical" value="4" />
        </attr>

        <attr name="attr_parent_orientation" format="enum">
            <enum name="IMAGE_LEFT_TEXT_RIGHT" value="0" />
            <enum name="IMAGE_TOP_TEXT_BOTTOM" value="1" />
            <enum name="IMAGE_RIGHT_TEXT_LEFT" value="2" />
            <enum name="IMAGE_BOTTOM_TEXT_TOP" value="3" />
            <enum name="IMAGE_RIGHT_TEXT_LEFT_SIDE_ALIGN" value="4" />
        </attr>
    </declare-styleable>

    <declare-styleable name="GroupProgressLayout">
        <attr name="attr_text" />
        <attr name="attr_text_color" />
        <attr name="attr_text_size" />
        <attr name="attr_text_single_line" />
        <attr name="attr_text_max_line" />
        <attr name="attr_text_max_width" />
        <attr name="attr_text_style_is_bold" />
        <attr name="attr_text_line_spacing_multiplier" />
        <attr name="attr_text_line_spacing_extra" />
        <attr name="attr_text_background" />

        <attr name="attr_progress_style" format="reference" />
        <attr name="attr_progress_width" format="dimension" />
        <attr name="attr_progress_height" format="dimension" />
        <attr name="attr_child_view_margin_start" />
        <attr name="attr_progress_margin_text" format="dimension" />

        <attr name="attr_progress_location" format="enum">
            <enum name="PROGRESS_LEFT_TEXT_RIGHT" value="0" />
            <enum name="PROGRESS_TOP_TEXT_BOTTOM" value="1" />
            <enum name="PROGRESS_RIGHT_TEXT_LEFT" value="2" />
            <enum name="PROGRESS_BOTTOM_TEXT_TOP" value="3" />
            <enum name="PROGRESS_RIGHT_TEXT_LEFT_SIDE_ALIGN" value="4" />
        </attr>
    </declare-styleable>


    <declare-styleable name="WidgetLoadingLayout">
        <attr name="attr_progress_width" />
        <attr name="attr_progress_height" />
        <attr name="attr_name" format="string" />
        <attr name="attr_text_color" />
        <attr name="attr_text_size" />
        <attr name="attr_inner_margin_size" />
    </declare-styleable>
</resources>