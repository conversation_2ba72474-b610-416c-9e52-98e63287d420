package com.aquila.lib.widget.view

import android.content.Context
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.Path
import android.graphics.PorterDuff
import android.graphics.PorterDuffXfermode
import android.graphics.RectF
import android.util.AttributeSet
import androidx.appcompat.widget.AppCompatImageView
import com.aquila.lib.widget.R

/***
 * @date 创建时间 2019-10-12 15:18
 * <AUTHOR> W.YuLong
 * @description 可以自适应图片比例的ImageView
 */
class AdaptiveImageView @JvmOverloads constructor(context: Context, attr: AttributeSet? = null) :
    AppCompatImageView(context, attr) {
    companion object {
        const val FIT_NONE = 0
        const val FIT_WIDTH = 1
        const val FIT_HEIGHT = 2
        const val FIT_IMG_SIZE = 3
        const val FIT_SQUARE = 4
    }

    private var fitType: Int = FIT_NONE

    private var maxViewWith = Integer.MAX_VALUE
    private var maxViewHeight = Integer.MAX_VALUE

    private var minViewWidth = Integer.MIN_VALUE
    private var minViewHeight = Integer.MIN_VALUE

    private var viewWidth = 0
    private var viewHeight = 0

    private var roundWidth = 0
    private var roundHeight = 0

    private var roundPaint: Paint = Paint(Paint.ANTI_ALIAS_FLAG)
    private var paint2: Paint = Paint(Paint.ANTI_ALIAS_FLAG)

    private var maskColor = Color.TRANSPARENT

    private val maskPaint = Paint(Paint.ANTI_ALIAS_FLAG)
    private var ratio: Float = -1f

    init {
        var ratioText: String? = null
        if (attr != null) {
            val a = context.obtainStyledAttributes(attr, R.styleable.AdaptiveImageView)
            roundWidth =
                a.getDimensionPixelSize(R.styleable.AdaptiveImageView_attr_round_width, roundWidth)
            roundHeight = a.getDimensionPixelSize(
                R.styleable.AdaptiveImageView_attr_round_height,
                roundHeight
            )
            fitType = a.getInt(R.styleable.AdaptiveImageView_attr_fit_type, FIT_NONE)
            maxViewWith = a.getDimensionPixelSize(
                R.styleable.AdaptiveImageView_attr_max_width,
                Integer.MAX_VALUE
            )
            maxViewHeight = a.getDimensionPixelSize(
                R.styleable.AdaptiveImageView_attr_max_height,
                Integer.MAX_VALUE
            )
            minViewWidth = a.getDimensionPixelSize(
                R.styleable.AdaptiveImageView_attr_min_width,
                Integer.MIN_VALUE
            )
            maskColor = a.getColor(R.styleable.AdaptiveImageView_attr_mask_color, Color.TRANSPARENT)
            ratioText = a.getString(R.styleable.AdaptiveImageView_attr_aspect_ratio)
            a.recycle()
        }

        if (!ratioText.isNullOrEmpty()) {
            val wh = ratioText.split(",")
            if (wh.size == 2) {
                try {
                    val width = wh[0].toFloat()
                    val height = wh[1].toFloat()
                    ratio = width / height
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
        }
        maskPaint.color = maskColor
        maskPaint.style = Paint.Style.FILL

        roundPaint.color = Color.WHITE
        roundPaint.xfermode = PorterDuffXfermode(PorterDuff.Mode.DST_OUT)
        paint2.xfermode = null
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        var resultWidth = MeasureSpec.getSize(widthMeasureSpec)
        var resultHeight = MeasureSpec.getSize(heightMeasureSpec)
        if (ratio > 0) {
            setMeasuredDimension(resultWidth, (resultWidth / ratio).toInt())
        } else {
            drawable?.let {
                val w = it.intrinsicWidth
                val h = it.intrinsicHeight
                when (fitType) {
                    FIT_WIDTH -> {
                        resultHeight = resultWidth * h / w
                        setMyMeasureResult(resultWidth, resultHeight)
                    }

                    FIT_HEIGHT -> {
                        resultWidth = resultHeight * w / h
                        setMyMeasureResult(resultWidth, resultHeight)
                    }

                    FIT_IMG_SIZE -> {
                        setMyMeasureResult(w, h)
                    }

                    FIT_SQUARE -> {
                        val size = w.coerceAtMost(h)
                        setMyMeasureResult(size, size)
                    }

                    else -> {
                        super.onMeasure(widthMeasureSpec, heightMeasureSpec)
                    }
                }
            } ?: super.onMeasure(widthMeasureSpec, heightMeasureSpec)
        }
    }

    private fun setMyMeasureResult(width: Int, height: Int) {
        val realWidth: Int
        val realHeight: Int
        var radio = 1.0f

        if (width > maxViewWith) {
            radio = maxViewWith.toFloat() / width.toFloat()
            if (height * radio > maxViewHeight) {
                radio = maxViewHeight.toFloat() / height.toFloat()
            }
        } else if (height > maxViewHeight) {
            radio = maxViewHeight.toFloat() / height.toFloat()
            if (width * radio > maxViewWith) {
                radio = maxViewWith.toFloat() / width.toFloat()
            }
        } else if (width < minViewWidth) {
            radio = minViewWidth.toFloat() / width.toFloat()
            if (height * radio < minViewHeight) {
                radio = minViewHeight.toFloat() / height.toFloat()
            }
        } else if (height < minViewHeight) {
            radio = minViewHeight.toFloat() / height.toFloat()
            if (width * radio < minViewWidth) {
                radio = minViewWidth.toFloat() / width.toFloat()
            }
        }
        realWidth = (width * radio).toInt()
        realHeight = (height * radio).toInt()
        setMeasuredDimension(realWidth, realHeight)
    }

    fun setMaskColor(color: Int) {
        maskColor = color
        maskPaint.color = maskColor
        invalidate()
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        viewWidth = w
        viewHeight = h
    }

    override fun draw(canvas: Canvas) {
        val bitmap = Bitmap.createBitmap(viewWidth, viewHeight, Bitmap.Config.ARGB_8888)
        val newCanvas = Canvas(bitmap)
        super.draw(newCanvas)
        drawLeftTop(newCanvas)
        drawLeftBottom(newCanvas)
        drawRightTop(newCanvas)
        drawRightBottom(newCanvas)
        canvas.drawBitmap(bitmap, 0f, 0f, paint2)
        bitmap.recycle()
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        canvas.drawColor(maskColor)
    }

    private fun drawLeftTop(canvas: Canvas) {
        val path = Path()
        path.moveTo(0f, roundHeight.toFloat())
        path.lineTo(0f, 0f)
        path.lineTo(roundWidth.toFloat(), 0f)
        path.arcTo(
            RectF(0f, 0f, (roundWidth * 2).toFloat(), (roundHeight * 2).toFloat()),
            -90f,
            -90f
        )
        path.close()
        canvas.drawPath(path, roundPaint)
    }

    private fun drawLeftBottom(canvas: Canvas) {
        val path = Path()
        path.moveTo(0f, height - roundHeight.toFloat())
        path.lineTo(0f, height.toFloat())
        path.lineTo(roundWidth.toFloat(), height.toFloat())
        path.arcTo(
            RectF(
                0f,
                (height - roundHeight * 2).toFloat(),
                (roundWidth * 2).toFloat(),
                height.toFloat()
            ),
            90f, 90f
        )
        path.close()
        canvas.drawPath(path, roundPaint)
    }

    private fun drawRightBottom(canvas: Canvas) {
        val path = Path()
        path.moveTo(width - roundWidth.toFloat(), height.toFloat())
        path.lineTo(width.toFloat(), height.toFloat())
        path.lineTo(width.toFloat(), height - roundHeight.toFloat())
        path.arcTo(
            RectF(
                (width - roundWidth * 2).toFloat(),
                (height - roundHeight * 2).toFloat(),
                width.toFloat(),
                height.toFloat()
            ),
            -0f, 90f
        )
        path.close()
        canvas.drawPath(path, roundPaint)
    }

    private fun drawRightTop(canvas: Canvas) {
        val path = Path()
        path.moveTo(width.toFloat(), roundHeight.toFloat())
        path.lineTo(width.toFloat(), 0f)
        path.lineTo(width - roundWidth.toFloat(), 0f)
        path.arcTo(
            RectF(
                (width - roundWidth * 2).toFloat(),
                0f,
                width.toFloat(),
                (0 + roundHeight * 2).toFloat()
            ),
            -90f, 90f
        )
        path.close()
        canvas.drawPath(path, roundPaint)
    }
}
