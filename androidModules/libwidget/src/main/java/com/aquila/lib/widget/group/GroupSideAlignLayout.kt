package com.aquila.lib.widget.group

import android.content.Context
import android.text.TextUtils
import android.util.AttributeSet
import android.util.TypedValue
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.annotation.StringRes
import com.aquila.lib.widget.R

/***
 * @date 创建时间 2018/5/7 16:39
 * <AUTHOR> YuLong
 * @description 文字的两端对齐控件
 */
class GroupSideAlignLayout @JvmOverloads constructor(context: Context, attrs: AttributeSet? = null) : RelativeLayout(context, attrs) {
    var leftTextView: TextView
        private set
    var rightTextView: TextView
        private set

    init {
        inflate(context, R.layout.group_algin_side_layout, this)
        leftTextView = findViewById(R.id.group_left_TextView)
        rightTextView = findViewById(R.id.group_right_TextView)

        attrs?.let {
            initAttrs(context, it)
        }
    }

    private fun initAttrs(context: Context, attrs: AttributeSet) {
        val a = context.obtainStyledAttributes(attrs, R.styleable.GroupSideAlignLayout)
        val leftText = a.getText(R.styleable.GroupSideAlignLayout_attr_left_text)
        val leftTextSize = a.getDimensionPixelSize(R.styleable.GroupSideAlignLayout_attr_left_text_size, -1)
        val leftTextColor = a.getColorStateList(R.styleable.GroupSideAlignLayout_attr_left_text_color)
        val leftMaxWidth = a.getDimensionPixelOffset(R.styleable.GroupSideAlignLayout_attr_left_max_width, -1)
        val leftGravity = a.getInt(R.styleable.GroupSideAlignLayout_attr_left_layout_gravity, 0)
        val ellipsize = a.getInt(R.styleable.GroupSideAlignLayout_attr_left_ellipsize, -1)
        val isLeftSingleLine = a.getBoolean(R.styleable.GroupSideAlignLayout_attr_left_single_line, false)

        val rightGravity = a.getInt(R.styleable.GroupSideAlignLayout_attr_right_layout_gravity, 0)
        val rightEllipsize = a.getInt(R.styleable.GroupSideAlignLayout_attr_right_ellipsize, 0)

        val rightText = a.getText(R.styleable.GroupSideAlignLayout_attr_right_text)
        val rightTextSize = a.getDimensionPixelSize(R.styleable.GroupSideAlignLayout_attr_right_text_size, -1)
        val rightTextColor = a.getColorStateList(R.styleable.GroupSideAlignLayout_attr_right_text_color)
        val rightMaxWidth = a.getDimensionPixelOffset(R.styleable.GroupSideAlignLayout_attr_right_max_width, -1)
        val isSingleLine = a.getBoolean(R.styleable.GroupSideAlignLayout_attr_right_single_line, false)
        a.recycle()
        if (leftTextColor != null) {
            leftTextView.setTextColor(leftTextColor)
        }
        if (leftTextSize > 0) {
            leftTextView.setTextSize(TypedValue.COMPLEX_UNIT_PX, leftTextSize.toFloat())
        }

        if (ellipsize != -1) {
            when (ellipsize) {
                0 -> leftTextView.ellipsize = TextUtils.TruncateAt.START
                1 -> leftTextView.ellipsize = TextUtils.TruncateAt.MIDDLE
                2 -> leftTextView.ellipsize = TextUtils.TruncateAt.END
                3 -> leftTextView.ellipsize = TextUtils.TruncateAt.MARQUEE
                else -> {
                }
            }
        }
        if (isLeftSingleLine) {
            leftTextView.setSingleLine()
        }

        if (leftMaxWidth != -1) {
            leftTextView.maxWidth = leftMaxWidth
        }

        setLeftText(leftText)

        setViewLayoutGravity(leftTextView, leftGravity)
        setViewLayoutGravity(rightTextView, rightGravity)
        setViewEllipsize(rightTextView, rightEllipsize)

        setRightText(rightText)
        if (rightMaxWidth != -1) {
            rightTextView.maxWidth = rightMaxWidth
        }

        if (rightTextSize > 0) {
            rightTextView.setTextSize(TypedValue.COMPLEX_UNIT_PX, rightTextSize.toFloat())
        }
        if (rightTextColor != null) {
            rightTextView.setTextColor(rightTextColor)
        }
        rightTextView.isSingleLine = isSingleLine
    }

    private fun setViewEllipsize(textView: TextView, rightEllipsize: Int) {
        when (rightEllipsize) {
            0 -> textView.ellipsize = TextUtils.TruncateAt.END
            1 -> textView.ellipsize = TextUtils.TruncateAt.START
        }
    }

    private fun setViewLayoutGravity(textView: TextView, gravity: Int) {
        val params = textView.layoutParams as LayoutParams
        when (gravity) {
            0 -> params.addRule(CENTER_VERTICAL)
            1 -> params.addRule(ALIGN_PARENT_BOTTOM)
            2 -> params.addRule(ALIGN_PARENT_TOP)
        }
        textView.layoutParams = params
    }

    fun setInfoText(leftText: CharSequence, rightText: CharSequence) {
        setLeftText(leftText)
        setRightText(rightText)
    }

    fun setAppRightText(text: CharSequence) {
        setRightText(text)
    }

    fun setRightText(text: CharSequence?) {
        if (text == null) {
            rightTextView.text = ""
        } else {
            rightTextView.text = text
        }
    }

    fun setLeftText(text: CharSequence?) {
        if (text == null) {
            leftTextView.text = ""
        } else {
            leftTextView.text = text
        }
    }

    fun setRightText(@StringRes id: Int) {
        rightTextView.setText(id)
    }

    fun setLeftText(@StringRes id: Int) {
        leftTextView.setText(id)
    }
}
