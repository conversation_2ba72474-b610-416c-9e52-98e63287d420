package com.aquila.lib.widget.view

import android.content.Context
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.View
import androidx.viewpager.widget.ViewPager
import com.aquila.lib.widget.R

/***
 * @date 创建时间 2018/3/30 13:44
 * <AUTHOR> yulong
 * @description 自定义的ViewPager，可以设置垂直滚动
 */

/***
 *@date 重构时间 2020/10/28 10:11
 *<AUTHOR> <PERSON><PERSON><PERSON><PERSON>
 *@description 自定义的ViewPager，可以设置垂直滚动
 */
open class CustomViewPager(context: Context, attrs: AttributeSet? = null) : ViewPager(context, attrs) {
    private var orientationStyle = SCROLL_MODE_HORIZONTAL
    private var touchX = 0f
    private var touchY = 0f
    private var ratio = -1f
    private var isSlideAble = true

    companion object {
        const val SCROLL_MODE_HORIZONTAL = 0
        const val SCROLL_MODE_VERTICAL = 1
    }

    init {
        val t = context.obtainStyledAttributes(attrs, R.styleable.CustomViewPager)
        orientationStyle = t.getInt(R.styleable.CustomViewPager_attr_layout_orientation, SCROLL_MODE_HORIZONTAL)
        isSlideAble = t.getBoolean(R.styleable.CustomViewPager_attr_is_slide_able, true)
        t.recycle()
        setScrollModeHorizontal(orientationStyle)
    }

    fun setScrollModeHorizontal(mode: Int) {
        if (mode == SCROLL_MODE_VERTICAL) {
            overScrollMode = OVER_SCROLL_NEVER
            setPageTransformer(true, VerticalPageTransformer())
        } else {
            setPageTransformer(true) { _, _ -> }
        }
        orientationStyle = mode
    }

    fun setSlide(slide: Boolean) {
        isSlideAble = slide
    }

    override fun onInterceptTouchEvent(event: MotionEvent): Boolean {
        return isSlideAble && super.onInterceptTouchEvent(customTouchEvent(MotionEvent.obtain(event)))
    }

    private fun customTouchEvent(ev: MotionEvent): MotionEvent {
        if (orientationStyle == SCROLL_MODE_VERTICAL) {
            if (ratio == -1f) {
                ratio = width.toFloat() / height
            }
            touchX = ev.x
            touchY = ev.y
            ev.setLocation(touchY * ratio, touchX / ratio)
        }
        return ev
    }

    override fun onTouchEvent(ev: MotionEvent): Boolean {
        return isSlideAble && super.onTouchEvent(customTouchEvent(ev))
    }

    /***
     *@date 创建时间 2020/10/28 10:12
     *<AUTHOR> W.YuLong
     *@description
     */
    private inner class VerticalPageTransformer : PageTransformer {
        override fun transformPage(view: View, position: Float) {
            if (position <= 1) {
                view.alpha = 1f
                view.translationX = view.width * -position
                // set Y position to swipe in from top
                val yPosition = position * view.height
                view.translationY = yPosition
            } else {
                view.alpha = 0f
            }
        }
    }
}
