package com.aquila.lib.widget.view

import android.content.Context
import android.view.ViewGroup
import android.view.ViewGroup.MarginLayoutParams
import androidx.recyclerview.widget.RecyclerView
import com.google.android.flexbox.FlexboxLayoutManager

/***
 * @date 创建时间 2021/12/17 14:18
 * <AUTHOR> <PERSON><PERSON>
 * @description
 */
class FilterFlexboxLayoutManager(context: Context, flexDirection: Int, flexWrap: Int) : FlexboxLayoutManager(context, flexDirection, flexWrap) {

    override fun generateLayoutParams(lp: ViewGroup.LayoutParams?): RecyclerView.LayoutParams {
        return if (lp is RecyclerView.LayoutParams) {
            LayoutParams(lp as RecyclerView.LayoutParams?)
        } else if (lp is MarginLayoutParams) {
            LayoutParams(lp as MarginLayoutParams?)
        } else {
            LayoutParams(lp)
        }
    }
}
