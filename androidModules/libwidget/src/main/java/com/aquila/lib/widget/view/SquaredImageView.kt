package com.aquila.lib.widget.view

import android.content.Context
import android.util.AttributeSet
import androidx.appcompat.widget.AppCompatImageView

/***
 * @date 创建时间 2018/11/16 16:36
 * <AUTHOR> <PERSON><PERSON>
 * @description 正方形的 ImageView
 */
class SquaredImageView : AppCompatImageView {
    constructor(context: Context) : super(context)

    constructor(context: Context, attrs: AttributeSet) : super(context, attrs)

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
//        super.onMeasure(widthMeasureSpec, heightMeasureSpec)
        val squareSize = MeasureSpec.getSize(widthMeasureSpec)
        setMeasuredDimension(squareSize, squareSize)
    }
}
