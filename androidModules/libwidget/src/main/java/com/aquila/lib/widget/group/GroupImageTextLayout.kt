package com.aquila.lib.widget.group

import android.content.Context
import android.util.AttributeSet
import android.util.TypedValue
import android.view.Gravity
import android.widget.ImageView
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.annotation.DrawableRes
import androidx.annotation.IntDef
import androidx.annotation.StringRes
import com.aquila.lib.widget.R

/***
 *@date 创建时间 2019-09-27 17:12
 *<AUTHOR> <PERSON><PERSON><PERSON>ong
 *@description 图文的局部封装
 */
class GroupImageTextLayout @JvmOverloads constructor(context: Context, attrs: AttributeSet? = null) : RelativeLayout(context, attrs) {

    @OrientationType private var orientationType = IMAGE_TOP_TEXT_BOTTOM

    var imageView: ImageView
        private set
    var textView: TextView
        private set

    var text: CharSequence
        get() = textView.text
        set(text) {
            textView.text = text
        }

    init {
        inflate(context, R.layout.group_image_text_layout, this)
        imageView = findViewById(R.id.group_image_ImageView)
        textView = findViewById(R.id.group_text_TextView)

        attrs?.let {
            initializeAttrs(context, it)
        }
    }

    private fun initializeAttrs(context: Context, attrs: AttributeSet?) {

        val a = context.obtainStyledAttributes(attrs, R.styleable.GroupImageTextLayout)
        val imageWidth = a.getDimensionPixelOffset(R.styleable.GroupImageTextLayout_attr_image_width, 0)
        val imageHeight = a.getDimensionPixelOffset(R.styleable.GroupImageTextLayout_attr_image_height, 0)
        val viewMarginStart = a.getDimensionPixelOffset(R.styleable.GroupImageTextLayout_attr_child_view_margin_start, 0)

        val textMaxWidth = a.getDimensionPixelOffset(R.styleable.GroupImageTextLayout_attr_text_max_width, -1)

        val imageDrawable = a.getDrawable(R.styleable.GroupImageTextLayout_attr_image_src)
        val scaleTypeIndex = a.getInt(R.styleable.GroupImageTextLayout_attr_scale_type, -1)

        val imageBackgroundDrawable = a.getDrawable(R.styleable.GroupImageTextLayout_attr_image_background)
        val textBackgroundDrawable = a.getDrawable(R.styleable.GroupImageTextLayout_attr_text_background)

        val textSize = a.getDimensionPixelSize(R.styleable.GroupImageTextLayout_attr_text_size, 16)
        val textColor = a.getColorStateList(R.styleable.GroupImageTextLayout_attr_text_color)
        val textString = a.getString(R.styleable.GroupImageTextLayout_attr_text)
        val gravityIndex = a.getInt(R.styleable.GroupImageTextLayout_attr_text_gravity, 0)
        val isSingleLine = a.getBoolean(R.styleable.GroupImageTextLayout_attr_text_single_line, false)
        val isSelected = a.getBoolean(R.styleable.GroupImageTextLayout_attr_is_selected, false)
        val isBold = a.getBoolean(R.styleable.GroupImageTextLayout_attr_text_style_is_bold, false)
        val maxLine = a.getInt(R.styleable.GroupImageTextLayout_attr_text_max_line, -1)

        val lineMultiple = a.getFloat(R.styleable.GroupImageTextLayout_attr_text_line_spacing_multiplier, 1f)
        val lineExtra = a.getDimension(R.styleable.GroupImageTextLayout_attr_text_line_spacing_extra, 0f)
        val tintColorStateList = a.getColorStateList(R.styleable.GroupImageTextLayout_attr_image_src_tint)

        orientationType = a.getInt(R.styleable.GroupImageTextLayout_attr_parent_orientation, IMAGE_TOP_TEXT_BOTTOM)
        val textMarginImageSize = a.getDimensionPixelOffset(R.styleable.GroupImageTextLayout_attr_text_margin_image_size, 0)
        a.recycle()

        textView.paint.isFakeBoldText = isBold

        textView.setLineSpacing(lineExtra, lineMultiple)

        textView.isSingleLine = isSingleLine
        if (maxLine != -1) {
            textView.maxLines = maxLine
        }
        if (imageDrawable != null) {
            imageView.setImageDrawable(imageDrawable)
        }

        if (tintColorStateList != null) {
            imageView.imageTintList = tintColorStateList
        }

        if (scaleTypeIndex >= 0) {
            setScaleType(SCALE_TYPE_ARRAY[scaleTypeIndex])
        }

        if (textColor != null) {
            textView.setTextColor(textColor)
        }

        if (textMaxWidth >= 0) {
            textView.maxWidth = textMaxWidth
        }

        if (textBackgroundDrawable != null) {
            textView.background = textBackgroundDrawable
        }

        if (imageBackgroundDrawable != null) {
            imageView.background = imageBackgroundDrawable
        }

        textView.setTextSize(TypedValue.COMPLEX_UNIT_PX, textSize.toFloat())

        if (textString != null) {
            textView.text = textString
        }
        textView.gravity = GRAVITY_ARRAY[gravityIndex]

        initTextOrientationStyle(imageWidth, imageHeight, textMarginImageSize, viewMarginStart)

        setSelectStatus(isSelected)
    }

    private fun initTextOrientationStyle(imageWidth: Int, imageHeight: Int, textMarginImageSize: Int, startMargin: Int) {
        val textParams = textView.layoutParams as RelativeLayout.LayoutParams
        val imageParams = imageView.layoutParams as RelativeLayout.LayoutParams

        if (imageWidth > 0) {
            imageParams.width = imageWidth
        }
        if (imageHeight > 0) {
            imageParams.height = imageHeight
        }

        when (orientationType) {
            IMAGE_LEFT_TEXT_RIGHT -> {
                textParams.addRule(RelativeLayout.RIGHT_OF, R.id.group_image_ImageView)
                textParams.addRule(RelativeLayout.CENTER_VERTICAL)

                imageParams.addRule(RelativeLayout.CENTER_VERTICAL)

                textParams.leftMargin = textMarginImageSize / 2
                imageParams.rightMargin = textMarginImageSize / 2
                imageParams.leftMargin = startMargin
            }
            IMAGE_RIGHT_TEXT_LEFT_SIDE -> {
                textParams.addRule(RelativeLayout.ALIGN_PARENT_LEFT)
                imageParams.addRule(RelativeLayout.ALIGN_PARENT_RIGHT)
                imageParams.addRule(RelativeLayout.CENTER_VERTICAL)
            }
            IMAGE_RIGHT_TEXT_LEFT -> {

                textParams.addRule(RelativeLayout.CENTER_VERTICAL)

                imageParams.addRule(RelativeLayout.RIGHT_OF, R.id.group_text_TextView)
                imageParams.addRule(RelativeLayout.CENTER_VERTICAL)

                textParams.leftMargin = startMargin

                textParams.rightMargin = textMarginImageSize / 2
                imageParams.leftMargin = textMarginImageSize / 2
            }
            IMAGE_TOP_TEXT_BOTTOM -> {
                textParams.addRule(RelativeLayout.BELOW, R.id.group_image_ImageView)
                textParams.addRule(RelativeLayout.CENTER_HORIZONTAL)
                imageParams.addRule(RelativeLayout.CENTER_HORIZONTAL)

                textParams.topMargin = textMarginImageSize / 2
                imageParams.bottomMargin = textMarginImageSize / 2
                imageParams.topMargin = startMargin
            }
            IMAGE_BOTTOM_TEXT_TOP -> {
                textParams.addRule(RelativeLayout.CENTER_HORIZONTAL)
                imageParams.addRule(RelativeLayout.BELOW, R.id.group_text_TextView)
                imageParams.addRule(RelativeLayout.CENTER_HORIZONTAL)

                textParams.topMargin = startMargin
                textParams.bottomMargin = textMarginImageSize / 2
                imageParams.topMargin = textMarginImageSize / 2
            }
        }

        textView.layoutParams = textParams
        imageView.layoutParams = imageParams
    }

    fun setTextColor(color: Int) {
        textView.setTextColor(color)
    }

    fun setImageAndText(@DrawableRes imgId: Int, @StringRes textID: Int) {
        imageView.setImageResource(imgId)
        textView.setText(textID)
    }

    fun setImageAndText(@DrawableRes imgId: Int, text: CharSequence) {
        imageView.setImageResource(imgId)
        textView.text = text
    }

    fun setImageByResId(imageResId: Int) {
        imageView.setImageResource(imageResId)
    }

    fun setText(@StringRes resId: Int) {
        textView.setText(resId)
    }

    fun setText(text: String?) {
        textView.text = text
    }

    fun setScaleType(scaleType: ImageView.ScaleType) {
        imageView.scaleType = scaleType
    }
    
    fun setImageTextEnabled(enable: Boolean) {
        imageView.isEnabled = enable
        textView.isEnabled = enable
        isEnabled = enable
    }
    
    fun setSelectStatus(isSelected: Boolean) {
        textView.isSelected = isSelected
        imageView.isSelected = isSelected
    }
    
    fun getSelectStatus(): Boolean {
        return imageView.isSelected
    }
    
    /**
     * 设置图文的布局样式
     */
    fun setTextOrientation(orientation: Int) {
        this.orientationType = orientation
        invalidate()
    }
    
    @IntDef(IMAGE_LEFT_TEXT_RIGHT, IMAGE_TOP_TEXT_BOTTOM, IMAGE_RIGHT_TEXT_LEFT, IMAGE_BOTTOM_TEXT_TOP, IMAGE_RIGHT_TEXT_LEFT_SIDE)
    annotation class OrientationType

    companion object {
        const val IMAGE_LEFT_TEXT_RIGHT = 0
        const val IMAGE_TOP_TEXT_BOTTOM = 1
        const val IMAGE_RIGHT_TEXT_LEFT = 2
        const val IMAGE_BOTTOM_TEXT_TOP = 3
        // 两端对齐
        const val IMAGE_RIGHT_TEXT_LEFT_SIDE = 4

        private val SCALE_TYPE_ARRAY = arrayOf(ImageView.ScaleType.MATRIX, ImageView.ScaleType.FIT_XY, ImageView.ScaleType.FIT_START, ImageView.ScaleType.FIT_CENTER, ImageView.ScaleType.FIT_END, ImageView.ScaleType.CENTER, ImageView.ScaleType.CENTER_CROP, ImageView.ScaleType.CENTER_INSIDE)
        private val GRAVITY_ARRAY = intArrayOf(Gravity.CENTER_HORIZONTAL, Gravity.LEFT, Gravity.RIGHT, Gravity.CENTER, Gravity.CENTER_VERTICAL)
    }
}
