package com.aquila.lib.widget.view

import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.util.AttributeSet
import androidx.annotation.ColorRes
import androidx.appcompat.widget.AppCompatImageView
import androidx.core.content.ContextCompat
import com.aquila.lib.widget.R

/***
 * @date 创建时间 2018/4/28 15:52
 * <AUTHOR> yulong
 * @description 带红点的ImageView
 */
class DotImageView(context: Context, attrs: AttributeSet?) : AppCompatImageView(context, attrs) {
    private val paint: Paint
    private var dotRadius = 5
    private var isShowDot = false

    private var dotPositionIndex = 0
    private var dotMarginLeft: Int = 0
    private var dotMarginRight: Int = 0
    private var dotMarginTop: Int = 0
    private var dotMarginBottom: Int = 0

    private var viewWidth: Int = 0
    private var viewHeight: Int = 0

    init {
        paint = Paint(Paint.ANTI_ALIAS_FLAG)
        paint.color = Color.RED
        initializeAttr(context, attrs)
    }

    private fun initializeAttr(context: Context, attrs: AttributeSet?) {
        val t = context.obtainStyledAttributes(attrs, R.styleable.DotImageView)
        dotRadius = t.getDimensionPixelOffset(R.styleable.DotImageView_attr_dot_radius, 5)
        val dotColor = t.getColor(R.styleable.DotImageView_attr_dot_color, Color.RED)
        isShowDot = t.getBoolean(R.styleable.DotImageView_attr_is_show_dot, false)
        dotPositionIndex = t.getInt(R.styleable.DotImageView_attr_dot_position, 0)

        dotMarginBottom = t.getDimensionPixelOffset(R.styleable.DotImageView_attr_dot_margin_bottom, 0)
        dotMarginLeft = t.getDimensionPixelOffset(R.styleable.DotImageView_attr_dot_margin_left, 0)
        dotMarginRight = t.getDimensionPixelOffset(R.styleable.DotImageView_attr_dot_margin_right, 0)
        dotMarginTop = t.getDimensionPixelOffset(R.styleable.DotImageView_attr_dot_margin_top, 0)

        val margin = t.getDimensionPixelOffset(R.styleable.DotImageView_attr_dot_margin, 0)
        if (margin != 0) {
            dotMarginTop = margin
            dotMarginRight = dotMarginTop
            dotMarginLeft = dotMarginRight
            dotMarginBottom = dotMarginLeft
        }
        t.recycle()

        paint.color = dotColor
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        viewWidth = w
        viewHeight = h
        super.onSizeChanged(w, h, oldw, oldh)
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        if (isShowDot) {
            var cx = ((viewWidth - paddingRight - paddingLeft) / 2).toFloat()
            var cy = ((viewHeight - paddingTop - paddingBottom) / 2).toFloat()
            when (dotPositionIndex) {
                DOT_POSITION_TOP_RIGHT -> {
                    cx = (viewWidth - paddingRight - dotRadius - dotMarginRight).toFloat()
                    cy = (paddingTop + dotRadius + dotMarginTop).toFloat()
                }
                DOT_POSITION_TOP_LEFT -> {
                    cx = (paddingLeft + dotRadius + dotMarginLeft).toFloat()
                    cy = (paddingTop + dotRadius + dotMarginTop).toFloat()
                }
                DOT_POSITION_BOTTOM_LEFT -> {
                    cx = (paddingLeft + dotRadius + dotMarginLeft).toFloat()
                    cy = (viewHeight - paddingTop - dotRadius - dotMarginBottom).toFloat()
                }
                DOT_POSITION_BOTTOM_RIGHT -> {
                    cx = (viewWidth - paddingRight - dotRadius - dotMarginRight).toFloat()
                    cy = (viewHeight - paddingTop - dotRadius - dotMarginBottom).toFloat()
                }
                DOT_POSITION_CENTER -> {
                    cx = (viewWidth / 2).toFloat()
                    cy = (viewHeight / 2).toFloat()
                }
            }
            canvas.drawCircle(cx, cy, dotRadius.toFloat(), paint)
        }
    }

    fun setColorRes(@ColorRes colorRes: Int) {
        paint.color = ContextCompat.getColor(context, colorRes)
        invalidate()
    }

    fun setDotColor(dotColor: Int) {
        paint.color = dotColor
        invalidate()
    }

    fun setShowDot(isShowDot: Boolean) {
        this.isShowDot = isShowDot
        invalidate()
    }

    companion object {

        private val DOT_POSITION_TOP_RIGHT = 0
        private val DOT_POSITION_BOTTOM_RIGHT = 1
        private val DOT_POSITION_BOTTOM_LEFT = 2
        private val DOT_POSITION_TOP_LEFT = 4
        private val DOT_POSITION_CENTER = 8
    }
}
