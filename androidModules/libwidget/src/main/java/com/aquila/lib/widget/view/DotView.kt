package com.aquila.lib.widget.view

import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.util.AttributeSet
import android.view.View
import androidx.annotation.ColorRes
import androidx.core.content.ContextCompat
import com.aquila.lib.widget.R

/***
 * @date 创建时间 2018/7/23 11:05
 * <AUTHOR> <PERSON><PERSON>
 * @description 小圆点的View
 */
class DotView(context: Context, attrs: AttributeSet?) : View(context, attrs) {
    private val paint: Paint

    private var dotRadius = 5
    private var viewWidth: Int = 0
    private var viewHeight: Int = 0

    init {
        paint = Paint(Paint.ANTI_ALIAS_FLAG)
        val t = context.obtainStyledAttributes(attrs, R.styleable.DotView)
        dotRadius = t.getDimensionPixelOffset(R.styleable.DotView_attr_dot_radius, 5)
        val dotColor = t.getColor(R.styleable.DotView_attr_dot_color, Color.RED)
        t.recycle()

        paint.color = dotColor
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        var width = MeasureSpec.getSize(widthMeasureSpec)
        var height = MeasureSpec.getSize(heightMeasureSpec)
        if (width < dotRadius * 2) {
            width = dotRadius * 2
        }
        if (height < dotRadius * 2) {
            height = dotRadius * 2
        }
        setMeasuredDimension(width, height)
//        super.onMeasure(width, height)
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        viewHeight = h
        viewWidth = w
        super.onSizeChanged(w, h, oldw, oldh)
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        val cx = (viewWidth / 2).toFloat()
        val cy = (viewHeight / 2).toFloat()
        canvas.drawCircle(cx, cy, dotRadius.toFloat(), paint)
    }

    fun setColorRes(@ColorRes colorRes: Int) {
        paint.color = ContextCompat.getColor(context, colorRes)
        invalidate()
    }

    fun setDotColor(dotColor: Int) {
        paint.color = dotColor
        invalidate()
    }

    fun setDotRadius(dotRadius: Int) {
        this.dotRadius = dotRadius
        invalidate()
    }
}
