package com.aquila.lib.widget.view

import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.util.AttributeSet
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import androidx.viewpager.widget.ViewPager
import com.aquila.lib.widget.R

/***
 * @date 创建时间 2018/11/13 13:47
 * <AUTHOR> W<PERSON>
 * @description
 */
class TabLinearLayout @JvmOverloads constructor(context: Context, attrs: AttributeSet? = null) : LinearLayout(context, attrs) {
    private var indicatorHeight = 15
    private var indicatorWidth = 100
    private var indicatorColor = Color.GREEN
    private var indicatorMarginBottom = 0
    /*指示器的X轴位置*/
    private var offsetX = 0f
    private var currentPosition: Int
    private val paint = Paint(Paint.ANTI_ALIAS_FLAG)

    var onItemSelectListener: OnItemSelectListener? = null

    fun getCurrentPosition(): Int {
        return currentPosition
    }

    fun setCurrentPosition(currentPosition: Int): TabLinearLayout {
        this.currentPosition = currentPosition
        invalidate()
        return this
    }

    fun <T> setDataList(dataList: List<T>?, iCustomChildViewInterface: ICustomChildViewInterface<T>) {
        if (dataList == null) {
            return
        }
        removeAllViews()
        var i = 0
        val len = dataList.size
        while (i < len) {
            val view = iCustomChildViewInterface.getChildView(this, dataList[i], i)
            val finalI = i
            view.setOnClickListener {
                currentPosition = finalI
                if (onItemSelectListener != null) {
                    onItemSelectListener!!.onItemSelect(currentPosition, dataList[finalI])
                }
                invalidate()
            }
            addView(view)
            i++
        }
    }

    /**和ViewPager绑定，这个已经在layout 的文件中设置过item的布局样式 */
    fun bindViewPager(viewPager: ViewPager) {
        viewPager.addOnPageChangeListener(onPageChangeListener)
        if (childCount > 0) {
            for (i in 0 until childCount) {
                getChildAt(i).setOnClickListener {
                    viewPager.currentItem = i
                    onItemSelectListener?.onItemSelect(i, "")
                    invalidate()
                }
            }
        }
    }

    /*和ViewPager绑定，并且有自定义的布局*/
    fun bindViewPager(viewPager: ViewPager, iCustomChildViewInterface: ICustomChildViewInterface<CharSequence>) {
        viewPager.addOnPageChangeListener(onPageChangeListener)
        val pagerAdapter = viewPager.adapter
        pagerAdapter?.let {
            if (it.count > 0) {
                removeAllViews()
                var i = 0
                val len = it.count
                while (i < len) {
                    var v: View = iCustomChildViewInterface.getChildView(this, it.getPageTitle(i), i)
                    v.setOnClickListener { _ ->
                        viewPager.currentItem = i
                        onItemSelectListener?.onItemSelect(i, it.getPageTitle(i))
                        invalidate()
                    }
                    addView(v)
                    i++
                }
            }
        }
    }

    private val onPageChangeListener: ViewPager.OnPageChangeListener = object :
        ViewPager.OnPageChangeListener {
        var scrollState = 0
        override fun onPageScrolled(position: Int, positionOffset: Float, positionOffsetPixels: Int) {
            currentPosition = position
            offsetX = positionOffset
            invalidate()
        }

        override fun onPageSelected(position: Int) {
            if (scrollState == ViewPager.SCROLL_STATE_IDLE) {
                currentPosition = position
                invalidate()
            }
        }

        override fun onPageScrollStateChanged(state: Int) {
            scrollState = state
        }
    }

    var realIndicatorSize: Int = 0
    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        if (childCount > 0 && currentPosition >= 0 && currentPosition < childCount) {
            val childView = getChildAt(currentPosition)
            realIndicatorSize = getRealIndicatorWidth(indicatorWidth, childView)
            val left = childView.left + (childView.width - realIndicatorSize) / 2 + offsetX * childView.width
            val right = left + realIndicatorSize
            val bottom = height - paddingBottom - indicatorMarginBottom.toFloat()
            val top = bottom - indicatorHeight
            canvas.drawRect(left, top, right, bottom, paint)
        }
    }

    fun getRealIndicatorWidth(indicatorWidth: Int, view: View): Int {
        return if (indicatorWidth < 0 || indicatorWidth > view.width) {
            view.width
        } else {
            indicatorWidth
        }
    }

    /***
     * @date 创建时间 2018/11/13 15:51
     * <AUTHOR> W.YuLong
     * @description
     */
    interface OnItemSelectListener {
        fun <T> onItemSelect(position: Int, data: T)
    }

    /***
     * @date 创建时间 2018/11/13 14:48
     * <AUTHOR> W.YuLong
     * @description
     */
    interface ICustomChildViewInterface<T> {
        fun getChildView(parent: ViewGroup?, data: T?, position: Int): View
    }

    init {
        val a = context.obtainStyledAttributes(attrs, R.styleable.TabLinearLayout)
        indicatorHeight = a.getDimensionPixelOffset(R.styleable.TabLinearLayout_app_indicator_height, 10)
        indicatorWidth = a.getDimensionPixelOffset(R.styleable.TabLinearLayout_app_indicator_width, 0)
        indicatorMarginBottom = a.getDimensionPixelOffset(R.styleable.TabLinearLayout_app_indicator_margin_bottom, 0)
        indicatorColor = a.getColor(R.styleable.TabLinearLayout_app_indicator_color, Color.WHITE)
        currentPosition = a.getInteger(R.styleable.TabLinearLayout_app_indicator_position, 0)
        a.recycle()

        paint.color = indicatorColor

        setWillNotDraw(false)
    }
}
