package com.aquila.lib.widget.view

import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.Path
import android.util.AttributeSet
import androidx.appcompat.widget.AppCompatTextView
import com.aquila.lib.widget.R

/**
 * <AUTHOR> <PERSON><PERSON>
 * @date 创建时间 2018/5/9 18:11
 * @description
 */
class TriangleTextView(context: Context, attrs: AttributeSet?) : AppCompatTextView(context, attrs) {

    private var bgColor = Color.BLACK

    private val path: Path
    private val paint: Paint
    private var rate: Float = 0.5f

    private var viewWidth = 0
    private var viewHeight = 0
    // 根据需求得出的算法公式为 √2 * (1 - rate) / 4
    private val resultRate: Float
        get() = (Math.sqrt(2.0) * (1 - rate) / 4).toFloat()

    init {
        if (attrs != null) {
            val t = context.obtainStyledAttributes(attrs, R.styleable.TriangleTextView)
            bgColor = t.getColor(R.styleable.TriangleTextView_attr_triangle_bg_color, Color.TRANSPARENT)
            rate = t.getFloat(R.styleable.TriangleTextView_attr_ratio, 0.5f)
            t.recycle()
        }

        paint = Paint(Paint.ANTI_ALIAS_FLAG)
        paint.color = bgColor

        path = Path()
    }

    fun setBgColor(color: Int) {
        paint.color = color
        invalidate()
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        viewHeight = h
        viewWidth = w
        super.onSizeChanged(w, h, oldw, oldh)
    }

    override fun onDraw(canvas: Canvas) {
        path.moveTo(0f, 0f) // 此点为多边形的起点
        path.lineTo(width * (1 - rate), 0f)
        path.lineTo(width.toFloat(), height * rate)
        path.lineTo(width.toFloat(), height.toFloat())
        path.close() // 使这些点构成封闭的多边形
        canvas.drawPath(path, paint)
        canvas.rotate(45f, (width / 2).toFloat(), (height / 2).toFloat())
        canvas.translate(0f, -height * resultRate)

        super.onDraw(canvas)
    }
}
