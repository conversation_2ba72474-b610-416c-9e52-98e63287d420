package com.aquila.lib.widget.group

import android.content.Context
import android.util.AttributeSet
import android.util.TypedValue
import android.widget.ProgressBar
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.annotation.IntDef
import androidx.annotation.StringRes
import com.aquila.lib.widget.R

/***
 * @date 创建时间 2020/2/19 14:02
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON>
 * @description
 */
class GroupProgressLayout @JvmOverloads constructor(context: Context, attrs: AttributeSet? = null) : RelativeLayout(context, attrs) {
    var progressBar: ProgressBar
        private set
    var textView: TextView
        private set

    @IntDef(PROGRESS_LEFT_TEXT_RIGHT, PROGRESS_TOP_TEXT_BOTTOM, PROGRESS_RIGHT_TEXT_LEFT, PROGRESS_BOTTOM_TEXT_TOP, PROGRESS_RIGHT_TEXT_LEFT_SIDE)
    annotation class OrientationType

    @OrientationType private var orientationType = PROGRESS_TOP_TEXT_BOTTOM

    companion object {
        const val PROGRESS_LEFT_TEXT_RIGHT = 0
        const val PROGRESS_TOP_TEXT_BOTTOM = 1
        const val PROGRESS_RIGHT_TEXT_LEFT = 2
        const val PROGRESS_BOTTOM_TEXT_TOP = 3
        // 两端对齐
        const val PROGRESS_RIGHT_TEXT_LEFT_SIDE = 4
    }

    init {
        inflate(context, R.layout.group_progress_layout, this)
        progressBar = findViewById(R.id.group_progressBar)
        textView = findViewById(R.id.group_desc_TextView)

        attrs?.let {
            initializeAttrs(context, it)
        }
    }

    private fun initializeAttrs(context: Context, attrs: AttributeSet) {
        val a = context.obtainStyledAttributes(attrs, R.styleable.GroupProgressLayout)
        val textSize = a.getDimensionPixelSize(R.styleable.GroupProgressLayout_attr_text_size, 16)
        val textColor = a.getColorStateList(R.styleable.GroupProgressLayout_attr_text_color)
        val textString = a.getString(R.styleable.GroupProgressLayout_attr_text)
        val isSingleLine = a.getBoolean(R.styleable.GroupProgressLayout_attr_text_single_line, false)
        val isBold = a.getBoolean(R.styleable.GroupProgressLayout_attr_text_style_is_bold, false)
        val maxLine = a.getInt(R.styleable.GroupProgressLayout_attr_text_max_line, -1)
        val lineMultiple = a.getFloat(R.styleable.GroupProgressLayout_attr_text_line_spacing_multiplier, 1f)
        val lineExtra = a.getDimension(R.styleable.GroupProgressLayout_attr_text_line_spacing_extra, 0f)
        val textMaxWidth = a.getDimensionPixelOffset(R.styleable.GroupProgressLayout_attr_text_max_width, -1)
        val textBackgroundDrawable = a.getDrawable(R.styleable.GroupProgressLayout_attr_text_background)

        val progressWidth = a.getDimensionPixelSize(R.styleable.GroupProgressLayout_attr_progress_width, 20)
        val progressHeight = a.getDimensionPixelSize(R.styleable.GroupProgressLayout_attr_progress_height, 20)

        val viewMarginStart = a.getDimensionPixelOffset(R.styleable.GroupImageTextLayout_attr_child_view_margin_start, 0)

        val progressMarginSize = a.getDimensionPixelSize(R.styleable.GroupProgressLayout_attr_progress_margin_text, 0)
//        orientationType = a.getInt(R.styleable.GroupProgressLayout_attr_progress_location, PROGRESS_TOP_TEXT_BOTTOM)
        a.recycle()

        textView.paint.isFakeBoldText = isBold

        textView.setLineSpacing(lineExtra, lineMultiple)

        textView.isSingleLine = isSingleLine
        if (maxLine != -1) {
            textView.maxLines = maxLine
        }
        if (textColor != null) {
            textView.setTextColor(textColor)
        }

        if (textMaxWidth >= 0) {
            textView.maxWidth = textMaxWidth
        }

        if (textBackgroundDrawable != null) {
            textView.background = textBackgroundDrawable
        }

        textView.setTextSize(TypedValue.COMPLEX_UNIT_PX, textSize.toFloat())

        if (textString != null) {
            textView.text = textString
        }

        initTextOrientationStyle(progressWidth, progressHeight, progressMarginSize, viewMarginStart)
    }

    private fun initTextOrientationStyle(imageWidth: Int, imageHeight: Int, textMarginImageSize: Int, startMargin: Int) {
        val textParams = textView.layoutParams as LayoutParams
        val progressParams = progressBar.layoutParams as LayoutParams

        if (imageWidth > 0) {
            progressParams.width = imageWidth
        }
        if (imageHeight > 0) {
            progressParams.height = imageHeight
        }

        when (orientationType) {
            PROGRESS_LEFT_TEXT_RIGHT -> {
                textParams.addRule(RIGHT_OF, R.id.group_progressBar)
                textParams.addRule(CENTER_VERTICAL)

                progressParams.addRule(CENTER_VERTICAL)

                textParams.leftMargin = textMarginImageSize / 2
                progressParams.rightMargin = textMarginImageSize / 2
                progressParams.leftMargin = startMargin
            }
            PROGRESS_RIGHT_TEXT_LEFT_SIDE -> {
                textParams.addRule(ALIGN_PARENT_LEFT)
                progressParams.addRule(ALIGN_PARENT_RIGHT)
                progressParams.addRule(CENTER_VERTICAL)
            }
            PROGRESS_RIGHT_TEXT_LEFT -> {

                textParams.addRule(CENTER_VERTICAL)

                progressParams.addRule(RIGHT_OF, R.id.group_desc_TextView)
                progressParams.addRule(CENTER_VERTICAL)

                textParams.leftMargin = startMargin

                textParams.rightMargin = textMarginImageSize / 2
                progressParams.leftMargin = textMarginImageSize / 2
            }
            PROGRESS_TOP_TEXT_BOTTOM -> {
                textParams.addRule(BELOW, R.id.group_progressBar)
                textParams.addRule(CENTER_HORIZONTAL)
                progressParams.addRule(CENTER_HORIZONTAL)

                textParams.topMargin = textMarginImageSize / 2
                progressParams.bottomMargin = textMarginImageSize / 2
                progressParams.topMargin = startMargin
            }
            PROGRESS_BOTTOM_TEXT_TOP -> {
                textParams.addRule(CENTER_HORIZONTAL)
                progressParams.addRule(BELOW, R.id.group_desc_TextView)
                progressParams.addRule(CENTER_HORIZONTAL)

                textParams.topMargin = startMargin
                textParams.bottomMargin = textMarginImageSize / 2
                progressParams.topMargin = textMarginImageSize / 2
            }
        }

        textView.layoutParams = textParams
        progressBar.layoutParams = progressParams
    }

    fun setText(@StringRes resId: Int) {
        textView.setText(resId)
    }

    fun setText(text: String?) {
        textView.text = text
    }

    fun setSelectStatus(isSelected: Boolean) {
        textView.isSelected = isSelected
        progressBar.isSelected = isSelected
    }

    fun setTextColor(color: Int) {
        textView.setTextColor(color)
    }

    /**
     * 设置图文的布局样式
     */
    fun setTextOrientation(orientation: Int) {
        this.orientationType = orientation
        invalidate()
    }
}
