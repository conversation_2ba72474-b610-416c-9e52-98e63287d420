package com.aquila.lib.widget

import android.content.Context
import android.view.animation.Interpolator
import android.widget.Scroller

/**
 * 利用这个类来修正ViewPager的滑动速度
 * 我们重写 startScroll方法，忽略传过来的 duration 属性
 * 而是采用我们自己设置的时间
 */
class FixedSpeedScroller @JvmOverloads constructor(context: Context, interpolator: Interpolator? = null) : <PERSON><PERSON><PERSON>(context, interpolator) {

    var mDuration = 1500

    override fun startScroll(startX: Int, startY: Int, dx: Int, dy: Int) {
        startScroll(startX, startY, dx, dy, mDuration)
    }

    override fun startScroll(startX: Int, startY: Int, dx: Int, dy: Int, duration: Int) {
        // 管你 ViewPager 传来什么时间，我完全不鸟你
        super.startScroll(startX, startY, dx, dy, mDuration)
    }
}
