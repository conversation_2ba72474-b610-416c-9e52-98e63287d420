package com.aquila.lib.widget.view

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Rect
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.View
import android.view.animation.DecelerateInterpolator
import android.view.animation.TranslateAnimation
import androidx.core.widget.NestedScrollView

/***
 * ScrollView反弹效果的实现
 * <AUTHOR>
 * 2012-5-17
 * 上午09:54:52
 */
class ElasticScrollView : NestedScrollView {
    private val MaxY = 1280 // App.windowHeight;//MerchantAPP.windowHeight
    /**
     * 阻尼效果 越小 效果越明显
     */
    var damp = 3
    /**
     * 弹簧动画持续时间
     */
    var animatioN_TIME = 1000
    /**
     * 弹力效果
     */
    /**
     * 以下是各种set 和get方法  设置 阻尼系数等
     *
     * @return
     */
    var spring = 4.0f
    private var mView: View? = null
    private val mRect = Rect()
    private var touchY: Float = 0.toFloat()

    private var canScroll = false

    private val isNeedMove: Boolean
        get() {
            val offset = mView!!.measuredHeight - height
            val scrollY = scrollY
            return scrollY == 0 || scrollY == offset
        }

    private val isNeedAnimation: Boolean
        get() = !mRect.isEmpty

    constructor(context: Context) : super(context)

    constructor(context: Context, attrs: AttributeSet) : super(context, attrs)

    constructor(context: Context, attrs: AttributeSet, defStyle: Int) : super(context, attrs, defStyle)

    @SuppressLint("MissingSuperCall")
    override fun onFinishInflate() {
        if (childCount > 0) {
            this.mView = getChildAt(0)
        }
    }

    override fun onTouchEvent(ev: MotionEvent): Boolean {
        if (mView != null) {
            commOnTouchEvent(ev)
        }
        return super.onTouchEvent(ev)
    }

    private fun commOnTouchEvent(ev: MotionEvent) {
        val action = ev.action
        when (action) {
            MotionEvent.ACTION_DOWN -> touchY = ev.y
            MotionEvent.ACTION_UP -> if (isNeedAnimation) {
                animation()
            }

            MotionEvent.ACTION_MOVE -> {
                val preY = touchY
                val nowY = ev.y
                val deltaY = (preY - nowY).toInt()
                canScroll = !(Math.abs(deltaY) < Math.abs(nowY) && Math.abs(deltaY) + 1 > Math.abs(nowY))
                touchY = nowY
                if (isNeedMove && canScroll) {
                    if (mRect.isEmpty) {
                        mRect.set(mView!!.left, mView!!.top, mView!!.right, mView!!.bottom)
                    }
                    if (mView!!.top - deltaY / damp < MaxY && mView!!.top - deltaY / damp > -MaxY) {
                        mView!!.layout(mView!!.left, mView!!.top - deltaY / damp, mView!!.right, mView!!.bottom - deltaY / damp)
                    }
                }
            }
            else -> {
            }
        }
    }

    private fun animation() {
        val ta = TranslateAnimation(0f, 0f, mView!!.top.toFloat(), mRect.top.toFloat())
        ta.duration = animatioN_TIME.toLong()
        /**
         * 此处是回弹动画设置
         * 体验效果更佳
         */
        ta.interpolator = DecelerateInterpolator(spring)
        mView!!.startAnimation(ta)
        mView!!.layout(mRect.left, mRect.top, mRect.right, mRect.bottom)
        mRect.setEmpty()
        touchY = 0f
    }
}
