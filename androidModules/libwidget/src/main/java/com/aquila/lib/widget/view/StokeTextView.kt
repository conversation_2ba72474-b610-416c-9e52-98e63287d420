package com.aquila.lib.widget.view

import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.text.TextPaint
import android.util.AttributeSet
import android.widget.TextView
import androidx.appcompat.widget.AppCompatTextView
import com.aquila.lib.widget.R
import java.lang.reflect.Field

/***
 * @date 创建时间 2020/3/10 16:27
 * <AUTHOR> <PERSON><PERSON>
 * @description 带有描边的文字控件
 */
class StokeTextView(context: Context, attrs: AttributeSet? = null) : AppCompatTextView(context, attrs) {
    var textPaint: TextPaint
    var borderColor: Int = Color.WHITE
    var borderSize: Float = 0f
    var borderEnable = true // 默认采用描边

    init {
        val a = context.obtainStyledAttributes(attrs, R.styleable.StokeTextView)
        borderColor = a.getColor(R.styleable.StokeTextView_attr_border_color, Color.WHITE)
        borderSize = a.getDimension(R.styleable.StokeTextView_attr_border_size, 0f)
        borderEnable = a.getBoolean(R.styleable.StokeTextView_attr_border_enable, true)
        a.recycle()

        textPaint = this.paint
    }

    override fun onDraw(canvas: Canvas) {
        if (borderEnable) { // 描外层
            setTextColorUseReflection(borderColor)
            textPaint.strokeWidth = borderSize // 描边宽度
            textPaint.style = Paint.Style.STROKE // 描边种类
            textPaint.isFakeBoldText = true // 外层text采用粗体
            textPaint.setShadowLayer(0f, 0f, 0f, borderColor) // 字体的阴影效果，可以忽略
            super.onDraw(canvas)

            // 描内层，恢复原先的画笔
            setTextColorUseReflection(Color.WHITE)
            textPaint.strokeWidth = 0f
            textPaint.style = Paint.Style.FILL_AND_STROKE
            textPaint.isFakeBoldText = false
            textPaint.setShadowLayer(0f, 0f, 0f, 0)
        }
        super.onDraw(canvas)
    }

    /**
     * 使用反射的方法进行字体颜色的设置
     */
    private fun setTextColorUseReflection(color: Int) {
        val textColorField: Field
        try {
            textColorField = TextView::class.java.getDeclaredField("mCurTextColor")
            textColorField.isAccessible = true
            textColorField.set(this, color)
            textColorField.isAccessible = false
        } catch (e: NoSuchFieldException) {
            e.printStackTrace()
        } catch (e: IllegalArgumentException) {
            e.printStackTrace()
        } catch (e: IllegalAccessException) {
            e.printStackTrace()
        }
        textPaint.color = color
    }
}
