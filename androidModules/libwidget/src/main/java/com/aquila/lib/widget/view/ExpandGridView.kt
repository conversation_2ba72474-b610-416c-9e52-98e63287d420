package com.aquila.lib.widget.view

import android.content.Context
import android.util.AttributeSet
import android.widget.GridView

/***
 * @date 创建时间 2018/4/11 11:35
 * <AUTHOR> yulong
 * @description 展开的GridView
 */
class ExpandGridView @JvmOverloads constructor(context: Context, attrs: AttributeSet? = null) : GridView(context, attrs) {

    /**
     * 设置不滚动
     */
    public override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        val expandSpec = MeasureSpec.makeMeasureSpec(
            Integer.MAX_VALUE shr 2,
            MeasureSpec.AT_MOST
        )
        super.onMeasure(widthMeasureSpec, expandSpec)
    }
}
