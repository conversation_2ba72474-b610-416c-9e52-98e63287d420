// package com.aquila.lib.widget.view
//
// import android.content.Context
// import android.graphics.*
// import android.util.AttributeSet
// import androidx.appcompat.widget.AppCompatImageView
// import com.aquila.lib.widget.R
//
// /***
// * @date 创建时间 2020/6/4 14:41
// * <AUTHOR> <PERSON><PERSON>
// * @description
// */
// class RoundImageView @JvmOverloads constructor(context: Context, attrs: AttributeSet? = null) : AppCompatImageView(context, attrs) {
//
//    /*** 这两个都是画圆的半径*/
//    private var roundWidth = 0
//    private var roundHeight = 0
//    private var roundPaint: Paint = Paint(Paint.ANTI_ALIAS_FLAG)
//    private var paint2: Paint = Paint(Paint.ANTI_ALIAS_FLAG)
//
//    init {
//        attrs?.let {
//            val a = context.obtainStyledAttributes(it, R.styleable.RoundImageView)
//            roundWidth = a.getDimensionPixelSize(R.styleable.RoundImageView_attr_round_width, roundWidth)
//            roundHeight = a.getDimensionPixelSize(R.styleable.RoundImageView_attr_round_height, roundHeight)
//
//        }
//        roundPaint.color = Color.WHITE
//        roundPaint.xfermode = PorterDuffXfermode(PorterDuff.Mode.DST_OUT)
//        paint2.xfermode = null
//    }
//
//
//    override fun draw(canvas: Canvas) {
//        val bitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
//        val newCanvas = Canvas(bitmap)
//        super.draw(newCanvas)
//        drawLeftTop(newCanvas)
//        drawLeftBottom(newCanvas)
//        drawRightTop(newCanvas)
//        drawRightBottom(newCanvas)
//        canvas.drawBitmap(bitmap, 0f, 0f, paint2)
//        bitmap.recycle()
//    }
//
//
//    private fun drawLeftTop(canvas: Canvas) {
//        val path = Path()
//        path.moveTo(0f, roundHeight.toFloat())
//        path.lineTo(0f, 0f)
//        path.lineTo(roundWidth.toFloat(), 0f)
//        path.arcTo(RectF(0f, 0f, (roundWidth * 2).toFloat(), (roundHeight * 2).toFloat()), -90f, -90f)
//        path.close()
//        canvas.drawPath(path, roundPaint)
//    }
//
//    private fun drawLeftBottom(canvas: Canvas) {
//        val path = Path()
//        path.moveTo(0f, height - roundHeight.toFloat())
//        path.lineTo(0f, height.toFloat())
//        path.lineTo(roundWidth.toFloat(), height.toFloat())
//        path.arcTo(RectF(0f, (height - roundHeight * 2).toFloat(), (roundWidth * 2).toFloat(), height.toFloat()), 90f, 90f)
//        path.close()
//        canvas.drawPath(path, roundPaint)
//    }
//
//    private fun drawRightBottom(canvas: Canvas) {
//        val path = Path()
//        path.moveTo(width - roundWidth.toFloat(), height.toFloat())
//        path.lineTo(width.toFloat(), height.toFloat())
//        path.lineTo(width.toFloat(), height - roundHeight.toFloat())
//        path.arcTo(RectF((width - roundWidth * 2).toFloat(), (height - roundHeight * 2).toFloat(), width.toFloat(), height.toFloat()), -0f, 90f)
//        path.close()
//        canvas.drawPath(path, roundPaint)
//    }
//
//    private fun drawRightTop(canvas: Canvas) {
//        val path = Path()
//        path.moveTo(width.toFloat(), roundHeight.toFloat())
//        path.lineTo(width.toFloat(), 0f)
//        path.lineTo(width - roundWidth.toFloat(), 0f)
//        path.arcTo(RectF((width - roundWidth * 2).toFloat(), 0f, width.toFloat(), (0 + roundHeight * 2).toFloat()), -90f, 90f)
//        path.close()
//        canvas.drawPath(path, roundPaint)
//    }
// }
