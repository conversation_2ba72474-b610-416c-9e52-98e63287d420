package com.aquila.lib.widget.group

import android.content.Context
import android.content.res.ColorStateList
import android.graphics.Typeface
import android.text.TextUtils
import android.util.AttributeSet
import android.util.TypedValue
import android.view.Gravity
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.annotation.StringRes
import com.aquila.lib.widget.R

/***
 * @date 创建时间 2018/5/7 16:39
 * <AUTHOR> YuLong
 * @description 文字的左右对齐
 */
class GroupTwoTextLayout @JvmOverloads constructor(context: Context, attrs: AttributeSet? = null) : RelativeLayout(context, attrs) {
    val firstTextView: TextView
    val secondTextView: TextView

    init {
        inflate(context, R.layout.group_two_text_layout, this)
        firstTextView = findViewById(R.id.group_two_left_TextView)
        secondTextView = findViewById(R.id.group_two_right_TextView)

        attrs?.let {

            initAttrs(context, it)
        }
    }

    private fun initAttrs(context: Context, attrs: AttributeSet) {
        val layoutOrientation: Int
        val isLinkage: Boolean
        var innerMargin: Int

        val firstConfigEntity = TextConfigEntity()
        val secondConfigEntity: TextConfigEntity

        val a = context.obtainStyledAttributes(attrs, R.styleable.GroupTwoTextLayout)
        firstConfigEntity.textColorState = a.getColorStateList(R.styleable.GroupTwoTextLayout_attr_first_text_color)
        firstConfigEntity.text = a.getString(R.styleable.GroupTwoTextLayout_attr_first_text)
        firstConfigEntity.textSize = a.getDimensionPixelSize(R.styleable.GroupTwoTextLayout_attr_first_text_size, -1)
        firstConfigEntity.width = a.getLayoutDimension(R.styleable.GroupTwoTextLayout_attr_first_text_width, -2)
        firstConfigEntity.height = a.getLayoutDimension(R.styleable.GroupTwoTextLayout_attr_first_text_height, -2)
        firstConfigEntity.gravityIndex = a.getInt(R.styleable.GroupTwoTextLayout_attr_first_text_gravity, -1)
        firstConfigEntity.isBold = a.getBoolean(R.styleable.GroupTwoTextLayout_attr_first_text_bold, false)

        innerMargin = a.getDimensionPixelOffset(R.styleable.GroupTwoTextLayout_attr_inner_margin_size, 0)
        layoutOrientation = a.getInt(R.styleable.GroupTwoTextLayout_attr_layout_orientation, 0)
        isLinkage = a.getBoolean(R.styleable.GroupTwoTextLayout_attr_is_linkage, true)

        if (isLinkage) {
            secondConfigEntity = firstConfigEntity.copyThisEntity()
        } else {
            secondConfigEntity = TextConfigEntity()
            secondConfigEntity.textColorState = a.getColorStateList(R.styleable.GroupTwoTextLayout_attr_second_text_color)
            secondConfigEntity.textSize = a.getDimensionPixelSize(R.styleable.GroupTwoTextLayout_attr_second_text_size, -1)
        }

        secondConfigEntity.text = a.getString(R.styleable.GroupTwoTextLayout_attr_second_text)
        secondConfigEntity.hintText = a.getString(R.styleable.GroupTwoTextLayout_attr_second_hint_text)
        secondConfigEntity.width = a.getLayoutDimension(R.styleable.GroupTwoTextLayout_attr_second_text_width, -2)
        secondConfigEntity.height = a.getLayoutDimension(R.styleable.GroupTwoTextLayout_attr_second_text_height, -2)
        secondConfigEntity.gravityIndex = a.getInt(R.styleable.GroupTwoTextLayout_attr_second_text_gravity, -1)
        secondConfigEntity.isBold = a.getBoolean(R.styleable.GroupTwoTextLayout_attr_second_text_bold, false)
        a.recycle()

        if (!TextUtils.isEmpty(firstConfigEntity.text)) {
            firstTextView.text = firstConfigEntity.text
        }

        if (firstConfigEntity.textSize != -1) {
            firstTextView.setTextSize(TypedValue.COMPLEX_UNIT_PX, firstConfigEntity.textSize.toFloat())
        }
        if (firstConfigEntity.textColorState != null) {
            firstTextView.setTextColor(firstConfigEntity.textColorState)
        }

        firstTextView.typeface = Typeface.defaultFromStyle(if (firstConfigEntity.isBold) Typeface.BOLD else Typeface.NORMAL)
        secondTextView.typeface = Typeface.defaultFromStyle(if (secondConfigEntity.isBold) Typeface.BOLD else Typeface.NORMAL)

        val firstParams = firstTextView.layoutParams as LayoutParams
        firstParams.width = firstConfigEntity.width
        firstParams.height = firstConfigEntity.height
        firstParams.addRule(ALIGN_PARENT_TOP)

        val secondParams = secondTextView.layoutParams as LayoutParams
        secondParams.width = secondConfigEntity.width
        secondParams.height = secondConfigEntity.height

        if (layoutOrientation == 0) {
            secondParams.addRule(RIGHT_OF, R.id.group_two_left_TextView)
            secondParams.addRule(CENTER_VERTICAL)
            firstParams.rightMargin = innerMargin / 2
            secondParams.leftMargin = firstParams.rightMargin
            firstTextView.gravity = Gravity.CENTER_VERTICAL
            secondTextView.gravity = Gravity.CENTER_VERTICAL
        } else {
            secondParams.addRule(BELOW, R.id.group_two_left_TextView)
            secondParams.addRule(CENTER_HORIZONTAL)
            firstParams.bottomMargin = innerMargin / 2
            secondParams.topMargin = firstParams.bottomMargin
            firstTextView.gravity = Gravity.CENTER_HORIZONTAL
            secondTextView.gravity = Gravity.CENTER_HORIZONTAL
        }

        secondTextView.text = secondConfigEntity.text
        secondTextView.hint = secondConfigEntity.hintText

        if (secondConfigEntity.textColorState != null) {
            secondTextView.setTextColor(secondConfigEntity.textColorState)
        }
        if (secondConfigEntity.textSize > 0) {
            secondTextView.setTextSize(TypedValue.COMPLEX_UNIT_PX, secondConfigEntity.textSize.toFloat())
        }

        firstTextView.layoutParams = firstParams
        secondTextView.layoutParams = secondParams
    }

    fun setLeftText(@StringRes textID: Int) {
        firstTextView.setText(textID)
    }

    fun setLeftText(text: CharSequence) {
        firstTextView.text = text
    }

    fun setContentText(text: CharSequence) {
        secondTextView.text = text
    }

    fun setContentText(@StringRes textId: Int) {
        secondTextView.setText(textId)
    }

    fun initTextGravity(textView: TextView, gravity: Int) {
        if (gravity < 0 || gravity >= GRAVITY_ARRAY.size) {
            return
        }
        textView.gravity = GRAVITY_ARRAY[gravity]
    }

    private class TextConfigEntity {
        var textColorState: ColorStateList? = null
        var textSize: Int = 0
        var text: String? = null
        var hintText: String? = null
        var width: Int = 0
        var height: Int = 0
        var gravityIndex: Int = 0
        var isBold: Boolean = false

        fun copyThisEntity(): TextConfigEntity {
            val entity = TextConfigEntity()
            entity.textColorState = textColorState
            entity.textSize = textSize
            entity.gravityIndex = gravityIndex
            return entity
        }
    }

    companion object {
        private val GRAVITY_ARRAY = intArrayOf(Gravity.CENTER_HORIZONTAL, Gravity.LEFT, Gravity.RIGHT, Gravity.CENTER, Gravity.CENTER_VERTICAL)
    }
}
