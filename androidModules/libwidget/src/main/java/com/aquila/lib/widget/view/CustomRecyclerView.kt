package com.aquila.lib.widget.view

import android.content.Context
import android.content.res.Resources
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.Rect
import android.util.AttributeSet
import android.view.View
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.StaggeredGridLayoutManager
import com.aquila.lib.widget.R
import com.google.android.flexbox.FlexDirection
import com.google.android.flexbox.FlexWrap

/***
 * @date 创建时间 2018/10/30 15:47
 * <AUTHOR> W.YuLong
 * @description 新版的自定义RecyclerView
 */
class CustomRecyclerView @JvmOverloads constructor(context: Context, attrs: AttributeSet? = null) : RecyclerView(context, attrs) {

    private var isExpanded = false
    private var maxHeight = -1

    companion object {
        val OTHER = 99
        val LIST_VERTICAL = 0
        val LIST_HORIZONTAL = 1
        val GRID_VERTICAL = 2
        val GRID_HORIZONTAL = 3
        val STAGGERED_VERTICAL = 4
        val STAGGERED_HORIZONTAL = 5
        val FLEXBOX = 6

        val TYPE_ITEM_DECORATION_NONE = 0
        val TYPE_ITEM_DECORATION_DIVIDE_LINE = 1
        val TYPE_ITEM_DECORATION_SPACE_MARGIN = 1 shl 1
    }

    var layoutStyle = LIST_VERTICAL

    @FlexDirection
    private var flexDirection: Int = FlexDirection.ROW

    @FlexWrap
    private var flexWrap: Int = FlexWrap.WRAP

    init {
        attrs?.let {
            val a = context.obtainStyledAttributes(attrs, R.styleable.CustomRecyclerView)
            layoutStyle = a.getInteger(R.styleable.CustomRecyclerView_attr_layout_style, LIST_VERTICAL)
            val itemDecorationType = a.getInteger(R.styleable.CustomRecyclerView_attr_item_decoration_type, 0)

            val spanCount = a.getInt(R.styleable.CustomRecyclerView_attr_span_count, 1)

            val itemSpace = a.getDimensionPixelOffset(R.styleable.CustomRecyclerView_attr_item_margin, 0)
            val startItemMargin = a.getDimensionPixelOffset(R.styleable.CustomRecyclerView_attr_start_item_margin, 0)
            val endItemMargin = a.getDimensionPixelOffset(R.styleable.CustomRecyclerView_attr_end_item_margin, 0)

            val leftDivideMargin = a.getDimensionPixelSize(R.styleable.CustomRecyclerView_attr_divide_left_margin, 0)
            val rightDivideMargin = a.getDimensionPixelSize(R.styleable.CustomRecyclerView_attr_divide_right_margin, 0)
            val topDivideMargin = a.getDimensionPixelSize(R.styleable.CustomRecyclerView_attr_divide_top_margin, 0)
            val bottomDivideMargin = a.getDimensionPixelSize(R.styleable.CustomRecyclerView_attr_divide_bottom_margin, 0)

            val divideLineColor = a.getColor(R.styleable.CustomRecyclerView_attr_item_divide_line_color, -1)
            val divideLineSize = a.getDimensionPixelSize(R.styleable.CustomRecyclerView_attr_item_divide_line_size, -1)
            maxHeight = a.getDimensionPixelOffset(R.styleable.CustomRecyclerView_attr_recycler_view_max_height, -1)
            isExpanded = a.getBoolean(R.styleable.CustomRecyclerView_attr_is_expanded, false)

            flexDirection = a.getInt(R.styleable.CustomRecyclerView_attr_flex_direction, FlexDirection.ROW)
            flexWrap = a.getInt(R.styleable.CustomRecyclerView_attr_flex_wrap, FlexWrap.WRAP)
            a.recycle()

            setLayoutStyle(layoutStyle, spanCount)

            // 设置了Item的间距的话就画间距
            if (itemSpace > 0 || startItemMargin > 0 || endItemMargin > 0) {
                val itemDecoration = CustomItemDecoration.with().setDivideLineColor(divideLineColor)
                    .setOrientation(layoutStyle).setSpanCount(spanCount)
                    .setDivideLineWidth(divideLineSize).setItemDecorationType(TYPE_ITEM_DECORATION_SPACE_MARGIN)
                    .setStartItemMargin(startItemMargin).setEndItemMargin(endItemMargin)
                    .setItemMargin(itemSpace).builder()
                addItemDecoration(itemDecoration)
            }

            // 设置了划线的话就画分割线
            if (itemDecorationType == TYPE_ITEM_DECORATION_DIVIDE_LINE) {
                val itemDecoration = CustomItemDecoration.with().setDivideLineColor(divideLineColor)
                    .setOrientation(layoutStyle).setSpanCount(spanCount)
                    .setLeftMargin(leftDivideMargin).setRightMargin(rightDivideMargin)
                    .setTopMargin(topDivideMargin).setBottomMargin(bottomDivideMargin)
                    .setDivideLineWidth(divideLineSize).setItemDecorationType(itemDecorationType)
                    .setStartItemMargin(startItemMargin).setEndItemMargin(endItemMargin)
                    .setItemMargin(itemSpace)
                    .builder()
                addItemDecoration(itemDecoration)
            }
        }
    }

    fun setItemDivide() {
        val itemDecoration = CustomItemDecoration.with().setDivideLineColor(Color.parseColor("#ddd"))
            .setOrientation(LIST_VERTICAL)
            .setDivideLineWidth(dp2px(0.7f).toInt()).setItemDecorationType(TYPE_ITEM_DECORATION_DIVIDE_LINE)
            .builder()
        addItemDecoration(itemDecoration)
    }

    private fun dp2px(i: Float): Float {
        val scale = Resources.getSystem().displayMetrics.density
        return i * scale
    }

    override fun onMeasure(widthSpec: Int, heightSpec: Int) {
        if (maxHeight != -1) {
            val width = MeasureSpec.getSize(widthSpec)
            var height = MeasureSpec.getSize(heightSpec)
            height = Math.min(height, maxHeight)
            setMeasuredDimension(width, height)
        } else {
            if (isExpanded) {
                val measureSpec = View.MeasureSpec.makeMeasureSpec(
                    Integer.MAX_VALUE shr 2,
                    View.MeasureSpec.AT_MOST
                )
                super.onMeasure(widthSpec, measureSpec)
            } else {
                super.onMeasure(widthSpec, heightSpec)
            }
        }
    }

    fun setLayoutStyle(layoutStyle: Int, spanCount: Int = 1) {
        layoutManager = when (layoutStyle) {
            LIST_HORIZONTAL -> LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
            GRID_VERTICAL -> GridLayoutManager(context, spanCount, GridLayoutManager.VERTICAL, false)
            GRID_HORIZONTAL -> GridLayoutManager(context, spanCount, GridLayoutManager.HORIZONTAL, false)
            STAGGERED_VERTICAL -> StaggeredGridLayoutManager(spanCount, StaggeredGridLayoutManager.VERTICAL)
            STAGGERED_HORIZONTAL -> StaggeredGridLayoutManager(spanCount, StaggeredGridLayoutManager.HORIZONTAL)
            LIST_VERTICAL -> LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false)
            FLEXBOX -> FilterFlexboxLayoutManager(context, flexDirection, flexWrap)
            else // 默认的就是列表模式
            -> LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false)
        }
        this.layoutStyle = layoutStyle
    }

    /***
     * @date 创建时间 2018/10/30 14:08
     * <AUTHOR> W.YuLong
     * @description
     */
    class CustomItemDecoration(private val builder: Builder) : RecyclerView.ItemDecoration() {
        private val paint: Paint

        init {
            paint = Paint(Paint.ANTI_ALIAS_FLAG)
            paint.color = builder.divideLineColor
            paint.style = Paint.Style.FILL
        }

        override fun onDrawOver(c: Canvas, parent: RecyclerView, state: RecyclerView.State) {
            if (builder.itemDecorationType == TYPE_ITEM_DECORATION_DIVIDE_LINE) {
                drawDivideLine(c, parent, builder.orientation)
            }
        }

        // 根据Item布局画分割线，目前主要针对水平和列表的list
        protected fun drawDivideLine(c: Canvas, parent: RecyclerView, orientation: Int) {
            val left = parent.paddingLeft + builder.leftMargin
            val right = parent.width - parent.paddingRight - builder.rightMargin
            val top = parent.paddingTop + builder.topMargin
            val bottom = parent.height - parent.paddingBottom - builder.bottomMargin
            val itemSpace = (builder.itemMargin + 1) / 2
            var i = 0
            val childCount = parent.childCount
            while (i < childCount) {
                val child = parent.getChildAt(i)
                if (orientation == LIST_VERTICAL) {
                    c.drawRect(
                        left.toFloat(),
                        (child.bottom + itemSpace).toFloat(),
                        right.toFloat(),
                        (child.bottom + itemSpace + builder.divideLineWidth).toFloat(),
                        paint
                    )
                } else if (orientation == LIST_HORIZONTAL) {
                    c.drawRect(
                        (child.right + itemSpace).toFloat(),
                        top.toFloat(),
                        (child.right + itemSpace + builder.divideLineWidth).toFloat(),
                        bottom.toFloat(),
                        paint
                    )
                }
                i++
            }
        }

        override fun getItemOffsets(outRect: Rect, view: View, parent: RecyclerView, state: RecyclerView.State) {

            if (builder.itemDecorationType == TYPE_ITEM_DECORATION_NONE) {
                return
            }

            val position = parent.getChildAdapterPosition(view)
            val itemCount = parent.adapter!!.itemCount

            if (itemCount <= 1) {
                return
            }

            if (builder.itemDecorationType == TYPE_ITEM_DECORATION_DIVIDE_LINE) {
                setDivideLineOffset(outRect, position, itemCount)
            } else if (builder.itemDecorationType == TYPE_ITEM_DECORATION_SPACE_MARGIN) {
                setItemMarginOffset(outRect, position, itemCount)
            }
        }

        /*设置每个Item的间距*/
        private fun setItemMarginOffset(outRect: Rect, position: Int, itemCount: Int) {
            var lastRemainCount = itemCount % builder.spanCount
            if (lastRemainCount == 0) {
                lastRemainCount = builder.spanCount
            }

            val startItemSpace = builder.startItemMargin
            val itemSpace = (builder.itemMargin + 1) / 2
            val endItemSpace = builder.endItemMargin
            val spanCount = builder.spanCount

            when (builder.orientation) {
                LIST_HORIZONTAL -> if (position == 0) {
                    outRect.left = startItemSpace
                    outRect.right = itemSpace
                } else if (position == itemCount - 1) {
                    outRect.right = endItemSpace
                    outRect.left = itemSpace
                } else {
                    outRect.left = itemSpace
                    outRect.right = itemSpace
                }
                LIST_VERTICAL -> if (position == 0) {
                    outRect.top = startItemSpace
                    outRect.bottom = itemSpace
                } else if (position == itemCount - 1) {
                    outRect.top = itemSpace
                    outRect.bottom = endItemSpace
                } else {
                    outRect.top = itemSpace
                    outRect.bottom = itemSpace
                }
                GRID_VERTICAL, STAGGERED_VERTICAL -> {
                    // 顶部一排的item
                    if (position < spanCount) {
                        outRect.top = startItemSpace
                        outRect.bottom = itemSpace
                    } else if (itemCount - position <= lastRemainCount) {
                        // 底部最后一排的下边距
                        outRect.bottom = endItemSpace
                        outRect.top = itemSpace
                    } else {
                        outRect.top = itemSpace
                        outRect.bottom = itemSpace
                    }

                    // 最左边一列Item不需要左边距
                    if (position % spanCount == 0) {
                        //                        outRect.right = itemSpace ;
                    } else if ((position + 1) % spanCount == 0) { // 最右边一列
                        outRect.left = itemSpace
                    } else {
                        outRect.left = itemSpace
                        outRect.right = itemSpace
                    }
                }
                GRID_HORIZONTAL, STAGGERED_HORIZONTAL -> {
                    // 最左边一排的item左边距
                    if (position < spanCount) {
                        outRect.left = startItemSpace
                        outRect.right = itemSpace
                    } else if (itemCount - position <= lastRemainCount) {
                        // 最右边一排的右边距
                        outRect.right = endItemSpace
                        outRect.left = itemSpace
                    } else {
                        outRect.left = itemSpace
                        outRect.right = itemSpace
                    }

                    // 顶部一排
                    if (position % spanCount == 0) {
                        outRect.bottom = itemSpace
                    } else if ((position + 1) % spanCount == 0) {
                        // 底部一排
                        outRect.top = itemSpace
                    } else {
                        outRect.top = itemSpace
                        outRect.bottom = itemSpace
                    }
                }
                OTHER -> {
                }
            }
        }

        /*设置分割线的Item位置*/
        private fun setDivideLineOffset(outRect: Rect, position: Int, itemCount: Int) {
            // 这个divideLineWidth + 1是为了如果divideLineWidth=1的时候除以2就为0了，
            val divideLineWidth = (builder.divideLineWidth + 1) / 2
            when (builder.orientation) {
                LIST_HORIZONTAL -> if (itemCount > 1) {

                    if (position == 0) {
                        outRect.left = 0
                        outRect.right = divideLineWidth
                    } else if (position == itemCount - 1) {
                        outRect.left = divideLineWidth
                        outRect.right = 0
                    } else {
                        outRect.right = divideLineWidth
                        outRect.left = divideLineWidth
                    }
                }
                LIST_VERTICAL -> if (itemCount > 0) {
                    if (position == 0) {
                        outRect.top = 0
                        outRect.bottom = divideLineWidth
                    } else if (position == itemCount - 1) {
                        outRect.top = divideLineWidth
                        outRect.bottom = 0
                    } else {
                        outRect.top = divideLineWidth
                        outRect.bottom = divideLineWidth
                    }
                }
            }
        }

        /***
         * @date 创建时间 2018/10/30 15:47
         * <AUTHOR> W.YuLong
         * @description
         */
        class Builder {
            internal var orientation: Int = 0

            internal var startItemMargin = 0
            internal var itemMargin = 0
            internal var endItemMargin = 0
            internal var spanCount = 1

            internal var leftMargin = 0
            internal var rightMargin = 0
            internal var topMargin = 0
            internal var bottomMargin = 0

            internal var divideLineColor = Color.TRANSPARENT

            internal var divideLineWidth = 0
            internal var itemDecorationType = TYPE_ITEM_DECORATION_DIVIDE_LINE

            fun setDivideLineColor(divideLineColor: Int): Builder {
                if (divideLineColor == -1) {
                    this.divideLineColor = Color.LTGRAY
                } else {
                    this.divideLineColor = divideLineColor
                }
                return this
            }

            fun setBottomMargin(value: Int): Builder {
                bottomMargin = value
                return this
            }

            fun setTopMargin(value: Int): Builder {
                topMargin = value
                return this
            }

            fun setRightMargin(value: Int): Builder {
                rightMargin = value
                return this
            }

            fun setLeftMargin(left: Int): Builder {
                leftMargin = left
                return this
            }

            fun setOrientation(orientation: Int): Builder {
                this.orientation = orientation
                return this
            }

            fun setStartItemMargin(startItemMargin: Int): Builder {
                this.startItemMargin = startItemMargin
                return this
            }

            fun setItemMargin(itemMargin: Int): Builder {
                this.itemMargin = itemMargin
                return this
            }

            fun setEndItemMargin(endItemMargin: Int): Builder {
                this.endItemMargin = endItemMargin
                return this
            }

            fun setSpanCount(spanCount: Int): Builder {
                this.spanCount = spanCount
                return this
            }

            fun setDivideLineWidth(divideLineWidth: Int): Builder {
                this.divideLineWidth = divideLineWidth
                return this
            }

            fun setItemDecorationType(type: Int): Builder {
                this.itemDecorationType = type
                return this
            }

            fun builder(): CustomItemDecoration {
                return CustomItemDecoration(this)
            }
        }

        companion object {

            fun with(): Builder {
                return Builder()
            }
        }
    }
}
