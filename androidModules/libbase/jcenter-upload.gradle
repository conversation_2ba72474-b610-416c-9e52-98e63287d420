/** 以下开始是将Android Library上传到JCenter的相关配置**/
apply plugin: 'com.github.dcendents.android-maven'
apply plugin: 'com.jfrog.bintray'


//项目主页
def siteUrl = 'https://github.com/aquila1988/AndroidLibProj/tree/master/libbase'
//项目的版本控制地址
def gitUrl = 'https://github.com/aquila1988/AndroidLibProj.git'

//发布到组织名称名字，必须填写
group = "com.aquila.lib.base"
//发布到JCenter上的项目名字，必须填写
def libName = "lib-base"
// 版本号，下次更新是只需要更改版本号即可
version = "0.0.6"
/**  上面配置后上传至JCenter后的编译路径是这样的： compile 'group:libName:version'  **/

//生成源文件
task sourcesJar(type: Jar) {
    from android.sourceSets.main.java.srcDirs
    classifier = 'sources'
}
//生成文档
task javadoc(type: Javadoc) {
    source = android.sourceSets.main.java.srcDirs
    classpath += project.files(android.getBootClasspath().join(File.pathSeparator))
    options.encoding "UTF-8"
    options.charSet 'UTF-8'
    options.author true
    options.version true
    options.links "https://github.com/aquila1988"
    failOnError false
}

//文档打包成jar
task javadocJar(type: Jar, dependsOn: javadoc) {
    classifier = 'javadoc'
    from javadoc.destinationDir
}
//拷贝javadoc文件
task copyDoc(type: Copy) {
    from "${buildDir}/docs/"
    into "docs"
}

//上传到jCenter所需要的源码文件
artifacts {
    archives javadocJar
    archives sourcesJar
}

// 配置maven库，生成POM.xml文件
install {
    repositories.mavenInstaller {
        // This generates POM.xml with proper parameters
        pom {
            project {
                packaging 'aar'
                name 'Android libs base'
                url siteUrl
                licenses {
                    license {
                        name 'The Apache Software License, Version 2.0'
                        url 'http://www.apache.org/licenses/LICENSE-2.0.txt'
                    }
                }
                developers {
                    developer {
                        id 'aquila'
                        name 'yulong wang'
                        email '<EMAIL>'
                    }
                }
                scm {
                    connection gitUrl
                    developerConnection gitUrl
                    url siteUrl
                }
            }
        }
    }
}

//上传到JCenter
Properties properties = new Properties()
properties.load(project.rootProject.file('local.properties').newDataInputStream())
bintray {
    user = properties.getProperty("bintray.user")    //读取 local.properties 文件里面的 bintray.user
    key = properties.getProperty("bintray.apikey")   //读取 local.properties 文件里面的 bintray.apikey
    configurations = ['archives']
    pkg {
        repo = "maven"
        name = libName    //发布到JCenter上的项目名字，必须填写
        desc = 'Android libs base'    //项目描述
        websiteUrl = siteUrl
        vcsUrl = gitUrl
        licenses = ["Apache-2.0"]
        publish = true
    }
}

repositories {
    mavenCentral()
}