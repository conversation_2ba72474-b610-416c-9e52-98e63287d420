<?xml version="1.0" encoding="utf-8"?>
<resources>
    <attr name="SwipeBackLayoutStyle" format="reference" />

    <declare-styleable name="SwipeBackLayout">
        <attr name="edge_size" format="dimension" />
        <attr name="edge_flag">
            <enum name="left" value="0" />
            <enum name="right" value="1" />
            <enum name="bottom" value="2" />
            <enum name="all" value="3" />
        </attr>
        <attr name="shadow_left" format="reference" />
        <attr name="shadow_right" format="reference" />
        <attr name="shadow_bottom" format="reference" />
    </declare-styleable>


    <style name="SwipeBackLayout">
        <item name="edge_size">50dip</item>
        <item name="shadow_left">@drawable/shadow_left</item>
        <item name="shadow_right">@drawable/shadow_right</item>
        <item name="shadow_bottom">@drawable/shadow_bottom</item>
    </style>
</resources>