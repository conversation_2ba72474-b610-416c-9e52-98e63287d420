package com.aquila.lib.base

import android.content.Intent
import androidx.appcompat.app.AppCompatActivity

/***
 * @date 创建时间 2018/3/22 11:07
 * <AUTHOR> <PERSON><PERSON>
 * @description 所有Activity的基类
 */
open class BaseRootActivity : AppCompatActivity() {

    override fun startActivity(intent: Intent) {
        super.startActivity(intent)
        enterPendingAnim()
    }

    override fun finish() {
        super.finish()
        exitPendingAnim()
    }

    override fun startActivityForResult(intent: Intent, requestCode: Int) {
        super.startActivityForResult(intent, requestCode)
        enterPendingAnim()
    }

    open fun exitPendingAnim() {
        overridePendingTransition(R.anim.base_anim_normal, R.anim.base_slide_out_right)
    }

    open fun enterPendingAnim() {
        overridePendingTransition(R.anim.base_slide_in_right, R.anim.base_anim_normal)
    }
}
