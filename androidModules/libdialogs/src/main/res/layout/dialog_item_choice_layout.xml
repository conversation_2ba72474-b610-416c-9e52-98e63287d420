<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/item_choice_root_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_gravity="center"

    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/item_choice_root_container_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:background="@drawable/shape_white_conner_bg"
        android:orientation="vertical">

        <RelativeLayout
            android:id="@+id/item_choice_title_container_Layout"
            android:layout_width="match_parent"
            android:layout_height="50dp">

            <TextView
                android:id="@+id/item_choice_OK_TextView"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_alignParentRight="true"
                android:background="@drawable/selector_comm_click_bg"
                android:gravity="center"
                android:paddingLeft="15dp"
                android:paddingRight="15dp"
                android:text="确定"
                android:textColor="#228be0"
                android:textSize="16sp" />

            <TextView
                android:id="@+id/item_choice_title_TextView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center"
                android:paddingLeft="60dp"
                android:paddingRight="60dp"
                android:singleLine="true"
                android:textColor="#333"
                android:textSize="16sp"
                tools:text="这个是标题" />

            <View
                android:layout_width="match_parent"
                android:layout_height="0.8dp"
                android:layout_alignParentBottom="true"
                android:background="#ddd" />
        </RelativeLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/item_choice_data_RecyclerView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

    </LinearLayout>

</FrameLayout>