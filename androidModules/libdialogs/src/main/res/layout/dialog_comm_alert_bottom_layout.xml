<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="bottom"
    android:background="@drawable/shape_white_top_conner_8dp_bg">

    <TextView
        android:id="@+id/dialog_alert_title_TextView"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:gravity="center"
        android:singleLine="true"
        android:text="标题"
        android:textColor="#333"
        android:textSize="18sp"
        android:visibility="visible"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/dialog_alert_bottom_button_group"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="visible"
        app:constraint_referenced_ids="dialog_alert_bottom_button_container_layout,dialog_alert_message_line_View" />

    <View
        android:id="@+id/dialog_alert_message_line_View"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="@drawable/shape_divide_line_horizontal"
        app:layout_constraintBottom_toTopOf="@id/dialog_alert_bottom_button_container_layout"
        app:layout_constraintTop_toBottomOf="@id/dialog_alert_message_container_layout" />


    <LinearLayout
        android:id="@+id/dialog_alert_bottom_button_container_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:divider="@drawable/shape_divide_line_horizontal"
        android:orientation="vertical"
        android:showDividers="middle"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/dialog_alert_message_container_layout">

        <Button
            android:id="@+id/dialog_alert_left_Button"
            android:layout_width="match_parent"
            android:layout_height="45dp"
            android:layout_weight="1"
            android:background="@drawable/selector_comm_click_bg"
            android:text="确定"
            android:textColor="#4A9DE2"
            android:textSize="16sp"
            android:visibility="gone"
            tools:visibility="visible" />

        <Button
            android:id="@+id/dialog_alert_middle_Button"
            android:layout_width="match_parent"
            android:layout_height="45dp"
            android:layout_weight="1"
            android:background="@drawable/selector_comm_click_bg"
            android:text="不再提醒"
            android:textColor="#9B9B9B"
            android:textSize="16sp"
            android:visibility="gone"
            tools:visibility="visible" />

        <Button
            android:id="@+id/dialog_alert_right_Button"
            android:layout_width="match_parent"
            android:layout_height="45dp"
            android:layout_weight="1"
            android:background="@drawable/selector_comm_click_bg"
            android:text="取消"
            android:textColor="#E02240"
            android:textSize="16sp"
            android:visibility="gone"
            tools:visibility="visible" />
    </LinearLayout>


    <androidx.core.widget.NestedScrollView
        android:id="@+id/dialog_alert_message_container_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="1dp"
        android:layout_marginBottom="1dp"
        android:maxHeight="100dp"
        android:orientation="vertical"
        app:layout_constrainedHeight="true"
        app:layout_constraintBottom_toTopOf="@id/dialog_alert_bottom_button_container_layout"
        app:layout_constraintTop_toBottomOf="@id/dialog_alert_title_TextView">

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingTop="10dp"
            android:paddingBottom="10dp">

            <EditText
                android:id="@+id/dialog_alert_message_EditText"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10dp"
                android:layout_marginRight="10dp"
                android:background="#0fff"
                android:cursorVisible="true"
                android:gravity="center_vertical"
                android:hint="这个是提示"
                android:minHeight="50dp"
                android:padding="10dp"
                android:text=""
                android:textColor="#333"
                android:textCursorDrawable="@null"
                android:textSize="16sp"
                android:visibility="gone" />

            <TextView
                android:id="@+id/dialog_alert_message_TextView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginLeft="15dp"
                android:layout_marginRight="15dp"
                android:gravity="center_vertical"
                android:lineSpacingMultiplier="1.2"
                android:minHeight="50dp"
                android:textColor="#333"
                android:textSize="16sp"
                android:visibility="visible"
                tools:text="这个是消息内容" />

        </FrameLayout>
    </androidx.core.widget.NestedScrollView>
</androidx.constraintlayout.widget.ConstraintLayout>