<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:background="@drawable/shape_white_conner_15dp_bg"
    android:gravity="center"
    android:orientation="vertical"
    android:paddingTop="16dp"
    android:paddingBottom="16dp"
    android:paddingLeft="50dp"
    android:paddingRight="50dp">

    <ProgressBar
        android:id="@+id/dialog_progress_ProgressBar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:indeterminate="false"
        android:indeterminateDrawable="@drawable/dialog_style_xml_icon" />

    <TextView
        android:id="@+id/dialog_progress_title_TextView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="15dp"
        android:maxWidth="220dp"
        android:text="@string/loading"
        android:visibility="visible"
        android:textColor="#333"
        android:textSize="16sp" />


</LinearLayout>