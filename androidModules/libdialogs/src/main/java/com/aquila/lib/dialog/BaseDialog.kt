package com.aquila.lib.dialog

import android.app.Dialog
import android.content.Context
import android.view.Gravity
import android.view.WindowManager

/***
 * @date 创建时间 2018/3/23 09:40
 * <AUTHOR> yulong
 * @description Dialog的基类
 */
open class BaseDialog(context: Context) : Dialog(context) {

    protected fun configDialogLayout(gravity: Int) {
        configDialog(gravity, WindowManager.LayoutParams.MATCH_PARENT, WindowManager.LayoutParams.WRAP_CONTENT)
    }

    protected fun configDialog(gravity: Int, width: Int, height: Int) {
        val wl = window!!.attributes
        wl.gravity = gravity

        if (gravity and Gravity.BOTTOM == Gravity.BOTTOM) {
            window!!.setWindowAnimations(R.style.bottomDialogWindowAnim)
        } else if (gravity == Gravity.CENTER) {
            window!!.setWindowAnimations(R.style.centerDialogWindowAnim)
        }
        wl.width = width
        wl.height = height
        window!!.setBackgroundDrawableResource(android.R.color.transparent)
        window!!.attributes = wl
    }
}
