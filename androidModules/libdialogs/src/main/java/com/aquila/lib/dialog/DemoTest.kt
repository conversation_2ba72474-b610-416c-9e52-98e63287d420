package com.aquila.lib.dialog

/***
 * @date 创建时间 2019-10-23 17:05
 * <AUTHOR> W<PERSON>
 * @description
 */
object DemoTest {
    val NO_GRAVITY = 0x0000

    /** Raw bit indicating the gravity for an axis has been specified.  */
    val AXIS_SPECIFIED = 0x0001

    /** Raw bit controlling how the left/top edge is placed.  */
    val AXIS_PULL_BEFORE = 0x0002
    /** Raw bit controlling how the right/bottom edge is placed.  */
    val AXIS_PULL_AFTER = 0x0004
    /** Raw bit controlling whether the right/bottom edge is clipped to its
     * container, based on the gravity direction being applied.  */
    val AXIS_CLIP = 0x0008

    /** Bits defining the horizontal axis.  */
    val AXIS_X_SHIFT = 0
    /** Bits defining the vertical axis.  */
    val AXIS_Y_SHIFT = 4

    /** Push object to the top of its container, not changing its size.  */
    val TOP = AXIS_PULL_BEFORE or AXIS_SPECIFIED shl AXIS_Y_SHIFT
    /** Push object to the bottom of its container, not changing its size.  */
    val BOTTOM = AXIS_PULL_AFTER or AXIS_SPECIFIED shl AXIS_Y_SHIFT
    /** Push object to the left of its container, not changing its size.  */
    val LEFT = AXIS_PULL_BEFORE or AXIS_SPECIFIED shl AXIS_X_SHIFT
    /** Push object to the right of its container, not changing its size.  */
    val RIGHT = AXIS_PULL_AFTER or AXIS_SPECIFIED shl AXIS_X_SHIFT

    /** Place object in the vertical center of its container, not changing its
     * size.  */
    val CENTER_VERTICAL = AXIS_SPECIFIED shl AXIS_Y_SHIFT
    /** Grow the vertical size of the object if needed so it completely fills
     * its container.  */
    val FILL_VERTICAL = TOP or BOTTOM

    /** Place object in the horizontal center of its container, not changing its
     * size.  */
    val CENTER_HORIZONTAL = AXIS_SPECIFIED shl AXIS_X_SHIFT
    /** Grow the horizontal size of the object if needed so it completely fills
     * its container.  */
    val FILL_HORIZONTAL = LEFT or RIGHT

    /** Place the object in the center of its container in both the vertical
     * and horizontal axis, not changing its size.  */
    val CENTER = CENTER_VERTICAL or CENTER_HORIZONTAL

    /** Grow the horizontal and vertical size of the object if needed so it
     * completely fills its container.  */
    val FILL = FILL_VERTICAL or FILL_HORIZONTAL

    /** Flag to clip the edges of the object to its container along the
     * vertical axis.  */
    val CLIP_VERTICAL = AXIS_CLIP shl AXIS_Y_SHIFT

    /** Flag to clip the edges of the object to its container along the
     * horizontal axis.  */
    val CLIP_HORIZONTAL = AXIS_CLIP shl AXIS_X_SHIFT

    /** Raw bit controlling whether the layout direction is relative or not (START/END instead of
     * absolute LEFT/RIGHT).
     */
    val RELATIVE_LAYOUT_DIRECTION = 0x00800000

    /**
     * Binary mask to get the absolute horizontal gravity of a gravity.
     */
    val HORIZONTAL_GRAVITY_MASK = AXIS_SPECIFIED or
        AXIS_PULL_BEFORE or AXIS_PULL_AFTER shl AXIS_X_SHIFT
    /**
     * Binary mask to get the vertical gravity of a gravity.
     */
    val VERTICAL_GRAVITY_MASK = AXIS_SPECIFIED or
        AXIS_PULL_BEFORE or AXIS_PULL_AFTER shl AXIS_Y_SHIFT

    /** Special constant to enable clipping to an overall display along the
     * vertical dimension.  This is not applied by default by
     * [.apply]; you must do so
     * yourself by calling [.applyDisplay].
     */
    val DISPLAY_CLIP_VERTICAL = 0x10000000

    /** Special constant to enable clipping to an overall display along the
     * horizontal dimension.  This is not applied by default by
     * [.apply]; you must do so
     * yourself by calling [.applyDisplay].
     */
    val DISPLAY_CLIP_HORIZONTAL = 0x01000000

    /** Push object to x-axis position at the start of its container, not changing its size.  */
    val START = RELATIVE_LAYOUT_DIRECTION or LEFT

    /** Push object to x-axis position at the end of its container, not changing its size.  */
    val END = RELATIVE_LAYOUT_DIRECTION or RIGHT

    /**
     * Binary mask for the horizontal gravity and script specific direction bit.
     */
    val RELATIVE_HORIZONTAL_GRAVITY_MASK = START or END
}

fun main() {
    var gravity = DemoTest.CENTER_VERTICAL
    print((gravity and DemoTest.CENTER == gravity))
}
