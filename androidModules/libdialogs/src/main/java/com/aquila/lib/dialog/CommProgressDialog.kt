package com.aquila.lib.dialog

import android.app.Dialog
import android.content.Context
import android.os.Bundle
import android.view.Gravity
import android.view.WindowManager
import android.widget.TextView

/***
 * @date 创建时间 2018/8/2 18:14
 * <AUTHOR> <PERSON><PERSON>
 * @description 通用的进度加载对话框
 */
class CommProgressDialog(private val builder: Builder) : Dialog(builder.context) {

    private lateinit var titleTextView: TextView

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.dialog_progress_layout)
        titleTextView = findViewById(R.id.dialog_progress_title_TextView)

        if (builder.textColor != -1) {
            titleTextView.setTextColor(builder.textColor)
        }
        if (builder.textSize != -1) {
            titleTextView.textSize = builder.textSize.toFloat()
        }

        setTitleText(builder.title)

        setCancelable(builder.isCancelable)
        setCanceledOnTouchOutside(builder.isTouchOutsideCancel)

        configDialog(builder.gravity)
    }

    fun setTitleText(title: CharSequence?) {
        if (title != null) {
            titleTextView.text = title
        }
    }

    protected fun configDialog(gravity: Int) {
        val wl = window!!.attributes
        wl.gravity = gravity // 设置重力

        if (gravity == Gravity.BOTTOM) {
            window!!.setWindowAnimations(R.style.bottomDialogWindowAnim)
        }
        window!!.attributes = wl
        window!!.setBackgroundDrawableResource(android.R.color.transparent)
        window!!.setLayout(
            WindowManager.LayoutParams.WRAP_CONTENT,
            WindowManager.LayoutParams.WRAP_CONTENT
        )
    }

    class Builder(val context: Context) {
        internal var title: CharSequence? = null
        internal var textSize = -1
        internal var textColor = -1
        internal var isCancelable = true
        internal var isTouchOutsideCancel = true
        internal var gravity = Gravity.CENTER

        fun setTitle(title: String): Builder {
            this.title = title
            return this
        }

        fun setTextSize(textSize: Int): Builder {
            this.textSize = textSize
            return this
        }

        fun setTextColor(textColor: Int): Builder {
            this.textColor = textColor
            return this
        }

        fun setCancelable(cancelable: Boolean): Builder {
            isCancelable = cancelable
            return this
        }

        fun setTouchOutsideCancel(touchOutsideCancel: Boolean): Builder {
            isTouchOutsideCancel = touchOutsideCancel
            return this
        }

        fun setGravity(gravity: Int): Builder {
            this.gravity = gravity
            return this
        }

        fun create(): CommProgressDialog {
            return CommProgressDialog(this)
        }
    }

    companion object {
        @JvmStatic
        fun with(context: Context): Builder {
            return Builder(context)
        }
    }
}
