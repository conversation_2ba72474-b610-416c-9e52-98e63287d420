package com.aquila.lib.dialog

import android.app.Dialog
import android.content.Context
import android.content.res.ColorStateList
import android.content.res.Resources
import android.os.Bundle
import android.text.TextUtils
import android.view.Gravity
import android.view.View
import android.view.WindowManager
import android.widget.Button
import android.widget.EditText
import android.widget.TextView
import androidx.annotation.ColorRes
import androidx.annotation.StringRes
import androidx.constraintlayout.widget.Group
import androidx.core.content.ContextCompat
import androidx.core.widget.NestedScrollView

/***
 * @date 创建时间 2018/5/22 18:48
 * <AUTHOR> <PERSON><PERSON>
 * @description APP的AlertDialog
 */
class CommAlertDialog private constructor(val builder: DialogBuilder) : Dialog(builder.context), View.OnClickListener {
    companion object {
        const val TAG_CLICK_START = 1
        const val TAG_CLICK_END = 2
        const val TAG_CLICK_MIDDLE = 3

        @JvmStatic
        fun with(context: Context): DialogBuilder {
            return DialogBuilder(context)
        }

        @JvmStatic
        fun showAlertDialog(context: Context, message: String?, onViewClickListener: (Dialog, View, Int) -> Unit) {
            with(context).setMessage(message).setMiddleText("确定").setMiddleColor(-0xdd7420)
                .setOnViewClickListener(object : OnDialogViewClickListener {
                    override fun onViewClick(dialog: Dialog, v: View, tag: Int) {
                        onViewClickListener(dialog, v, tag)
                    }
                }).create().show()
        }
    }

    lateinit var titleTextView: TextView
    lateinit var messageTextView: TextView
    lateinit var startButton: Button
    lateinit var middleButton: Button
    lateinit var endButton: Button
    lateinit var bottomGroup: Group
    lateinit var messageEditText: EditText
    lateinit var scrollView: NestedScrollView

    // 获取输入的字符
    val inputString: CharSequence
        get() = messageEditText.text

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        if (builder.gravity or Gravity.BOTTOM == Gravity.BOTTOM) {
            setContentView(R.layout.dialog_comm_alert_bottom_layout)
        } else {
            setContentView(R.layout.dialog_comm_alert_layout)
        }
        initViewFromXML()
        initUI()
        configDialog(builder.gravity)

        setViewListener()
    }

    private fun initViewFromXML() {
        titleTextView = findViewById(R.id.dialog_alert_title_TextView)
        messageTextView = findViewById(R.id.dialog_alert_message_TextView)
        startButton = findViewById(R.id.dialog_alert_left_Button)
        middleButton = findViewById(R.id.dialog_alert_middle_Button)
        endButton = findViewById(R.id.dialog_alert_right_Button)
        bottomGroup = findViewById(R.id.dialog_alert_bottom_button_group)
        messageEditText = findViewById(R.id.dialog_alert_message_EditText)
        scrollView = findViewById(R.id.dialog_alert_message_container_layout)
    }

    private fun setViewListener() {
        startButton.setOnClickListener(this)
        middleButton.setOnClickListener(this)
        endButton.setOnClickListener(this)
    }

    private fun initUI() {
        if (TextUtils.isEmpty(builder.endText) && TextUtils.isEmpty(builder.middleText) && TextUtils.isEmpty(builder.startText)) {
            bottomGroup.visibility = View.GONE
        } else {
            initText(startButton, builder.startText)
            initText(endButton, builder.endText)
            initText(middleButton, builder.middleText)
            bottomGroup.visibility = View.VISIBLE
        }

        if (TextUtils.isEmpty(builder.title)) {
            titleTextView.visibility = View.GONE
        } else {
            titleTextView.visibility = View.VISIBLE
            titleTextView.text = builder.title
        }

        initText(messageTextView, builder.message)

        if (builder.hintText != null) {
            messageEditText.hint = builder.hintText
        }

        if (builder.message == null) {
            scrollView.visibility = View.GONE
        } else {
            scrollView.visibility = View.VISIBLE
        }

        if (builder.isEditMode) {
            messageTextView.visibility = View.GONE
            messageEditText.visibility = View.VISIBLE
        } else {
            messageTextView.visibility = View.VISIBLE
            messageEditText.visibility = View.GONE
        }

        if (builder.messageMinHeight > 0) {
            messageTextView.minHeight = dp2px(builder.messageMinHeight.toFloat())
            messageEditText.minHeight = dp2px(builder.messageMinHeight.toFloat())
        }

        messageTextView.gravity = builder.messageGravity
        messageEditText.gravity = builder.messageGravity

        initTextColor(titleTextView, builder.titleColorStateList)
        initTextColor(messageTextView, builder.messageColorStateList)
        initTextColor(messageEditText, builder.messageColorStateList)

        initTextColor(startButton, builder.leftColorStateList)
        initTextColor(middleButton, builder.middleColorStateList)
        initTextColor(endButton, builder.rightColorStateList)

        initTextSize(titleTextView, builder.titleTextTextSize)
        initTextSize(messageTextView, builder.messageTextTextSize)
        initTextSize(messageEditText, builder.messageTextTextSize)

        initTextSize(startButton, builder.leftTextTextSize)
        initTextSize(endButton, builder.rightTextTextSize)
        initTextSize(middleButton, builder.middleTextTextSize)

        setCanceledOnTouchOutside(builder.isTouchOutsideCancel)
        setCancelable(builder.isCancelAble)
    }

    fun dp2px(dpValue: Float): Int {
        return (0.5f + dpValue * Resources.getSystem().displayMetrics.density).toInt()
    }

    private fun initText(textView: TextView, text: CharSequence?) {
        if (!TextUtils.isEmpty(text)) {
            textView.visibility = View.VISIBLE
            textView.text = text
        } else {
            textView.visibility = View.GONE
        }
    }

    /*设置控件的字体颜色*/
    private fun initTextColor(textView: TextView, colorStateList: ColorStateList?) {
        if (colorStateList != null) {
            textView.setTextColor(colorStateList)
        }
    }

    /*设置控件的字体大小*/
    private fun initTextSize(view: TextView, textSize: Int) {
        if (textSize != -1) {
            view.textSize = textSize.toFloat()
        }
    }

    protected fun configDialog(gravity: Int) {
        val wl = window!!.attributes
        wl.gravity = gravity // 设置重力

        var width = if (builder.dialogWidth > 0) builder.dialogWidth else WindowManager.LayoutParams.MATCH_PARENT
        var height = if (builder.dialogHeight > 0) builder.dialogHeight else WindowManager.LayoutParams.WRAP_CONTENT

        if ((gravity and Gravity.BOTTOM) == gravity) {
            window!!.setWindowAnimations(R.style.bottomDialogWindowAnim)
        }
        wl.width = width
        wl.height = height

        window!!.attributes = wl
        window!!.setBackgroundDrawableResource(android.R.color.transparent)
    }

    override fun onClick(v: View) {
        when (v) {
            startButton -> builder.onViewClickListener?.onViewClick(this, v, TAG_CLICK_START)
            endButton -> builder.onViewClickListener?.onViewClick(this, v, TAG_CLICK_END)
            middleButton -> builder.onViewClickListener?.onViewClick(this, v, TAG_CLICK_MIDDLE)
        }

        if (builder.isClickButtonDismiss) {
            dismiss()
        }
    }

    class DialogBuilder(var context: Context) {

        internal var title: CharSequence? = null
        internal var message: CharSequence? = null
        internal var startText: CharSequence? = null
        internal var middleText: CharSequence? = null
        internal var endText: CharSequence? = null

        internal var hintText: CharSequence? = null

        internal var onViewClickListener: OnDialogViewClickListener? = null

        internal var isCancelAble = true
        internal var isTouchOutsideCancel = true

        internal var leftColorStateList: ColorStateList? = null
        internal var middleColorStateList: ColorStateList? = null
        internal var rightColorStateList: ColorStateList? = null

        internal var titleColorStateList: ColorStateList? = null
        internal var messageColorStateList: ColorStateList? = null

        internal var gravity = Gravity.CENTER

        // 默认高度为
        internal var messageMinHeight = 50

        internal var titleTextTextSize = -1
        internal var messageTextTextSize = -1
        internal var leftTextTextSize = -1
        internal var middleTextTextSize = -1
        internal var rightTextTextSize = -1

        internal var isEditMode = false
        internal var isClickButtonDismiss = true

        internal var messageGravity = Gravity.CENTER

        internal var dialogWidth = -1
        internal var dialogHeight = -1

        fun setDialogLayout(width: Int, height: Int): DialogBuilder {
            dialogWidth = width
            dialogHeight = height
            return this
        }

        fun setDialogHeight(height: Int): DialogBuilder {
            dialogHeight = height
            return this
        }

        fun setDialogWidth(width: Int): DialogBuilder {
            dialogWidth = width
            return this
        }

        fun setMessageMinHeight(messageMinHeight: Int): DialogBuilder {
            this.messageMinHeight = messageMinHeight
            return this
        }

        fun setHintText(hintText: CharSequence): DialogBuilder {
            this.hintText = hintText
            return this
        }

        fun setEditMode(editMode: Boolean): DialogBuilder {
            isEditMode = editMode
            return this
        }

        fun setMessageGravity(messageGravity: Int): DialogBuilder {
            this.messageGravity = messageGravity
            return this
        }

        fun setTitleTextTextSize(titleTextTextSize: Int): DialogBuilder {
            this.titleTextTextSize = titleTextTextSize
            return this
        }

        fun setMessageTextTextSize(messageTextTextSize: Int): DialogBuilder {
            this.messageTextTextSize = messageTextTextSize
            return this
        }

        fun setLeftTextTextSize(leftTextTextSize: Int): DialogBuilder {
            this.leftTextTextSize = leftTextTextSize
            return this
        }

        fun setMiddleTextTextSize(middleTextTextSize: Int): DialogBuilder {
            this.middleTextTextSize = middleTextTextSize
            return this
        }

        fun setClickButtonDismiss(clickButtonDismiss: Boolean): DialogBuilder {
            isClickButtonDismiss = clickButtonDismiss
            return this
        }

        fun setRightTextTextSize(rightTextTextSize: Int): DialogBuilder {
            this.rightTextTextSize = rightTextTextSize
            return this
        }

        fun setTouchOutsideCancel(touchOutsideCancel: Boolean): DialogBuilder {
            isTouchOutsideCancel = touchOutsideCancel
            return this
        }

        fun setLeftColorRes(@ColorRes leftColorRes: Int): DialogBuilder {
            this.leftColorStateList = ContextCompat.getColorStateList(context, leftColorRes)
            return this
        }

        fun setMiddleColorRes(@ColorRes colorRes: Int): DialogBuilder {
            this.middleColorStateList = ContextCompat.getColorStateList(context, colorRes)
            return this
        }

        fun setAllButtonColor(color: Int): DialogBuilder {
            leftColorStateList = ColorStateList.valueOf(color)
            middleColorStateList = leftColorStateList
            rightColorStateList = middleColorStateList
            return this
        }

        // 设置所有按钮统一的颜色
        fun setAllButtonColorRes(@ColorRes colorRes: Int): DialogBuilder {
            rightColorStateList = ContextCompat.getColorStateList(context, colorRes)
            middleColorStateList = rightColorStateList
            leftColorStateList = middleColorStateList
            return this
        }

        fun setRightColorRes(@ColorRes colorRes: Int): DialogBuilder {
            this.rightColorStateList = ContextCompat.getColorStateList(context, colorRes)
            return this
        }

        fun setTitleColorRes(@ColorRes colorRes: Int): DialogBuilder {
            this.titleColorStateList = ContextCompat.getColorStateList(context, colorRes)
            return this
        }

        fun setMessageColorRes(@ColorRes colorRes: Int): DialogBuilder {
            this.messageColorStateList = ContextCompat.getColorStateList(context, colorRes)
            return this
        }

        fun setLeftColor(leftColor: Int): DialogBuilder {
            leftColorStateList = ColorStateList.valueOf(leftColor)
            return this
        }

        fun setMiddleColor(middleColor: Int): DialogBuilder {
            this.middleColorStateList = ColorStateList.valueOf(middleColor)
            return this
        }

        fun setRightColor(rightColor: Int): DialogBuilder {
            this.rightColorStateList = ColorStateList.valueOf(rightColor)
            return this
        }

        fun setTitleColor(titleColor: Int): DialogBuilder {
            this.titleColorStateList = ColorStateList.valueOf(titleColor)
            return this
        }

        fun setContentColor(contentColor: Int): DialogBuilder {
            this.messageColorStateList = ColorStateList.valueOf(contentColor)
            return this
        }

        fun setTitle(@StringRes titleRes: Int): DialogBuilder {
            this.title = context.getString(titleRes)
            return this
        }

        fun setTitle(title: CharSequence?): DialogBuilder {
            this.title = title
            return this
        }

        fun setMessage(@StringRes message: Int): DialogBuilder {
            this.message = context.getString(message)
            return this
        }

        fun setMessage(message: CharSequence?): DialogBuilder {
            this.message = message
            return this
        }

        fun setStartText(@StringRes textId: Int): DialogBuilder {
            this.startText = context.getString(textId)
            return this
        }

        fun setStartText(startText: CharSequence?): DialogBuilder {
            this.startText = startText
            return this
        }

        fun setMiddleText(middleText: CharSequence?): DialogBuilder {
            this.middleText = middleText
            return this
        }

        fun setMiddleText(@StringRes textId: Int): DialogBuilder {
            this.middleText = context.getString(textId)
            return this
        }

        fun setEndText(endText: CharSequence?): DialogBuilder {
            this.endText = endText
            return this
        }

        fun setEndText(@StringRes textId: Int): DialogBuilder {
            this.endText = context.getString(textId)
            return this
        }

        fun setOnViewClickListener(clickListener: (Dialog, View, Int) -> Unit): DialogBuilder {
            this.onViewClickListener = object : OnDialogViewClickListener {
                override fun onViewClick(dialog: Dialog, v: View, tag: Int) {
                    clickListener(dialog, v, tag)
                }
            }
            return this
        }

        fun setOnViewClickListener(onViewClickListener: OnDialogViewClickListener): DialogBuilder {
            this.onViewClickListener = onViewClickListener
            return this
        }

        fun setCancelAble(cancelAble: Boolean): DialogBuilder {
            isCancelAble = cancelAble
            return this
        }

        fun setGravity(gravity: Int): DialogBuilder {
            this.gravity = gravity
            return this
        }

        fun showDialog() {
            create().show()
        }

        fun create(): CommAlertDialog {
            return CommAlertDialog(this)
        }
    }
}
