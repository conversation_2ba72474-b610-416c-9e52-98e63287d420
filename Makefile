define xcodebuild
	@if [ ! -z $1 ]; then \
		echo "\033[0;33mxcodebuild iphones[$1]\033[0m"; \
		xcodebuild -workspace iosApp/WDBook.xcworkspace \
			-scheme $(2) \
			-configuration '$1' \
			-sdk iphoneos \
			-allowProvisioningUpdates; \
	else \
		echo "\033[0;33mxcodebuild iphones[Debug]\033[0m"; \
		xcodebuild -workspace iosApp/WDBook.xcworkspace \
			-scheme $(2) \
			-arch 'arm64' \
			-configuration 'Debug' \
			-sdk iphonesimulator \
			-allowProvisioningUpdates; \
	fi
endef

clean:
	@echo Clean built codes
	@./gradlew clean

ac: clean

android-debug: clean
	@if [ -z "$(strip $(appFlavor))" ]; then \
		./gradlew assembleWdbookprodGooglePlayDebug; \
	else \
		./gradlew assemble$(shell echo $(appFlavor) | awk '{print toupper(substr($$0,1,1)) substr($$0,2)}')GooglePlayDebug; \
	fi

ad: android-debug

android-release: clean
	@if [ -z "$(strip $(appFlavor))" ]; then \
		./gradlew assembleWdbookprodGooglePlayRelease; \
	else \
		./gradlew assemble$(shell echo $(appFlavor) | awk '{print toupper(substr($$0,1,1)) substr($$0,2)}')Release; \
	fi

ar: android-release

android-test: clean
	@./gradlew testWdbooktestGooglePlayDebugUnitTest

at: android-test

wdbooktest: clean
	@./gradlew assembleWdbooktestGooglePlayDebug

#iOS
IOS_APP_WORKSPACE := iosApp/WDBook.xcworkspace
LIST_SCHEMES := xcodebuild -list -workspace $(IOS_APP_WORKSPACE)
SCHEMES := $(shell $(LIST_SCHEMES) | awk '/Schemes:/,0' | sed -e 1d | grep -E '^\s*(wdbook)')

ios-clean: clean
	@for scheme in $(SCHEMES); do \
		echo "Cleaning $$scheme..."; \
		xcodebuild clean -workspace $(IOS_APP_WORKSPACE) -scheme "$$scheme"; \
	done

pod-update:
	@pod update --project-directory=./iosApp

ios-update: ios-clean
	./gradlew :shared:generateDummyFramework
	cd ./iosApp; pod install

#share-emu-debug: clean
#	@echo "\033[0;33mLink Debug Framework Simulator\033[0m";
#	@./gradlew linkDebugFrameworkIosX64
#
#sed: share-emu-debug
#
#share-emu-release: clean
#	@echo "\033[0;33mLink Release Framework Simulator\033[0m";
#	@./gradlew linkReleaseFrameworkIosX64
#
#ser: share-emu-release
#
#share-iphone-debug: clean
#	@echo "\033[0;33mLink Debug Framework iPhone\033[0m";
#	@./gradlew linkDebugFrameworkIosArm64
#
#sid: share-iphone-debug
#
#share-iphone-release: clean
#	@echo "\033[0;33mLink Release Framework iPhone\033[0m";
#	@./gradlew linkDebugReleaseIosArm64
#
#sir:share-iphone-release

ios-debug: clean
	$(call xcodebuild,Debug,wdbooktest)

id: ios-debug

ios-release: clean
	$(call xcodebuild,Release,wdbooktest)

ir: ios-release

ios-archive:
	xcodebuild -workspace iosApp/WDBook.xcworkspace \
           -scheme WDBook \
           -configuration 'Release' \
           -sdk iphoneos \
           archive -archivePath ${PWD}/iosApp/build/wdbook.xcarchive \

ios-export:
	xcodebuild -exportArchive \
           -allowProvisioningUpdates \
           -archivePath ${PWD}/iosApp/build/wdbook.xcarchive \
           -exportOptionsPlist exportOption.plist \
           -exportPath ${PWD}/iosApp/build \

SCHEME ?= wdbooktest
CURRENT_PROJECT_VERSION ?= 2024061901
MARKETING_VERSION ?= 3.5

ios-book-archive:
	xcodebuild -workspace iosApp/WDBook.xcworkspace \
           -scheme  $(SCHEME) \
           -configuration 'Release' \
           -sdk iphoneos \
           archive -archivePath ${PWD}/iosApp/build/$(SCHEME).xcarchive \
           CURRENT_PROJECT_VERSION=$(CURRENT_PROJECT_VERSION) \
           MARKETING_VERSION=$(MARKETING_VERSION) \

ios-book-export:
	xcodebuild -exportArchive \
           -allowProvisioningUpdates \
           -archivePath ${PWD}/iosApp/build/$(SCHEME).xcarchive \
           -exportOptionsPlist exportOption.plist \
           -exportPath ${PWD}/iosApp/build \

enable-swiftui-preview:
	@sed -i '' 's/val forSwiftUIPreview = false/val forSwiftUIPreview = true/' shared/build.gradle.kts
	@sed -i '' 's/^\( *\)\(if (config.enableCftProxy) protocolClasses = listOf(cocoapods.wdNetwork.WDUrlProtocol.`class`())\)/\/\/\1\2/' shared/src/iosMain/kotlin/com/wedevote/ktor/client/engine/darwin/internal/DarwinSession.kt
	@sed -i '' 's/^\( *\)\(val cftInstance = cocoapods.wdNetwork.WDCft.sharedInstance()\)/\/\/\1\2/' shared/src/iosMain/kotlin/com/wedevote/wdbook/utils/Platform.kt
	@sed -i '' 's/^\( *\)\(return cftInstance != null \&\& cftInstance.isConnected()\)/\/\/\1\2\n\1return false/' shared/src/iosMain/kotlin/com/wedevote/wdbook/utils/Platform.kt

esp: enable-swiftui-preview

disable-swiftui-preview:
	@sed -i '' 's/val forSwiftUIPreview = true/val forSwiftUIPreview = false/' shared/build.gradle.kts
	@sed -i '' 's/\/\/        if (config.enableCftProxy) protocolClasses = listOf(cocoapods.wdNetwork.WDUrlProtocol.`class`())/        if (config.enableCftProxy) protocolClasses = listOf(cocoapods.wdNetwork.WDUrlProtocol.`class`())/' shared/src/iosMain/kotlin/com/wedevote/ktor/client/engine/darwin/internal/DarwinSession.kt
	@sed -i '' 's/\/\/            val cftInstance = cocoapods.wdNetwork.WDCft.sharedInstance()/            val cftInstance = cocoapods.wdNetwork.WDCft.sharedInstance()/' shared/src/iosMain/kotlin/com/wedevote/wdbook/utils/Platform.kt
	@sed -i '' '/^\/\/[[:space:]]*return cftInstance != null && cftInstance.isConnected()/ { \
		N; \
		s|//            return cftInstance != null && cftInstance.isConnected()\n            return false|            return cftInstance != null \&\& cftInstance.isConnected()|; \
	}' shared/src/iosMain/kotlin/com/wedevote/wdbook/utils/Platform.kt

dsp: disable-swiftui-preview
