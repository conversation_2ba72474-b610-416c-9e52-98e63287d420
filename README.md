## Generate debug key file

```shell script
keytool -genkey -v -keystore debug.keystore \
  -storepass android -alias androiddebugkey -keypass android \
  -keyalg RSA -keysize 2048 -validity 10000 \
  -dname "C=US, O=WeDevote, CN=WD"
```

## Update `local.properties` file

```properties
sdk.dir=<sdk.dir>
ndk.dir=<ndk.dir>
key.file=<key file path>
keyAlias=<key alias>
keyPassword=<key password>
storePassword=<store password>
```

### `local.properties` Example

```properties
sdk.dir=/Users/<USER>/Library/Android/sdk
ndk.dir=/Users/<USER>/Library/Android/sdk/ndk/21.0.6113669
key.file=/Users/<USER>/.signing/debug.keystore
keyAlias=androiddebugkey
keyPassword=android
storePassword=android
```

## Open iOS app

```shell script
open iosApp/WDBook.xcworkspace
```

## 编译shared framework

### 给模拟器的Debug版本

```shell script
make sed
```
### 给物理机的Debug版本

```shell script
make sid
```
## 安装Lint

## Setup 'pre-commit'

### Install `ktlint`

Lint for kotlin files need to install the `ktlint` tool, to install it use the following command:

```shell
brew install ktlint
```

### Install 'swiftlint'

```shell
brew install swiftlint
```

### Install pre-commit

```shell
brew install pre-commit
```

### Setup `pre-commit`

```shell
# Install hooks
pre-commit install

# Auto update hooks
pre-commit autoupdate

# Clean cache
pre-commit clean

# Install hooks
pre-commit install-hooks
```

## Preview for SwiftUI

### Enable Preview for SwiftUI

```shell
make esp
```

### Disable Preview for SwiftUI

```shell
make dsp
```
