stages:
  - build
  - test

variables:
  LANG: "en_US.UTF-8"
  LC_ALL: "en_US.UTF-8"

before_script:
  - echo -e "$WDBOOK_LOCAL_PROPERTIES" > local.properties

build-android:
  stage: build
  script:
    - make ac
    - make ad
  tags:
    - macos
    - wdapp

build-ios:
  stage: build
  script:
    - export LANG=en_US.UTF-8
    - ./gradlew :shared:podspec
    - ./gradlew :shared:podGenIOS
    - make ios-update
    - ./gradlew :shared:podInstall
    - make ios-debug
  tags:
    - macos
    - wdapp

test-android:
  stage: test
  script:
    - make at
  artifacts:
    when: always
    expire_in: 1 week
    reports:
      junit: [
        ./**/build/test-results/testDebugUnitTest/TEST-*.xml,
      ]
  tags:
    - macos
    - wdapp

test-ios:
  stage: test
  script:
    - export LANG=en_US.UTF-8
    - ./gradlew :shared:podspec
    - ./gradlew :shared:podGenIOS
    - make ios-update
    - ./gradlew :shared:podInstall
    - cd iosApp
    - fastlane test
  artifacts:
    when: always
    expire_in: 1 week
    reports:
      junit:
        - ./iosApp/fastlane/test_output/report.junit
  tags:
    - macos
    - wdapp
