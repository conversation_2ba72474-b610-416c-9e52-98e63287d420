# Android 15 (API 35) 适配指南

## 概述
本文档详细说明了 WDBook 应用针对 Android 15 的适配方案，重点解决了全屏显示适配、状态栏颜色管理、手势导航和显示切口处理等关键问题。**所有适配代码都包含了版本判断，确保只在 Android 15 及以上版本生效，不影响旧版本的正常运行。**

## 版本兼容性设计

### 关键原则
- **渐进式增强**：新功能仅在 Android 15 及以上版本启用
- **向后兼容**：Android 15 以下版本保持原有逻辑不变
- **平滑过渡**：用户升级系统后自动获得新特性
- **代码路径分离**：不同版本的代码路径完全独立，避免冗余

### 版本判断示例
```kotlin
// Android 15 是 API 35
if (Build.VERSION.SDK_INT >= 35) {
    // Android 15 及以上版本的新逻辑
    WindowInsetsUtils.setEdgeToEdgeDisplay(this)
} else {
    // Android 14 及以下版本保持原有逻辑
    // 使用 ImmersionBar 处理状态栏
}
```

### 代码路径清晰分离
在 RootActivity 中，Android 15 和旧版本的代码路径是完全分离的：

```kotlin
if (!interceptSetStatusBar()) {
    if (Build.VERSION.SDK_INT >= 35) {
        // Android 15 及以上版本：调用 updateStatusBar()
        updateStatusBar()
    } else {
        // Android 15 以下版本：使用 ImmersionBar
        if (APPConfig.isCurrentThemeLight()) {
            immersionBar.statusBarDarkFont(false).statusBarColor(R.color.white)
                .navigationBarColor(R.color.white).navigationBarDarkIcon(false)
        } else {
            immersionBar.statusBarDarkFont(true).statusBarColor(R.color.color_dark_1E1E1E)
                .navigationBarColor(R.color.color_dark_1E1E1E).navigationBarDarkIcon(true)
        }
        immersionBar.init()
    }
}
```

`updateStatusBar()` 方法内部不需要再包含 ImmersionBar 的兼容代码，因为它只会在 Android 15 及以上版本被调用：

```kotlin
/**
 * 更新状态栏设置（仅在 Android 15 及以上版本使用）
 */
protected open fun updateStatusBar() {
    val isLightTheme = APPConfig.isCurrentThemeLight()
    
    WindowInsetsUtils.setStatusBarColor(
        this,
        getStatusBarColor(),
        needTopPadding,
        needBottomPadding,
        transparentStatusBar,
        transparentNavigationBar
    )
    
    WindowInsetsUtils.setSystemBarsColor(
        this,
        android.graphics.Color.TRANSPARENT,
        android.graphics.Color.TRANSPARENT,
        isLightStatusBar = isLightTheme,
        isLightNavigationBar = isLightTheme
    )
}
```

## 核心改动

### 1. WindowInsetsUtils 工具类
创建了专门的工具类来处理 Android 15 的边到边显示模式：

#### 主要功能：
- **边到边显示设置**：`setEdgeToEdgeDisplay(activity)`
- **状态栏颜色管理**：`setStatusBarColor(activity, color, ...)`
- **窗口插入处理**：`applyWindowInsetsListener(view, ...)`
- **系统栏高度获取**：`getStatusBarHeight()`, `getNavigationBarHeight()`
- **显示切口检测**：`hasDisplayCutout()`, `getDisplayCutoutSafeInsets()`

#### 关键实现：
```kotlin
// 设置状态栏颜色（适配 Android 15）
fun setStatusBarColor(
    activity: Activity,
    @ColorInt color: Int,
    needTopPadding: Boolean = true,
    needBottomPadding: Boolean = true,
    transparentStatusBar: Boolean = false,
    transparentNavigationBar: Boolean = false
)
```

此方法通过在 DecorView 中添加占位视图来实现状态栏和导航栏的颜色控制，确保在边到边模式下颜色显示正确。

### 2. RootActivity 更新
RootActivity 作为所有 Activity 的基类，提供了统一的状态栏管理，并包含完整的版本判断：

#### onCreate 中的版本判断
```kotlin
override fun onCreate(savedInstanceState: Bundle?) {
    super.onCreate(savedInstanceState)
    // ... 其他代码 ...
    
    // Android 15 (API 35) 及以上版本使用新的适配方案
    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
        // 设置边到边显示模式
        WindowInsetsUtils.setEdgeToEdgeDisplay(this)
    }
    
    // 初始化 ImmersionBar（所有版本都需要，用于兼容性）
    immersionBar = ImmersionBar.with(this).autoDarkModeEnable(true)
        .fitsSystemWindows(false).autoNavigationBarDarkModeEnable(true)
        
    if (!interceptSetStatusBar()) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
            // Android 15 及以上版本使用新的状态栏设置方法
            updateStatusBar()
        } else {
            // Android 15 以下版本保持原有逻辑
            if (APPConfig.isCurrentThemeLight()) {
                immersionBar.statusBarDarkFont(false).statusBarColor(R.color.white)
                    .navigationBarColor(R.color.white).navigationBarDarkIcon(false)
            } else {
                immersionBar.statusBarDarkFont(true).statusBarColor(R.color.color_dark_1E1E1E)
                    .navigationBarColor(R.color.color_dark_1E1E1E).navigationBarDarkIcon(true)
            }
            immersionBar.init()
        }
    }
}
```

#### 新增功能（仅在 Android 15 及以上版本有效）：
- **状态栏颜色获取**：`getStatusBarColor()` - 子类可重写此方法返回自定义颜色
- **内边距控制**：`updateNeedTopPadding()`, `updateNeedBottomPadding()`
- **透明度控制**：`updateTransparentStatusBar()`, `updateTransparentNavigationBar()`
- **自动更新**：`updateStatusBar()` - 统一的状态栏更新方法

所有这些方法内部都包含版本判断：
```kotlin
protected fun updateNeedTopPadding(need: Boolean) {
    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
        needTopPadding = need
        if (!interceptSetStatusBar()) {
            updateStatusBar()
        }
    }
    // Android 15 以下版本不执行任何操作
}
```

### 3. 具体页面适配示例

#### FavoriteBookListActivity 适配：
```kotlin
/**
 * 重写状态栏颜色，使其与顶部标题栏背景色一致
 * 由于顶部使用了 theme_bg_color_white_bottom_line，返回对应的纯色背景
 * 仅在 Android 15 及以上版本生效
 */
override fun getStatusBarColor(): Int {
    return if (APPConfig.isCurrentThemeLight()) {
        getColor(R.color.white)
    } else {
        getColor(R.color.color_dark_1E1E1E)
    }
}
```

通过重写 `getStatusBarColor()` 方法，确保状态栏颜色与页面顶部标题栏背景色一致。**此方法仅在 Android 15 及以上版本被调用。**

#### BookReadActivity 适配：
阅读页面的特殊处理：

1. **重写 getStatusBarColor()**：返回与主题一致的颜色
2. **重写 interceptSetStatusBar()**：返回 true，表示阅读页面自己管理状态栏
3. **设置内容全屏**：在 onCreate 中调用 `updateNeedTopPadding(false)` 和 `updateNeedBottomPadding(false)`
4. **BookReadToolLayout 改进**：
   - 使用 `WindowInsetsUtils.setStatusBarColor()` 管理系统栏颜色
   - 通过占位视图确保导航栏颜色与状态栏一致
   - 工具栏显示时使用主题色，隐藏时使用透明色实现沉浸式阅读
   - **重要**：`needTopPadding` 和 `needBottomPadding` 始终为 false，确保内容不会移动

#### 导航栏透明度控制逻辑：
- **工具栏显示时**：导航栏显示为不透明的主题色（白天模式为白色，黑夜模式为深色）
- **工具栏隐藏时**：导航栏变为透明，实现真正的沉浸式阅读体验
- **切换时机**：随着工具栏的显示/隐藏动画同步切换

```kotlin
// BookReadActivity onCreate 中的关键设置
override fun onCreate(savedInstanceState: Bundle?) {
    super.onCreate(savedInstanceState)
    setContentView(R.layout.activity_book_read_layout)
    
    // 阅读页面不需要内边距，内容应该全屏显示
    updateNeedTopPadding(false)
    updateNeedBottomPadding(false)
    
    // 初始状态：工具栏显示，导航栏不透明
    toolLayout.setStatusBarShowState(this, true)
    // ...
}

// BookReadToolLayout 中的关键代码
fun setStatusBarShowState(activity: Activity, isShow: Boolean) {
    val isLightTheme = APPConfig.isCurrentThemeLight()
    val barColor = if (isLightTheme) {
        activity.getColor(R.color.white)
    } else {
        activity.getColor(R.color.color_dark_1E1E1E)
    }
    
    WindowInsetsUtils.setStatusBarColor(
        activity, barColor,
        needTopPadding = false,  // 始终不需要顶部内边距
        needBottomPadding = false,  // 始终不需要底部内边距
        transparentStatusBar = !isShow,  // 工具栏隐藏时状态栏透明
        transparentNavigationBar = !isShow  // 工具栏隐藏时导航栏透明
    )
}

// 工具栏元素的 Insets 处理
private fun setupWindowInsets() {
    // 顶部工具栏使用 padding 避开状态栏
    WindowInsetsUtils.applyWindowInsetsListener(
        rootLayout,
        applyTop = true,
        applyBottom = false,
        applyLeft = true,
        applyRight = true
    )
    
    // 底部容器使用 padding 避开导航栏
    WindowInsetsUtils.applyWindowInsetsListener(
        bottomContainerLayout,
        applyTop = false,
        applyBottom = true,
        applyLeft = true,
        applyRight = true
    )
}
```

这样的设计确保了：
- ✅ 阅读内容始终全屏显示，不会因为工具栏的显示/隐藏而移动
- ✅ 工具栏正确覆盖在内容上方
- ✅ 工具栏元素正确避开了系统栏区域
- ✅ 导航栏在需要时显示（工具栏可见），不需要时透明（沉浸式阅读）

### 4. 主题配置
更新了 themes.xml，为所有主题添加了必要的系统栏颜色设置：

```xml
<!-- Android 15 edge-to-edge display -->
<item name="android:statusBarColor" tools:targetApi="lollipop">@color/white</item>
<item name="android:navigationBarColor" tools:targetApi="lollipop">@color/white</item>
<item name="android:windowDrawsSystemBarBackgrounds" tools:targetApi="lollipop">true</item>
```

## 使用指南

### 1. 基本使用
所有继承自 RootActivity 的 Activity 会自动获得 Android 15 的适配支持，无需额外配置。

### 2. 自定义状态栏颜色
如果页面需要特殊的状态栏颜色（例如与顶部视图背景色一致），重写 `getStatusBarColor()` 方法：

```kotlin
override fun getStatusBarColor(): Int {
    // 返回你需要的颜色
    return getColor(R.color.your_custom_color)
}
```

**注意**：此方法仅在 Android 15 及以上版本被调用，旧版本仍使用 ImmersionBar 的设置。

### 3. 控制内边距
对于特殊布局需求，可以控制是否需要顶部或底部内边距：

```kotlin
override fun onCreate(savedInstanceState: Bundle?) {
    super.onCreate(savedInstanceState)
    setContentView(R.layout.your_layout)
    
    // 不需要顶部内边距（例如全屏图片页面）
    updateNeedTopPadding(false)
    
    // 不需要底部内边距（例如有底部导航栏的页面）
    updateNeedBottomPadding(false)
}
```

**注意**：这些方法仅在 Android 15 及以上版本有效。

### 4. 手动应用窗口插入
对于自定义视图，可以使用 WindowInsetsUtils 手动应用窗口插入：

```kotlin
// 应用内边距
WindowInsetsUtils.applyWindowInsetsListener(
    yourView,
    applyTop = true,
    applyBottom = true,
    applyLeft = true,
    applyRight = true
)

// 应用外边距
WindowInsetsUtils.applyWindowInsetsMargin(
    yourView,
    applyTop = true,
    applyBottom = false
)
```

## 注意事项

1. **版本判断**：所有新功能都包含版本判断，确保向后兼容
2. **颜色一致性**：确保状态栏颜色与页面顶部元素的背景色协调
3. **手势导航**：底部内容应避免被手势导航区域遮挡
4. **显示切口**：重要内容应避免放置在显示切口区域
5. **性能考虑**：状态栏背景视图的添加和更新都经过优化，不会影响性能
6. **测试覆盖**：需要在不同 Android 版本上测试，确保兼容性

## 测试要点

### Android 15 及以上版本
1. 在不同类型的显示切口设备上测试（水滴屏、刘海屏、挖孔屏）
2. 测试手势导航和三按钮导航模式
3. 测试主题切换时的状态栏颜色变化
4. 测试横竖屏切换
5. 验证自定义状态栏颜色是否生效

### Android 15 以下版本
1. 确保原有功能正常工作
2. 验证 ImmersionBar 设置是否正常
3. 确保不会因为新代码而崩溃或异常

## 已知问题修复

### 问题：状态栏颜色显示不正确
**症状**：某些页面的状态栏显示为根视图背景色，而不是顶部标题栏的背景色

**解决方案**：
1. 通过在 DecorView 中添加状态栏背景视图
2. 允许子类重写 `getStatusBarColor()` 方法返回正确的颜色
3. 使用 `WindowInsetsUtils.setStatusBarColor()` 统一管理状态栏颜色
4. **所有改动仅在 Android 15 及以上版本生效**

## 总结
通过以上适配方案，WDBook 应用能够在 Android 15 设备上提供现代化的沉浸式体验，同时保持良好的向后兼容性。所有界面元素都能正确处理系统 UI 的遮挡，确保用户获得最佳的使用体验。**最重要的是，所有改动都包含了版本判断，不会影响旧版本的正常运行。** 