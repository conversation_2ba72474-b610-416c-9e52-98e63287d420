plugins {
    id("com.android.application").version(Versions.GRADLE).apply(false)
    id("com.android.library").version(Versions.GRADLE).apply(false)
    kotlin("android").version(Versions.KOTLIN).apply(false)
    kotlin("multiplatform").version(Versions.KOTLIN).apply(false)
    kotlin("plugin.serialization").version(Versions.KOTLIN).apply(false)
    id("com.squareup.sqldelight").version(Versions.SQL_DELIGHT).apply(false)
    id("io.realm.kotlin").version(Versions.realm).apply(false)
    id("com.google.gms.google-services").version(Versions.googleServices).apply(false)
    id("com.google.firebase.crashlytics").version(Versions.firebaseCrashlytics).apply(false)
}

tasks.register("clean", Delete::class) {
    delete(rootProject.buildDir)
}