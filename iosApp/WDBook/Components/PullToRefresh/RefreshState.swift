//
//  RefreshState.swift
//  RefreshDemo
//
//  Created by 杜文泽 on 2021/7/1.
//

import SwiftUI


struct HeaderBoundsPreferenceKey: PreferenceKey {
    struct Item {
        let bounds: Anchor<CGRect>
    }
    
    static var defaultValue: [Item] = []
    
    static func reduce(value: inout [Item], nextValue: () -> [Item]) {
        value.append(contentsOf: nextValue())
    }
}

struct FooterBoundsPreferenceKey: PreferenceKey {
    struct Item {
        let bounds: Anchor<CGRect>
    }
    
    static var defaultValue: [Item] = []
    
    static func reduce(value: inout [Item], nextValue: () -> [Item]) {
        value.append(contentsOf: nextValue())
    }
}

struct HeaderReferenceDataKey: EnvironmentKey {
    static var defaultValue: RefreshData = .init()
}

struct FooterReferenceDataKey: EnvironmentKey {
    static var defaultValue: RefreshData = .init()
}

extension EnvironmentValues {
    var headerRefreshData: RefreshData {
        get { self[HeaderReferenceDataKey.self] }
        set { self[HeaderReferenceDataKey.self] = newValue}
    }
    
    var footerRefreshData: RefreshData {
        get { self[FooterReferenceDataKey.self] }
        set { self[FooterReferenceDataKey.self] = newValue}
    }
}

enum RefreshState: Int {
    case invalid
    case stopped
    case triggered
    case loading
}

struct RefreshData {
    var thresold: CGFloat = 0
    var progress: Double = 0
    var refreshState: RefreshState = .invalid
}
