//
//  RefreshDefaultViews.swift
//  RefreshDemo
//
//  Created by 杜文泽 on 2021/7/1.
//

import SwiftUI

struct RefreshDefaultHeader: View {
    var body: some View {
        HStack {
            ProgressView()
                .padding(10)
            Text("下拉刷新".localized)
                .font(Font.system(size: 14)).opacity(0.5)
        }
    }
}

struct RefreshDefaultFooter: View {
    var body: some View {
        HStack {
            ProgressView()
                .padding(10)
            Text("加载更多".localized)
                .font(Font.system(size: 14)).opacity(0.5)
        }
    }
}


