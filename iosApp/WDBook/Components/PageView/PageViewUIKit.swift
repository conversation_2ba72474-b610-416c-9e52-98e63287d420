//
//  PageViewUIKit.swift
//  PageViewUIKit
//
//  Created by ka<PERSON> <PERSON>hou on 2021/7/29.
//  Copyright © 2021 WeDevote Bible. All rights reserved.
//

import Foundation

import SwiftUI
import UIKit

struct PageViewUIKit: UIViewControllerRepresentable {
    var pages: [PageModel]
    var onTap: ((Int)->())?
    
    func makeUIViewController(context: Context) -> UIViewController {
        let pageViewController = PageVC()
        pageViewController.models = pages
        pageViewController.onTap = onTap
        return pageViewController
    }

    func updateUIViewController(_ pageViewController: UIViewController, context: Context) {
//        (pageViewController as! PageVC).reloadPages(pages)
    }

}
