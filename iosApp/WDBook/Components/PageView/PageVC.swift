//
//  PageVC.swift
//  PageVC
//
//  Created by <PERSON><PERSON> <PERSON><PERSON> on 2021/7/29.
//  Copyright © 2021 WeDevote Bible. All rights reserved.
//

import UIKit

struct PageModel{
    var imagePath:String?
}
class PageVC: UIViewController, UIPageViewControllerDataSource, UIPageViewControllerDelegate {
    var pageController: UIPageViewController!
    var controllers = [UIViewController]()
    var pageControl:UIPageControl!
    
    var isDragging = false
    var currentPage = 0
    var models = [PageModel]()
    var autoscrollTimer:Timer?
    var onTap: ((Int)->())?
    
    override func viewDidLoad() {
        super.viewDidLoad()

        pageController = UIPageViewController(transitionStyle: .scroll, navigationOrientation: .horizontal, options: nil)
        pageController.dataSource = self
        pageController.delegate = self

        addChild(pageController)
        view.addSubview(pageController.view)

        let views = ["pageController": pageController.view] as [String: AnyObject]
        view.addConstraints(NSLayoutConstraint.constraints(withVisualFormat: "H:|[pageController]|", options: [], metrics: nil, views: views))
        view.addConstraints(NSLayoutConstraint.constraints(withVisualFormat: "V:|[pageController]|", options: [], metrics: nil, views: views))

        pageControl = UIPageControl()
        view.addSubview(pageControl)
        pageControl.snp.makeConstraints { make in
            make.bottom.equalToSuperview()
            make.centerX.equalToSuperview()
            make.height.equalTo(28)
        }
        pageControl.backgroundColor = UIColor.clear
        pageControl.pageIndicatorTintColor = UIColor.white
        pageControl.currentPageIndicatorTintColor = UIColor.primaryColor1
        pageControl.addTarget(self,action: #selector(pageControllerValueChanged(sender:)),for: .valueChanged)
        
        reloadPages(models)
        
        view.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(tapPage(g:))))
    }
    
    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        restartTimer()
    }
    
    func reloadPages(_ pageModels:[PageModel]){
        models = pageModels
        
        for model in models {
            let vc = ImageVC()
            vc.imagePath = model.imagePath
            controllers.append(vc)
        }
        
        if pageController != nil{
            if controllers.count > 1{
                pageController.isPagingEnabled = true
            }else{
                pageController.isPagingEnabled = false
            }
            pageController.setViewControllers([controllers[0]], direction: .forward, animated: false)
        }
        
        pageControl.numberOfPages = models.count
        pageControl.hidesForSinglePage = true
    }

    //MARK: UIPageViewControllerDelegate
    func pageViewController(_ pageViewController: UIPageViewController, viewControllerBefore viewController: UIViewController) -> UIViewController? {
        if let index = controllers.firstIndex(of: viewController) {
            if index > 0 {
                return controllers[index - 1]
            } else {
                return nil
            }
        }
        return nil
    }

    func pageViewController(_ pageViewController: UIPageViewController, viewControllerAfter viewController: UIViewController) -> UIViewController? {
        if let index = controllers.firstIndex(of: viewController) {
            if index < controllers.count - 1 {
                return controllers[index + 1]
            } else {
                return nil
            }
        }
        return nil
    }

    func pageViewController(_ pageViewController: UIPageViewController, willTransitionTo pendingViewControllers: [UIViewController]) {
        isDragging = true
        stopTimer()
    }
    
    func pageViewController(_ pageViewController: UIPageViewController, didFinishAnimating finished: Bool, previousViewControllers: [UIViewController], transitionCompleted completed: Bool) {
        if completed,
           let visibleViewController = pageViewController.viewControllers?.first,
           let index = controllers.firstIndex(of: visibleViewController) {
            pageControl.currentPage = index
            currentPage = index
        }
        isDragging = false
        restartTimer()
    }
    
    //MARK: UIPageControl
    @objc func pageControllerValueChanged(sender: UIPageControl) {
        guard sender.currentPage != currentPage else{return}
        stopTimer()
        pageController.setViewControllers([controllers[sender.currentPage]], direction: sender.currentPage > currentPage ? .forward : .reverse, animated: true) {[weak self] success in
            self?.currentPage = sender.currentPage
            self?.restartTimer()
        }
    }
    
    //MARK: 计时器
    private func restartTimer() {
        stopTimer()
        //        autoscrollTimer = Timer.scheduledTimer(withTimeInterval: 3, repeats: false) {  _ in self.scrollToNextElement() }
        autoscrollTimer = Timer(timeInterval: 5, repeats: true, block: {[weak self] t in
            guard let self = self else {return}
            if !self.isDragging && self.models.count > 1{
                self.scrollToNextElement()
            }
        })
        RunLoop.main.add(autoscrollTimer!, forMode: .default)
    }
    
    private func stopTimer() {
        autoscrollTimer?.invalidate()
        autoscrollTimer = nil
    }
    
    private func scrollToNextElement() {
        guard models.count > 0 else { return }
        if currentPage == models.count - 1 {
            changePage(0)
            restartTimer()
            return
        } else {
            changePage(currentPage + 1)
            restartTimer()
        }
    }
    
    private func changePage(_ pageNumber:Int){
        guard pageNumber != currentPage else{return}
        pageController.setViewControllers([controllers[pageNumber]], direction: pageNumber > currentPage ? .forward : .reverse, animated: true) {[weak self] success in
            self?.pageControl.currentPage = pageNumber
            self?.currentPage = pageNumber
        }
    }
    
    @objc private func tapPage(g:UIGestureRecognizer){
        onTap?(pageControl.currentPage)
    }
}

extension UIPageViewController {
    var isPagingEnabled: Bool {
        get {
            var isEnabled: Bool = true
            for view in view.subviews {
                if let subView = view as? UIScrollView {
                    isEnabled = subView.isScrollEnabled
                }
            }
            return isEnabled
        }
        set {
            for view in view.subviews {
                if let subView = view as? UIScrollView {
                    subView.isScrollEnabled = newValue
                }
            }
        }
    }
}

//ipad分屏，转向。
extension PageVC {
    static let viewWillTransitionSizeNotification = Notification.Name(rawValue: "PageVC.viewWillTransitionSizeNotification")
    
    open override func viewWillTransition(to size: CGSize, with coordinator: UIViewControllerTransitionCoordinator) {
        super.viewWillTransition(to: size, with: coordinator)
        debugPrint("分屏-宽度高度:\(size)")
        
        let imgWidth:CGFloat = size.width - 24 * 2
        let imgHeight:CGFloat = (size.width - 24 * 2) / BannerWidgetPageView.WHRate
        
        debugPrint("分屏-banner:\(CGSize(width: imgWidth, height: imgHeight))")
        NotificationCenter.default.post(name: PageVC.viewWillTransitionSizeNotification, object: CGSize(width: imgWidth, height: imgHeight))
    }
}


import SnapKit
import SDWebImage

class ImageVC:UIViewController{
    var imagePath:String?
    var imageView:UIImageView!
    override func viewDidLoad() {
        imageView = UIImageView()
        imageView.contentMode = UIView.ContentMode.scaleToFill
        view.addSubview(imageView)
        imageView.snp.makeConstraints { make in
            make.top.bottom.equalToSuperview()
            make.leading.equalTo(24)
            make.trailing.equalTo(-24)
        }
        if let path = imagePath,let url = URL(string: path){
            imageView.sd_imageIndicator = SDWebImageActivityIndicator()
            imageView.sd_setImage(with: url, placeholderImage: nil, context: ImageManager.getImageContext())
        }
    }
}
