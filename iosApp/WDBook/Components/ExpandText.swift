//
//  ExpandText.swift
//  WDBook
//
//  Created by <PERSON> on 2021/1/4.
//  Copyright © 2021 WeDevote Bible. All rights reserved.
//

import SwiftUI

struct ExpandText: View {
    @State private var expanded: Bool = false
    @State private var truncated: Bool = false
    
    private var text: String
    private var lineLimit: Int
    private var uifont: UIFont
    private var foregroundColor: UIColor
    
    init(_ text: String,
         lineLimit: Int = 3,
         uifont: UIFont = UIFont.regular(size: 12),
         foregroundColor: UIColor = dynamicTitleColor2) {
        self.text = text
        self.lineLimit = lineLimit
        self.uifont = uifont
        self.foregroundColor = foregroundColor
    }
    
    private func determineTruncation(_ geometry: GeometryProxy) {
        // Calculate the bounding box we'd need to render the
        // text given the width from the GeometryReader.
        let total = self.text.boundingRect(
            with: CGSize(
                width: geometry.size.width,
                height: .greatestFiniteMagnitude
            ),
            options: .usesLineFragmentOrigin,
            attributes: [.font: uifont],
            context: nil
        )
        
        if total.size.height > geometry.size.height {
            self.truncated = true
        }
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(self.text)
                .lineLimit(self.expanded ? nil : self.lineLimit)
                .fixedSize(horizontal: false, vertical: true)
                .font(Font(uifont))
                .foregroundColor(Color(self.foregroundColor))
                .frame(maxWidth:.infinity, maxHeight: .infinity,alignment: .leading)
                .background(GeometryReader { geometry in
                    Color.clear.onAppear {
                        self.determineTruncation(geometry)
                    }
                })
                .onTapGesture {
                    self.toggleExpanded()
                }
            
            if self.truncated {
                self.toggleButton
            }
        }
    }
    
    var toggleButton: some View {
        HStack(alignment: .center) {
            Spacer()
            Button(action: { self.toggleExpanded() }) {
                Image(self.expanded ? "arrow_up" : "arrow_down")
            }
            Spacer()
        }
    }
    
    func toggleExpanded() {
        if self.truncated {
            self.expanded.toggle()
        }
    }
}

#if DEBUG
struct ExpandText_Previews: PreviewProvider {
    static var previews: some View {
        ExpandText("丝黛芬妮的搜房ID搜房你我的房间功能看第三方丝黛芬妮的搜房ID搜房你我的房间功能看第三方丝黛芬妮的搜房ID搜房你我的房间功能看第三方丝黛芬妮的搜房ID搜房你我的房间功能看第三方丝黛芬妮的搜房ID搜房你我的房间功能看第三方丝黛芬妮的搜房ID搜房你我的房间功能看第三方")
    }
}
#endif
