//
//  ClearButtonMode.swift
//  WDBook
//
//  Created by <PERSON><PERSON> <PERSON><PERSON> on 2023/6/23.
//  Copyright © 2023 WeDevote Bible. All rights reserved.
//

import Foundation
import SwiftUI

struct ClearButtonMode: ViewModifier {
    @Binding var text: String
    @Binding var focused: Bool
    
    func body(content: Content) -> some View {
        content
            .overlay((text.count > 0 && focused) ? AnyView(But<PERSON>(action: {
                self.text = ""
            }) {
                Image(systemName: "xmark.circle.fill")
                    .foregroundColor(.secondary)
            }) : AnyView(EmptyView()),alignment: .trailing)
    }
}
