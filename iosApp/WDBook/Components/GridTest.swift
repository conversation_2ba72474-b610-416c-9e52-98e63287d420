//
//  GridTest.swift
//  WDBook
//
//  Created by <PERSON><PERSON> <PERSON><PERSON> on 2021/12/22.
//  Copyright © 2021 WeDevote Bible. All rights reserved.
//

import SwiftUI

//struct ContentView2: View {
////    let data = (1...100).map { "Item \($0)" }
//    let data = ["Some long item here", "And then some longer one",
//                "Short", "Items", "Here", "And", "A", "Few", "More",
//                "And then a very very very long one"]
//
//    let columns = [
//        GridItem(.flexible())
//    ]
//
//    var body: some View {
//        ScrollView {
//            LazyVGrid(columns: columns, spacing: 20) {
//                ForEach(data, id: \.self) { item in
//                    Text(item)
//                }
//            }
//            .padding(.horizontal)
//        }
//        .frame(maxHeight: 300)
//    }
//}
//
//struct GridTest: View {
//    var body: some View {
//        ContentView2()
//    }
//}
//
//struct GridTest_Previews: PreviewProvider {
//    static var previews: some View {
//        GridTest()
//    }
//}
