//
//  AlphaShape.swift
//  WDBook
//
//  Created by <PERSON><PERSON> <PERSON><PERSON> on 2022/6/9.
//  Copyright © 2022 WeDevote Bible. All rights reserved.
//

import SwiftUI

struct AlphaShape: View {
    
    var path:some View{
        return Path { path in
            
            path.move(to: CGPoint(x:0,y:0))
            path.addLine(to: CGPoint(x:45,y: 0))

            path.addArc(center: CGPoint(x: 50, y: 0), radius: 5, startAngle: .degrees(-180), endAngle: .degrees(0), clockwise: true)
            path.addLine(to: CGPoint(x:55,y: 0))
            path.addLine(to: CGPoint(x:100,y: 0))
            path.addLine(to: CGPoint(x:100,y: 100))
            path.addLine(to: CGPoint(x:0,y: 100))
            path.addLine(to: CGPoint(x:0,y: 0))
        }.stroke(Color(UIColor.black.alpha(0.5)), lineWidth: 0.5)
    }
    var body: some View {
//        ZStack{
//            MyArc()
//                .fill(LinearGradient(gradient: Gradient(colors: [.green, .yellow, .orange, .red,]), startPoint: .bottom, endPoint: .top ))
//            Triangle()
//                .clipped()
//                .opacity(0.74)
//        }
        ZStack{
            ZStack{
//                AngularGradient(gradient: Gradient(colors: [.red, .blue.opacity(0)]),
//                        center: .center, startAngle: .degrees(360), endAngle: .degrees(180) )

                Path { path in

                    path.move(to: CGPoint(x:0,y:0))
                    path.addLine(to: CGPoint(x:45,y: 0))

                    path.addArc(center: CGPoint(x: 50, y: 0), radius: 5, startAngle: .degrees(-180), endAngle: .degrees(0), clockwise: true)
                    path.addLine(to: CGPoint(x:55,y: 0))
                    path.addLine(to: CGPoint(x:100,y: 0))
                    path.addLine(to: CGPoint(x:100,y: 100))
                    path.addLine(to: CGPoint(x:0,y: 100))
                    path.addLine(to: CGPoint(x:0,y: 0))
                }.fill(.green)
//                .stroke(Color(UIColor.black.alpha(0.5)), lineWidth: 0.5)
                    
                
            }.frame(width: 100, height: 100, alignment: .center)
//                .background(path)
//                        .clipShape(Circle())
                        .shadow(color: Color(UIColor.black.alpha(0.5)), radius: 5, x: 0, y: 2)
        }.frame(width: 200, height: 200, alignment: .center)
        .background(Color.yellow)
        
    }
}

#if DEBUG
struct AlphaShape_Previews: PreviewProvider {
    static var previews: some View {
        AlphaShape()
    }
}
#endif

struct MyArc: Shape {
    func path(in rect: CGRect) -> Path {
        var path = Path()
        path.addArc(center: CGPoint(x: rect.midX, y: rect.midY),
                    radius: rect.size.width / 2,
                    startAngle: .degrees(0),
                    endAngle: .degrees(275),
                    clockwise: false)
        
        return path
    }
}
struct Triangle: Shape {
    func path(in rect: CGRect) -> Path {
        var path = Path()
        path.move(to: CGPoint(x: rect.midX, y: rect.minY))
        path.addLine(to: CGPoint(x: rect.midX, y: rect.midY))
        path.addLine(to: CGPoint(x: rect.maxX, y: rect.midY))
        
        return path
    }
}
