//
//  CustomSecureField.swift
//  WDBook
//
//  Created by ka<PERSON> <PERSON>hou on 2023/8/24.
//  Copyright © 2023 WeDevote Bible. All rights reserved.
//

import Foundation
import UIKit
import SwiftUI

struct CustomSecureField: UIViewRepresentable {
    var placeHolder:String?
    @Binding var text: String
    var onEditingChanged: ((Bool) -> Void)?
    
    func makeUIView(context: Context) -> UITextField {
        let textField = UITextField()
        textField.placeholder = placeHolder
        textField.text = text
        textField.isSecureTextEntry = true
        textField.delegate = context.coordinator
        textField.addTarget(context.coordinator, action: #selector(context.coordinator.textFieldDidChange(_:)), for: .editingChanged)
        return textField
    }
    
    func updateUIView(_ uiView: UITextField, context: Context) {
//        uiView.text = text
    }
    
    func makeCoordinator() -> Coordinator {
        Coordinator(text: $text, onEditingChanged: onEditingChanged)
    }
    
    class Coordinator: NSObject, UITextFieldDelegate {
        @Binding var text: String
        var onEditingChanged: ((Bool) -> Void)?
        
        init(text: Binding<String>, onEditingChanged: ((Bool) -> Void)?) {
            _text = text
            self.onEditingChanged = onEditingChanged
        }
        
        func textFieldDidEndEditing(_ textField: UITextField) {
            onEditingChanged?(false)
        }
        
        func textFieldDidBeginEditing(_ textField: UITextField) {
            onEditingChanged?(true)
        }
        
        @objc func textFieldDidChange(_ textField: UITextField) {
            text = textField.text ?? ""
        }
    }
    
}
