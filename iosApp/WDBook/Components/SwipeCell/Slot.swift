import SwiftUI

public struct Slot: Identifiable {
    /// Id
    public let id = UUID()
    /// To allow modification on Text, wrap it with AnyView.
    public let title: () -> AnyView
    /// Tap Action
    public let action: () -> Void
    /// Style
    public let style: SlotStyle
    /// alert
    public var alertTitle = ""
    public var alertMessage = ""
    public var primaryButtonText = ""
    public var alertTyle = AlertType.NONE

    public enum AlertType {
        case NONE
        case DELETE
        case RECOVER
    }

    public init(
        title: @escaping () -> AnyView,
        action: @escaping () -> Void,
        style: SlotStyle,
        alertTyle: AlertType
    ) {
        self.title = title
        self.action = action
        self.style = style
        self.alertTyle = alertTyle
        switch alertTyle {
        case .DELETE:
            alertTitle = "删除".localized
            alertMessage = "\n\("确定删除当前笔记吗?".localized)\n"
            primaryButtonText = "删除".localized
        case .RECOVER:
            alertTitle = "恢复".localized
            alertMessage = "\n\("确定删除当前笔记吗?".localized)\n"
            primaryButtonText = "恢复".localized
        default: break
        }
    }
}

public struct SlotStyle {
    /// Background color of slot.
    public let background: Color
    /// Individual slot width
    public let slotWidth: CGFloat

    public init(background: Color, slotWidth: CGFloat = 80) {
        self.background = background
        self.slotWidth = slotWidth
    }
}
