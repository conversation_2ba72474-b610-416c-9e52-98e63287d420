import SwiftUI

extension SlidableModifier {
    static let hideSlotControlNotification = Notification.Name(rawValue: "SlidableModifier.hideSlotControlNotification")
}

public struct SlidableModifier: ViewModifier, Animatable {
    public enum SlideAxis {
        case left2Right
        case right2Left
    }

    private var contentOffset: CGSize {
        switch slideAxis {
        case .left2Right:
            return .init(width: currentSlotsWidth, height: 0)
        case .right2Left:
            return .init(width: -currentSlotsWidth, height: 0)
        }
    }

    private var slotOffset: CGSize {
        switch slideAxis {
        case .left2Right:
            return .init(width: currentSlotsWidth - totalSlotWidth, height: 0)
        case .right2Left:
            return .init(width: totalSlotWidth - currentSlotsWidth, height: 0)
        }
    }

    private var zStackAlignment: Alignment {
        switch slideAxis {
        case .left2Right:
            return .leading
        case .right2Left:
            return .trailing
        }
    }

    /// Animated slot widths of total
    @State var currentSlotsWidth: CGFloat = 0

    /// To restrict the bounds of slots
    private func optWidth(value: CGFloat) -> CGFloat {
        return min(abs(value), totalSlotWidth)
    }

    public var animatableData: Double {
        get { Double(currentSlotsWidth) }
        set { currentSlotsWidth = CGFloat(newValue) }
    }

    private var totalSlotWidth: CGFloat {
        return slots.map { $0.style.slotWidth }.reduce(0, +)
    }

    private var slots: [Slot] {
        slideAxis == .left2Right ? leadingSlots : trailingSlots
    }

    @State private var slideAxis = SlideAxis.left2Right
    @State private var isHorizontalDrag = false
    @State private var initialDragLocation: CGPoint? = nil
    @State private var hasDecidedDirection = false
    private var leadingSlots: [Slot]
    private var trailingSlots: [Slot]
    @Binding private var disable: Bool
    public init(disable: Binding<Bool> = .constant(false), leading: [Slot], trailing: [Slot]) {
        leadingSlots = leading
        trailingSlots = trailing
        _disable = disable
        // Initialize state vars
        _isHorizontalDrag = State(initialValue: false)
        _initialDragLocation = State(initialValue: nil)
        _hasDecidedDirection = State(initialValue: false)
    }

    private func flushState() {
        withAnimation(.easeIn(duration: 0.1)) {
            self.currentSlotsWidth = 0
        }
    }

    public func body(content: Content) -> some View {
        ZStack(alignment: zStackAlignment) {
            content
                .offset(self.contentOffset)

            if !currentSlotsWidth.isZero {
                Rectangle()
                    .foregroundColor(.white)
                    .opacity(0.001)
                    .onTapGesture {
                        // 已经展开,点击的时候
                        NotificationCenter.default.post(name: SlidableModifier.hideSlotControlNotification, object: slot.id)
                        flushState()
                    }
            }

            slotContainer
                .offset(self.slotOffset)
                .frame(width: self.totalSlotWidth)
        }
        // Use basic simultaneousGesture to allow both horizontal and vertical gestures
        // Our custom gesture logic will handle differentiating between them
        .simultaneousGesture(gesture)
        .onReceive(NotificationCenter.default.publisher(for: SlidableModifier.hideSlotControlNotification, object: nil)) { noti in
            guard let uuid = noti.object as? UUID, uuid == slot.id else {
                debugPrint("滑动：onReceive不是自己，隐藏: \(slot.id)")
                flushState()
                return
            }
        }
    }

    // MARK: Slot Container

    @State var slot = Slot(
        title: { Text("").embedInAnyView() },
        action: {},
        style: .init(background: Color(UIColor(hex: 0x4E4E4E))),
        alertTyle: .NONE
    )
    @State private var isShowAlert = false
    private var slotContainer: some View {
        HStack(spacing: 0) {
            ForEach(self.slots) { slot in
                Button(action: {
                    if slot.alertTyle == .NONE {
                        self.flushState()
                        DispatchQueue.main.asyncAfter(deadline: .now() + 0.25) {
                            slot.action()
                        }
                    } else {
                        self.isShowAlert = true
                    }
                    self.slot = slot
                }) {
                    VStack(spacing: 0) {
                        Spacer() // To extend top edge

                        slot.title()
                            .font(.system(size: 14))
                            .foregroundColor(Color(UIColor(hex: 0xF0F0F2)))

                        Spacer() // To extend bottom edge
                    }
                    .frame(width: slot.style.slotWidth)
                    .background(slot.style.background)
                }
            }
        }
        .alert(isPresented: $isShowAlert) {
            Alert(title: Text(slot.alertTitle),
                  message: Text(slot.alertMessage),
                  primaryButton: .default(Text("取消".localized)) {
                      withAnimation {
                          self.currentSlotsWidth = 0
                      }
                      isShowAlert = false

                  }, secondaryButton: .default(Text(slot.primaryButtonText)) {
                      self.flushState()
                      isShowAlert = false
                      DispatchQueue.main.asyncAfter(deadline: .now() + 0.25) {
                          slot.action()
                      }

                  })
        }
    }

    // MARK: - Drag Gesture

    private var gesture: some Gesture {
        DragGesture(minimumDistance: 10, coordinateSpace: .local)
            .onChanged { value in
                guard !disable else { return }

                // Store initial position if not already set
                if initialDragLocation == nil {
                    initialDragLocation = value.startLocation
                    hasDecidedDirection = false
                    isHorizontalDrag = false
                }

                // Calculate total drag distances
                let dx = value.location.x - (initialDragLocation?.x ?? 0)
                let dy = value.location.y - (initialDragLocation?.y ?? 0)
                let horizontalAmount = abs(dx)
                let verticalAmount = abs(dy)

                // If we haven't decided on the direction yet
                if !hasDecidedDirection {
                    // Wait until we have enough movement to make a clear decision
                    if horizontalAmount + verticalAmount > 20 {
                        // Clear horizontal movement (3x more horizontal than vertical)
                        if horizontalAmount > verticalAmount * 3 {
                            isHorizontalDrag = true
                        } else {
                            isHorizontalDrag = false
                        }
                        hasDecidedDirection = true
                    } else {
                        // Not enough movement to decide
                        return
                    }
                }

                // If this isn't a horizontal drag, don't process it
                if !isHorizontalDrag {
                    return
                }

                // Process the swipe
                let amount = value.translation.width

                if amount < 0 {
                    self.slideAxis = .right2Left
                } else {
                    self.slideAxis = .left2Right
                }

                self.currentSlotsWidth = self.optWidth(value: amount)

                NotificationCenter.default.post(name: SlidableModifier.hideSlotControlNotification, object: slot.id)
            }
            .onEnded { _ in
                guard !disable else { return }

                // Always reset tracking state
                defer {
                    initialDragLocation = nil
                    hasDecidedDirection = false
                    isHorizontalDrag = false
                }

                // Only animate if we've determined this was a horizontal drag
                if isHorizontalDrag {
                    withAnimation {
                        if self.currentSlotsWidth < (self.totalSlotWidth / 2) {
                            self.currentSlotsWidth = 0
                        } else {
                            self.currentSlotsWidth = self.totalSlotWidth
                        }
                    }
                } else {
                    // For non-horizontal gestures, just reset without animation
                    self.currentSlotsWidth = 0
                }
            }
    }
}
