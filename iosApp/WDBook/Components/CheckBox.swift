//
//  CheckBox.swift
//  WDBook
//
//  Created by <PERSON> on 2020/9/10.
//  Copyright © 2020 WeDevote Bible. All rights reserved.
//

import SwiftUI

struct CheckBox: View {
    @Binding var isChecked:Bool
    func toggle(){isChecked = !isChecked}
    var body: some View {
        Button(action: toggle) {
            Image(self.isChecked ? "checkbox_check" : "checkbox_uncheck")
            .renderingMode(.original)
        }
        
    }
}

struct CheckBox20: View {
    @Binding var isChecked:Bool
    func toggle(){isChecked = !isChecked}
    var body: some View {
        Button(action: toggle) {
            Image(self.isChecked ? "checkbox20_checked" : "checkbox20_unchecked")
            .renderingMode(.original)
        }
        
    }
}

#if DEBUG
struct CheckView_Previews: PreviewProvider {
    @State static var isChecked = false
    
    static var previews: some View {
        Group{
            CheckBox(isChecked:self.$isChecked)
            CheckBox20(isChecked:self.$isChecked)
        }
        
    }
}
#endif
