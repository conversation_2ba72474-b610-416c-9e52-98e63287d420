//
//  UITabbarExtension.swift
//  Waka
//
//  Created by z<PERSON><PERSON> on 6/28/16.
//  Copyright © 2016 Waka. All rights reserved.
//

import UIKit
let RedDotTag = 999
extension  UITabBar {

    func tabBarButtonAtIndex(_ index:Int)->UIView?{
        let subViews = tabBarItemViews()
        guard (0 ..< subViews.count).contains(index) else {
            return nil
        }
        return subViews[index]
    }
    
    func tabBarItemViews() -> [UIView] {
        let interactionViews = subviews.filter({$0.isUserInteractionEnabled && $0.isKind(of: NSClassFromString("UITabBarButton")!)})
        return interactionViews.sorted(by: {$0.frame.minX < $1.frame.minX})
    }
    
    /*注意：
     先获取到对应index的tabBarButton，再找它subview里的imageview。
     发现模块没有设置tabBarItem.image，直接遍历获取到的imageview比tabBarButton少一个
     */
    func tabBarItemImageViewAtIndex(_ index:Int)->UIView?{
        guard let tabbarButton = tabBarButtonAtIndex(index) else {
            return nil
        }
        let interactionViews = tabbarButton.subviews.filter({$0.isKind(of: NSClassFromString("UIImageView")!)})
        if interactionViews.count > 0 {
            return interactionViews.last!
        }
        return nil
    }
    
    //把红点加到tabbar的icon上，如果加到tabBarButton上，因为获取到的tabBarButton.frame不正确，在ipad显示位置不对
    func addRedDot(_ index:Int){
        guard !hasRedDot(index) else {
            return
        }
        
        guard let imageIcon = tabBarItemImageViewAtIndex(index) else {
            return
        }
        let dotRadius:CGFloat = 4.0 //红点半径
        
        let  newIcon = UIView(frame:CGRect(x:imageIcon.frame.width,
            y:-dotRadius,
            width:dotRadius*2,
            height:dotRadius*2))
        newIcon.backgroundColor = UIColor(hex: 0xFF342A)
        newIcon.layer.masksToBounds = true
        newIcon.layer.cornerRadius = dotRadius;
        newIcon.tag = RedDotTag
        imageIcon.addSubview(newIcon)
    }
    
    func removeRedDot(_ index:Int){
        guard let imageIcon = tabBarItemImageViewAtIndex(index) else {
            return
        }
        removeSubViewWhichTagEqual999(view: imageIcon)
    }
    
    func hasRedDot(_ index:Int)->Bool{
        guard let imageIcon = tabBarItemImageViewAtIndex(index) else {
            return false
        }
        
        for subview in imageIcon.subviews {
            if subview.tag == RedDotTag {
                return true
            }
        }
        
        return false
    }
    
    func removeAllRedDot(){
        let tabbarButtons = tabBarItemViews()
        guard tabbarButtons.count > 0 else{
            return
        }
        
        for button in tabbarButtons {
            let interactionViews = button.subviews.filter({$0.isKind(of: NSClassFromString("UIImageView")!)})
            for imageView in interactionViews {
                removeSubViewWhichTagEqual999(view: imageView)
            }
        }
    }

    func removeSubViewWhichTagEqual999(view: UIView) {
        for subview in view.subviews {
            if subview.tag == RedDotTag {
                subview.removeFromSuperview()
            }
        }
    }
}
