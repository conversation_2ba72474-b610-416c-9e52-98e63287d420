//
//  LightWebView.swift
//  WDBook
//
//  Created by <PERSON> on 2020/8/31.
//  Copyright © 2020 WeDevote Bible. All rights reserved.
//

import Foundation
import SwiftUI
import wdNetwork
import WebKit

struct LightWebview: View {
    @Environment(\.presentationMode) var presentationMode: Binding<PresentationMode>

    var url: String = ""
    var title: String = ""
    var content: String = ""
    @State var didStart: Bool = false

    var btnBack: some View { Button(action: {
        self.presentationMode.wrappedValue.dismiss() // 单独使用
    }) {
        HStack {
            Image("back_ui")
                .aspectRatio(contentMode: .fit)
                .foregroundColor(Color(btnTintColor))
        }
    }
    }

    var body: some View {
        ZStack {
            if !url.isEmpty || !content.isEmpty {
                LightWebViewUIKit(url: url, content: content, didStartProvisionalNavigation: {
                    didStart = true
                })
            }

            if !didStart {
                ActivityIndicator()
            }
        }.frame(maxWidth: .infinity, maxHeight: .infinity)
            .navigationBarBackButtonHidden(true)
            .navigationBarItems(leading: btnBack)
            .navigationBarTitle(Text(self.title), displayMode: .inline)
    }
}

struct LightWebViewUIKit: UIViewRepresentable {
    var url: String
    var content: String
    var activity = UIActivityIndicatorView(style: .medium)

    // 开始和结束的外部捕获
    var didStartProvisionalNavigation: (() -> Void)?
    var didFinish: (() -> Void)?

    func makeCoordinator() -> Coordinator {
        Coordinator(activity: activity, owner: self)
    }

    class Coordinator: NSObject, WKNavigationDelegate {
        private var isLoading: Bool = true
        private weak var activity: UIActivityIndicatorView?
        private var owner: LightWebViewUIKit?
        weak var webview: WKWebView?

        init(activity: UIActivityIndicatorView?, owner: LightWebViewUIKit?) {
            self.activity = activity
            self.owner = owner
        }

        func webView(_ webView: WKWebView, didStartProvisionalNavigation _: WKNavigation!) {
//            webView.isHidden = true
            owner?.didStartProvisionalNavigation?()
            activity?.center = webView.center // 必须webview start后，赋值位置才正确。必须gray样式才看得见。
            activity?.startAnimating()
        }

        func webView(_ webView: WKWebView, didFinish _: WKNavigation!) {
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) { [weak self] in
                self?.showWebView(webView: webView)
            }
        }

        @objc func showWebView(webView _: WKWebView) {
            activity?.stopAnimating()
            activity?.removeFromSuperview()
            owner?.didFinish?()
        }

        func webView(_: WKWebView, decidePolicyFor navigationAction: WKNavigationAction, decisionHandler: @escaping (WKNavigationActionPolicy) -> Void) {
            debugPrint(navigationAction.request)
            if navigationAction.navigationType == WKNavigationType.linkActivated {
                if let url = navigationAction.request.url {
                    if URLManager.shared.allowScheme(url.scheme ?? "") {
                        URLManager.shared.parseWDBookUrl(url: url)
                        decisionHandler(WKNavigationActionPolicy.cancel)
                        return

                                // 统一处理http跳转到外置浏览器
                    } else if url.isHttpProtocol {
                        if UIApplication.shared.canOpenURL(url) {
                            UIApplication.shared.open(url, options: [:]) { success in
                                if success {
                                    Log.d("10以后可以跳转url")
                                } else {
                                    Log.d("10以后不能完成跳转")
                                }
                            }
                        }
                        decisionHandler(WKNavigationActionPolicy.cancel)
                        return
                    }
                }
                decisionHandler(WKNavigationActionPolicy.allow)
                return
            }

            decisionHandler(WKNavigationActionPolicy.allow)
        }

        func requestCftDataOnWebView(url: URL) {
            let configuration = URLSessionConfiguration.default
            configuration.protocolClasses?.insert(WDUrlProtocol.self, at: 0)
            NetworkUtils.configureCftHeaders(for: configuration)
            let session = URLSession(configuration: configuration)
            let task = session.dataTask(with: url) { [weak self] data, _, error in
                if let error = error {
                    Log.d("Error: \(error)")
                } else if let data = data {
                    if let responseString = String(data: data, encoding: .utf8) {
                        self?.webview?.loadHTMLString(responseString, baseURL: nil)
                    }
                }
            }
            task.resume()
        }
    }

    func makeUIView(context: Context) -> WKWebView {
        activity.color = .gray
        activity.hidesWhenStopped = true

        let webView = WKWebView()
        webView.addSubview(activity)
        webView.isOpaque = false
        webView.backgroundColor = .clear
        webView.navigationDelegate = context.coordinator
        context.coordinator.webview = webView

        if let url = URL(string: url) {
            let request = URLRequest(url: url)
            webView.load(request)

            NetworkUtils.shared.checkNetworkStatus {} cftHandler: {
                context.coordinator.requestCftDataOnWebView(url: url)
            }

            return webView
        } else if !content.isEmpty {
            webView.loadHTMLString(content, baseURL: nil)
            return webView
        } else {
            return WKWebView()
        }
    }

    func updateUIView(_: WKWebView, context _: UIViewRepresentableContext<LightWebViewUIKit>) {}
}

#if DEBUG
    struct LightWebview_Previews: PreviewProvider {
        static var previews: some View {
            Group {
                NavigationView {
                    LightWebview(url: "https://google.com", title: "谷歌")
                }
                NavigationView {
                    LightWebview(url: "https://google.com", title: "谷歌").environment(\.colorScheme, .dark)
                }
            }
        }
    }
#endif
