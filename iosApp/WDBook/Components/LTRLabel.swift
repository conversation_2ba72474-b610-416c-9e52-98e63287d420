//
//  LTRLabel.swift
//  WDBook
//
//  Created by ka<PERSON> <PERSON><PERSON> on 2023/10/30.
//  Copyright © 2023 WeDevote Bible. All rights reserved.
//

import Foundation
import SwiftUI

struct LTRLabel: UIViewRepresentable {
    var text: String
    var textColor: UIColor
    var font: UIFont
    var bgColor:UIColor?
    var lineLimit: Int = 0
    var lineSpacing: CGFloat?
    var textAlignment: NSTextAlignment?
    var horizontalPadding:CGFloat? = nil
    
    init(text: String, textColor: UIColor, font: UIFont, bgColor:UIColor? = nil,maxWidth:CGFloat? = nil, lineLimit: Int = 0, lineSpacing: CGFloat? = nil, textAlignment: NSTextAlignment? = nil,horizontalPadding:CGFloat? = nil) {
        self.text = text
        self.textColor = textColor
        self.font = font
        self.bgColor = bgColor
        self.horizontalPadding = horizontalPadding
        self.lineLimit = lineLimit
        self.lineSpacing = lineSpacing
        self.textAlignment = textAlignment
    }
    
    func makeCoordinator() -> Coordinator {
        Coordinator()
    }
        
    class Coordinator {
        var label: UILabel?
    }
    
    func makeUIView(context: Context) -> UILabel {
        let label = UILabel()
        setAttr(label: label)
        if let hpadding = horizontalPadding{
            label.preferredMaxLayoutWidth = UIScreen.main.bounds.width - hpadding * 2
//            label.snp.makeConstraints { make in
//                make.width.equalTo(UIScreen.main.bounds.width - hpadding * 2)
//            }
        }
        return label
    }
    
    func updateUIView(_ label: UILabel, context: Context) {
        setAttr(label: label)
        if let hpadding = horizontalPadding{
            label.preferredMaxLayoutWidth = UIScreen.main.bounds.width - hpadding * 2
//            label.snp.updateConstraints { make in
//                make.width.equalTo(UIScreen.main.bounds.width - hpadding * 2)
//            }
        }
    }
    
    func setAttr(label:UILabel){
        label.text = text
        label.textColor = textColor
        label.font = font
        label.numberOfLines = lineLimit
        if bgColor != nil{
            label.backgroundColor = bgColor
        }
        
        if textAlignment != nil{
            label.textAlignment = textAlignment!
        }
        
        let paragraphStyle = NSMutableParagraphStyle()
        if lineSpacing != nil{
            paragraphStyle.lineSpacing = lineSpacing!
        }
        paragraphStyle.lineBreakMode = .byTruncatingTail
        paragraphStyle.baseWritingDirection = .leftToRight
        let attr = NSMutableAttributedString(string: text, attributes: [NSAttributedString.Key.paragraphStyle: paragraphStyle,
                                                                        NSAttributedString.Key.font: font,
                                                                        NSAttributedString.Key.foregroundColor:textColor])
        if bgColor != nil{
            attr.addAttribute(NSAttributedString.Key.backgroundColor, value: bgColor!, range: NSRange(location: 0, length: text.count))
        }
        
        label.attributedText = attr
    }
}
