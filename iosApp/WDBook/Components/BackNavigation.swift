//
//  BackNavigation.swift
//  WDBook
//
//  Created by <PERSON> on 2020/9/26.
//  Copyright © 2020 WeDevote Bible. All rights reserved.
//

import SwiftUI
import UIKit

let CustomNavigationDidShowNotification = Notification.Name(rawValue: "CustomNavigationDidShowNotification")
let CustomNavigationDidHideNotification = Notification.Name(rawValue: "CustomNavigationDidHideNotification")

class SlideOffsetObj{
    var offsetX:CGFloat = 0
}

struct BackNavigation<Content>: View where Content: View {
    @Environment(\.presentationMode) var presentationMode: Binding<PresentationMode>
    @Environment(\.safeAreaInsets3) private var safeAreaInsets
    @GestureState private var dragOffset = CGSize.zero
    @State private var slideOffsetX:CGFloat = 0.0
    private var slideOffsetObj = SlideOffsetObj()
    
    @Binding var isPresent:Bool
    var title:String
    var rightContent:String
    @Binding var isNavigationHide:Bool
    var isHideBackButton:Bool = false
    var isHideBottomLine:Bool = false
    var ignoreSafeArea:Bool = false
    var isEnableSlideBack = false
    var onTapBack:(()->())?
    var onTapRight:(()->())?
    
    func backFunction(){
        self.onTapBack?()
        DispatchQueue.main.async {
            self.isPresent = false
            NotificationCenter.default.post(name: CustomNavigationDidHideNotification, object: nil)
        }
        self.presentationMode.wrappedValue.dismiss()
    }
    
    func btnBack(left:Bool = true) -> some View { Button(action: {
            self.backFunction()
        }) {
            HStack {
                !left ? AnyView(Spacer()) : AnyView(EmptyView())
                Image("back_ui")
                .aspectRatio(contentMode: .fit)
                .foregroundColor(Color(btnTintColor)) //ios14无效
                left ? AnyView(Spacer()) : AnyView(EmptyView())
            }.frame(width:40,height: 45)
//            .background(Color.yellow)
        }
    }
    
    
    private let content: () -> Content

    public init(isPresent:Binding<Bool>? = nil,
                title: String = "",
                rightContent: String = "",
                isNavigationHide:Binding<Bool>? = nil,
                isHideBackButton:Bool = false,
                isHideBottomLine:Bool = false,
                ignoreSafeArea:Bool = false,
                isEnableSlideBack:Bool = true,
                onTapBack: (()->())? = nil,
                onTapRight: (()->())? = nil,
                @ViewBuilder _ content: @escaping () -> Content) {
        _isPresent = isPresent ?? .constant(false)
        self.title = title
        self.rightContent = rightContent
        _isNavigationHide = isNavigationHide ?? .constant(false)
        self.isHideBackButton = isHideBackButton
        self.isHideBottomLine = isHideBottomLine
        self.ignoreSafeArea = ignoreSafeArea
        self.isEnableSlideBack = isEnableSlideBack
        self.onTapBack = onTapBack
        self.onTapRight = onTapRight
        self.content = content
    }

    var mainContent : some View{
        ZStack(alignment: .top, content: {
            content()
                .navigationBarHidden(true)
                .padding(.top, (45 + (self.ignoreSafeArea ? 0 : safeAreaInsets.top)))
            
                HStack(alignment: .center, spacing: 0, content: {
                    btnBack().padding(EdgeInsets(top: 0, leading: 0, bottom: 0, trailing: 20))
                        .opacity(isHideBackButton ? 0:1)
                    if !rightContent.isBlank {//占位
                        Text(rightContent)
                            .foregroundColor(Color(dynamicBackgroundColor1))
                            .font(Font.semibold(size: 14))
                            .opacity(0)
                    }
                    Spacer()
                    Text(LocalizedStringKey(title))
                        .foregroundColor(Color(dynamicTitleColor2))
                        .font(Font.semibold(size: 18))
                    Spacer()
                    btnBack(left: false).padding(EdgeInsets(top: 0, leading: 20, bottom: 0, trailing: 0)).opacity(0) //占位
                    if !rightContent.isBlank {
                        Button {
                            self.onTapRight?()
                        } label: {
                            Text(rightContent)
                                .foregroundColor(Color(dynamicTitleColor2))
                                .font(Font.regular(size: 14))
                                .contentShape(Rectangle())
                                .border(Color.red, width: 1)
                        }
                    }
                }).frame(height:45)
                .frame(maxWidth:.infinity)
                .padding(.horizontal, 20)
                .padding(.top, self.ignoreSafeArea ? 0 : safeAreaInsets.top)
                .background(Color(dynamicBackgroundColor1))
                .modifier(BottomLineViewModifier(isShowBottomLine: !isHideBottomLine))
        })
        .navigationBarHidden(true)
        .edgesIgnoringSafeArea(.top)
        .onAppear(perform: {
            isPresent = true
            NotificationCenter.default.post(name: CustomNavigationDidShowNotification, object: nil)
        })
    }
    var body : some View {
        if isEnableSlideBack {
            mainContent
        }else{
            mainContent            //.offset(x: $dragOffset.wrappedValue.width, y: 0)
                .gesture(DragGesture().updating($dragOffset) { (value, state, transaction) in

                    state.width = value.translation.width
    //                self.slideOffsetX = dragOffset.width //值改不了!!!
                    self.slideOffsetObj.offsetX = dragOffset.width
    //                Log.d("滑动中：dragOffset:\(dragOffset), value:\(value), state:\(state),transcation:\(transaction)，transcation width：\(value.translation.width)")
    //                Log.d("滑动中：slideOffsetX:\(slideOffsetX), dragOffset:\(dragOffset),transcation width：\(value.translation.width)")
    //                if(value.startLocation.x < 20 && value.translation.width > 100) {
    //                    self.backFunction()
    //                }

                }.onEnded { value in
    //                结束后 dragOffset是0
    //                Log.d("滑动结束：dragOffset:\(dragOffset), value:\(value),transcation width:\(value.translation.width)")
    //                Log.d("滑动结束：slideOffsetX:\(slideOffsetX), dragOffset:\(dragOffset),transcation width:\(value.translation.width)")
    //                let speed = value.translation.width - dragOffset.width
                    let speed = value.translation.width - self.slideOffsetObj.offsetX
                    if(value.startLocation.x < 80 && speed > 2) {
                        self.backFunction()
                    }

                })
        }

    }
}

struct BottomLineViewModifier: ViewModifier {
        
    var isShowBottomLine: Bool = false
    
    init(isShowBottomLine: Bool = false) {
        self.isShowBottomLine = isShowBottomLine
    }
    
    func body(content: Content) -> some View {
        content.overlay(self.isShowBottomLine ? AnyView(Text("").frame(maxWidth:.infinity).frame(height:0.5).background(Color(dynamicNavigationBarBottomLineColor))) : AnyView(EmptyView()),alignment: .bottom)
    }
}

#if DEBUG
struct BackViewWithCustomNavigationDemo: View {
    
    var body: some View {
        BackNavigation(title: "标题", onTapBack: {
            Log.d("退出")
        }) {
            VStack {
                SimpleList().border(Color.gray, width: 2)
                NavigationLink(destination: BackViewWithCustomNavigationDemo()) {
                    Text("push self")
                }
            }

        }

    }
}

struct BackViewWithCustomNavigationDemoWithNav: View {
    var body: some View {
        NavigationView{
                NavigationLink(destination: BackViewWithCustomNavigationDemo()) {
                    Text("Mutiple List")
                }
            .navigationBarTitle("主页", displayMode: .inline)
        }
    }
}

//例子看SettingV.swift
struct CustomNavigationForPreview<Content>: View where Content: View {
    
    let content: Content
    
    init(content: Content) {
        self.content = content
    }
    
    var body: some View {
        NavigationView{
                NavigationLink(destination: content) {
                    Text("跳转")
                }
            .navigationBarTitle("主页", displayMode: .inline)
        }
    }
}


struct BackViewWithCustomNavigation_Previews: PreviewProvider {
    static var previews: some View {
        
            Group {
                BackViewWithCustomNavigationDemoWithNav()

                BackViewWithCustomNavigationDemoWithNav()
                .environment(\.colorScheme, .dark)
                
                BackViewWithCustomNavigationDemoWithNav()
                .environment(\.colorScheme, .dark)
                .previewDevice("iPhone SE (2nd generation)")
               
            }

    }
}
#endif
