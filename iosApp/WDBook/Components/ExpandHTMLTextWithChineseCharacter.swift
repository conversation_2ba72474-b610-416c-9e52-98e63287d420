//
//  ExpandHTMLTextWithChineseCharacter.swift
//  WDBook
//
//  Created by <PERSON> on 2020/11/6.
//  Copyright © 2020 WeDevote Bible. All rights reserved.
//

import Foundation
import UIKit
import SwiftUI
import SnapKit
import WebKit

struct ExpandHTMLTextWithChineseCharacter: UIViewRepresentable {
    
    var text: String?
    var attributedText: NSAttributedString?
    @Binding var desiredHeight: CGFloat
    @Binding var isTruncated:Bool
    var isExpandWhenFirstLoad:Bool
    var compactMaxLineNum:Int
    var fgColor:UIColor
    var bgColor:UIColor
    var font:UIFont
    var horizontalPadding:CGFloat
    
    init(text: String? = nil,attributedText:NSAttributedString? = nil,
         desiredHeight: Binding<CGFloat> = .constant(0.0),
         truncated:Binding<Bool> = .constant(false),
         isExpandWhenFirstLoad:Bool = true,
         compactMaxLineNum:Int = 3,
         fgColor:UIColor = dynamicTitleColor11,
         bgColor:UIColor = dynamicBackgroundColor1,
         font:UIFont = UIFont.regular(size:16),
         horizontalPadding:CGFloat = 23) {
        self.text = text
        _desiredHeight = desiredHeight
        _isTruncated = truncated
        self.attributedText = attributedText
        self.isExpandWhenFirstLoad = isExpandWhenFirstLoad
        self.compactMaxLineNum = compactMaxLineNum
        self.fgColor = fgColor
        self.bgColor = bgColor
        self.font = font
        self.horizontalPadding = horizontalPadding
    }
    
    func makeUIView(context: Context) -> ExpandTextViewUIKitWithChineseCharacter {
        
        let container = ExpandTextViewUIKitWithChineseCharacter(text: text, attributedString: attributedText,isExpandWhenFirstLoad:isExpandWhenFirstLoad,compactMaxLineNum:compactMaxLineNum,fgColor: fgColor,bgColor: bgColor,font: font,horizontalPadding: horizontalPadding)
        container.resetHeightBlock = {(size,truncated) in
            if size == CGSize.zero{
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {  //通过attributedString初始化的时候，必须加上延时，否则高度改变后界面没有改变
                    self.desiredHeight = container.frame.size.height
                    withAnimation {
                        self.isTruncated = truncated
                        container.updateExpandModeStateAfterLayout()
                    }
                }
            }else{
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                    self.desiredHeight = size.height
                    withAnimation {
                        self.isTruncated = truncated
                        container.updateExpandModeStateAfterLayout()
                    }
                }
            }
        }
        return container
    }
    
    func updateUIView(_ uiView: ExpandTextViewUIKitWithChineseCharacter, context: Context) {
        uiView.update(text: self.text, attributedString: self.attributedText)
    }
}


class ExpandTextViewUIKitWithChineseCharacter : UIView{
    
    //用lazy无效果
    var label = UILabel()
    var fullLabel:UILabel?
    
    var resetHeightBlock:((CGSize,Bool)->())? //大小高度，是否truncated
    
    var lastContentHeight:CGFloat?
    var attributeString:NSAttributedString!
    
    var minSize:CGSize?
    var maxSize:CGSize?
    var expandMaxSize:CGSize?
    
    var truncated: Bool = false
    var bottomV:UILabel!
    
    var sourceText:String?
    var sourceAttributedString:NSAttributedString?
    
    var isExpandWhenFirstLoad:Bool = true
    var isExpand:Bool = true{
        didSet{
            bottomV?.isHighlighted = isExpand
        }
    }
    var initTextWithAttr = false
    let COMPACT_MAX_LINE_NUM:Int
    //当前字体下，3行内容的textview的高度。101.666， 102
    var NONE_EXPAND_CONTENT_MAX_HEIGHT:CGFloat{
        if COMPACT_MAX_LINE_NUM == 4{
            return NONE_EXPAND_CONTENT_MAX_HEIGHT_4
        }else{
            return NONE_EXPAND_CONTENT_MAX_HEIGHT_3
        }
    }
    let NONE_EXPAND_CONTENT_MAX_HEIGHT_3:CGFloat = 103
    let NONE_EXPAND_CONTENT_MAX_HEIGHT_4:CGFloat = 130
    
    var fgColor:UIColor
    var bgColor:UIColor
    var font:UIFont
    var horizontalPadding:CGFloat
    
    init(text:String?,attributedString:NSAttributedString?,
         isExpandWhenFirstLoad:Bool = true,
         compactMaxLineNum:Int = 3,
         fgColor:UIColor,
         bgColor:UIColor,
         font:UIFont,
         horizontalPadding:CGFloat) {
        
        self.sourceText = text
        self.sourceAttributedString = attributedString
        self.isExpandWhenFirstLoad = isExpandWhenFirstLoad
        self.COMPACT_MAX_LINE_NUM = compactMaxLineNum
        self.fgColor = fgColor
        self.bgColor = bgColor
        self.font = font
        self.horizontalPadding = horizontalPadding
        
        super.init(frame:CGRect.zero)
        
        DispatchQueue.main.async {[weak self] in
            guard let self = self else{
                return
            }
            
            if attributedString != nil {
                self.initTextWithAttr = true
                self.attributeString = attributedString!.resetFormat(fgColor: fgColor)  //传来的html没有字体颜色，需要客户端设置，特别在处理黑夜模式的情况。
            }else{
                self.initTextWithAttr = false
                if text != nil {
                    self.attributeString = self.formatAttributedString(att: text!.attributeStringFromHTML().resetFormatForDescription(font: font,fgColor: fgColor))
                }
            }
            
            self.initView()
        }
    }
    
    func initView(){
        label.backgroundColor = bgColor
        label.numberOfLines = 4
        label.lineBreakMode = .byTruncatingTail  //html的情况无效
        addSubview(label)
        
        let expandAttributedString = NSMutableAttributedString(attributedString: attributeString)
        expandAttributedString.append("展开".attributeStringFromHTML().resetFormatForDescription(font: font,fgColor: UIColor.clear)) //占位
        label.attributedText = expandAttributedString
        
        truncated = label.maxNumberOfLines(width: UIScreen.main.bounds.width - horizontalPadding * 2,f:self.font) > COMPACT_MAX_LINE_NUM
        //纯文本 + “展开” 超过行数，
        //                    收起： 显示限制行数的"文本...展开"。
        //                    展开： 文本+展开，计算高度， 但是文本只显示文本。 展开显示在右下角。
        
        if truncated {
            let fixedWidth = frame.size.width > 0 ? frame.size.width : UIScreen.main.bounds.width - horizontalPadding * 2
            let s = label.sizeThatFits(CGSize(width: fixedWidth, height: CGFloat.greatestFiniteMagnitude))
            let firstNLinesText = label.truncatedTextForLines(COMPACT_MAX_LINE_NUM,size: s)
            let firstNLinestruncateText =  truncateString(firstNLinesText)
            debugPrint("前四行的内容是：\(firstNLinesText)")
            debugPrint("处理后前四行内容是：\(firstNLinestruncateText)")
            if initTextWithAttr{
                label.attributedText = attributeString!.resetFormat(fgColor: fgColor)
            }else{
                label.attributedText = formatAttributedString(att: firstNLinestruncateText.attributeStringFromHTML().resetFormatForDescription(font: font,fgColor: fgColor))
            }
            
            
            label.snp.makeConstraints { (make) in
                make.top.leading.trailing.bottom.equalToSuperview()
            }
            
            fullLabel = UILabel()
            fullLabel!.backgroundColor = bgColor
            fullLabel!.attributedText = expandAttributedString
            fullLabel!.numberOfLines = 0
            fullLabel!.isHidden = true
            addSubview(fullLabel!)
//            insertSubview(fullLabel!, at: 0)
            fullLabel!.snp.makeConstraints { (make) in
                make.top.leading.trailing.bottom.equalToSuperview()
            }
            
            bottomV = UILabel()
            bottomV?.attributedText = "展开".attributeStringFromHTML().resetFormatForDescription(font: font,fgColor: dynamicTextBlueColor)
            bottomV.isUserInteractionEnabled = true
            addSubview(bottomV)
            bottomV?.sizeToFit()
            bottomV.snp.makeConstraints { (make) in
                make.trailing.bottom.equalToSuperview()
                make.height.equalTo(bottomV!.frame.height)
                make.width.equalTo(bottomV!.frame.width)
            }
            bottomV.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(tapExpandBtn)))
            
            DispatchQueue.main.async {[weak self] in
                if self?.isExpandWhenFirstLoad ?? false{
                    self?.expandMode()
                }else{
                    self?.compactMode()
                }
            }
        } else {
            label.snp.makeConstraints { (make) in
                make.edges.equalToSuperview()
            }
            DispatchQueue.main.async {
                self.compactMode()
            }
        }
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    //当前仅支持html格式。
    func update(text:String?,attributedString:NSAttributedString?) {
    }
    
    func formatAttributedString(att:NSAttributedString) -> NSMutableAttributedString{
        let mutableAttributedString = NSMutableAttributedString(attributedString: att)
        let paragraphStyle = NSMutableParagraphStyle()
        paragraphStyle.alignment = .justified
        paragraphStyle.lineBreakMode = .byWordWrapping//可以换行。 //.byTruncatingTail 显示...
        mutableAttributedString.addAttribute(.paragraphStyle, value: paragraphStyle, range: NSRange(location: 0, length: mutableAttributedString.length))
        return mutableAttributedString
    }
    
    func truncateString(_ text: String) -> String {
        let maxWidth = text.count - "…展开".count
        
        if maxWidth <= 0 {
            return ""
        }
        
        var truncatedText = String(text.prefix(maxWidth))
        truncatedText.append("…")
        return truncatedText
    }
    
    func compactMode(){
        isExpand = false

        if fullLabel != nil {
//            fullLabel!.isHidden = true
            bottomV?.attributedText = "展开".attributeStringFromHTML().resetFormatForDescription(font: font,fgColor: dynamicTextBlueColor)
            
            let fixedWidth = frame.size.width > 0 ? frame.size.width : UIScreen.main.bounds.width - horizontalPadding * 2
            minSize = label.sizeThatFits(CGSize(width: fixedWidth, height: CGFloat.greatestFiniteMagnitude))
            resetHeightBlock?(CGSize(width: minSize!.width, height: minSize!.height), truncated)
            
        }else{
            fullLabel?.isHidden = true
            bottomV?.isHidden = true
        }
    }
    
    func expandMode(){
        isExpand = true
        
        if fullLabel != nil{
            fullLabel?.isHidden = false
            bottomV?.attributedText = "收起".attributeStringFromHTML().resetFormatForDescription(font: font,fgColor: dynamicTextBlueColor)
            
            let fixedWidth = frame.size.width > 0 ? frame.size.width : UIScreen.main.bounds.width - horizontalPadding * 2
            maxSize = fullLabel!.sizeThatFits(CGSize(width: fixedWidth, height: CGFloat.greatestFiniteMagnitude))
            resetHeightBlock?(CGSize(width: maxSize!.width, height: maxSize!.height),truncated)
        }else{
            fullLabel?.isHidden = true
            bottomV?.isHidden = true
        }
    }
    
    func updateExpandModeStateAfterLayout(){
        if truncated{
            if !isExpand{
                if fullLabel != nil {
                    label.isHidden = false
                    fullLabel!.isHidden = true
                }else{
//                                fullLabel?.isHidden = true
//                                bottomV?.isHidden = true
                }
            }else{
                if fullLabel != nil{
                    fullLabel?.isHidden = false
                    label.isHidden = true
                }else{
//                                fullLabel?.isHidden = true
//                                bottomV?.isHidden = true
                }
            }
        }
    }
    
    @objc func tapExpandBtn(){
        if isExpand{
            compactMode()
        }else{
            expandMode()
        }
    }
    
}
