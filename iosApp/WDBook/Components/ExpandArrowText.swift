//
//  ExpandArrowText.swift
//  WDBook
//
//  Created by <PERSON><PERSON> <PERSON><PERSON> on 2023/10/26.
//  Copyright © 2023 WeDevote Bible. All rights reserved.
//


import Foundation
import UIKit
import SwiftUI
import SnapKit
import WebKit

struct ExpandArrowText: View {
    @StateObject var expandTextData:ExpandTextData
    
    var text: String = ""
    var fgColor:UIColor
    var bgColor:UIColor
    var font:UIFont
    var horizontalPadding:CGFloat
    var compactMaxLineNum:Int
    
    init(text: String, fgColor: UIColor, bgColor: UIColor, font: UIFont, horizontalPadding: CGFloat, compactMaxLineNum: Int = 4) {
        self.text = text
        self.fgColor = fgColor
        self.bgColor = bgColor
        self.font = font
        self.horizontalPadding = horizontalPadding
        self.compactMaxLineNum = compactMaxLineNum
        let s = ExpandTextData(text: text,fgColor: fgColor, bgColor: bgColor, font: font, horizontalPadding: horizontalPadding, isExpandWhenFirstLoad: false, compactMaxLineNum: compactMaxLineNum,isEllipsisMode: false)
        _expandTextData = StateObject(wrappedValue: s)
    }
    
    var body: some View{
        VStack(spacing:0){
            Text(expandTextData.showText)
                .font(Font(font))
                .multilineTextAlignment(.leading)
                .frame(maxWidth:.infinity,alignment:.leading)
                .fixedSize(horizontal: false, vertical: true)
                .foregroundColor(Color(fgColor))
                .background(Color(bgColor))
            if expandTextData.truncated{
                Button {
                    expandTextData.switchMode()
                } label: {
                    Image(expandTextData.isExpand ? "arrow_up" : "arrow_down")
                        .frame(height:24)
                }
            }
        }.background(Color(bgColor))
        .onTapGesture {
            if expandTextData.truncated{
                expandTextData.switchMode()
            }
        }
    }
}

#Preview {
    ExpandArrowText(text: "", fgColor: dynamicTitleColor11, bgColor: dynamicBackgroundColor1, font: UIFont.regular(size:16), horizontalPadding: 24)
}
