//
//  DownloadProgressView.swift
//  WDBook
//
//  Created by <PERSON> on 2020/9/9.
//  Copyright © 2020 WeDevote Bible. All rights reserved.
//

import SwiftUI

struct ProgressView: View {
    @Binding var progress: Float
    @State var isShowing = false
    var trackTintColor:UIColor = UIColor.trackTintColor
    var progressTintColor:UIColor = UIColor.primaryColor1
    
    var body: some View {
        GeometryReader { geometry in
            ZStack(alignment: .leading) {
                Rectangle()
                    .foregroundColor(Color(self.trackTintColor))
//                    .opacity(0.3)
                    .frame(width: geometry.size.width, height: geometry.size.height)
                Rectangle()
                    .foregroundColor(Color(self.progressTintColor))
                    .frame(width: self.isShowing ? (geometry.size.width * CGFloat(self.progress)) : 0.0,
                           height: geometry.size.height)
                    .animation(.linear(duration: 0.1))
            }
            .onAppear {
                self.isShowing = true
            }
            .cornerRadius(geometry.size.height / 2.0)
        }
    }
}

//struct DownloadProgressView_Previews: PreviewProvider {
//    static var previews: some View {
//        DownloadProgressView(progress: .constant(25.0))
//            .frame(width: 345.0, height: 8.0)
//    }
//}

//struct DownloadProgressView {
//    <#fields#>
//}

struct DownloadProgressViewDemo: View {
    @State var progress: Float = 0.5
    var body: some View {
        VStack {
            ZStack {
                ProgressView(progress: $progress)
                    
                Text("\(Int(progress * 100))%")
                    .font(Font.medium(size: 12))
                    .foregroundColor(Color.white)
            }.frame(width: 80, height: 32)
            Button(
                action: {
                    self.progress = Float.random(in: 0...1)
                }
            ) {
                Text("Random Progress")
            }
            .padding()
        }
    }
}

#if DEBUG
struct DownloadProgressViewDemo_Previews: PreviewProvider {
    static var previews: some View {
        
        Group {
            DownloadProgressViewDemo()
            DownloadProgressViewDemo().environment(\.colorScheme, .dark)
        }
    }
}
#endif
