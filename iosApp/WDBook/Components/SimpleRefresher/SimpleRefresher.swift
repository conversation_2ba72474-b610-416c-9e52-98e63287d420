//
//  SimpleRefresher.swift
//  WDBook
//
//  Created by <PERSON> on 2020/9/2.
//  Copyright © 2020 WeDevote Bible. All rights reserved.
//

import Foundation
import SwiftUI

struct SimpleRefreshHeader: View {
    @Binding var refreshing:Bool
    let action: () -> Void
    
    public init(refreshing: Binding<Bool>, action: @escaping () -> Void) {
        self.action = action
        self._refreshing = refreshing
    }
    
    var body: some View {
        RefreshHeader(refreshing: $refreshing, action: {
            self.action()
        }) { progress in
            if self.refreshing {
                ActivityIndicator()//Text("正在刷新...") //refreshing...
            } else {
                Text("下拉刷新").font(Font.system(size: 14)) //Text("Pull to refresh")
            }
        }
    }
}


struct SimpleRefreshFooter: View {
    @Binding var refreshing:Bool
    @Binding var noMore:Bool
    let action: () -> Void
    
    public init(refreshing: Binding<Bool>,noMore: Binding<Bool>, action: @escaping () -> Void) {
        self._noMore = noMore
        self._refreshing = refreshing
        self.action = action
    }
    
    var body: some View {
        RefreshFooter(refreshing: $refreshing, action: {
            self.action()
        }) {
            if self.noMore {
                Text("没有更多了").font(Font.system(size: 14)) //Text("No more data !")
            } else {
                ActivityIndicator() //Text("refreshing...")
            }
        }
        .noMore(noMore)
        .preload(offset: 50)
    }
}
