//
//  WDRefreshHeader.swift
//  WDBook
//
//  Created by <PERSON> on 2020/10/29.
//  Copyright © 2020 WeDevote Bible. All rights reserved.
//


import Foundation
import SwiftUI

struct WDRefreshHeader: View {
    @Binding var refreshing:Bool
    let action: () -> Void
    
    public init(refreshing: Binding<Bool>, action: @escaping () -> Void) {
        self.action = action
        self._refreshing = refreshing
    }
    
    var body: some View {
        
        RefreshHeader(refreshing: $refreshing, action: {
            self.action()
        })
        { progress in
            if self.refreshing {
                SimpleRefreshingView().padding()
            } else {
                Text("下拉刷新".localized).font(Font.regular())
                    .foregroundColor(Color(dynamicTextColor9)).frame(height:14).offset(y: -15)
            }
        }
        
    }
}

struct WDRefreshHeader2: View {
    @Binding var refreshing:Bool
    let action: () -> Void
    
    public init(refreshing: Binding<Bool>, action: @escaping () -> Void) {
        self.action = action
        self._refreshing = refreshing
    }
    
    var body: some View {
        
        RefreshHeader(refreshing: $refreshing, action: {
            self.action()
        })
        { progress in
            if self.refreshing {
                SimpleRefreshingView().padding()
            } else {
                Text("下拉刷新".localized).font(Font.regular())
                    .foregroundColor(Color(dynamicTextColor9))
                    .frame(maxHeight:.infinity,alignment: .center)
            }
        }
        
    }
}

struct WDRefreshHeader3: View {
    @Binding var refreshing:Bool
    let action: () -> Void
    
    public init(refreshing: Binding<Bool>, action: @escaping () -> Void) {
        self.action = action
        self._refreshing = refreshing
    }
    
    var body: some View {
        
        RefreshHeader(refreshing: $refreshing, action: {
            self.action()
        })
        { progress in
            if self.refreshing {
                SimpleRefreshingView()
            } else {
                Text("下拉刷新".localized).font(Font.regular())
                    .foregroundColor(Color(dynamicTextColor9))
            }
        }.frame(height:30,alignment: .center)
    }
}

struct WDRefreshFooter: View {
    @Binding var refreshing:Bool
    @Binding var noMore:Bool
    let action: () -> Void
    
    public init(refreshing: Binding<Bool>,noMore: Binding<Bool>, action: @escaping () -> Void) {
        self._noMore = noMore
        self._refreshing = refreshing
        self.action = action
    }
    
    var body: some View {
        RefreshFooter(refreshing: $refreshing, action: {
            self.action()
        })
        {
            if !self.noMore {
                SimpleRefreshingView().padding()
            } else {
                Text("没有更多".localized).font(Font.regular())
                    .foregroundColor(Color(dynamicTextColor9))
                    .padding()
            }
        }
        .noMore(noMore)
    }
}
