//
//  ViewExtension.swift
//  WDBook
//
//  Created by <PERSON><PERSON> <PERSON><PERSON> on 2022/2/22.
//  Copyright © 2022 WeDevote Bible. All rights reserved.
//

import Foundation
import SwiftUI
import shared
import SDWebImageSwiftUI

struct StaticButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
    }
}

public extension View {
    func onSwipe(disable:Binding<Bool> = .constant(false),leading: [Slot] = [], trailing: [Slot] = []) -> some View {
        return self.modifier(SlidableModifier(disable: disable,leading: leading, trailing: trailing))
    }

    func embedInAnyView() -> AnyView {
        return AnyView(self)
    }
}

//iOS13去掉List Separator
public struct ListSeparatorStyleNoneModifier: ViewModifier {
    public func body(content: Content) -> some View {
        content.onAppear {
            //分割线
            UITableView.appearance().separatorStyle = .none
            
            //实现：长按无高亮效果。
            //下面两行，配合.buttonStyle(PlainButtonStyle())
            UITableView.appearance().backgroundColor = UIColor.clear
            UITableViewCell.appearance().selectionStyle = .none
        }.onDisappear {
            UITableView.appearance().separatorStyle = .singleLine
            UITableView.appearance().backgroundColor = UIColor.clear
            UITableViewCell.appearance().selectionStyle = .default
        }
    }
}

extension View {
    public func listSeparatorStyleNone() -> some View {
        modifier(ListSeparatorStyleNoneModifier())
    }
}

struct ViewOffsetKey: PreferenceKey {
    typealias Value = CGFloat
    static var defaultValue = CGFloat.zero
    static func reduce(value: inout Value, nextValue: () -> Value) {
        value += nextValue()
    }
}

struct ViewFrameHeightKey: PreferenceKey {
    typealias Value = CGFloat
    static var defaultValue = CGFloat.zero
    static func reduce(value: inout Value, nextValue: () -> Value) {
        value += nextValue()
    }
}

struct ViewHeightKey: PreferenceKey {
    static var defaultValue: CGFloat { 0 }
    static func reduce(value: inout Value, nextValue: () -> Value) {
        value = value + nextValue()
    }
}

struct CoverImageView: View {
    let product: ProductEntity
    
    var body: some View {
        if product.purchased == 1 {
            ImageManager.getWebImage(url:URL(string: ImageManager.getImageUrl(WDBookAppSDK.shared.getFullImageUrl(url: product.cover))))
                .resizable()
                .placeholder{
                    Image("cover_81*108").resizable().frame(width: 82, height: 110, alignment: .center)
                }
                .transition(.fade(duration: 0.5))
                .scaledToFit()
                .frame(width: 82, height: 110, alignment: .center)
                .overlay(
                    RoundedRectangle(cornerRadius: 0)
                        .stroke(Color("ShelfItemBorderColor"), lineWidth: 0.5)
                )
                .overlay(Image("purchased".localized), alignment: .topTrailing)
        } else {
            ImageManager.getWebImage(url:URL(string: ImageManager.getImageUrl(WDBookAppSDK.shared.getFullImageUrl(url: product.cover))))
                .resizable()
                .placeholder{
                    Image("cover_81*108").resizable().frame(width: 82, height: 110, alignment: .center)
                }
                .transition(.fade(duration: 0.5))
                .scaledToFit()
                .frame(width: 82, height: 110, alignment: .center)
                .overlay(
                    RoundedRectangle(cornerRadius: 0)
                        .stroke(Color("ShelfItemBorderColor"), lineWidth: 0.5)
                )
        }
    }
}

struct ProductCoverView: View {
    let purchased:Int
    let cover:String
    
    var width:CGFloat = 82
    var height:CGFloat = 110
    
    var body: some View {
        if purchased == 1 {
            ImageManager.getWebImage(url:URL(string: ImageManager.getImageUrl(WDBookAppSDK.shared.getFullImageUrl(url: cover))))
                .resizable()
                .placeholder{
                    Image("cover_81*108").resizable().frame(width: width, height: height, alignment: .center)
                }
                .transition(.fade(duration: 0.5))
                .scaledToFit()
                .frame(width: width, height: height, alignment: .center)
                .overlay(
                    RoundedRectangle(cornerRadius: 0)
                        .stroke(Color("ShelfItemBorderColor"), lineWidth: 0.5)
                )
                .overlay(Image("purchased".localized), alignment: .topTrailing)
        } else {
            ImageManager.getWebImage(url:URL(string: ImageManager.getImageUrl(WDBookAppSDK.shared.getFullImageUrl(url: cover))))
                .resizable()
                .placeholder{
                    Image("cover_81*108").resizable().frame(width: width, height: height, alignment: .center)
                        
                }
                .transition(.fade(duration: 0.5))
                .scaledToFit()
                .frame(width: width, height: height, alignment: .center)
                .overlay(
                    RoundedRectangle(cornerRadius: 0)
                        .stroke(Color("ShelfItemBorderColor"), lineWidth: 0.5)
                )
        }
    }
}

struct EditorPictureView: View {
    let picture:String
    
    var width:CGFloat = 48
    var height:CGFloat = 48
    
    var body: some View {
        ImageManager.getWebImage(url:URL(string: ImageManager.getImageUrl(WDBookAppSDK.shared.getFullImageUrl(url: picture))))
            .resizable()
            .placeholder{
                Image("avator_editor").resizable().frame(width: width, height: height, alignment: .center)
                    
            }
            .transition(.fade(duration: 0.5))
            .aspectRatio(contentMode: .fill)
            .frame(width: width, height: height, alignment: .center)
            .cornerRadius(width/2)
    }
}

struct PublisherPictureView: View {
    let picture:String
    
    var width:CGFloat = 48
    var height:CGFloat = 48
    
    var body: some View {
        ImageManager.getWebImage(url:URL(string: ImageManager.getImageUrl(WDBookAppSDK.shared.getFullImageUrl(url: picture))))
            .resizable()
            .placeholder{
                Image("avator_publisher").resizable().frame(width: width, height: height, alignment: .center)
            }
            .transition(.fade(duration: 0.5))
            .scaledToFit()
            .frame(width: width, height: height, alignment: .center)
            .cornerRadius(width/2)
    }
}

#if DEBUG
struct ProductCoverView_Previews: PreviewProvider {
    static var previews: some View {
        VStack{
            ProductCoverView(purchased: 0, cover: "")
            ProductCoverView(purchased: 0, cover: "", width: 40, height: 53)
            EditorPictureView(picture: "")
        }
    }
}
#endif
