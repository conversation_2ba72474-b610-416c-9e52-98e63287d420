//
//  LightWebviewWithNav.swift
//  WDBook
//
//  Created by <PERSON> on 2020/8/31.
//  Copyright © 2020 WeDevote Bible. All rights reserved.
//

import Foundation
import SwiftUI
import WebKit
import wdNetwork

struct LightWebviewWithNav: View {
    @Environment(\.presentationMode) var presentationMode: Binding<PresentationMode>
    @Environment(\.safeAreaInsets3) private var safeAreaInsets
    
    var url:String = ""
    var title:String = ""
    var content:String = ""
    @State var didStart:Bool = false
    
    var btnBack : some View { 
        Button(action: {
            presentationMode.wrappedValue.dismiss()
        }) {
            HStack {
            Image("back_ui")
                .aspectRatio(contentMode: .fit)
                .foregroundColor(Color(btnTintColor))
            }
        }
    }
    
    var contentV: some View {
        ZStack {
            if !url.isEmpty || !content.isEmpty{
                LightWebViewUIKit(url:url,content: content, didStartProvisionalNavigation: {
                    didStart = true
                })
            }
            
            if !didStart{
                ActivityIndicator()
            }
        }.frame(maxWidth:.infinity,maxHeight: .infinity)
    }
    
    var body: some View{
        ZStack(alignment: .top, content: {
            contentV
                .navigationBarHidden(true)
                .padding(.top, (45 + safeAreaInsets.top))
            
            HStack(alignment: .center, spacing: 0, content: {
                btnBack.padding(EdgeInsets(top: 0, leading: 0, bottom: 0, trailing: 20))
                Spacer()
            }).frame(height:45)
                .frame(maxWidth:.infinity)
                .padding(.horizontal, 20)
                .overlay(
                    Text(title)
                    .foregroundColor(Color(dynamicTitleColor2))
                    .font(Font.semibold(size: 18)), alignment: .center)
                .padding(.top, safeAreaInsets.top)
                .background(Color(dynamicBackgroundColor1))
                .modifier(BottomLineViewModifier(isShowBottomLine: true))
        })
        .navigationBarHidden(true)
        .edgesIgnoringSafeArea(.top)
    }
}

#if DEBUG
struct LightWebviewWithNav_Previews: PreviewProvider {
    static var previews: some View {
        Group {
            LightWebviewWithNav(url: "https://google.com", title: "谷歌")
            LightWebviewWithNav(url: "https://google.com", title: "谷歌").environment(\.colorScheme, .dark)
        }
        
    }
}
#endif
