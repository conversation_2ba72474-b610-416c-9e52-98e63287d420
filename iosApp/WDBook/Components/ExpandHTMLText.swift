//
//  ExpandHTMLText.swift
//  WDBook
//
//  Created by <PERSON> on 2020/11/6.
//  Copyright © 2020 WeDevote Bible. All rights reserved.
//

import Foundation
import UIKit
import SwiftUI
import SnapKit
import WebKit

struct ExpandHTMLText: UIViewRepresentable {
    
    var text: String?
    var attributedText: NSAttributedString?
    @Binding var desiredHeight: CGFloat
    @Binding var isTruncated:Bool
    var isExpandWhenFirstLoad:Bool
    var compactMaxLineNum:Int
    var fgColor:UIColor
    var bgColor:UIColor
    var font:UIFont
    var horizontalPadding:CGFloat
    
    init(text: String? = nil,attributedText:NSAttributedString? = nil,
         desiredHeight: Binding<CGFloat> = .constant(0.0),
         truncated:Binding<Bool> = .constant(false),
         isExpandWhenFirstLoad:Bool = true,
         compactMaxLineNum:Int = 3,
         fgColor:UIColor = dynamicTitleColor11,
         bgColor:UIColor = dynamicBackgroundColor1,
         font:UIFont = UIFont.regular(size:16),
         horizontalPadding:CGFloat = 23) {
        self.text = text
        _desiredHeight = desiredHeight
        _isTruncated = truncated
        self.attributedText = attributedText
        self.isExpandWhenFirstLoad = isExpandWhenFirstLoad
        self.compactMaxLineNum = compactMaxLineNum
        self.fgColor = fgColor
        self.bgColor = bgColor
        self.font = font
        self.horizontalPadding = horizontalPadding
    }
    
    func makeUIView(context: Context) -> ExpandTextViewUIKit {
        
        let container = ExpandTextViewUIKit(text: text, attributedString: attributedText,isExpandWhenFirstLoad:isExpandWhenFirstLoad,compactMaxLineNum:compactMaxLineNum,fgColor: fgColor,bgColor: bgColor,font: font,horizontalPadding: horizontalPadding)
        container.resetHeightBlock = {(size,truncated) in
            if size == CGSize.zero{
                //                container.frame.size = container.frame.size
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.0) {
                    self.desiredHeight = container.frame.size.height
                    self.isTruncated = truncated
                }
            }else{
                //                container.frame.size = size
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                    self.desiredHeight = size.height
                    self.isTruncated = truncated
                }
            }
        }
        return container
    }
    
    func updateUIView(_ uiView: ExpandTextViewUIKit, context: Context) {
        uiView.update(text: self.text, attributedString: self.attributedText)
    }
}


class ExpandTextViewUIKit : UIView{
    
    //用lazy无效果
    var label = UILabel()
    var textView:UITextView?
    
    var expandImageV:UIImageView?
    var resetHeightBlock:((CGSize,Bool)->())? //大小高度，是否truncated
    
    var lastContentHeight:CGFloat?
    var attributeString:NSAttributedString?
    
    var minSize:CGSize?
    var maxSize:CGSize?
    
    let BOTTOM_HEIGHT:CGFloat = 23
    var truncated: Bool = false
    var bottomV:UIView!
    var isExpandWhenFirstLoad:Bool = true
    var isExpand:Bool = true{
        didSet{
            expandImageV?.isHighlighted = isExpand
        }
    }
    let COMPACT_MAX_LINE_NUM:Int
    //当前字体下，3行内容的textview的高度。101.666， 102
    var NONE_EXPAND_CONTENT_MAX_HEIGHT:CGFloat{
        if COMPACT_MAX_LINE_NUM == 4{
            return NONE_EXPAND_CONTENT_MAX_HEIGHT_4
        }else{
            return NONE_EXPAND_CONTENT_MAX_HEIGHT_3
        }
    }
    let NONE_EXPAND_CONTENT_MAX_HEIGHT_3:CGFloat = 103
    let NONE_EXPAND_CONTENT_MAX_HEIGHT_4:CGFloat = 130
    
    var fgColor:UIColor
    var bgColor:UIColor
    var font:UIFont
    var horizontalPadding:CGFloat
    
    init(text:String?,attributedString:NSAttributedString?,
         isExpandWhenFirstLoad:Bool = true,
         compactMaxLineNum:Int = 3,
         fgColor:UIColor,
         bgColor:UIColor,
         font:UIFont,
         horizontalPadding:CGFloat) {
        
        self.isExpandWhenFirstLoad = isExpandWhenFirstLoad
        self.COMPACT_MAX_LINE_NUM = compactMaxLineNum
        self.fgColor = fgColor
        self.bgColor = bgColor
        self.font = font
        self.horizontalPadding = horizontalPadding
        
        super.init(frame:CGRect.zero)
        
        DispatchQueue.main.async {[weak self] in
            guard let self = self else{
                return
            }
            
            if attributedString != nil {
                self.attributeString = attributedString!.resetFormatForDescription()
            }else{
                if text != nil {
                    let mutableAttributedString = NSMutableAttributedString(attributedString: text!.attributeStringFromHTML().resetFormatForDescription(font: font,fgColor: fgColor))
//                    let attributes: [NSAttributedString.Key: Any] = [
//                        .foregroundColor: UIColor.red,
//                        .backgroundColor: UIColor.yellow,
//                        .font: UIFont.systemFont(ofSize: 18.0)
//                    ]
//                    mutableAttributedString.addAttributes(attributes, range: NSRange(location: 0, length: mutableAttributedString.length))

                    self.attributeString = mutableAttributedString
                }
            }
            
            self.initView()
        }
    }
    
    func initView(){
        label.backgroundColor = bgColor
        
        label.attributedText = self.attributeString
        label.numberOfLines = COMPACT_MAX_LINE_NUM
        //        label.lineBreakMode = .byTruncatingTail  //html的情况无效
        addSubview(label)
        
        truncated = label.maxNumberOfLines(width: UIScreen.main.bounds.width - horizontalPadding * 2,f:self.font) > label.numberOfLines
        if truncated {
            expandImageV = UIImageView(image: UIImage(named: "arrow_down"), highlightedImage: UIImage(named: "arrow_up"))
            addSubview(expandImageV!)
            
            bottomV = UIView()
            addSubview(bottomV)
            bottomV.snp.makeConstraints { (make) in
                make.leading.trailing.bottom.equalToSuperview()
                make.height.equalTo(BOTTOM_HEIGHT)
            }
            expandImageV!.snp.makeConstraints { (make) in
                make.center.equalTo(bottomV)
            }
            
            label.snp.makeConstraints { (make) in
                make.top.leading.trailing.equalToSuperview()
                make.bottom.equalTo(bottomV.snp.top)
            }
            
            textView = UITextView()
            textView?.backgroundColor = bgColor
            textView?.textColor = fgColor //会被attriuteString替代
            textView?.font = font //会被attriuteString替代
            
            textView!.textContainerInset = UIEdgeInsets.zero
            textView!.textContainer.lineFragmentPadding = 0
            textView!.isUserInteractionEnabled = false
            textView!.isHidden = true
            textView!.attributedText = self.attributeString
            addSubview(textView!)
            //        textView.textContainer.maximumNumberOfLines = 3 //不如UILabel灵敏，会出现白板。
            //        textView.textContainer.lineBreakMode = .byTruncatingTail
            
            addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(tapExpandBtn)))
            
            textView!.snp.makeConstraints { (make) in
                make.top.leading.trailing.equalToSuperview()
                make.bottom.equalTo(bottomV.snp.top)
            }
            
            DispatchQueue.main.async {[weak self] in
                if self?.isExpandWhenFirstLoad ?? false{
                    self?.expandMode()
                }else{
                    self?.compactMode()
                }
            }
        } else {
            label.snp.makeConstraints { (make) in
                make.edges.equalToSuperview()
            }
            DispatchQueue.main.async {
                self.compactMode()
            }
        }
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    //当前仅支持html格式。
    func update(text:String?,attributedString:NSAttributedString?) {
    }
    
    func compactMode(){
        isExpand = false
        let fixedWidth = self.frame.size.width > 0 ? self.frame.size.width : UIScreen.main.bounds.width - horizontalPadding * 2
        minSize = self.label.sizeThatFits(CGSize(width: fixedWidth, height: CGFloat.greatestFiniteMagnitude))
        
        if self.textView != nil {
            self.textView?.isHidden = true
            
            maxSize = self.textView!.sizeThatFits(CGSize(width: fixedWidth, height: CGFloat.greatestFiniteMagnitude))
            expandImageV?.isHidden = maxSize!.height <= NONE_EXPAND_CONTENT_MAX_HEIGHT
            
            let actualHeight = minSize!.height + (truncated ? BOTTOM_HEIGHT : 0)
            self.resetHeightBlock?(CGSize(width: minSize!.width, height: actualHeight), truncated)
            
        }else{
            self.textView?.isHidden = true
            expandImageV?.isHidden = true
            
            maxSize = label.sizeThatFits(CGSize(width: fixedWidth, height: CGFloat.greatestFiniteMagnitude))
            expandImageV?.isHidden = maxSize!.height <= NONE_EXPAND_CONTENT_MAX_HEIGHT
            
            let actualHeight = minSize!.height + (truncated ? BOTTOM_HEIGHT : 0)
            self.resetHeightBlock?(CGSize(width: minSize!.width, height: actualHeight),truncated)
        }
    }
    
    func expandMode(){
        isExpand = true
        let fixedWidth = self.frame.size.width > 0 ? self.frame.size.width : UIScreen.main.bounds.width - horizontalPadding * 2
        maxSize = self.textView!.sizeThatFits(CGSize(width: fixedWidth, height: CGFloat.greatestFiniteMagnitude))

        if self.textView != nil{
            self.textView?.isHidden = false
            expandImageV?.isHidden = maxSize!.height <= NONE_EXPAND_CONTENT_MAX_HEIGHT
            
            let actualHeight = maxSize!.height + (truncated ? BOTTOM_HEIGHT : 0)
            self.resetHeightBlock?(CGSize(width: maxSize!.width, height: actualHeight),truncated)
        }else{
            self.textView?.isHidden = true
            expandImageV?.isHidden = true
        }
    }
    
    @objc func tapExpandBtn(){
        guard expandImageV?.isHidden == false else {
            return
        }
        
        if isExpand{
            compactMode()
        }else{
            expandMode()
        }
    }
    
}

extension String{
    func maxNumberOfLines(width:CGFloat,font:UIFont) -> Int{
        let maxSize = CGSize(width: width, height: CGFloat(MAXFLOAT))
        let text = self as NSString
        let textHeight = text.boundingRect(with: maxSize, options: .usesLineFragmentOrigin, attributes: [.font: font], context: nil).height
        let lineHeight = font.lineHeight
        return Int(ceil(textHeight / lineHeight))
    }
}

extension UILabel {
    var numberOfVisibleLines: Int {
        let maxSize = CGSize(width: frame.size.width, height: CGFloat(MAXFLOAT))
        let textHeight = sizeThatFits(maxSize).height
        let lineHeight = font.lineHeight
        return Int(ceil(textHeight / lineHeight))
    }
    
    var maxNumberOfLines: Int {
        let maxSize = CGSize(width: frame.size.width, height: CGFloat(MAXFLOAT))
        let text = (self.text ?? "") as NSString
        let textHeight = text.boundingRect(with: maxSize, options: .usesLineFragmentOrigin, attributes: [.font: font ?? UIFont()], context: nil).height
        let lineHeight = font.lineHeight
        return Int(ceil(textHeight / lineHeight))
    }
    
    func maxNumberOfLines(width:CGFloat,f:UIFont? = nil) -> Int{
        let maxSize = CGSize(width: width, height: CGFloat(MAXFLOAT))
        let text = (self.text ?? "") as NSString
        let textHeight = text.boundingRect(with: maxSize, options: .usesLineFragmentOrigin, attributes: [.font: f ?? font ?? UIFont()], context: nil).height
        let lineHeight = font.lineHeight
        return Int(ceil(textHeight / lineHeight))
    }
    
    func maxNumberOfLines(width:CGFloat,f:UIFont? = nil,testText:String?) -> Int{
        let maxSize = CGSize(width: width, height: CGFloat(MAXFLOAT))
        let text = (testText ?? "") as NSString
        let textHeight = text.boundingRect(with: maxSize, options: .usesLineFragmentOrigin, attributes: [.font: f ?? font ?? UIFont()], context: nil).height
        let lineHeight = font.lineHeight
        return Int(ceil(textHeight / lineHeight))
    }
    
    func truncatedTextForLines(_ numberOfLines: Int,size:CGSize) -> String {
        if let attributedText = self.attributedText {
            return attributedText.truncatedAttributedTextForLines(numberOfLines:numberOfLines,size: size,lineBreakMode: lineBreakMode)
        } else if let text = self.text {
            return NSAttributedString(string: text).truncatedAttributedTextForLines(numberOfLines:numberOfLines,size: size,lineBreakMode: lineBreakMode)
        } else {
            return ""
        }
    }
    

}

extension UITextView {
    func sizeTo(width:CGFloat) -> CGSize{
        let maxSize = CGSize(width: width, height: CGFloat(MAXFLOAT))
        return (text ?? "").boundingRect(with: maxSize, options: .usesLineFragmentOrigin, attributes: [.font: font ?? UIFont()], context: nil).size
    }
    
    func getTextForVisibleLines(lineCount: Int,minSize:CGSize) -> String {
        guard let text = self.text else {
            return ""
        }
        
        let layoutManager = self.layoutManager
        let textContainer = self.textContainer
        let textStorage = NSTextStorage(string: text)
        
        layoutManager.addTextContainer(textContainer)
        textStorage.addLayoutManager(layoutManager)
        
        textContainer.lineFragmentPadding = 0.0
        textContainer.lineBreakMode = self.textContainer.lineBreakMode
        
//        let visibleRect = self.bounds //总区域，用来判断是否和现实区域相交。不再使用
        let visibleRect = text.boundingRect(with: minSize, options: .usesLineFragmentOrigin, attributes: [.font: font ?? UIFont()], context: nil)
//        let visibleRect = CGRect(origin: bounds.origin, size: minSize)
        debugPrint("前四行的内容是,debug, minSize:\(minSize),visibleRect:\(visibleRect),bounds:\(bounds)")
        var lines: [String] = []
        var lineIndex = 0
        var lastLineRange = NSMakeRange(0, 0)
        
        //每一行可能有多项。通过判断第二个rect来判断是否在行中
        layoutManager.enumerateLineFragments(forGlyphRange: NSMakeRange(0, layoutManager.numberOfGlyphs)) { (rect, usedRect, _, gRange, _) in
            if 
//                lineIndex < lineCount &&
                visibleRect.intersects(usedRect)
            {
                let lineRange = layoutManager.characterRange(forGlyphRange: gRange, actualGlyphRange: nil)
                let lineText = (text as NSString).substring(with: lineRange)
                lines.append(lineText)
//                lineIndex += 1
                debugPrint("前四行的内容是,rect:\(rect),usedRect:\(usedRect),glyphsRange:\(gRange),lineRange:\(lineRange),text:[\(lineText)]")
            }
        }
        
        let visibleLinesText = lines.joined(separator: "")
        return visibleLinesText
    }
}

extension NSAttributedString {
    
    func truncatedAttributedTextForLines(numberOfLines: Int,size:CGSize,lineBreakMode:NSLineBreakMode) -> String {
        let textStorage = NSTextStorage(attributedString: self)
        let layoutManager = NSLayoutManager()
        textStorage.addLayoutManager(layoutManager)
        
//        self.bounds.size //bounds还是最初的区域，只一行。使用目标区域size
        let textContainer = NSTextContainer(size: size)
        textContainer.lineFragmentPadding = 0
        textContainer.maximumNumberOfLines = numberOfLines
        textContainer.lineBreakMode = lineBreakMode
        layoutManager.addTextContainer(textContainer)
        
        let glyphRange = layoutManager.glyphRange(for: textContainer)
        let truncatedText = attributedSubstring(from: glyphRange).string
        
        return truncatedText
    }
    
    func addFormatForDescription(font:UIFont = UIFont.regular(size: 16),fgColor:UIColor = dynamicTextColor10) -> NSAttributedString{
        guard let output = self.mutableCopy() as? NSMutableAttributedString else {
            return self
        }

        output.beginEditing()
        output.addAttribute(NSAttributedString.Key.font, value: font, range: NSRange(location: 0, length: string.count))
        output.addAttribute(NSAttributedString.Key.foregroundColor, value: fgColor, range: NSRange(location: 0, length: string.count))
        output.endEditing()

        return output
    }
    
    func resetFormatForDescription(font:UIFont = UIFont.regular(size: 16),fgColor:UIColor = dynamicTextColor10) -> NSAttributedString{
        guard let output = self.mutableCopy() as? NSMutableAttributedString else {
            return self
        }

        output.beginEditing()
        
        //修改字体大小
        output.enumerateAttribute(NSAttributedString.Key.font,
                                  in: NSRange(location: 0, length: self.length),
                                  options: []) { (value, range, stop) -> Void in
            guard let oldFont = value as? UIFont else {
                return
            }
            let newFont = font
            output.removeAttribute(NSAttributedString.Key.font, range: range)
            output.addAttribute(NSAttributedString.Key.font, value: newFont, range: range)
        }
        
        output.enumerateAttribute(NSAttributedString.Key.foregroundColor,
                                  in: NSRange(location: 0, length: self.length),
                                  options: []) { (value, range, stop) -> Void in
            
            let newColor = fgColor
            output.removeAttribute(NSAttributedString.Key.foregroundColor, range: range)
            output.addAttribute(NSAttributedString.Key.foregroundColor, value: newColor, range: range)
        }
        
        output.endEditing()

        return output
    }
    
    func resetFormat(fgColor:UIColor = dynamicTextColor10) -> NSAttributedString{
        guard let output = self.mutableCopy() as? NSMutableAttributedString else {
            return self
        }

        output.beginEditing()
        
        output.enumerateAttribute(NSAttributedString.Key.foregroundColor,
                                  in: NSRange(location: 0, length: self.length),
                                  options: []) { (value, range, stop) -> Void in
            
            let newColor = fgColor
            output.removeAttribute(NSAttributedString.Key.foregroundColor, range: range)
            output.addAttribute(NSAttributedString.Key.foregroundColor, value: newColor, range: range)
        }
        
        output.endEditing()

        return output
    }
}
