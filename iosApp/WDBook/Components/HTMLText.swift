//
//  ExpandHTMLTextTwo.swift
//  WDBook
//
//  Created by QK on 2022/2/7.
//  Copyright © 2022 WeDevote Bible. All rights reserved.
//

import Foundation
import UIKit
import SwiftUI
import SnapKit
import WebKit

struct HTMLText: UIViewRepresentable {
    
    var text: String?
    var attributedText: NSAttributedString?
    var fontSize: Int
    var isBold: Bool
    var limitLine: Int
    
    init(text: String? = nil,attributedText:NSAttributedString? = nil,fontSize: Int, isBold:Bool, limitLine: Int) {
        self.text = text
        self.attributedText = attributedText
        self.fontSize = fontSize
        self.isBold = isBold
        self.limitLine = limitLine
    }
    
    func makeUIView(context: Context) -> HtmlUILabel {
        let container = HtmlUILabel(text: text, attributedString: attributedText, fontSize: fontSize, isBold:isBold, limitLine: limitLine)
        return container
    }
    
    func updateUIView(_ uiView: HtmlUILabel, context: Context) {
        uiView.update(text: self.text, attributedString: self.attributedText)
    }
}


class HtmlUILabel : UIView{
    
    //用lazy无效果
    var label = UILabel()
    
    var resetHeightBlock:((CGSize)->())?
    
    var lastContentHeight:CGFloat?
    var attributeString:NSAttributedString?
    
    var minSize:CGSize?
    var maxSize:CGSize?
    
    let BOTTOM_HEIGHT:CGFloat = 23
    var bottomV:UIView!
    init(text:String?,attributedString:NSAttributedString?,fontSize:Int, isBold:Bool,limitLine:Int) {
        super.init(frame:CGRect.zero)
        
        DispatchQueue.main.async {[weak self] in
            guard let self = self else{
                return
            }
            
            if attributedString != nil {
                self.attributeString = attributedString!.resetFormatForDescriptionTwo(fontSize: fontSize, isBold: isBold)
            }else{
                if text != nil {
                    self.attributeString = text!.attributeStringFromHTML().resetFormatForDescriptionTwo(fontSize: fontSize, isBold: isBold)
                }
            }
            
            self.initView(limitLine: limitLine)
        }
    }
    
    func initView(limitLine:Int){
        label.attributedText = self.attributeString
        label.numberOfLines = limitLine
        label.lineBreakMode = .byTruncatingTail  //html的情况无效
        addSubview(label)
        
        label.snp.makeConstraints { (make) in
            make.top.leading.trailing.equalToSuperview()
        }
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    //当前仅支持html格式。
    func update(text:String?,attributedString:NSAttributedString?) {
    }
}

extension NSAttributedString {
    
    func resetFormatForDescriptionTwo(fontSize:Int, isBold:Bool) -> NSAttributedString{
        guard let output = self.mutableCopy() as? NSMutableAttributedString else {
            return self
        }
        output.beginEditing()
        //修改字体大小
        output.enumerateAttribute(NSAttributedString.Key.font,
                                  in: NSRange(location: 0, length: self.length),
                                  options: []) { (value, range, stop) -> Void in
            guard let oldFont = value as? UIFont else {
                return
            }
            var newFont = UIFont.regular(size: CGFloat(fontSize))
            if (isBold){
                newFont = UIFont.boldSystemFont(ofSize: CGFloat(fontSize))
            }
            output.removeAttribute(NSAttributedString.Key.font, range: range)
            output.addAttribute(NSAttributedString.Key.font, value: newFont, range: range)
        }
        output.endEditing()
        return output
    }
}

