//
//  PageView.swift
//  WDBook
//
//  Created by <PERSON> on 2020/8/31.
//  Copyright © 2020 WeDevote Bible. All rights reserved.
//
//  Created by <PERSON><PERSON> on 12/5/19.
// TODO：删除
import SwiftUI

class IsDraggingObj{
//    var lastValue: DragGesture.Value?
    var timer:Timer?
    var isDragging:Bool = false
    func start(_ block: @escaping ()->()){
        timer = Timer(timeInterval: 5, repeats: true, block: {[weak self] t in
            guard let self = self else{ return }
            if !self.isDragging{
                block()
            }
        })
        RunLoop.main.add(timer!, forMode: .default)
    }
    func stop(){
        timer?.invalidate()
        timer = nil
    }
}

struct PagerViewWithSwiftUI<Content: View>: View {
    let pageCount: Int
    @Binding var currentIndex: Int
    var showPageControl = false
    let content: Content
    
    @GestureState private var translation: CGFloat = 0

    var isDraggingObj = IsDraggingObj()
    
    init(pageCount: Int, currentIndex: Binding<Int>,enableTimer:Bool = false, showPageCountrol:Bool = false, @ViewBuilder content: () -> Content) {
        self.pageCount = pageCount
        _currentIndex = currentIndex
        self.showPageControl = showPageCountrol
        self.content = content()
        
        if enableTimer{
            if pageCount > 1 {
                self.isDraggingObj.start {
    //                Log.d("滑动")
                    if currentIndex.wrappedValue >= pageCount - 1{
                        currentIndex.wrappedValue = 0
                    }else{
                        currentIndex.wrappedValue += 1
                    }
                }
            }else{
                self.isDraggingObj.stop()
            }
        }
    }

    var body: some View {
        GeometryReader { geometry in
            ZStack(alignment: .bottom) {
                HStack(spacing: 0) {
                    self.content.frame(width: geometry.size.width)
                }
                .frame(width: geometry.size.width, alignment: .leading)
                .offset(x: -CGFloat(self.currentIndex) * geometry.size.width)
                .offset(x: self.translation)
                .animation(.interactiveSpring())
                .gesture(
                    
                    DragGesture().updating(self.$translation) { value, state, _ in
//                        Log.d("更新：value:\(value),state:\(state),translation:\(translation)")
                        if self.pageCount > 1{
//                            self.isDraggingObj.lastValue = value
                            state = value.translation.width
                            self.isDraggingObj.isDragging = true
                        }
                        
                    }.onEnded { value in
//                        Log.d("结束：value:\(value)")

//                        Log.d("value:\(value),translation:\(translation),offset:\(offset)")
                        if self.pageCount > 1{
                            
//                            if let lastValue = self.isDraggingObj.lastValue {
//                                let timeDiff = value.time.timeIntervalSince(lastValue.time)
//                                var speed:CGFloat = CGFloat(value.translation.width - lastValue.translation.width) / CGFloat(timeDiff)

                                var speed = value.translation.width
//                                Log.d("timeDiff:\(timeDiff), speed:\(speed)")
                                
                                if(speed > 50) {
                                    var newIndex = self.currentIndex - 1
                                    if newIndex < 0 { newIndex = 0}
                                    self.currentIndex = newIndex
                                    self.isDraggingObj.isDragging = false
                                    
                                }else if (speed < -50){
                                    var newIndex = self.currentIndex + 1
                                    if newIndex > self.pageCount - 1 { newIndex = self.pageCount - 1}
                                    self.currentIndex = newIndex
                                    self.isDraggingObj.isDragging = false
                                }
//                                else{
//                                    let offset = value.translation.width / geometry.size.width
//                                    let newIndex = (CGFloat(self.currentIndex) - offset).rounded()
//                                    self.currentIndex = min(max(Int(newIndex), 0), self.pageCount - 1)
//                                    self.isDraggingObj.isDragging = false
//                                }
//                           }
                        }
                    }
                )
                
                if showPageControl{
                    if self.pageCount > 1{
                        HStack(alignment: .center, spacing: CirclePoint.radius) {
                            ForEach(0 ..< self.pageCount) { (i) in
                                CirclePoint(selected:self.$currentIndex,index: i)
                            }
                            
                        }.frame(width:(CGFloat(self.pageCount * 2 - 1) * CirclePoint.radius), height: CirclePoint.radius, alignment: .center)
                        .offset(x: 0, y: -10)
    //                    .alignmentGuide(.bottom) { (d)  in
    //                        CirclePoint.radius + 10
    //                    }
                    }
                }
            }
        }
    }
}

struct CirclePoint: View {
    static let radius:CGFloat = 10.0
    @Binding var selected:Int
    var index:Int = 0
    var body: some View {
        Circle()
            .background(Color.clear)
            .foregroundColor(self.selected == index ? Color(UIColor.primaryColor1): Color.white)
            .frame(width: CirclePoint.radius, height: CirclePoint.radius)
    }
}

#if DEBUG
struct PageViewDemo: View {
    @State private var currentPage = 0
    var pageCount:Int = 3
    var body: some View {
        PagerViewWithSwiftUI(pageCount: pageCount, currentIndex: $currentPage,enableTimer: true,showPageCountrol:true) {
            ForEach(0 ..< pageCount) { (i) in
                Text("\(i)")
                    .frame( maxWidth: .infinity,maxHeight: .infinity, alignment: .center)
                    .padding()
                    .background(Color.green)
                    .padding(.horizontal, 10)
            }
        }.frame( maxWidth: .infinity, alignment: .center)
        .frame(height: 200, alignment: .center)
        .onTapGesture {
            Log.d("haha\(self.currentPage)")
        }

    }
}

struct PageView_Previews: PreviewProvider {
    static var previews: some View {
        PageViewDemo()
    }
}
#endif
