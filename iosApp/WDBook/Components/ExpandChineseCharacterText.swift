//
//  ExpandChineseCharacterText.swift
//  WDBook
//
//  Created by <PERSON><PERSON> <PERSON><PERSON> on 2023/10/25.
//  Copyright © 2023 WeDevote Bible. All rights reserved.
//

import Foundation
import UIKit
import SwiftUI
import SnapKit
import WebKit

struct ExpandChineseCharacterText: View {
    @StateObject var expandTextData:ExpandTextData
    
    var text: String = ""
    var fgColor:UIColor
    var bgColor:UIColor
    var font:UIFont
    var horizontalPadding:CGFloat
    var compactMaxLineNum:Int
    
    init(text: String, fgColor: UIColor, bgColor: UIColor, font: UIFont, horizontalPadding: CGFloat, compactMaxLineNum: Int = 4) {
        self.text = text
        self.fgColor = fgColor
        self.bgColor = bgColor
        self.font = font
        self.horizontalPadding = horizontalPadding
        self.compactMaxLineNum = compactMaxLineNum
        let s = ExpandTextData(text: text,fgColor: fgColor, bgColor: bgColor, font: font, horizontalPadding: horizontalPadding, isExpandWhenFirstLoad: false, compactMaxLineNum: compactMaxLineNum, isEllipsisMode: true)
        _expandTextData = StateObject(wrappedValue: s)
    }
    
    var body: some View{
        ZStack{
            Text(expandTextData.showText)
                .font(Font(font))
                .multilineTextAlignment(.leading)
                .lineLimit(nil)
                .frame(maxWidth:.infinity,alignment:.leading)
                .foregroundColor(Color(fgColor))
                .background(Color(bgColor))
                .overlay(
                    Group{
                        if expandTextData.truncated{
                            Text(expandTextData.isExpand ? "收起" : "展开").font(Font(font)).foregroundColor(Color(dynamicTextBlueColor))
                                .padding(.trailing,2)
                                .onTapGesture {
                                    expandTextData.switchMode()
                                }
                        }else{
                            EmptyView()
                        }
                    }
                    , alignment: .bottomTrailing)
        }
    }
}

class ExpandTextData:ObservableObject{
    var text:String = ""
    @Published var showText:String = ""
    @Published var truncated: Bool = false //是否超过行数需要有折叠功能
    @Published var isExpand:Bool = false //当前是否折叠
    
    var data:ExpandTextDataUIKit
    
    init(text: String,fgColor: UIColor, bgColor: UIColor, font: UIFont, horizontalPadding: CGFloat, isExpandWhenFirstLoad: Bool, compactMaxLineNum: Int,isEllipsisMode:Bool) {
        data = ExpandTextDataUIKit(text: text, compactMaxLineNum: compactMaxLineNum,isEllipsisMode: isEllipsisMode, fgColor: fgColor, bgColor: bgColor, font: font, horizontalPadding: horizontalPadding)
        data.resetHeightBlock = { [weak self] (truncated,expand,txt) in
            debugPrint("ExpandTextTest:收到的内容是:\(txt)")
            self?.truncated = truncated
            self?.isExpand = expand
            self?.showText = txt
        }
    }
    
    func switchMode(){
        if isExpand{
            data.compactMode()
        }else{
            data.expandMode()
        }
    }
}


class ExpandTextDataUIKit : UIView{
    
    var resetHeightBlock:((Bool,Bool,String)->())? //是否truncated,是否expand，文本
    var isEllipsisMode:Bool = true
    var label = UILabel()
    
    var attributeString:NSAttributedString! //不需要折叠组件的时候，显示的内容。
    var compactAttributedString:NSAttributedString?
    var expandAttributedString:NSAttributedString? //需要折叠组件的时候，收起的时候，显示的内容。
    
    var truncated: Bool = false
    var sourceText:String
    
    var COMPACT_MAX_LINE_NUM:Int
    //当前字体下，3行内容的textview的高度。101.666， 102
    var NONE_EXPAND_CONTENT_MAX_HEIGHT:CGFloat{
        if COMPACT_MAX_LINE_NUM == 4{
            return NONE_EXPAND_CONTENT_MAX_HEIGHT_4
        }else{
            return NONE_EXPAND_CONTENT_MAX_HEIGHT_3
        }
    }
    let NONE_EXPAND_CONTENT_MAX_HEIGHT_3:CGFloat = 103
    let NONE_EXPAND_CONTENT_MAX_HEIGHT_4:CGFloat = 130
    
    var fgColor:UIColor
    var bgColor:UIColor
    var font:UIFont
    var horizontalPadding:CGFloat
    
    init(text:String = "",
         showText:Binding<String> = .constant(""),
         compactMaxLineNum:Int = 4,
         isEllipsisMode:Bool = true,
         fgColor:UIColor = dynamicTitleColor11,
         bgColor:UIColor = dynamicBackgroundColor1,
         font:UIFont = UIFont.regular(),
         horizontalPadding:CGFloat = 24) {
        
        self.sourceText = text
        self.COMPACT_MAX_LINE_NUM = compactMaxLineNum
        self.isEllipsisMode = isEllipsisMode
        self.fgColor = fgColor
        self.bgColor = bgColor
        self.font = font
        self.horizontalPadding = horizontalPadding
        
        super.init(frame:CGRect.zero)
        
        DispatchQueue.main.async {[weak self] in
            guard let self = self else{ return }
            self.attributeString = self.formatAttributedString(att: NSAttributedString(string: sourceText).addFormatForDescription(font: font,fgColor: fgColor))
            self.initView()
        }
    }
    
    func initView(){
        label.backgroundColor = bgColor
        label.numberOfLines = COMPACT_MAX_LINE_NUM
        label.lineBreakMode = .byTruncatingTail  //html的情况无效
        addSubview(label)
        
        let att = NSMutableAttributedString(attributedString: attributeString)
//        att.append("展开".attributeStringFromHTML().resetFormatForDescription(font: font,fgColor: UIColor.clear)) //占位
        att.append("    ".attributeStringFromHTML().resetFormatForDescription(font: font,fgColor: UIColor.clear)) //占位
        expandAttributedString = att
        
        truncated = expandAttributedString!.string.maxNumberOfLines(width: UIScreen.main.bounds.width - horizontalPadding * 2,font:self.font) > COMPACT_MAX_LINE_NUM
        //纯文本 + “展开” 超过行数，
        //                    收起： 显示限制行数的"文本...展开"。
        //                    展开： 文本+展开，计算高度， 但是文本只显示文本。 展开显示在右下角。
        
        if truncated {
            let fixedWidth = frame.size.width > 0 ? frame.size.width : UIScreen.main.bounds.width - horizontalPadding * 2
            label.attributedText = expandAttributedString
            let s = label.sizeThatFits(CGSize(width: fixedWidth, height: CGFloat.greatestFiniteMagnitude))
            let firstNLinesText = label.truncatedTextForLines(COMPACT_MAX_LINE_NUM,size: s)
            let firstNLinestruncateText =  truncateString(firstNLinesText)
            debugPrint("ExpandTextTest:所有内容是：\(attributeString.string)")
            debugPrint("ExpandTextTest:前四行的内容是：\(firstNLinesText)")
            debugPrint("ExpandTextTest:处理后前四行内容是：\(firstNLinestruncateText)")
            if isEllipsisMode{
                compactAttributedString = formatAttributedString(att: NSAttributedString(string: firstNLinestruncateText).addFormatForDescription(font: font,fgColor: fgColor))
            }else{
                compactAttributedString = NSAttributedString(string: firstNLinesText).addFormatForDescription(font: font,fgColor: fgColor)
            }
            
            label.attributedText = compactAttributedString
            
            label.snp.makeConstraints { (make) in
                make.edges.equalToSuperview()
            }
            
            compactMode()
            
        } else {
            label.attributedText = attributeString
            label.snp.makeConstraints { (make) in
                make.edges.equalToSuperview()
            }
            normalMode()
        }
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func formatAttributedString(att:NSAttributedString) -> NSMutableAttributedString{
        let mutableAttributedString = NSMutableAttributedString(attributedString: att)
        let paragraphStyle = NSMutableParagraphStyle()
        paragraphStyle.alignment = .justified
        paragraphStyle.lineBreakMode = .byWordWrapping//可以换行。 //.byTruncatingTail 显示...
        mutableAttributedString.addAttribute(.paragraphStyle, value: paragraphStyle, range: NSRange(location: 0, length: mutableAttributedString.length))
        return mutableAttributedString
    }
    
    func truncateString(_ text: String) -> String {
        let maxWidth = text.count - "…展开".count
        
        if maxWidth <= 0 {
            return ""
        }
        
        var truncatedText = String(text.prefix(maxWidth))
        truncatedText.append("…")
        return truncatedText
    }
    
    func normalMode(){
        resetHeightBlock?(truncated,false, attributeString.string)
    }
    
    func compactMode(){
        resetHeightBlock?(truncated,false, compactAttributedString?.string ?? "")
    }
    
    func expandMode(){
        resetHeightBlock?(truncated,true, expandAttributedString?.string ?? "")
    }
}


#Preview {
    ExpandChineseCharacterText(text: "", fgColor: dynamicTitleColor11, bgColor: dynamicBackgroundColor1, font: UIFont.regular(), horizontalPadding: 24)
}
