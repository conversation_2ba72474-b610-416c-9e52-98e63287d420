//
//  ExpandableLabelView.swift
//  WDBook
//
//  Created by <PERSON><PERSON> <PERSON><PERSON> on 2023/9/29.
//  Copyright © 2023 WeDevote Bible. All rights reserved.
//

import SwiftUI

struct ExpandableLabelWrapper: UIViewRepresentable {
    typealias UIViewType = ExpandableLabel
    
    func makeUIView(context: Context) -> ExpandableLabel {
        return ExpandableLabel()
    }
    
    func updateUIView(_ uiView: ExpandableLabel, context: Context) {
        // 更新视图的代码，例如设置文本、字体等
    }
}


class ExpandableLabel: UILabel {
    internal let ml = 4
    private let expandText = "展开"
    private let originalText = "这是一段可能超过4行的文本，一旦超过4行，将显示展开按钮。展开按钮可以点击。这是第二行文本。这是第三行文本。这是第四行文本。这是第五行文本这是第五行文本这是第五行文本这是第五行文本这是第五行文本这是第五行文本这是第五行文本这是第五行文本。"
    
    private let expandButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitleColor(.blue, for: .normal)
        button.addTarget(self, action: #selector(expandButtonTapped), for: .touchUpInside)
        return button
    }()
    
    private var isExpanded = false {
        didSet {
            updateText()
        }
    }
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        configureLabel()
        configureExpandButton()
        updateText()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        configureLabel()
        configureExpandButton()
        updateText()
    }
    
    private func configureLabel() {
        numberOfLines = ml
        lineBreakMode = .byTruncatingTail
    }
    
    private func configureExpandButton() {
        expandButton.setTitle(expandText, for: .normal)
        expandButton.sizeToFit()
        addSubview(expandButton)
    }
    
    override func layoutSubviews() {
        super.layoutSubviews()
        expandButton.frame.origin.x = bounds.width - expandButton.bounds.width
        expandButton.frame.origin.y = bounds.height - expandButton.bounds.height
    }
    
    private func updateText() {
        if isExpanded {
            text = originalText
            expandButton.isHidden = true
        } else {
            let truncatedText = truncatedText()
            text = truncatedText
            expandButton.isHidden = truncatedText == originalText
        }
    }
    
    private func truncatedText() -> String {
        let lines = originalText.components(separatedBy: "\n")
        var truncatedText = ""
        
        for (index, line) in lines.enumerated() {
            if index >= maxNumberOfLines {
                break
            }
            
            truncatedText += line
            
            if index < lines.count - 1 {
                truncatedText += "\n"
            }
        }
        
        return truncatedText
    }
    
    @objc private func expandButtonTapped() {
        isExpanded = true
    }
}


struct ExpandableLabelView_Previews: PreviewProvider {
    static var previews: some View {
        ExpandableLabelWrapper()
    }
}
