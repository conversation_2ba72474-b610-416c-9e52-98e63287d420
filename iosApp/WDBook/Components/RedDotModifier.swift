//
//  RedDotModifier.swift
//  WDBook
//
//  Created by <PERSON><PERSON> <PERSON><PERSON> on 2022/5/18.
//  Copyright © 2022 WeDevote Bible. All rights reserved.
//

import Foundation
import SwiftUI

struct RedDotVModifier: ViewModifier {
    @Binding var hasDot:Bool
    
    func body(content: Content) -> some View {
        if hasDot {
            content
                .overlay(Circle()
                    .background(Color.clear)
                    .foregroundColor(Color(UIColor(hex: 0xFF342A)))
                    .frame(width: 8, height: 8), alignment: .topTrailing)
        }else{
            content
        }
    }
}

extension View {
    func hasRedDot(hasDot:Binding<Bool>) -> some View {
        modifier(RedDotVModifier(hasDot: hasDot))
    }
}


struct RedDotNumVModifier: ViewModifier {
    @Binding var dotNum:Int
    
    func body(content: Content) -> some View {
        if dotNum > 0 {
            content
                .overlay(
                    Text("\(dotNum)")
                        .font(Font.regular(size: 8))
                        .foregroundColor(Color.white)
                        .padding(.horizontal, 2.5)
                        .frame(minWidth:11,minHeight: 11)
                        .background(Color(hex: 0xFF342A))
                        .cornerRadius(12), alignment: .topTrailing)
        }else{
            content
        }
    }
}

extension View {
    func redDot(num:Binding<Int>) -> some View {
        modifier(RedDotNumVModifier(dotNum: num))
    }
}

#if DEBUG
struct RedDot_Previews: PreviewProvider {
    static var previews: some View {

            VStack{
                Text("你好").redDot(num: .constant(999))
                
                Text("你好").redDot(num: .constant(25))
                Text("你好").redDot(num: .constant(2))
                Text("你好").redDot(num: .constant(1))
                Text("你好").redDot(num: .constant(0))
            }
    }
}
#endif
