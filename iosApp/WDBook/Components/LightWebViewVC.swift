//
//  WebViewVC.swift
//  WDBook
//
//  Created by <PERSON> on 2020/10/20.
//  Copyright © 2020 WeDevote Bible. All rights reserved.
//

import Foundation
import UIKit
import SwiftUI
import WebKit
import SnapKit
import wdNetwork

struct LightWebViewVC: UIViewControllerRepresentable {
    
    @Environment(\.presentationMode) var presentationMode
    @Binding var isPresent:Bool  //无效
    private var title:String?
    private var url:String?
    
    public init(isPresent: Binding<Bool>? = nil,url:String? = nil,title:String? = nil) {
        _isPresent = isPresent ?? .constant(true)
        self.url = url
        self.title = title
        Log.d("LightWebViewVC init")
    }
    
    public func makeUIViewController(context: Context) -> UINavigationController {
        Log.d("LightWebViewVC makeUIViewController")
        let epubVC = WebViewVC(url: url)
        epubVC.title = title
        epubVC.backHandler = {
            self.isPresent = false
            self.presentationMode.wrappedValue.dismiss()
        }
        let navigationController = UINavigationController(rootViewController: epubVC)
        return navigationController
    }
    
    public func updateUIViewController(_ uiViewController: UINavigationController, context: Context) {
        Log.d("LightWebViewVC updateUIViewController")
    }
}

class WebViewVC:UIViewController,WKNavigationDelegate,WKScriptMessageHandler {
    enum JSInvokeNative:String {
        case login
    }
    var url:String
    var webView:WKWebView!
    var activity = UIActivityIndicatorView(style: .medium)
    public var backHandler:(()->())?
    
    required init(url:String? = nil,title:String? = nil) {
        self.url = url ?? ""
        super.init(nibName: nil, bundle: nil)
        self.title = title
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    deinit {
        Log.i("释放WebViewVC")
    }
    public override func viewDidLoad() {
        super.viewDidLoad()
        self.view.backgroundColor = dynamicBackgroundColor1
        
        self.navigationController?.navigationBar.shadowImage = UIImage()
        self.navigationController?.navigationBar.tintColor = dynamicSegmentTitleColor //按钮前色
        self.navigationController?.navigationBar.barTintColor = dynamicBackgroundColor1  //背景色
        self.navigationController?.navigationBar.isTranslucent = false
        
        self.navigationItem.setLeftBarButtonItems([UIBarButtonItem(image: UIImage(named: "back_ui"), style: .plain,  target: self, action: #selector(tapBack))], animated: false)
        
        var request = URLRequest(url:URL(string: self.url)!)
        request.allHTTPHeaderFields = NetworkUtils.getCftConnectionHeader()
        
        self.activity.hidesWhenStopped = true
        
        webView = WKWebView(frame: .zero,configuration: webConfiguration())
        webView.addSubview(self.activity)
        webView.navigationDelegate = self
        webView.load(request)
        
        view.addSubview(webView)
        webView.snp.makeConstraints { (make) in
            make.edges.equalToSuperview()
        }
    }
    
    func webConfiguration() -> WKWebViewConfiguration{
        let userContentController = WKUserContentController()
        userContentController.add(self, name: JSInvokeNative.login.rawValue)
        let webConfiguration = WKWebViewConfiguration()
        webConfiguration.userContentController = userContentController
        return webConfiguration
    }
    
    @objc func tapBack(){
        if webView?.canGoBack ?? true {
            webView?.goBack()
        }else{
            close()
            backHandler?()
        }
    }
    
    func close(){
        if webView.superview != nil {
            //删除js绑定，会释放本页
            webView.configuration.userContentController.removeScriptMessageHandler(forName: JSInvokeNative.login.rawValue)
            
            //设置blank页面，会停止后台播放
            let urlEncodingString = "about:blank".addingPercentEncoding(withAllowedCharacters:NSCharacterSet.urlQueryAllowed)
            let request = URLRequest(url:URL(string: urlEncodingString!)!)
            webView.load(request)
            
            webView.removeFromSuperview()
        }
    }
    
    func webView(_ webView: WKWebView, didStartProvisionalNavigation navigation: WKNavigation!) {
        self.activity.center = webView.center //必须webview start后，赋值位置才正确。必须gray样式才看得见。
        self.activity.startAnimating()
    }
    func webView(_ webView: WKWebView, didFinish navigation: WKNavigation!) {
        self.activity.stopAnimating()
        self.activity.removeFromSuperview()
        navigationItem.title = webView.title
    }
    
    func webView(_ webView: WKWebView, didFailProvisionalNavigation navigation: WKNavigation!, withError error: Error) {
        self.activity.stopAnimating()
        self.activity.removeFromSuperview()
        Toaster.showToast(message: "网络加载失败，请重新尝试！".localized,duration: 1.0) {[weak self] (success) in
            self?.tapBack()
        }
    }
    
    func userContentController(_ userContentController: WKUserContentController, didReceive message: WKScriptMessage) {
        guard let type = JSInvokeNative(rawValue: message.name) else {
            return
        }
        switch type {
        case .login:
            if let jsonString:String = message.body as? String{
                WDBookSessionSDK.shared.setUserTokenString(tokenString: jsonString)
                AppState.shared.tokenString = jsonString
                AppState.shared.refreshUserInfo(forceRefresh: false)
                WDBookUserSDK.shared.checkAndAddDevice(deviceId: WDBookSessionSDK.shared.deviceId) { result in
                    switch result {
                    case .success(let result):
                        if (result == 1312){
                            self.tapBack()
                            AppState.shared.hideLoginRegisterV(isClearCache:false)
                            RoutableManager.showDeviceManagerV()
                        }else {
                            WDBookDataSyncManager.shared.loginComplete()
                            Toaster.showToast(message: "登录成功".localized,duration: 1.5) {[weak self] (success) in
                                self?.close()
                                AppState.shared.hideLoginRegisterV(isSuccess: true)
                            }
                        }
                        break
                    case .failure(let error):
                        print(error)
                        WDBookDataSyncManager.shared.loginComplete()
                        Toaster.showToast(message: "登录成功".localized,duration: 1.5) {[weak self] (success) in
                            self?.close()
                            AppState.shared.hideLoginRegisterV(isSuccess: true)
                        }
                    }
                }
                
            }
        }
    }
}

#if DEBUG
struct LightWebviewVC_Previews: PreviewProvider {
    static var previews: some View {
        Group {
            NavigationView{
                LightWebViewVC(url: "https://google.com")
            }
            NavigationView{
                LightWebViewVC(url: "https://google.com").environment(\.colorScheme, .dark)
            }
        }
        
    }
}
#endif
