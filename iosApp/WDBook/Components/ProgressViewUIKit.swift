//
//  ProgressViewUIKit.swift
//  WDBook
//
//  Created by <PERSON> on 2020/9/4.
//  Copyright © 2020 WeDevote Bible. All rights reserved.
//  ProgressView For iOS13

import Foundation
import SwiftUI

public struct ProgressViewUIKit: UIViewRepresentable {
    var trackTintColor:UIColor
    var progressTintColor:UIColor
    var size:CGSize?
    @Binding public var value: Float
    
    public init(_ value: Binding<Float>,trackTintColor:UIColor = UIColor(hex:0xD8D8D8),progressTintColor:UIColor = UIColor(hex:0x373636),size:CGSize? = nil) {
        _value = value
        self.trackTintColor = trackTintColor
        self.progressTintColor = progressTintColor
        self.size = size
    }
    
    public func makeUIView(context: Context) -> UIProgressView {
        let p = UIProgressView()
        p.trackTintColor = trackTintColor
        p.progressTintColor = progressTintColor
        if let s = size{
            p.frame = CGRect(x: 0, y: 0, width: s.width, height: s.height)
        }
        return p
    }
    
    public func updateUIView(_ uiView: UIProgressView, context: Context) {
        uiView.setProgress(value, animated: true)
    }
}
