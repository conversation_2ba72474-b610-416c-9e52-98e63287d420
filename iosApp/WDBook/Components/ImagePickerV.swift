//
//  ImagePickerV.swift
//  WDBook
//
//  Created by ka<PERSON> <PERSON><PERSON> on 2023/5/8.
//  Copyright © 2023 WeDevote Bible. All rights reserved.
//

import Foundation
import SwiftUI
import UIKit

struct ImagePickerV: UIViewControllerRepresentable {
    @Environment(\.presentationMode) private var presentationMode
    let sourceType: UIImagePickerController.SourceType
    let onImagePicked: (UIImage) -> Void
    
    func makeUIViewController(context: Context) -> UIImagePickerController {
        let picker = UIImagePickerController()
        picker.sourceType = self.sourceType
        picker.delegate = context.coordinator
        return picker
    }
    
    func updateUIViewController(_ uiViewController: UIImagePickerController, context: Context) {}
    
    func makeCoordinator() -> Coordinator {
        Coordinator(
            ondismiss: { self.presentationMode.wrappedValue.dismiss() },
            onImagePicked: self.onImagePicked
        )
    }
    
    final public class Coordinator: NSObject, UINavigationControllerDelegate, UIImagePickerControllerDelegate {
        private let ondismiss: () -> Void
        private let onImagePicked: (UIImage) -> Void
        
        init(ondismiss: @escaping () -> Void, onImagePicked: @escaping (UIImage) -> Void) {
            self.ondismiss = ondismiss
            self.onImagePicked = onImagePicked
        }
        
        func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey: Any]) {
            if let image = info[.originalImage] as? UIImage {
                self.onImagePicked(image)
            }
            self.ondismiss()
        }
        
        func imagePickerControllerDidCancel(_: UIImagePickerController) {
            self.ondismiss()
        }
    }
}
