//
//  HTMLTextWebView.swift
//  WDBook
//
//  Created by ka<PERSON> <PERSON>hou on 2022/2/7.
//  Copyright © 2022 WeDevote Bible. All rights reserved.
//

import Foundation
import SwiftUI
import WebKit

struct HTMLTextWebView: View {
    var htmlText:String
    
    var body: some View {
        HTMLTextWebViewUIKit(htmlText: htmlText)
    }
}

struct HTMLTextWebViewUIKit:UIViewRepresentable {
    
    var htmlText: String
    func makeUIView(context: Context) -> WKWebView {
        let webView = WKWebView()
        webView.navigationDelegate = context.coordinator
        webView.loadHTMLString(htmlText, baseURL: nil)
        webView.backgroundColor = UIColor.yellow
        return webView
    }

    func updateUIView(_ uiView: WKWebView, context: UIViewRepresentableContext<HTMLTextWebViewUIKit>) {

    }
    
    func makeCoordinator() -> Coordinator {
        Coordinator()
    }
    
    class Coordinator: NSObject, WKNavigationDelegate {
        
        override init() {
        }

        func webView(_ webView: WKWebView, didStartProvisionalNavigation navigation: WKNavigation!) {
        }
        func webView(_ webView: WKWebView, didFinish navigation: WKNavigation!) {
        }
    }
}

#if DEBUG
struct HTMLTextWebView_Previews: PreviewProvider {
    static let str = """
《<font color="#FF8A00">马太福音</font>》<font color="#FF8A00">释经默想</font>
<p>
<em>福</em>杯满溢-弟兄篇
<p>
<p style="background-color:rgb(255,255,0)">
通过 rbg 值设置背景颜色
</p>
"""
    
    static var previews: some View {
        Group {
            HTMLTextWebView(htmlText: str).frame(height:100).frame(maxWidth:.infinity)
            HTMLTextWebView(htmlText: str).frame(height:100).frame(maxWidth:.infinity).environment(\.colorScheme, .dark)
        }
    }
}
#endif
