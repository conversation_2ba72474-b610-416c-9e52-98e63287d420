//
//  ActivityInd.swift
//  WDBook
//
//  Created by <PERSON> on 2020/9/2.
//  Copyright © 2020 WeDevote Bible. All rights reserved.
//

import Foundation
import UIKit
import SwiftUI

//TODO:ActivityIndicator使用ios14的

//iOS14原生支持
//VStack {
//    ProgressView()
//       // and if you want to be explicit / future-proof...
//       // .progressViewStyle(CircularProgressViewStyle())
//}

//struct ActivityIndicator: UIViewRepresentable {
//
//    @Binding var isAnimating: Bool
//    let style: UIActivityIndicatorView.Style
//
//    func makeUIView(context: UIViewRepresentableContext<ActivityIndicator>) -> UIActivityIndicatorView {
//        return UIActivityIndicatorView(style: style)
//    }
//
//    func updateUIView(_ uiView: UIActivityIndicatorView, context: UIViewRepresentableContext<ActivityIndicator>) {
//        isAnimating ? uiView.startAnimating() : uiView.stopAnimating()
//    }
//}

struct ActivityIndicator: UIViewRepresentable {

    var style: UIActivityIndicatorView.Style = .medium

    func makeUIView(context: UIViewRepresentableContext<ActivityIndicator>) -> UIActivityIndicatorView {
        let indicator = UIActivityIndicatorView(style: style)
        indicator.startAnimating()
        return indicator
    }

    func updateUIView(_ uiView: UIActivityIndicatorView, context: UIViewRepresentableContext<ActivityIndicator>) {
        
    }
}
