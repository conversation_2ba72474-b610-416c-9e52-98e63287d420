//
//  DownloadProgressView.swift
//  WDBook
//
//  Created by <PERSON> on 2020/9/9.
//  Copyright © 2020 WeDevote Bible. All rights reserved.
// ProgressView iOS13适配。

import SwiftUI

struct ProgressViewSwiftUI: View {
    @Binding var progress: Float
    @State var isShowing = false
    @Binding var trackTintColor:UIColor
    @Binding var progressTintColor:UIColor
    
    init(progress:Binding<Float>,
         trackTintColor:Binding<UIColor> = .constant(dynamicBtnBGColor),
         progressTintColor:Binding<UIColor> = .constant(UIColor.primaryColor1)) {
        _progress = progress
        _trackTintColor = trackTintColor
        _progressTintColor = progressTintColor
    }
    var body: some View {
        GeometryReader { geometry in
            ZStack(alignment: .leading) {
                Rectangle()
                    .foregroundColor(Color(self.trackTintColor))
//                    .opacity(0.3)
                    .frame(width: geometry.size.width, height: geometry.size.height)
                Rectangle()
                    .foregroundColor(Color(self.progressTintColor))
                    .frame(width: self.isShowing ? (geometry.size.width * CGFloat(self.progress)) : 0.0,
                           height: geometry.size.height)
//                    .animation(.linear(duration: 0.3))
            }
            .onAppear {
                self.isShowing = true
            }
            .cornerRadius(geometry.size.height / 2.0)
        }
    }
}

//struct DownloadProgressView_Previews: PreviewProvider {
//    static var previews: some View {
//        DownloadProgressView(progress: .constant(25.0))
//            .frame(width: 345.0, height: 8.0)
//    }
//}

struct DownloadProgressView: View {
    @Binding var progress: Float
    @Binding var trackTintColor:UIColor
    @Binding var progressTintColor:UIColor
    @Binding var textColor:UIColor
    var fontSize:CGFloat = 12.0
    
    
    /// <#Description#>
    /// - Parameters:
    ///   - progress: <#progress description#>
    ///   - trackTintColor: 背景色
    ///   - progressTintColor: 进度色
    ///   - textColor: 文字色
    init(progress:Binding<Float>,
         trackTintColor:Binding<UIColor> = .constant(dynamicColorBGDownload),
         progressTintColor:Binding<UIColor> = .constant(UIColor.primaryColor1),
         textColor:Binding<UIColor> = .constant(dynamicTextColor4Download)) {
        _progress = progress
        _trackTintColor = trackTintColor
        _progressTintColor = progressTintColor
        _textColor = textColor
    }
    
    var body: some View {
        ZStack {
            ProgressViewSwiftUI(progress: $progress, trackTintColor: $trackTintColor,progressTintColor: $progressTintColor)
                
            Text("\(lroundf(progress * 100))%")
                .font(Font.medium(size: fontSize))
                .foregroundColor(Color(textColor))
        }//.frame(width: 80, height: 32)

    }
}


#if DEBUG
struct DownloadProgressViewDemo: View {
    @State var progress: Float = 0.0
    var body: some View {
        VStack {
            DownloadProgressView(progress: $progress).frame(width: 80, height: 32)
            DownloadProgressView(progress: $progress).frame(width: 200, height: 32)
            Button(
                action: {
                    self.progress = Float.random(in: 0...1)
                }
            ) {
                Text("Random Progress")
            }
            .padding()
        }
    }
}


struct DownloadProgressViewDemo_Previews: PreviewProvider {
    static var previews: some View {
        
        Group {
            DownloadProgressViewDemo()
            DownloadProgressViewDemo().environment(\.colorScheme, .dark)
        }
    }
}
#endif
