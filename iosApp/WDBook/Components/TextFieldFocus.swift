//
//  TextFieldFocus.swift
//  WDBook
//
//  Created by <PERSON><PERSON> <PERSON><PERSON> on 2021/12/15.
//  Copyright © 2021 WeDevote Bible. All rights reserved.
//

import Foundation
import SwiftUI

enum FocusableField: Hashable {
    case groupName
}

@available(iOS 15.0, *)
struct TextFieldFocus: View{
    @FocusState private var focus:FocusableField?
    @Binding var text:String
    
    var body: some View{
        TextField("", text: $text)
        .focused($focus, equals: .groupName)
        .onAppear {
//            DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
//                focus = .groupName
//            }
            DispatchQueue.main.async{
                focus = .groupName
            }
        }
    }
}
