//
//  BackView.swift
//  WDBook
//
//  Created by <PERSON> on 2020/9/1.
//  Copyright © 2020 WeDevote Bible. All rights reserved.
//
//简单的返回navigation包装。用于预览
import SwiftUI

struct BackView<Content>: View where Content: View {
    @Environment(\.presentationMode) var presentationMode: Binding<PresentationMode>
    
    var onTapBack:(()->())?
    var btnBack : some View { Button(action: {
        self.onTapBack?()
        self.presentationMode.wrappedValue.dismiss()
        }) {
            HStack {
            Image("back_ui")
                .aspectRatio(contentMode: .fit)
                .foregroundColor(Color(btnTintColor)) //ios14无效
            }
        }
    }
    
    private let content: Content

    public init(onTapBack: (()->())? = nil, @ViewBuilder _ content: () -> Content) {
        self.content = content()
        self.onTapBack = onTapBack
    }

    var body : some View {
        content
            .navigationBarBackButtonHidden(true)
            .navigationBarItems(leading: btnBack)
    }
}
#if DEBUG
struct BackViewDemo: View {
    var body: some View {
        BackView(onTapBack: {
            debugPrint("退出")
        }) {
            NavigationLink(destination: BackViewDemo()) {
                Text("push self")
            }
        }.navigationBarTitle("标题",displayMode: .inline)
        //        .navigationBarBackButtonHidden(false)

    }
}

struct BackView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationView{
            BackViewDemo()
        }
    }
}
#endif
