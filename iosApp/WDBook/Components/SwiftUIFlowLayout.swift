import SwiftUI

public let flowLayoutDefaultItemSpacing: CGFloat = 4

let FlowLayoutLineNumDidChangedNotification = Notification.Name(rawValue: "FlowLayout_lineNumDidChangedNotification")
let FlowLayoutHeightDidChangedNotification = Notification.Name(rawValue: "FlowLayout_HeightDidChangedNotification")

public struct FlowLayout<RefreshBinding, Data, ItemView: View>: View {
  var id:Int?
  let mode: Mode
  @Binding var binding: RefreshBinding
  let items: [Data]
  let itemSpacing: CGFloat
  @ViewBuilder let viewMapping: (Int,Data) -> ItemView

  @State private var totalHeight: CGFloat

    public init(id:Int? = nil,
                mode: Mode,
              binding: Binding<RefreshBinding>,
              items: [Data],
              itemSpacing: CGFloat = flowLayoutDefaultItemSpacing,
              totalHeight:Binding<CGFloat>,
              @ViewBuilder viewMapping: @escaping (Int,Data) -> ItemView) {
        self.id = id
    self.mode = mode
    _binding = binding
    self.items = items
    self.itemSpacing = itemSpacing
    self.viewMapping = viewMapping
    _totalHeight = State(initialValue: (mode == .scrollable) ? .zero : .infinity)
  }

  public var body: some View {
    let stack = VStack {
       GeometryReader { geometry in
         self.content(in: geometry)
       }
    }
    return Group {
      if mode == .scrollable {
        stack.frame(height: totalHeight)
      } else {
        stack.frame(maxHeight: totalHeight)
      }
    }
  }

  private func content(in g: GeometryProxy) -> some View {
    var width = CGFloat.zero
    var height = CGFloat.zero
    var lastHeight = CGFloat.zero
    let itemCount = items.count
    var lineNum = 0
    return ZStack(alignment: .topLeading) {
        ForEach(Array(items.enumerated()), id: \.offset) { index, item in
            viewMapping(index,item)
              .padding([.vertical,.horizontal], itemSpacing)
              .alignmentGuide(.leading, computeValue: { d in
                if (abs(width - d.width) > g.size.width) {
                  width = 0
                  height -= lastHeight
                }
                lastHeight = d.height
                let result = width
                if index == itemCount - 1 {
                  width = 0
                } else {
                  width -= d.width
                }
                  if index == 0{
                      lineNum = 0
                  }
                  if result == 0{
                      lineNum += 1
//                      debugPrint("共有:\(lineNum)行")
//                      NotificationCenter.default.post(name: FlowLayoutLineNumDidChangedNotification, object: lineNum)
                  }
                return result
              })
              .alignmentGuide(.top, computeValue: { d in
                let result = height
                if index == itemCount - 1 {
                  height = 0
                }
                return result
              })
        }
      }
      .background(viewHeightReader($totalHeight))
  }

  private func viewHeightReader(_ binding: Binding<CGFloat>) -> some View {
    return GeometryReader { geo -> Color in
      DispatchQueue.main.async {
        binding.wrappedValue = geo.frame(in: .local).size.height
          NotificationCenter.default.post(name: FlowLayoutHeightDidChangedNotification, object: (id,binding.wrappedValue))
      }
      return .clear
    }
  }

  public enum Mode {
    case scrollable, vstack
  }
}

public extension FlowLayout where RefreshBinding == Never? {
    init(id:Int? = nil,
         mode: Mode,
         items: [Data],
         itemSpacing: CGFloat = flowLayoutDefaultItemSpacing,
         totalHeight:Binding<CGFloat> = .constant(0),
         @ViewBuilder viewMapping: @escaping (Int,Data) -> ItemView) {
        self.init(id:id,
                  mode: mode,
                  binding: .constant(nil),
                  items: items,
                  itemSpacing: itemSpacing,
                  totalHeight: totalHeight,
                  viewMapping: viewMapping)
    }
}

#if DEBUG
struct FlowLayout_Previews: PreviewProvider {
  static var previews: some View {
    FlowLayout(mode: .scrollable,
               items: ["Some long item here", "And then some longer one",
                      "Short", "Items", "Here", "And", "A", "Few", "More",
                      "And then a very very very long long long long long long long long longlong long long long long long longlong long long long long long longlong long long long long long longlong long long long long long longlong long long long long long long long one", "and", "then", "some", "short short short ones"]) { i,d in
        Text(d)
        .font(.system(size: 12))
        .foregroundColor(.black)
        .padding()
        .background(RoundedRectangle(cornerRadius: 4)
                               .border(Color.gray)
                               .foregroundColor(Color.gray))
    }.padding()
  }
}
#endif

struct TestWithDeletion: View {
    @State private var items = ["Some long item here", "And then some longer one",
                                "Short", "Items", "Here", "And", "A", "Few", "More",
                                "And then a very very very long long long long long long long long longlong long long long long long longlong long long long long long longlong long long long long long longlong long long long long long longlong long long long long long long long one", "and", "then", "some", "short short short ones"]
    
    var body: some View {
        VStack {
        Button("Delete all") {
            items.removeAll()
        }
            Button("Restore") {
                items = ["Some long item here", "And then some longer one",
                         "Short", "Items", "Here", "And", "A", "Few", "More",
                         "And then a very very very long long long long long long long long longlong long long long long long longlong long long long long long longlong long long long long long longlong long long long long long longlong long long long long long long long one", "and", "then", "some", "short short short ones"]
            }
            Button("Add one") {
                items.append("\(Date().timeIntervalSince1970)")
            }
        FlowLayout(mode: .vstack,
                   items: items) { i,d in
          Text(d)
            .font(.system(size: 12))
            .foregroundColor(.black)
            .padding()
            .background(RoundedRectangle(cornerRadius: 4)
                                   .border(Color.gray)
                                   .foregroundColor(Color.gray))
        }.padding()
        }
    }
}

#if DEBUG
struct TestWithDeletion_Previews: PreviewProvider {
  static var previews: some View {
    TestWithDeletion()
  }
}
#endif
