//
//  ClearHighlightStyle.swift
//  ClearHighlightStyle
//
//  Created by <PERSON><PERSON> <PERSON><PERSON> on 2021/7/28.
//  Copyright © 2021 WeDevote Bible. All rights reserved.
//

import Foundation
import SwiftUI

struct ClearHighlightStyle: ButtonStyle {
    func makeBody(configuration: Self.Configuration) -> some View {
        configuration.label
            .background(configuration.isPressed ? Color.clear : Color.clear)
    }
}
