//
//  PECropViewController.m
//  PhotoCropEditor
//
//  Created by k<PERSON><PERSON> katsu<PERSON> on 2013/05/19.
//  Copyright (c) 2013 kishikawa katsumi. All rights reserved.
//

#import "PECropViewController.h"
#import "PECropView.h"
#define WD_SYSTEM_VERSION_LESS_THAN(v) ([[[UIDevice currentDevice] systemVersion] compare:v options:NSNumericSearch] == NSOrderedAscending)
#define IS_BELOW_IOS8 WD_SYSTEM_VERSION_LESS_THAN(@"8.0")
@interface PECropViewController () <UIActionSheetDelegate>

@property (nonatomic) PECropView *cropView;
@property (nonatomic) UIActionSheet *actionSheet;

@end

@implementation PECropViewController

- (void)loadView
{
    UIView *contentView = [[UIView alloc] init];
    contentView.autoresizingMask = UIViewAutoresizingFlexibleWidth | UIViewAutoresizingFlexibleHeight;
    contentView.backgroundColor = [UIColor blackColor];
    self.view = contentView;
    
    self.cropView = [[PECropView alloc] initWithFrame:contentView.bounds];
    self.cropView.scale = _scale;
    self.cropView.disableChangeScale = _disableChangeScale;
    [contentView addSubview:self.cropView];
}

- (void)viewDidLoad
{
    [super viewDidLoad];
    [self.navigationController setNavigationBarHidden:YES];
    self.navigationController.toolbar.translucent = NO;
    if (!self.toolbarItems) {
        UIBarButtonItem *constrainLeftButton = [[UIBarButtonItem alloc] initWithTitle:_cancelText
                                                                            style:UIBarButtonItemStyleBordered
                                                                           target:self
                                                                           action:@selector(cancel:)];
        
        UIBarButtonItem *flexibleSpace = [[UIBarButtonItem alloc] initWithBarButtonSystemItem:UIBarButtonSystemItemFlexibleSpace
                                                                                       target:nil
                                                                                       action:nil];
        UIBarButtonItem *constrainRightButton = [[UIBarButtonItem alloc] initWithTitle:_finishText
                                                                            style:UIBarButtonItemStyleBordered
                                                                           target:self
                                                                           action:@selector(done:)];
        self.toolbarItems = @[constrainLeftButton, flexibleSpace, constrainRightButton];
    }
    self.navigationController.toolbarHidden = self.toolbarHidden;
    
    self.cropView.image = self.image;
    if (!IS_BELOW_IOS8) {
        [self.navigationController.toolbar setBarTintColor:_bgColor];
    }
    [self.navigationController.toolbar setTintColor:_textColor];
}

- (BOOL)shouldAutorotateToInterfaceOrientation:(UIInterfaceOrientation)toInterfaceOrientation
{
    return YES;
}

- (void)setImage:(UIImage *)image
{
    _image = image;
    self.cropView.image = image;
}

- (void)cancel:(id)sender
{
    if ([self.delegate respondsToSelector:@selector(cropViewControllerDidCancel:)]) {
        [self.delegate cropViewControllerDidCancel:self];
    }
   [self dissmissView];
}

- (void)done:(id)sender
{
    [self dissmissView];
    if ([self.delegate respondsToSelector:@selector(cropViewController:didFinishCroppingImage:)]) {
        [self.delegate cropViewController:self didFinishCroppingImage:self.cropView.croppedImage];
    }
}

- (void)dissmissView {
    [self.navigationController setToolbarHidden:YES];
    if(IS_BELOW_IOS8) {
        [self.navigationController popViewControllerAnimated:NO];
    } else {
        [self dismissViewControllerAnimated:YES completion:nil];
    }
}

@end
