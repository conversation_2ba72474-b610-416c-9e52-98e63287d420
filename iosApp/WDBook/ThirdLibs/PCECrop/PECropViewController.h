//
//  PECropViewController.h
//  PhotoCropEditor
//
//  Created by k<PERSON><PERSON> katsumi on 2013/05/19.
//  Copyright (c) 2013 kishikawa katsumi. All rights reserved.
//

#import <UIKit/UIKit.h>

@interface PECropViewController : UIViewController

@property (nonatomic) NSString *cancelText;
@property (nonatomic) NSString *finishText;
@property (nonatomic) UIColor *bgColor;
@property (nonatomic) UIColor *textColor;
@property (nonatomic) id delegate;
@property (nonatomic) UIImage *image;

@property (nonatomic) BOOL toolbarHidden;
@property (nonatomic) CGFloat scale;
@property (nonatomic) BOOL disableChangeScale;

@property (nonatomic, assign, getter = isRotationEnabled) BOOL rotationEnabled;


@end

@protocol PECropViewControllerDelegate <NSObject>

- (void)cropViewController:(PECropViewController *)controller didFinishCroppingImage:(UIImage *)croppedImage;
- (void)cropViewControllerDidCancel:(PECropViewController *)controller;

@end
