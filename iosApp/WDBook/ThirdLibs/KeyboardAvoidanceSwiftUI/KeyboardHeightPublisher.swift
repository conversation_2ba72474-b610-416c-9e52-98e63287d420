//
//  KeyboardHeightPublisher.swift
//  KeyboardAvoidanceSwiftUI
//
//  Created by <PERSON><PERSON><PERSON> on 3/27/20.
//  Copyright © 2020 V<PERSON><PERSON>. All rights reserved.
//

import Combine
import UIKit

extension Publishers {
    static var keyboardHeight: AnyPublisher<CGFloat, Never> {
        let willShow = NotificationCenter.default.publisher(for: UIApplication.keyboardWillShowNotification)
            .map { $0.keyboardHeight }
        
        let willHide = NotificationCenter.default.publisher(for: UIApplication.keyboardWillHideNotification)
            .map { _ in CGFloat(0) }
        
        return MergeMany(willShow, willHide)
            .eraseToAnyPublisher()
    }
}

extension Publishers {
    static var keyboardOnHeight: AnyPublisher<CGFloat, Never> {
        let willShow = NotificationCenter.default.publisher(for: UIApplication.keyboardWillShowNotification)
            .map { $0.keyboardHeight }
        
        let didHide = NotificationCenter.default.publisher(for: UIApplication.keyboardDidHideNotification)
            .map { _ in CGFloat(0) }
        
        return MergeMany(willShow, didHide)
            .eraseToAnyPublisher()
    }
}

extension Notification {
    var keyboardHeight: CGFloat {
        return (userInfo?[UIResponder.keyboardFrameEndUserInfoKey] as? CGRect)?.height ?? 0
    }
}
