//
//  String+Extensions.swift
//  WDBook
//
//  Created by <PERSON> on 2025/2/19.
//  Copyright © 2025 WeDevote Bible. All rights reserved.
//

import Foundation

extension String {
    /// Validates if the string is an appropriate search term.
    /// Rules:
    /// - Not empty or only whitespace
    /// - Length must be between minLength and maxLength characters inclusive.
    /// - Parameters:
    ///   - minLength: The minimum allowed character length.
    ///   - maxLength: The maximum allowed character length.
    func isValidSearchLength(minLength: Int, maxLength: Int) -> Bool {
        // Check for empty or whitespace-only strings
        let trimmedText = trimmingCharacters(in: .whitespaces)
        guard !trimmedText.isEmpty else { return false }

        // Ensure minLength is not greater than maxLength (programmer error check)
        assert(minLength <= maxLength, "minLength (\(minLength)) must not exceed maxLength (\(maxLength)) in isValidSearchLength")

        // Check character count is within the valid range [minLength, maxLength]
        return (minLength ... maxLength).contains(trimmedText.count)
    }
}
