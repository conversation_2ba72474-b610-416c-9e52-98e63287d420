//
//  UITraitCollection+Extension.swift
//  WDBook
//
//  Created by <PERSON> on 2025/3/11.
//  Copyright © 2025 WeDevote Bible. All rights reserved.
//

import UIKit

extension UITraitCollection {
    /// Returns true if the interface style is light mode
    var isLight: Bool {
        return userInterfaceStyle == .light || userInterfaceStyle == .unspecified
    }

    /// Returns true if the interface style is dark mode
    var isDark: Bool {
        return userInterfaceStyle == .dark
    }
}
