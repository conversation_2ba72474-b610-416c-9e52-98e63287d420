//
//  ProductDetailEntityExtension.swift
//  WDBook
//
//  Created by 杜文泽 on 2021/6/7.
//  Copyright © 2021 WeDevote Bible. All rights reserved.
//

import Foundation
import shared

extension ProductDetailEntity {

    var authorNames:String {
        if authorList.count > 0 {
            return getAuthorNames(authorEntitites: authorList as! [AuthorEntity])
        }
        return ""
    }
    
    var currencyText:String{
        if currency == "USD"{
            return "$"
        }
        return currency
    }
        
    //是否所有子商品都购买过
    var isAllPurchased:Bool{
        let purchasedNum = subProductList.reduce(0) { (r, p) -> Int in
            if (p as! ProductDetailEntity).isPurchased {
                return r + 1
            }else{
                return r
            }
        }
        return subProductList.count > 0 ? purchasedNum == subProductList.count : false
    }
    
    var isPurchased:Bool{
        purchased == 1
    }
    
    var statusAvailable:Bool{
        status == 1
    }
    
    var allPurchasedSubProductsPrice:Double{
        subProductList.reduce(0.0) { (r, p) -> Double in
            if (p as! ProductDetailEntity).isPurchased {
                return r + Double((p as! ProductDetailEntity).price)
            }else{
                return r
            }
        }
    }
    
    func hasActivity() -> Bool{
        activitiesList?.count > 0
    }
    
    func hasActivity(activityId:Int64) -> Bool{
        (activitiesList as? [ActivityEntity] ?? []).contains{$0.activityId == activityId}
    }
    
    //活动价
    var activityAmount:Double{
        Double((activitiesList as? [ActivityEntity] ?? []).filter{$0.productId == productId}.first?.amount ?? 0.0)
    }
    
    var activityDiscount: Double{
        Double((activitiesList as? [ActivityEntity] ?? []).filter{$0.productId == productId}.first?.discount ?? 0.0)
    }
    
    var activityPriceCNY: Double{
        Double((activitiesList as? [ActivityEntity] ?? []).filter{$0.productId == productId}.first?.amountCNY ?? 0.0)
    }

    //最低价
    var lowestPrice: Double{
        if Double(discount) < activityDiscount {
            return Double(price)
        }else{
            return activityAmount
        }
    }
    
    var lowestDiscount: Double{
        if Double(discount) < activityDiscount {
            return Double(discount)
        }else{
            return activityDiscount
        }
    }
    
    var lowestPriceCNY: Double{
        if Double(discount) < activityDiscount {
            return Double(priceCNY)
        }else{
            return activityPriceCNY
        }
    }
}




extension AccountedProductsEntity {
    
    var currencyText:String{
        if currency == "USD"{
            return "$"
        }
        return currency
    }
        
    //是否所有子商品都购买过
    var isAllPurchased:Bool{
        let purchasedNum = subProducts.reduce(0) { (r, p) -> Int in
            if (p as! AccountedProductsEntity).isPurchased {
                return r + 1
            }else{
                return r
            }
        }
        return subProducts.count > 0 ? purchasedNum == subProducts.count : false
    }
    
    var isPurchased:Bool{
        purchased == 1
    }
    
    
    var allPurchasedSubProductsPrice:Double{
        subProducts.reduce(0.0) { (r, p) -> Double in
            if (p as! AccountedProductsEntity).isPurchased {
                return r + Double((p as! AccountedProductsEntity).price)
            }else{
                return r
            }
        }
    }
}


extension PaymentAmountEntity{
    var currencyText:String{
        if currency == "USD"{
            return "$"
        }
        if !currency.isEmpty{
            return currency
        }
        return "$"
    }
}
