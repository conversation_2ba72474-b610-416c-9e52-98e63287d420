//
//  StoreCategoryEntity.swift
//  WDBook
//
//  Created by ka<PERSON> zhou on 2022/1/27.
//  Copyright © 2022 WeDevote Bible. All rights reserved.
//

import Foundation
import shared

private var StoreCategoryEntity_KEY: UInt8 = 0
extension StoreCategoryEntity{
    var subCategories:[StoreCategoryEntity]{
        set{
            objc_setAssociatedObject(self, &StoreCategoryEntity_KEY, newValue, .OBJC_ASSOCIATION_RETAIN_NONATOMIC)
        }
        get{
            return objc_getAssociatedObject(self, &StoreCategoryEntity_KEY) as? [StoreCategoryEntity] ?? []
        }
    }
}
