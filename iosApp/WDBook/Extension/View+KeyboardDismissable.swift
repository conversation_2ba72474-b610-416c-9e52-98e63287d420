//
//  View+KeyboardDismissable.swift
//  WDBook
//
//  Created by <PERSON> on 2025/2/18.
//  Copyright © 2025 WeDevote Bible. All rights reserved.
//

import SwiftUI

/// A view modifier that adds keyboard dismissal behavior to any view.
/// The keyboard will be dismissed when the user either:
/// - Drags on the view
/// - Taps on the view
///
/// This is particularly useful for views that contain text input fields where you want
/// to provide an easy way for users to dismiss the keyboard.
struct KeyboardDismissableModifier: ViewModifier {
    func body(content: Content) -> some View {
        content
            .simultaneousGesture(
                DragGesture().onChanged { _ in
                    UIApplication.dismissKeyboard()
                }
            )
            .onTapGesture {
                UIApplication.dismissKeyboard()
            }
    }
}

public extension View {
    /// Adds gesture-based keyboard dismissal to a view.
    ///
    /// Use this modifier when you want to allow users to dismiss the keyboard
    /// by either tapping or dragging on the view. This is commonly used with:
    /// - ScrollViews containing text input
    /// - Form views
    /// - Search interfaces
    ///
    /// Example usage:
    /// ```swift
    /// ScrollView {
    ///     TextField("Search", text: $searchText)
    ///     // Other content...
    /// }
    /// .dismissKeyboardOnGesture()
    /// ```
    ///
    /// - Note: This modifier should be applied to a container view rather than
    ///         directly to text input fields to avoid gesture conflicts.
    ///
    /// - Returns: A view that dismisses the keyboard on drag or tap gestures.
    func dismissKeyboardOnGesture() -> some View {
        modifier(KeyboardDismissableModifier())
    }
}
