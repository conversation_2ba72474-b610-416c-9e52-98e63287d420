//
//  UIFont+Extension.swift
//  WDBook
//
//  Created by WeDevote on 2025/4/26.
//  Copyright © 2025 WeDevote Bible. All rights reserved.
//

import UIKit

// MARK: - Standard App Font Constants
extension UIFont {
    // Named app font constants
    static let sectionHeaderSemibold = UIFont(name: "PingFangSC-Semibold", size: 16) ?? .systemFont(ofSize: 16, weight: .semibold)
    static let primaryTextRegular = UIFont(name: "PingFangSC-Regular", size: 14) ?? .systemFont(ofSize: 14, weight: .regular)
    static let secondaryTextRegular = UIFont(name: "PingFangSC-Regular", size: 12) ?? .systemFont(ofSize: 12, weight: .regular)
    
    // Utility methods for creating fonts
    public static func medium(size: CGFloat = 14) -> UIFont {
        return UIFont(name: "PingFangSC-Medium", size: size) ?? UIFont.systemFont(ofSize: size)
    }
    
    public static func regular(size: CGFloat = 14) -> UIFont {
        return UIFont(name: "PingFangSC-Regular", size: size) ?? UIFont.systemFont(ofSize: size)
    }
    
    public static func semibold(size: CGFloat = 14) -> UIFont {
        return UIFont(name: "PingFangSC-Semibold", size: size) ?? UIFont.systemFont(ofSize: size)
    }
    
    public static func sourceHanSerif(size: CGFloat = 14) -> UIFont {
        return UIFont(name: "SourceHanSerifCN-Regular", size: size) ?? UIFont.systemFont(ofSize: size)
    }
    
    // Debug utility to print available fonts
    static func printFonts() {
        for familyName in UIFont.familyNames {
            for fontName in UIFont.fontNames(forFamilyName: familyName) {
                print(fontName)
            }
        }
    }
}

// MARK: - Font Weight Extension
extension UIFont {
    func withWeight(_ weight: UIFont.Weight) -> UIFont {
        let newDescriptor = fontDescriptor.addingAttributes([.traits: [
            UIFontDescriptor.TraitKey.weight: weight]
        ])
        return UIFont(descriptor: newDescriptor, size: pointSize)
    }
} 