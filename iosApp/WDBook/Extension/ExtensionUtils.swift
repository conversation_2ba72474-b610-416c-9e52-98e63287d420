//
//  ExtensionUtils.swift
//  WDBook
//
//  Created by 杜文泽 on 2021/6/8.
//  Copyright © 2021 WeDevote Bible. All rights reserved.
//

import Foundation
import shared

func getAuthorNames(authorEntitites:[AuthorEntity]) -> String {
    return authorEntitites.reduce("") { (r, a) -> String in
        if r.isEmpty {
            return a.name
        } else {
            return r + "，" + a.name
        }
    }
}

func getAuthorNames(authors:[Author]) -> String {
    return authors.reduce("") { (r, a) -> String in
        if r.isEmpty {
            return a.name
        } else {
            return r + "，" + a.name
        }
    }
}

func getCategoryNames(categories:[AuthorEntity]) -> String {
    if categories.count > 1{
        return categories[1].name
    }else if categories.count > 0{
        return categories[0].name
    }else{
        return ""
    }
}

func getCacheDestFilePath(downloadData:DownloadDataEntity) -> String {
    return FilePath.homeDictionary() + "/" + PathManager.zipPathRelative(downloadData.fileId ?? "")
}

func getShelfBookEntity(resourceId:String) -> DownloadResourceObservable {
    let combineEntity = HomeShelfItemCombineEntity()
    combineEntity.bookItemEntity = WDBookUserSDK.shared.getShelfBookItemEntityByResourceId(resourceId: resourceId)
    combineEntity.dataType = ShelfDataType.resource
    
    
    return DownloadResourceObservable.init(item: combineEntity)
}
