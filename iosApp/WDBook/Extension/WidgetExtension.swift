//
//  WidgetExtension.swift
//  WDBook
//
//  Created by 杜文泽 on 2021/5/13.
//  Copyright © 2021 WeDevote Bible. All rights reserved.
//

import SwiftUI
import shared

extension WidgetContainerCombineEntity {
    var viewParamTypeKey:String?{
        return viewParamDic()["typeKey"]
    }
    
    func viewParamDic() -> [String:String]{
        //测试数据
//        return (detailEntityList?[0].widgetViewParam.split{[Character("\n"), Character("\r"), Character("\r\n"),Character(" ")].contains($0)}.compactMap { (line) -> (String,String)? in
//            let param = line.split(separator: "=")
//            if param.count > 1,
//               let key = param.first,
//               let value = param.last{
//                return (String(key),String(value))
//            }
//            return nil
//        }.reduce([String:String]()) { (result, oneParam) -> [String:String] in
//            var resultDic = result
//            resultDic[oneParam.0] = oneParam.1
//            return resultDic
//        }) as! [String : String]
        return detailEntityList?.first?.paramsMap as? [String : String] ?? [:]
    }
    
    func widgetView(container:WidgetContainerCombineEntity) -> some View{
        Group<AnyView>{
            switch container.containerKey {
            case "sections":
                return AnyView(MenuWidget(containerEntity: container))
            case "upDownItems":
                return AnyView(UpDownWidget(containerEntity: container))
            case "banners":
                return AnyView(BannerWidgetPageView(containerEntity: container))
            case "leftRightItems":
                return AnyView(LeftRightWidget(containerEntity: container))
            default:
                return AnyView(EmptyView())
            }
        }
    }
}

extension WidgetContainerCombineEntity:Identifiable{
    public var id: Int64 { Int64(containerId) }
}

private var WidgetContainerCombineEntity_SuggestProductList_KEY: UInt8 = 0
extension WidgetContainerCombineEntity{
    var suggestProductList:SuggestProductList{
        set{
            objc_setAssociatedObject(self, &WidgetContainerCombineEntity_SuggestProductList_KEY, newValue, .OBJC_ASSOCIATION_RETAIN_NONATOMIC)
        }
        get{
            return objc_getAssociatedObject(self, &WidgetContainerCombineEntity_SuggestProductList_KEY) as? SuggestProductList ?? SuggestProductList()
        }
    }
}

extension NewWidgetDetailEntity: Identifiable {
    public var id: Int64 { Int64(detailId) }
    public var imagePath: String { ImageManager.getImageUrl(imgPath) }
    public var paramDic:[String:String]{
        return widgetViewParam.components(separatedBy: "\n")
            .reduce([String:String]()) { (result, paramValueStr) -> [String:String] in
            let paramValueArr = paramValueStr.components(separatedBy: "=")
            if paramValueArr.count >= 2 {
                var r = result
                r[paramValueArr[0]] = paramValueArr[1]
                return r
            }else{
                return result
            }
        }
    }
    
    public var paramCategoryID:Int64 {
        guard widgetViewId == "categoryView" else {
            return 0
        }
        let paramsDic = paramDic
        guard let categoryIdStr = paramsDic["categoryId"],!categoryIdStr.isEmpty,let categoryId = Int64(categoryIdStr) else {
            return 0
        }
        return categoryId
    }
}



