//
//  EditorDetailsEntity+Extension.swift
//  WDBook
//
//  Created by <PERSON><PERSON> <PERSON><PERSON> on 2023/9/8.
//  Copyright © 2023 WeDevote Bible. All rights reserved.
//

import Foundation
import shared

extension EditorDetailsEntity{
    func copy(entity:EditorDetailsEntity){
        editorId = entity.editorId
        name = entity.name
        picture = entity.picture
        description0 = entity.description0
        url = entity.url
        type = entity.type
        lastUpdateTime = entity.lastUpdateTime
    }
}
