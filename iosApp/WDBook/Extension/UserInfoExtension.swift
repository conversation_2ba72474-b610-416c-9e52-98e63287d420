//
//  UserInfoExtension.swift
//  WDBook
//
//  Created by 杜文泽 on 2021/5/21.
//  Copyright © 2021 WeDevote Bible. All rights reserved.
//

import Foundation
import shared

class UserInfo:ObservableObject{
    @Published var avatarPath:String?
    @Published var nickname:String?
    @Published var userId:String?
    @Published var email:String = ""
    @Published var mobile:String = ""
    @Published var headerImageUrl:String = ""
    
    @Published var mobileForSecurity:String = ""
    
    init(){}
    
    convenience init(entity:UserInfoEntity) {
        self.init()
        avatarPath = entity.avatarPath
        nickname = entity.nickname
        userId = entity.userId
        email = entity.email ?? ""
        mobile = entity.mobile ?? ""
        headerImageUrl = ImageManager.getImageUrl(avatarPath)
        mobileForSecurity = replaceMidCharsWithAsterisk(mobile)
    }
    
    func replaceMidCharsWithAsterisk(_ sourceStr: String) -> String {
        let str = sourceStr.replacingOccurrences(of: "+", with: "")
        let length = str.count
        guard length > 4 else {
            return str
        }
        
        var midStartIndex = str.index(str.startIndex, offsetBy: (length - 4) / 2)
        if length % 2 > 0{
            midStartIndex = str.index(after: midStartIndex) // 向后移动一个位置
        }
        let midEndIndex = str.index(midStartIndex, offsetBy: 4)
            
        let newStr = str.replacingCharacters(in: midStartIndex..<midEndIndex, with: "****")
        if sourceStr.hasPrefix("+"){
            return "+" + newStr
        }
        return newStr
    }

}
