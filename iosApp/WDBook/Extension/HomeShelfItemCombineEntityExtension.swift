//
//  HomeShelfItemCombineEntityExtension.swift
//  WDBook
//
//  Created by ka<PERSON> zhou on 2023/1/30.
//  Copyright © 2023 WeDevote Bible. All rights reserved.
//

import Foundation
import shared

extension HomeShelfItemCombineEntity:Identifiable{
    public var id: String{
        if let e = self.bookItemEntity{
            return e.itemId
        }else if let e2 = self.archiveEntity{
            return e2.clientArchiveId
        }else{
            return ""
        }
    }
}
