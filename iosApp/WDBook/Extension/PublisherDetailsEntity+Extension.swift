//
//  PublisherDetailsEntity+Extension.swift
//  WDBook
//
//  Created by <PERSON><PERSON> <PERSON><PERSON> on 2023/9/12.
//  Copyright © 2023 WeDevote Bible. All rights reserved.
//

import Foundation
import shared

extension PublisherDetailsEntity{
    func copy(entity:PublisherDetailsEntity){
        publisherId = entity.publisherId
        name = entity.name
        picture = entity.picture
        description0 = entity.description0
        url = entity.url
        type = entity.type
    }
}
