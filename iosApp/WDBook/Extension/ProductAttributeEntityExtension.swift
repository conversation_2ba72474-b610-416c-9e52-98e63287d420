//
//  File.swift
//  WDBook
//
//  Created by 杜文泽 on 2021/6/7.
//  Copyright © 2021 WeDevote Bible. All rights reserved.
//

import Foundation
import shared

extension ProductAttributeEntity:Identifiable {
    var valuesDesc:String {
        if valueEntityList == nil {
            return ""
        }
        
        return valueEntityList!.reduce("") { (result, entity) -> String in
            if result.isEmpty {
                return (entity as! AttributeValueEntity).name
            }else{
                return result + " " + (entity as! AttributeValueEntity).name
            }
        }
    }
}

