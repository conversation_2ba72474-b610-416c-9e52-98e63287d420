//
//  File.swift
//  WDBook
//
//  Created by 杜文泽 on 2021/6/8.
//  Copyright © 2021 WeDevote Bible. All rights reserved.
//

import Foundation
import shared

extension ProductEntity: Identifiable {
    var authorNames:String {
        if let list = authorList as? [AuthorEntity] , list.count > 0 {
            return getAuthorNames(authorEntitites: list)
        }
        return ""
    }
    
    var categories:String {
        if let list = categoriesList as? [AuthorEntity] , list.count > 0 {
            return getCategoryNames(categories: list)
        }
        return ""
    }
    
    var currencyText:String{
        if currency == "USD"{
            return "$"
        }
        return currency
    }
    
    var isPurchased:Bool{
        purchased == 1
    }
    
    func hasActivity() -> Bool{
        activitiesList?.count > 0
    }
    
    var getDiscount:Double{
        let actdiscount = activityDiscount
        if actdiscount > 0{
            return actdiscount
        }
        return Double(discount)
    }
    
    //活动价
    var activityAmount:Double{
        Double((activitiesList as? [ActivityEntity] ?? []).filter{$0.productId == productId}.first?.amount ?? 0.0)
    }
    
    var activityDiscount: Double{
        Double((activitiesList as? [ActivityEntity] ?? []).filter{$0.productId == productId}.first?.discount ?? 0.0)
    }
    
    var activityPriceCNY: Double{
        Double((activitiesList as? [ActivityEntity] ?? []).filter{$0.productId == productId}.first?.amountCNY ?? 0.0)
    }
}

