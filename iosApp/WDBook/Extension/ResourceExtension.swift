//
//  ResourceExtension.swift
//  WDBook
//
//  Created by 杜文泽 on 2021/5/21.
//  Copyright © 2021 WeDevote Bible. All rights reserved.
//

import Foundation
import shared

extension PurchasedResourceEntity {
    var imageUrl: String {
        return ImageManager.getImageUrl(cover)
    }
    
    var authorNames: String {
        if authorList?.count > 0 {
            return getAuthorNames(authorEntitites: authorList!)
        }
        return ""
    }
    
    var fileId: String {
        return resourceDownloadInfo.fileId ?? ""
    }
    
    var md5: String {
        return resourceDownloadInfo.downloadInfo?.md5 ?? ""
    }
}

extension OrderEntity {
    var orderTitle: String {
        Log.d("\(titles)")
        if titles.count == 0 {
            return ""
        } else if titles.count == 1 {
            return titles.firstObject as! String
        } else {
            return "%@ 等%lld件".localizedFormat((titles.firstObject as! String),titles.count)
        }
    }
    
    var currencyText:String{
        if currency == "USD"{
            return "$"
        }
        return currency
    }
}

extension OrderProductEntity{
    var authorNames:String{
        (authorList as? [String] ?? []).reduce("") { $0 == "" ? $1 : $0 + ",\($1)" }
    }
}

extension ResourceDownloadInfo {
    var progress: Float {
        if downloadInfo != nil {
            return downloadInfo!.progress
        }
        return 0.0
    }
}

extension DownloadInfo {
    var progress: Float {
        return fileSize > 0 ? Float(downloadSize) / Float(fileSize) : 0.0
    }
}

extension DownloadDataEntity {
    var progress: Float {
        return fileSize > 0 ? Float(downloadSize) / Float(fileSize) : 0.0
    }
}

private var BookNoteCountEntity_AuthorNames_KEY: UInt8 = 0
extension BookNoteCountEntity {
    var authorNames: String {
        if authorEntityList?.count > 0 {
            return getAuthorNames(authorEntitites: authorEntityList as! [AuthorEntity])
        }else{
            return objcAuthorNames
        }
    }
    
    var objcAuthorNames:String{
        set{
            objc_setAssociatedObject(self, &BookNoteCountEntity_AuthorNames_KEY, newValue, .OBJC_ASSOCIATION_COPY)
        }
        get{
            return objc_getAssociatedObject(self, &BookNoteCountEntity_AuthorNames_KEY) as? String ?? ""
        }
    }
}
