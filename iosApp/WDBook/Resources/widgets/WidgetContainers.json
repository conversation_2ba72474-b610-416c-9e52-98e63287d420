[{"containerId": 6, "market": "zh-Hans", "homeType": "1", "widgetKey": "recentRead", "widgetType": "widget", "containerTitle": "最近阅读", "containerVieId": "", "containerViewParam": "", "imgPath": "", "displayOrder": 1, "status": 1, "publishId": 1}, {"containerId": 7, "market": "zh-Hans", "homeType": "1", "widgetKey": "recentItems", "widgetType": "widget", "containerTitle": "近期上架", "containerVieId": "", "containerViewParam": "dataSource=suggestProducts\ntypeKey=recent", "imgPath": "", "displayOrder": 1, "status": 1, "publishId": 2}, {"containerId": 8, "market": "zh-Hans", "homeType": "2", "widgetKey": "adContainer", "widgetType": "container", "containerTitle": "", "containerVieId": "", "containerViewParam": "", "imgPath": "", "displayOrder": 1, "status": 1, "publishId": 3}, {"containerId": 9, "market": "zh-Hans", "homeType": "2", "widgetKey": "categoryC<PERSON>r", "widgetType": "container", "containerTitle": "", "containerVieId": "", "containerViewParam": "", "imgPath": "", "displayOrder": 1, "status": 1, "publishId": 4}, {"containerId": 10, "market": "zh-Hans", "homeType": "2", "widgetKey": "packageItems", "widgetType": "widget", "containerTitle": "打包优惠", "containerVieId": "", "containerViewParam": "dataSource=suggestProducts\ntypeKey=discount", "imgPath": "", "displayOrder": 1, "status": 1, "publishId": 5}]