//
//  CountryCode.swift
//  WDBook
//
//  Created by <PERSON><PERSON> <PERSON><PERSON> on 2023/4/19.
//  Copyright © 2023 WeDevote Bible. All rights reserved.
//

import Foundation

class CountryCode:Decodable,ObservableObject{
    @Published var name:String = ""
    @Published var dial_code:String = ""
    @Published var code:String = ""
    
    @Published var id:String = ""
    @Published var localeName:String = ""
    
    enum CodingKeys: String, CodingKey {
        case name
        case dial_code
        case code
    }
    
    required init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        name = try container.decode(String.self, forKey: .name)
        dial_code = try container.decode(String.self, forKey: .dial_code)
        code = try container.decode(String.self, forKey: .code)
    }
    
    init() {
        
    }
    
    func clone()->CountryCode{
        var o = CountryCode()
        o.name = name
        o.dial_code = dial_code
        o.code = code
        o.id = id
        o.localeName = localeName
        return o
    }
}
