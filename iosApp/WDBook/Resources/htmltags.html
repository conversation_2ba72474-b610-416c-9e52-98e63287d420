<!DOCTYPE html>
<html lang="en-US">
    
    <head>
      <title>My First HTML</title>
      <meta charset="UTF-8">
      <meta name="description" content="Free Web tutorials">
      <meta name="keywords" content="HTML,CSS,XML,JavaScript">
      <meta name="author" content="John Doe">

    </head>
    
<body>
    <p>大多数中文护教学书籍源自于英文著述的翻译，见解固然精辟，但缺乏在中国文化氛围下的“处境化”(contextualization)，在面对中国文化背景的慕道友时，会有“所答非所问”的情况。</p><p>此外，不少专题性护教学书籍涵盖面窄，而综合性的护教学教科书较少，内容难以面面具到；或探讨时引用的材料过时，对于当代的新议题－例如后现代思想或最新科技的挑战等问题，很少有中文的护教学书籍深入地讨论。</p><p>本书着重在上述几方面做开创性地弥补，以解答各种似是而非的“迷思”(Myths)。</p><p>先阐释了四种不同的护教途径或方法，内容既关注中国文化“处境化”以与基督教信仰建立桥梁，同时针对新思潮（如后现代、新无神论者等）提出回应，也论到佛教、伊斯兰教与基督教的对比。</p><p>本书虽未全面涵盖所有基督教的异端并加以辩析，也未更深入对各种异教或哲学作比较，但作者衷心期望本书能提供面面顾到，又提纲挈领的护教学专书，以帮助基督徒回应福音朋友的问题。</p>
    ------header-------<p>
    style能使用，style link,base有效。<p>
    webview设置meta content width=device-width, initial-scale=1.0 可适配手机屏幕但是attributedtext不需要设置此项。
    ⁉️ 未尝试：title，meta charset, content。
  <p>

------样式--------
    <h2>HTML Image</h2>
  
    网络图
    <img src="https://www.w3schools.com/html/img_girl.jpg" alt="Girl in a jacket" width="500" height="600">
    <img src="https://www.w3schools.com/html/img_chania.jpg" alt="Flowers in Chania" width="460" height="345">
    <img src="https://www.w3schools.com/html/img_girl.jpg" alt="Girl in a jacket" style="width:500px;height:600px;">
   
<div>
    You can specify background images<br>
    for any visible HTML element.<br>
    In this example, the background image<br>
    is specified for a div element.<br>
    By default, the background-image<br>
    will repeat itself in the direction(s)<br>
    where it is smaller than the element<br>
    where it is specified. (Try resizing the<br>
    browser window to see how the<br>
    background image behaves.
    </div>
    
    
</body>
</html>
