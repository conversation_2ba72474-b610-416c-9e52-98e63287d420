//
//  Resources.swift
//  WDBook
//
//  Created by <PERSON> on 2020/8/28.
//  Copyright © 2020 WeDevote Bible. All rights reserved.
//

import Foundation

#if DEBUG

let test_json_WidgetContainers = "WidgetContainers.json"
let test_json_TabStoreContainers = "TabStoreContainers.json"
let test_json_BannerContainer = "BannerContainer.json"
let test_json_CategoryContainer = "CategoryContainer.json"
let test_json_DiscountsContainer = "DiscountsContainer.json"
let test_json_DiscountsContainerOne = "DiscountsContainerOne.json"
let test_json_DiscountsProductList = "DiscountsProductList.json"

let test_json_TabHomeContainers = "TabHomeContainers.json"
let test_json_RecentItemsContainer = "RecentItemsContainer.json"
let test_json_RecentReadContainer = "RecentReadContainer.json"

let test_json_CategoryList = "CategoryList.json"
let test_json_ProductList = "ProductList.json"
let test_json_ProductDetailsOne = "ProductDetailsOne.json"
let test_json_ProductDetailsMutli = "ProductDetailsMutli.json"

let test_json_ShelfList = "ShelfList.json"
let test_json_ShelfListLess = "ShelfListLess.json"
let test_json_PurchasedList = "PurchasedList.json"

let test_json_PaymentAmount = "PaymentAmount.json"
let test_json_WalletTranscations = "WalletTranscations.json"
let test_json_OrderList = "OrderList.json"
let test_json_editor_details = "editor_details.json"
let test_json_editor_details_2 = "editor_details_2.json"
let test_json_editor_details_3 = "editor_details_3.json"
let test_json_publisher_details = "publisher_details.json"

//let test_WidgetContainers:[WidgetContainer] = load(test_json_WidgetContainers)
//let test_TabStoreContainers:[WidgetContainer] = load(test_json_TabStoreContainers)
//let test_BannerContainer:WidgetContainer = load(test_json_BannerContainer)
//let test_CategoryContainer:WidgetContainer = load(test_json_CategoryContainer)
//let test_DiscountsContainer:WidgetContainer = load(test_json_DiscountsContainer)
//let test_DiscountsContainerOne:WidgetContainer = load(test_json_DiscountsContainerOne)
//let test_DiscountsProductList:SuggestProductsResult = load(test_json_DiscountsProductList)
//
//let test_TabHomeContainers:[WidgetContainer] = load(test_json_TabHomeContainers)
//let test_RecentItemsContainer:WidgetContainer = load(test_json_RecentItemsContainer)
//let test_RecentReadContainer:WidgetContainer = load(test_json_RecentReadContainer)
//
//let test_CategoryList:[Category] = load(test_json_CategoryList)
//let test_ProductList:ProductListResult = load(test_json_ProductList)
//
//let test_PaymentAmount:PaymentAmount = load(test_json_PaymentAmount)
//func test_WalletTranscations() -> [Recharge]{
//    return load(test_json_WalletTranscations)
//}
//let test_OrderList:[Order] = load(test_json_OrderList)

let test_QuestionList:[HelpQuestion] = load("QuestionList.json")
let test_Article:HelpArticle = load("HelpArticle.json")
let test_Feedbacks:[Feedback] = load("FeedbackList.json")
let test_FeedbackTags:[FeedbackTag] = load("FeedbackTags.json")
let test_ReaderFeedbackTags:[FeedbackTag] = load("ReaderFeedbackTags.json")
let test_FeedbackDetails:[FeedbackDetail] = load("FeedbackDetails.json")
let test_notificationList:[NotificationListItem] = load("notification_list.json")
let test_notificationDetails:NotificationDetails = load("notification_details.json")
let test_coupons:[Coupon] = load("Coupons.json")
let test_promotions:[Promotion] = load("Promotions.json")
let test_promotionDetails:PromotionDetails = load("PromotionDetails.json")
let test_paymentAmount:PaymentAmountResult = load("get_payment_amount.json")
let test_unpaidorder:UnPaidModel = load("unpaidorder.json")


#endif

struct Resources{
    static func loadCountryCodeList()->[CountryCode]{
        load("diallingcode.json")
    }
}


func load<T: Decodable>(_ filename: String) -> T {
    let data: Data
    
    guard let file = Bundle.main.url(forResource: filename, withExtension: nil)
    else {
        fatalError("Couldn't find \(filename) in main bundle.")
    }
    
    do {
        data = try Data(contentsOf: file)
    } catch {
        fatalError("Couldn't load \(filename) from main bundle:\n\(error)")
    }
    
    do {
        let decoder = JSONDecoder()
        decoder.dateDecodingStrategy = .iso8601
        return try decoder.decode(T.self, from: data)
    } catch {
        fatalError("Couldn't parse \(filename) as \(T.self):\n\(error)")
    }
}

func load<T: Decodable>(_ type: T.Type, from filename: String) -> T {
    let data: Data
    
    guard let file = Bundle.main.url(forResource: filename, withExtension: nil)
    else {
        fatalError("Couldn't find \(filename) in main bundle.")
    }
    
    do {
        data = try Data(contentsOf: file)
    } catch {
        fatalError("Couldn't load \(filename) from main bundle:\n\(error)")
    }
    
    do {
        let decoder = JSONDecoder()
        decoder.dateDecodingStrategy = .iso8601
        return try decoder.decode(T.self, from: data)
    } catch {
        fatalError("Couldn't parse \(filename) as \(T.self):\n\(error)")
    }
}

func loadJsonString(from filename: String) -> String {
    let data: Data
    
    guard let file = Bundle.main.url(forResource: filename, withExtension: nil)
    else {
        fatalError("Couldn't find \(filename) in main bundle.")
    }
    
    do {
        data = try Data(contentsOf: file)
        return String(data: data, encoding: .utf8)!
    } catch {
        fatalError("Couldn't load \(filename) from main bundle:\n\(error)")
    }
    
    return ""
}

extension Bundle {
    func decode<T: Decodable>(_ type: T.Type, from file: String, dateDecodingStrategy: JSONDecoder.DateDecodingStrategy = .deferredToDate, keyDecodingStrategy: JSONDecoder.KeyDecodingStrategy = .useDefaultKeys) -> T {
        guard let url = self.url(forResource: file, withExtension: nil) else {
            fatalError("Failed to locate \(file) in bundle.")
        }

        guard let data = try? Data(contentsOf: url) else {
            fatalError("Failed to load \(file) from bundle.")
        }

        let decoder = JSONDecoder()
        decoder.dateDecodingStrategy = dateDecodingStrategy
        decoder.keyDecodingStrategy = keyDecodingStrategy

        do {
            return try decoder.decode(T.self, from: data)
        } catch DecodingError.keyNotFound(let key, let context) {
            fatalError("Failed to decode \(file) from bundle due to missing key '\(key.stringValue)' not found – \(context.debugDescription)")
        } catch DecodingError.typeMismatch(_, let context) {
            fatalError("Failed to decode \(file) from bundle due to type mismatch – \(context.debugDescription)")
        } catch DecodingError.valueNotFound(let type, let context) {
            fatalError("Failed to decode \(file) from bundle due to missing \(type) value – \(context.debugDescription)")
        } catch DecodingError.dataCorrupted(_) {
            fatalError("Failed to decode \(file) from bundle because it appears to be invalid JSON")
        } catch {
            fatalError("Failed to decode \(file) from bundle: \(error.localizedDescription)")
        }
    }
}

