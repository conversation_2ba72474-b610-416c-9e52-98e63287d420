//
//  DownloadRequest.swift
//  WDDownload
//
//  Created by <PERSON><PERSON> <PERSON><PERSON> on 07/12/2016.
//  Copyright © 2016 kevin. All rights reserved.

// ->ready: Returns true to indicate that the operation is ready to execute, or false if there are still unfinished initialization steps on which it is dependent.
// ->executing: Returns true if the operation is currently working on its task, or false otherwise.
// ->finished Returns true if the operation’s task finished execution successfully, or if the operation was cancelled. An NSOperationQueue does not dequeue an operation until finished changes to true, so it is critical to implement this correctly in subclasses to avoid deadlock.
//

import UIKit

import wdNetwork

public typealias DownloadTaskStartHandler = (_ task: DownloadTask) -> Void
public typealias DownloadTaskProgressHandler = (_ task: DownloadTask, _ progress: Float, _ writedBytes: Int64, _ totalBytes: Int64) -> Void
public typealias DownloadTaskFailHandler = (_ task: DownloadTask, _ error: Error) -> Void
public typealias DownloadTaskSuccessHandler = (_ task: DownloadTask) -> Void

/// 下载任务对象
/// Download task object
public class DownloadTask: NSObject {
    private var dataTask: URLSessionDataTask?
    private var outputStream: OutputStream?

    var taskID: String = ""
    var group: String?
    var urlString: String = ""
    var destFilePath = ""
    var cacheFilePath = ""

    var fileName: String = "" // 用于打印 use to pring

    /// true：not execute failedHandler callback.
    /// false：execute failedHandler callback.
    var isCantnotCallback: Bool = false

    private var progress: Progress = .init()

    private var startHandler: DownloadTaskStartHandler?
    private var progressHandler: DownloadTaskProgressHandler?
    private var failedHandler: DownloadTaskFailHandler?
    private var successHandler: DownloadTaskSuccessHandler?

    lazy var session: URLSession = {
        let configuration = URLSessionConfiguration.default
        configuration.protocolClasses = [WDUrlProtocol.self]
        NetworkUtils.configureCftHeaders(for: configuration)
        let session = URLSession(configuration: configuration, delegate: DownloadManager.shared.delegate, delegateQueue: OperationQueue.main)
        return session
    }()

//    public func urlSession(_ session: URLSession, didBecomeInvalidWithError error: Error?) {
//        print("didBecomeInvalidWithError:\(error)")
//    }
//

    public init(taskID: String) {
        self.taskID = taskID
        super.init()
    }

    // MARK: 控制

    public func start(urlString: String,
                      cacheFilePath: String,
                      destFilePath: String,
                      group: String? = nil,
                      startHandler: @escaping DownloadTaskStartHandler,
                      progressHandler: @escaping DownloadTaskProgressHandler,
                      successHandler: @escaping DownloadTaskSuccessHandler,
                      failHandler: @escaping DownloadTaskFailHandler)
    {
        self.urlString = urlString
        self.cacheFilePath = cacheFilePath
        self.destFilePath = destFilePath
        self.group = group
        self.startHandler = startHandler
        self.progressHandler = progressHandler
        self.successHandler = successHandler
        failedHandler = failHandler

        fileName = String(urlString.split(separator: "/").last!.split(separator: ".").first!)

        FilePath.checkOrCreateFile(filePath: cacheFilePath)

        let cacheFileSize = getFileSize(cacheFilePath: cacheFilePath)

        var url = URL(string: self.urlString)!

        if DownloadManager.shared.needUrlEncoding {
            let urlEncodingString = self.urlString.addingPercentEncoding(withAllowedCharacters: NSCharacterSet.urlQueryAllowed)
            url = URL(string: urlEncodingString!)!
        }

        var request = URLRequest(url: url)
        request.timeoutInterval = DownloadManager.shared.timeoutInterval
        request.setValue("bytes=\(cacheFileSize)-", forHTTPHeaderField: "Range")

        if DownloadManager.shared.isMultiSession {
            dataTask = session.dataTask(with: request) // 单独session，包含单独的一个task。超时的几率大大降低。
        } else {
            dataTask = DownloadManager.shared.session.dataTask(with: request) // 容易超时
        }

        dataTask?.taskID = taskID
        dataTask!.resume()

        progress.setUserInfoObject(progress.completedUnitCount, forKey: .fileCompletedCountKey)
    }

    public func suspend() {
        dataTask?.suspend()
    }

    public func resume() {
        dataTask?.resume()
    }

    public func cancel() {
        dataTask?.cancel()
    }

    public func stop() {
        isCantnotCallback = true
        dataTask?.cancel()
//        session.finishTasksAndInvalidate()
//        dataTask = nil
    }

    /// 停止运行，并且删除文件
    public func delete() {
        stop()
        if FileManager.default.fileExists(atPath: destFilePath) {
            try? FileManager.default.removeItem(atPath: destFilePath)
        }
        if FileManager.default.fileExists(atPath: cacheFilePath) {
            try? FileManager.default.removeItem(atPath: cacheFilePath)
        }
    }

    // MARK: 状态

    public var state: URLSessionTask.State? {
        if let task = dataTask {
            return task.state
        } else {
            return nil
        }
    }

    public var isRunning: Bool {
        return state == .running
    }

    public var isSuspended: Bool {
        return state == .suspended
    }

    public var isCanceling: Bool {
        return state == .canceling
    }

    public var isCompleted: Bool {
        return state == .completed
    }
}

// MARK: - download callback

extension DownloadTask: URLSessionDataDelegate {
    /// 下载前检测
    public func urlSession(_: URLSession, dataTask: URLSessionDataTask, didReceive response: URLResponse, completionHandler: @escaping (URLSession.ResponseDisposition) -> Void) {
        progress.completedUnitCount = 0
        progress.totalUnitCount = 0

        var contentLength: Int64 = 0
        if let response = response as? HTTPURLResponse,
           let contentLengthString = response.allHeaderFields["Content-Length"] as? String,
           let contentLengthBytes = Int64(contentLengthString)
        {
            contentLength = contentLengthBytes
        }

        var breakPoint: Int64 = 0
        // "bytes=24393258-" eg: bytes 10-395961/395962
        if let rangeHeader = dataTask.currentRequest?.allHTTPHeaderFields!["Range"] {
            let index = rangeHeader.index(rangeHeader.startIndex, offsetBy: 6)
            let endIndex = rangeHeader.firstIndex(of: "-")
            breakPoint = Int64(rangeHeader[index ..< endIndex!])!
        }

        DownloadLog.i("已下载文件大小Downloaded file size ：\(breakPoint), content-Lenght: \(contentLength)")
        progress.completedUnitCount = breakPoint
        progress.totalUnitCount = breakPoint + contentLength

        outputStream = OutputStream(toFileAtPath: cacheFilePath, append: true)
        outputStream?.open()

        completionHandler(.allow)

        DownloadLog.i("开始下载Start Download: \(String(describing: dataTask.currentRequest?.url?.absoluteString))")
        startHandler?(self)
    }

    // 下载进度
    public func urlSession(_: URLSession, dataTask _: URLSessionDataTask, didReceive data: Data) {
        progress.completedUnitCount += Int64((data as NSData).length)
        _ = data.withUnsafeBytes { outputStream?.write($0.baseAddress!.assumingMemoryBound(to: UInt8.self), maxLength: data.count) }
//        _ = data.withUnsafeBytes { outputStream?.write($0, maxLength: data.count) }

//        let s = String(format: "下载进度-- url=\(urlString) \n  , 已下载\(progress.completedUnitCount), 总大小\(progress.totalUnitCount)， %.2f", progress.fractionCompleted * 100)
        let s = "下载url: \(fileName) , 下载量completedUnitCount/totalUnitCount: \(progress.completedUnitCount)/\(progress.totalUnitCount)， 进度progress,\(DownloadFormatter.fractionDigits2.string(from: NSNumber(value: progress.fractionCompleted * 100)))%"
        DownloadLog.d(s)

        progressHandler?(self, Float(progress.fractionCompleted), progress.completedUnitCount, progress.totalUnitCount)
    }
}

public enum DownloadFormatter {
    static var fractionDigits2: NumberFormatter = {
        let f = NumberFormatter()
        f.minimumFractionDigits = 2 // 0
        f.maximumFractionDigits = 2 // 0
        f.numberStyle = .none // .decimal三位一个逗号
        return f
    }()
}

extension DownloadTask: URLSessionTaskDelegate {
    // 下载完成
    public func urlSession(_: URLSession, task _: URLSessionTask, didCompleteWithError error: Error?) {
        outputStream?.close()
        outputStream = nil
        if let e = error as? NSError {
            if e.domain == NSURLErrorDomain && e.code == -999 {
                // 用户取消不做任何操作. do nothings when user cancel this task
                if isCantnotCallback {
                    isCantnotCallback = false
                    return
                }
                DownloadLog.e("Download failed-\(urlString)--Cancel: \(e)")
            } else if e.domain == NSURLErrorDomain && e.code == -1001 {
                DownloadLog.e("Download failed-\(urlString)--Time Out: \(e)") // "The request timed out."
            } else if e.domain == NSURLErrorDomain && e.code == -1009 {
                DownloadLog.e("Download failed-\(urlString)--no network: \(e)") // "似乎已断开与互联网的连接。","The Internet connection appears to be offline." ---无网络情况，点击下载。
            } else if e.domain == NSURLErrorDomain && e.code == -1005 {
                DownloadLog.e("Download failed-\(urlString)--offline-close the wifi: \(e)") // "网络连接已中断。" --关闭wifi。
            } else {
                DownloadLog.e("Download failed-\(urlString)--error: \(e)")
            }

            isCantnotCallback = false

            failedHandler?(self, error!)
        } else {
            isCantnotCallback = false
            do {
                if FileManager.default.fileExists(atPath: destFilePath) {
                    try FileManager.default.removeItem(atPath: destFilePath)
                }
                // Just create last path dir
                FilePath.checkOrCreateDic(dicPath: (destFilePath as NSString).deletingLastPathComponent)

                try FileManager.default.moveItem(atPath: cacheFilePath, toPath: destFilePath)

                DownloadLog.i("Download Complete-\(urlString), saved path：\(destFilePath)")

                successHandler?(self)

            } catch let error as NSError {
                DownloadLog.e("Download failed--\(urlString)--error relate file--\(error)")
                failedHandler?(self, error)
            }
        }
    }
}

private var URLSessionTask_TaskIdentifier: String = "URLSessionTask_TaskIdentifier"
extension URLSessionTask {
    var taskID: String? {
        set {
            objc_setAssociatedObject(self, &URLSessionTask_TaskIdentifier, newValue, objc_AssociationPolicy.OBJC_ASSOCIATION_RETAIN_NONATOMIC)
        }
        get {
            return objc_getAssociatedObject(self, &URLSessionTask_TaskIdentifier) as? String
        }
    }
}
