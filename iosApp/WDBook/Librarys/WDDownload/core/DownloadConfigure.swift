//
//  DownloadConfigure.swift
//  WDDownload
//
//  Created by <PERSON><PERSON><PERSON> on 2018/4/12.
//  Copyright © 2018 kai zhou. All rights reserved.
//

import Foundation

public func getFileSize(cacheFilePath:String)->Int64{
    var fileSize:Int64 = 0
    //try resumeData = Data(contentsOf: URL(fileURLWithPath:resumeDataPath))
    if let fileInfo = try? FileManager().attributesOfItem(atPath: cacheFilePath),
        let fileLength = fileInfo[.size] as? Int64{
        let createdData = fileInfo[.creationDate] as! Date
        let modificationDate = fileInfo[.modificationDate] as! Date
        
        DownloadLog.i("Downloaded file：size:\(fileLength), created date:\(createdData), modify date：\(modificationDate)")
        fileSize = fileLength
    }
    return fileSize
}

