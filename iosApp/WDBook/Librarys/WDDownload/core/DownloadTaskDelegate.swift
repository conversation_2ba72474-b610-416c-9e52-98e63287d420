//
//  DownloadDelegate.swift
// DownloadManager
//
//  Created by <PERSON><PERSON><PERSON> on 2018/4/12.
//  Copyright © 2018 <EMAIL>. All rights reserved.
//

import Foundation
class DownloadTaskDelegate:NSObject {
    private weak var manager: DownloadManager?
    init(_ manager:DownloadManager) {
        self.manager = manager
    }
    
}

extension DownloadTaskDelegate:URLSessionDataDelegate {
    public func urlSession(_ session: URLSession, dataTask: URLSessionDataTask, didReceive response: URLResponse, completionHandler: @escaping (URLSession.ResponseDisposition) -> Void) {
        guard let manager = manager,
            let taskID = dataTask.taskID,
            let downloadTask = manager.findTask(taskID: taskID),
            let response = response as? HTTPURLResponse else {
                return
        }
        downloadTask.urlSession(session, dataTask: dataTask, didReceive: response, completionHandler: completionHandler)
    }

    public func urlSession(_ session: URLSession, dataTask: URLSessionDataTask, didReceive data: Data) {
        guard let manager = manager,
            let taskID = dataTask.taskID,
            let downloadTask = manager.findTask(taskID: taskID) else {
                return
        }
        downloadTask.urlSession(session, dataTask: dataTask, didReceive: data)
    }
}

extension  DownloadTaskDelegate:URLSessionTaskDelegate{
    public func urlSession(_ session: URLSession, task: URLSessionTask, didCompleteWithError error: Error?) {
        guard let manager = manager,
        let taskID = task.taskID,
        let downloadTask = manager.findTask(taskID: taskID)else {
                return
        }
        downloadTask.urlSession(session, task: task, didCompleteWithError: error)
        
        manager.remove(taskID: taskID)
    }
}
