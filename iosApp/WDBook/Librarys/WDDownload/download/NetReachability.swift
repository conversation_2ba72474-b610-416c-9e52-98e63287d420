//
//  NetReachability.swift
//  WDDownload
//
//  Created by <PERSON> on 2020/8/11.
//  Copyright © 2020 kai zhou. All rights reserved.
//


import Foundation
import Network
import Reachability
import NetworkExtension

public class NetReachability{
    public static let reachability = try! Reachability()
    public static func startMonitor(){
        reachability.whenReachable = { reachability in
            if reachability.connection == .wifi {
                Log.d("Reachable via WiFi")
            } else {
                Log.d("Reachable via Cellular")
            }
        }
        reachability.whenUnreachable = { _ in
            Log.d("Not reachable")
        }
        
        do {
            try reachability.startNotifier()
        } catch {
            Log.d("Unable to start notifier")
        }
    }
    
    public static func stopMonitor(){
        reachability.stopNotifier()
    }
    
    public static func isReachability() -> Bool{
        return reachability.connection != .unavailable
    }
    
    public static func isWIFI() -> Bool{
        return reachability.connection == .wifi
    }
    
    public static func isCellular() -> Bool{
        return reachability.connection == .cellular
    }
}

public class NetMonitor {
    private static let monitor = NWPathMonitor()
    private static var isMonitoring = false
    
    public static let reachabilityChangedNotification = NSNotification.Name("reachabilityChanged")
    
    public static var currentPath:Network.NWPath{
        monitor.currentPath
    }
    
    public static func startMonitor() {
        guard !isMonitoring else { return }
        
        monitor.pathUpdateHandler = { path in
//            手机无卡，无论wifi开闭。值都是status == .satisfied和 isExpensive = false,和 isConstrained = false
//            if path.status == .satisfied {
//                if path.isExpensive {
//                    Log.d("Reachable via Cellular")
//                } else {
//                    Log.d("Reachable via WiFi")
//                }
//            } else {
//                Log.d("Not reachable")
//            }
            
            NotificationCenter.default.post(name: reachabilityChangedNotification, object: nil)
        }
        
        let queue = DispatchQueue(label: "com.example.networkmonitor")
        monitor.start(queue: queue)
        
        isMonitoring = true
    }
    
    public static func stopMonitor() {
        guard isMonitoring else { return }
        
        monitor.cancel()
        isMonitoring = false
    }
    
    public static func isConnectedToWiFiAndWiredEthernet() -> Bool {
        let path = monitor.currentPath
        return path.availableInterfaces.contains { interface in
            interface.type == .wifi || interface.type == .wiredEthernet
        }
    }
    
    public static func isConnectedToWiFi() -> Bool {
        let path = monitor.currentPath
        return path.availableInterfaces.contains { interface in
            interface.type == .wifi
        }
    }
    
    public static func isConnectedToCellular() -> Bool {
        let path = monitor.currentPath
        return path.availableInterfaces.contains { interface in
            interface.type == .cellular
        }
    }
    
    //other状态可能包括vpn
    public static func isConnectedToOtherNetwork() -> Bool {
        let path = monitor.currentPath
        return !path.availableInterfaces.isEmpty
    }
}

struct WDReachability{
    func isWDAPIReachability(hostURL:String) -> Bool{
        if let candidate = URL(string: hostURL){
           var request = URLRequest(url: candidate)
            request.httpMethod = "HEAD"
            
            var result = false
            let semaphore = DispatchSemaphore(value: 0)
            let session = URLSession(configuration: URLSessionConfiguration.default)
            let task = session.dataTask(with: request) { data, response, error in
                if error == nil{
                    result = true
                }
                semaphore.signal()
            }
            task.resume()
            _ = semaphore.wait(timeout: .distantFuture)
            return result
        }else{
            return false
        }
    }
}
