//
//  DownloadManager.swift
//  WDDownload
//
//  Created by <PERSON><PERSON> <PERSON>hou on 06/12/2016.
//  Copyright © 2016 kevin. All rights reserved.
//

import UIKit
import shared

public enum DownloadFailReason:String{
    case unknown
    case timeout
    case noNetwork
    case fullSpace
    case md5Verification
    case cancel
    case overRetries
}

import CocoaLumberjack

/// 通知：object 是 DownloadInfo
public let Noti_Download_Add_To_Downlaod_List = Notification.Name(rawValue: "Noti_Download_Add_To_Downlaod_List")
public let Noti_Download_Start = Notification.Name(rawValue: "Noti_Download_Start")
public let Noti_Download_Progress = Notification.Name(rawValue: "Noti_Download_Progress")
public let Noti_Download_Complete = Notification.Name(rawValue: "Noti_Download_Complete")
public let Noti_Download_Fails = Notification.Name(rawValue: "Noti_Download_Fails")
public let Noti_Download_Waiting = Notification.Name(rawValue: "Noti_Download_Waiting")
public let Noti_Download_Pause = Notification.Name(rawValue: "Noti_Download_Pause")
public let Noti_Download_Delete = Notification.Name(rawValue: "Noti_Download_Delete")

///磁盘空间即将满: object 是 DownloadInfo。 无论单个DownloadInfo还是批量DownloadInfo通知，都追加发一个object == nil的通知。
///DownloadInfo: whenever how many notification with DownloadInfo object be post, the notification with nil DownloadInfo will be post after thems.
public let Noti_Download_FullSpace = Notification.Name(rawValue: "Noti_Download_FullSpace")

//public protocol WDDownloadDelegate:class{
//    func md5FromFile(filePath:URL) -> String?
//}

public class WDDownloadManager:NSObject {
    
    public static let shared:WDDownloadManager = WDDownloadManager()
//    public weak var delegate:WDDownloadDelegate?
    
    public var canAutoDownload = true
    
    /// 同时最大下载数 Concurrency downloading count
    public let MAX_CONCURRENCE_COUNT = 2
    
    /// md5重试次数  retry count when md5 fails
    public var maxRetryCount = 3
    
    /// 下载失败重试逻辑
    let RETRY_DURATION_DTIMEINTERVAL:TimeInterval = 5.0
    let MAX_RETRY_COUNT = 3
    var retryDic = Dictionary<String,Int>() //resourceid: 重试次数。从0开始，大于3结束
    struct DownloadCacheInfo{
        let key:String
        let url:String
        let destinationFilePath:String
        let md5:String?
    }
    var downloadCache = Dictionary<String,DownloadCacheInfo>()
    let cacheLock = NSLock()
    
    private let syncQueue = DispatchQueue(label: "com.wdbook.downloadManager", attributes: .concurrent)
    
    // 设置和获取 retryDic
    func setRetryCount(_ count: Int, forResourceId resourceId: String) {
        syncQueue.async(flags: .barrier) {
            self.retryDic[resourceId] = count
            DownloadLog.d("Set retryDic[\(resourceId)] = \(count) on thread \(Thread.current)")
        }
    }

    func getRetryCount(forResourceId resourceId: String) -> Int? {
        syncQueue.sync {
            self.retryDic[resourceId]
        }
    }

    // 设置和获取 lastPostTimeIntervalDic
    func setProgressTimeInterval(_ time: TimeInterval, forKey key: String) {
        syncQueue.async(flags: .barrier) {
            self.lastPostTimeIntervalDic[key] = time
            DownloadLog.d("Set lastPostTimeIntervalDic[\(key)] = \(time) on thread \(Thread.current)")
        }
    }

    func getProgressTimeInterval(forKey key: String) -> TimeInterval? {
        syncQueue.sync {
            self.lastPostTimeIntervalDic[key]
        }
    }

    /// 下载进度刷新间隔最短时间
    /// min timeinterval between two download progress delegate
    public var progressPostTimeInterval:TimeInterval = 0.3
    
    public var lastPostTimeIntervalDic:[String:Double] = [:]
    
    public var allowCellularDownload:Bool = false{
        didSet{
            if allowCellularDownload != oldValue{
                checkReabilityChanged()
            }
        }
    }
    
    private override init() {
        super.init()
        //打开监听
        NetReachability.startMonitor()
        NetMonitor.startMonitor()
        addNotifications()
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self)
    }
    
    public var showLogger:Bool = true {
        didSet{
            DownloadLog.showLogger = showLogger
        }
    }
    
    public func configLogger(level:DDLogLevel? = .info, timeFormatter:(()->String)? = nil){
        showLogger = true
        DownloadLog.config(level: level, timeFormatter: timeFormatter)
    }
    
    //MARK: Save and post notification
    func failedStatusAndPost(downloadInfo:DownloadDataEntity, reason:DownloadFailReason) {
        downloadInfo.downloadStatus = .error
        WDBookDownloadSDK.shared.updateDownloadStatus(fileId: downloadInfo.fileId ?? "", status: .error)
        DispatchQueue.main.async {
            NotificationCenter.default.post(name: Noti_Download_Fails, object: downloadInfo, userInfo: ["reason":reason])
        }
    }
    
    func pauseStatusAndPost(downloadInfo:DownloadDataEntity){
        downloadInfo.downloadStatus = .pause
        DispatchQueue.main.async {
            WDBookDownloadSDK.shared.updateDownloadStatus(fileId: downloadInfo.fileId ?? "", status: .pause)
        }
    }
    
    
    func waitingStatusAndPost(downloadInfo:DownloadDataEntity){
        downloadInfo.downloadStatus = .wait
        WDBookDownloadSDK.shared.updateDownloadStatus(fileId: downloadInfo.fileId ?? "", status: .wait)
        DispatchQueue.main.async {
            NotificationCenter.default.post(name: Noti_Download_Waiting, object: downloadInfo, userInfo: nil)
        }
    }

    func downloadingStatus(downloadInfo:DownloadDataEntity) {
        downloadInfo.downloadStatus = .downloading
        WDBookDownloadSDK.shared.updateDownloadingProgressStatus(fileId: downloadInfo.fileId ?? "", downloadSize: downloadInfo.downloadSize)
    }
    
    func downloadingStatusAndPost(downloadInfo:DownloadDataEntity) {
        downloadInfo.downloadStatus = .downloading
        WDBookDownloadSDK.shared.updateDownloadStatus(fileId: downloadInfo.fileId ?? "", status: downloadInfo.downloadStatus)
        WDBookDownloadSDK.shared.updateDownloadingProgressStatus(fileId: downloadInfo.fileId ?? "", downloadSize: downloadInfo.downloadSize)
        DispatchQueue.main.async {
            NotificationCenter.default.post(name: Noti_Download_Progress, object: downloadInfo, userInfo: nil)
        }
    }
    
    func completeStatusAndPost(downloadInfo:DownloadDataEntity){
        downloadInfo.downloadStatus = .complete
        WDBookDownloadSDK.shared.updateDownloadStatus(fileId: downloadInfo.fileId ?? "", status: .complete)
        DispatchQueue.main.async {
            NotificationCenter.default.post(name: Noti_Download_Complete, object: downloadInfo, userInfo: nil)
        }
    }

    func deleteAndPost(downloadInfo:DownloadDataEntity){
        WDBookDownloadSDK.shared.deleteDownloadItem(downloadDataEntity: downloadInfo)
        DispatchQueue.main.async {
            NotificationCenter.default.post(name: Noti_Download_Delete, object: downloadInfo, userInfo: nil)
        }
    }
}


