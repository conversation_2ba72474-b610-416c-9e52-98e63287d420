//
//  DownloadManager+Net.swift
//  WDDownload
//
//  Created by <PERSON> on 2020/8/11.
//  Copyright © 2020 kai zhou. All rights reserved.
//

import Foundation
import Reachability
import shared
import NetworkExtension

public let Noti_Download_UNFOUND_FILE = Notification.Name(rawValue: "Noti_Download_UNFOUND_FILE")


private var downloadTimerKey: Void?

public extension WDDownloadManager{
    var timer:Timer?{
        set{
            objc_setAssociatedObject(self, &downloadTimerKey, newValue, objc_AssociationPolicy.OBJC_ASSOCIATION_RETAIN_NONATOMIC)
        }
        get{
            return objc_getAssociatedObject(self, &downloadTimerKey) as? Timer
        }
    }
    
    //MARK: Notification
    func addNotifications(){
        NotificationCenter.default.addObserver(self, selector:  #selector(reachabilityChangedHandler(noti:)), name: NetMonitor.reachabilityChangedNotification, object: nil)
        
        //进入后台
        NotificationCenter.default.addObserver(self, selector: #selector(appEnterBackground(noti:)), name: UIApplication.didEnterBackgroundNotification, object: nil)
        
        //进入前台
        NotificationCenter.default.addObserver(self, selector: #selector(appEnterForeground(noti:)), name: UIApplication.willEnterForegroundNotification, object: nil)
        
        NotificationCenter.default.addObserver(self, selector: #selector(appWillTerminate(noti:)), name: UIApplication.willTerminateNotification, object: nil)
    }
    
    func removeNotifications(){
        NotificationCenter.default.removeObserver(self)
    }
    
    @objc func appEnterBackground(noti:Notification){
        debugPrint("+++++++++++++++++++++++++网络状态+++进入后台+++++++++++++++++++++++++++++++++++++")
//        stopDownload()
    }
        
    @objc func appEnterForeground(noti:Notification) {
        debugPrint("+++++++++++++++++++++++++网络状态+++进入前台+++++++++++++++++++++++++++++++++++++")
//        checkReabilityChanged()
    }
    
    @objc func reachabilityChangedHandler(noti:Notification){
        reachabilityDownloadOrNot()
    }
    
    @objc func appWillTerminate(noti:Notification) {
        debugPrint("appWillTerminate ++++++++++++++++++++ 终止下载任务++++++++++++++++++++++++++++++++++++++++")
        stopDownload()
    }

    //MARK: public
    func checkToStart(duration:TimeInterval = 1.0){
        if canAutoDownload{
            if NetReachability.isReachability(){
                if !AppState.shared.isHide4GDownloadControl && NetReachability.isCellular() && AppState.shared.downloadOnlyOnWifi{
                    AppState.shared.alert.showCanCellularDownloadAlert {[weak self] in
                        self?.startDownloadDelay(duration:duration)
                    }
                } else {
                    startDownloadDelay(duration:duration)
                }
            }
        }
    }
    
    //调整设置的，是否允许手机网络下载的时候，可能会调用。
    func stopDownload(){
        stopTimer()
        shutDown()
    }

    //MARK: timer
    private func stopTimer(){
        timer?.invalidate()
        timer = nil
    }

    @objc private func timerHandler(){
        checkStartAutoDownload()
        stopTimer()
    }

    private func startDownloadDelay(duration:TimeInterval = 1){
        stopTimer()
        timer = Timer.scheduledTimer(timeInterval: duration, target: self, selector: #selector(timerHandler), userInfo: nil, repeats: false)
        DispatchQueue.main.async { [weak self] in
            guard let self = self else{return}
            if let t = self.timer{
                RunLoop.main.add(t, forMode: .common)
                t.fire()
            }
        }
    }
    
    func checkReabilityChanged(){
        reachabilityDownloadOrNot()
    }

    private func reachabilityDownloadOrNot(){
        debugPrint("+++++++++++++++++++++++++网络状态+++\(NetMonitor.currentPath.availableInterfaces)+++++++++++++++++++++++++++++++++++++")
        if NetMonitor.isConnectedToWiFiAndWiredEthernet() {
            handleFreeNetwork()
        } else if NetMonitor.isConnectedToCellular() {
            handleCellularNetwork()
        } else {
            handleNoNetwork()
        }
    }
    
    private func handleFreeNetwork(){
        debugPrint("+++++++++++++++++++++++++网络状态+++WIFI+++++++++++++++++++++++++++++++++++++")  //进入前台会执行此处。
        if !NetworkUtils.shared.isCftStarted {
            DispatchQueue.main.async {
                NetworkUtils.shared.updateNetworkConnection()
            }
        }
        AppState.shared.trySyncRefreshWidgets()
        startDownloadDelay(duration: 2)
    }
    
    private func handleCellularNetwork(){
        if !NetworkUtils.shared.isCftStarted {
            DispatchQueue.main.async {
                NetworkUtils.shared.updateNetworkConnection()
            }
        }
        AppState.shared.trySyncRefreshWidgets()
        if !AppState.shared.isHide4GDownloadControl && AppState.shared.downloadOnlyOnWifi{
            stopDownload()
        } else {
            startDownloadDelay()
        }
    }
    
    private func handleNoNetwork(){
        stopDownload()
    }
    
    //开始下载1个或多个，不会超过最大下载限制个数。
    private func checkStartAutoDownload(){
        guard WDBookSessionSDK.shared.isLogin else {
            return
        }
        
        if !AppState.shared.isHide4GDownloadControl && NetReachability.isCellular() && AppState.shared.downloadOnlyOnWifi{
            AppState.shared.alert.showCanCellularDownloadAlert { [weak self] in
                self?.startAutoDownload()
            }
        }else{
            startAutoDownload()
        }
    }
    
    private func startAutoDownload(){
        let downloadingCount = DownloadManager.shared.runningCount()
        guard downloadingCount < MAX_CONCURRENCE_COUNT else{
            return
        }
        
        let downloadingEntities = AppState.shared.shelfItems.filter({ $0.downloadState == .downloading }).sorted{$0.lastVisitTime > $1.lastVisitTime}
        let waitingEntities = AppState.shared.shelfItems.filter{ $0.downloadState == .waiting}.sorted{$0.lastVisitTime > $1.lastVisitTime}
        let unDownloadEntities = AppState.shared.shelfItems.filter{ $0.downloadState == .unDownload}.sorted{$0.lastVisitTime > $1.lastVisitTime}
        
        if unDownloadEntities.count > 0{
            WDBookSyncSDK.shared.syncReadProgressData { result in }
        }

        let canDownloadCount = MAX_CONCURRENCE_COUNT - downloadingCount
        var startedCount = 0
        let canDownloadEntities = (downloadingEntities + waitingEntities + unDownloadEntities)
        for downloadObservable in canDownloadEntities{
            if startedCount >= canDownloadCount{
                break
            }
            
            let resourceId = downloadObservable.resourceId
           
           if let retryCount = retryDic[resourceId], retryCount >= MAX_RETRY_COUNT {
               continue
           }
           
           startedCount += 1
           let retryCount = retryDic[resourceId] ?? 0
           Log.d("AutoDownload重试resourceId: \(resourceId)，次数: \(retryCount)")
           startDownload(fileId: downloadObservable.fileId, resourceId: resourceId, md5: downloadObservable.md5)
        }
    }
    
    func startDownload(fileId:String,resourceId:String,md5:String){
//        if downloadObservable.needUpgradeApp {
//            AppState.shared.alert.showFormatVersionAlert()
//            return
//        }

        if fileId.isEmpty {
            WDBookStoreSDK.shared.getResourceFileEntity(resourceId: resourceId) {[weak self] result in
                guard let self = self else{return}
                
                switch result {
                case .success(let fileEntity):
                    if let fileId = fileEntity?.fileId {
                        self.download(fileId: fileId,resourceId: resourceId,md5: fileEntity?.md5 ?? "")
                    } else {
                        // showTips
//                        Toaster.showToast(message: "没有获取到文件信息！".localized)
                        NotificationCenter.default.post(name: Noti_Download_UNFOUND_FILE, object: nil,userInfo: ["resourceId":resourceId,"fileId":fileId,"md5":md5])
                        
                    }
                case .failure(_):
                    // showTips
//                    Toaster.showToast(message: "没有获取到文件信息！".localized)
                    NotificationCenter.default.post(name: Noti_Download_UNFOUND_FILE, object: nil,userInfo: ["resourceId":resourceId,"fileId":fileId,"md5":md5])
                }
            }
        } else {
            download(fileId: fileId, resourceId: resourceId,md5: md5)
        }
    }

    private func download(fileId:String,resourceId:String,md5:String) {
        WDBookDownloadSDK.shared.getFileDownloadUrl(fileId: fileId) { result in

            switch result {
            case .success(let fileDownloadEntity):
                if fileDownloadEntity == nil {
                    // showTips
//                    Toaster.showToast(message: "没有获取到文件信息！".localized)
                    NotificationCenter.default.post(name: Noti_Download_UNFOUND_FILE, object: nil,userInfo: ["resourceId":resourceId,"fileId":fileId,"md5":md5])
                } else {
                    Log.d("获取downloadurl 成功了")
                    
                    //3.0.4加上的修补逻辑
                    //修补FileDownload表
                    if let downloadDataInfo = WDBookDownloadSDK.shared.fetchDownloadFileEntity(fileId: fileId),
                       downloadDataInfo.downloadStatus == .complete,
                       downloadDataInfo.resourceId.isEmpty{
                        downloadDataInfo.resourceId = resourceId
                        downloadDataInfo.resourceTypeId = "eBook"
                        WDBookDownloadSDK.shared.saveDownloadData(entity: downloadDataInfo)

                        AppState.shared.reloadShelfData()
                        return
                    }
                    
                    //修补UserFileInfo表
                    if let fileinfo = WDBookUserSDK.shared.getUserFileInfo(fileId: fileId){
                       if fileinfo.resourceId.isEmpty{
                          WDBookUserSDK.shared.updatUserFileInfo(fileId: fileId, setResourceId: resourceId)
                          AppState.shared.reloadShelfData()
                          return
                       }
                    }
                    
                    WDBookUserSDK.shared.getEncryptionKey(fileId: fileId) { result in
                        switch result {
                        case .success(_):
                            Log.d("开始下载了")
                            AppDownloadManager.shared.start(key: fileId, url: fileDownloadEntity!.downloadUrl, destinationFilePath:PathManager.zipPathRelative(fileId), md5:md5,isNeedSyncReadProgress: false)
                        case .failure(_):
                            Log.d("获取加密key失败")
                        }
                    }
                }
            case .failure(_):
                Log.d("获取 downloadurl 失败")
            }
        }
    }
}
