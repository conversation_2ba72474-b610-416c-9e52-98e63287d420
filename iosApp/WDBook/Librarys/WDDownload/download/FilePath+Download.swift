//
//  FilePath+DownloadManager.swift
//  WDDownload
//
//  Created by z<PERSON><PERSON> on 2018/4/26.
//  Copyright © 2018 kai zhou. All rights reserved.
//

import Foundation
public extension FilePath{
    static func getCacheDirPath() -> String{
        let tempDic = FilePath.cacheDictionary() + "/temp"
        FilePath.checkOrCreateDic(dicPath: tempDic)
        Log.d("\(tempDic)")
        return tempDic
    }
    
    static func getCacheFilePath(key:String) -> String{
        let tempDic = FilePath.cacheDictionary() + "/temp"
        FilePath.checkOrCreateDic(dicPath: tempDic)
        let cachePath = tempDic + "/" + key
        return cachePath
    }
    
    static func getRelativeCacheFilePath(key:String) -> String{
        let tempDic = FilePath.cacheDictionary() + "/temp"
        FilePath.checkOrCreateDic(dicPath: tempDic)
        
        return "Library/Caches/temp/\(key)"
    }
    
    static func getDestinationFilePath(relativeFilePath:String) -> String{
        let destinationPath = homeDictionary() + "/" + relativeFilePath
        return destinationPath
    }
    
    @discardableResult
    static func deleteCacheFile(key:String) -> Bool{
        let cacheFilePath = getCacheFilePath(key: key)
        do{
            try FileManager.default.removeItem(atPath: cacheFilePath)
            return true
        }catch {
            return false
        }
    }
}
