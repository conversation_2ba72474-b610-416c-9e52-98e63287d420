<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Headers/WDCrypt.h</key>
		<data>
		AZ5e/tkE6M2+8mAI/oxerKKZSU8=
		</data>
		<key>Headers/WDCryptor.h</key>
		<data>
		u+OI/VRRyYAyOOGR4ZCf/2D2pic=
		</data>
		<key>Info.plist</key>
		<data>
		X1PgELXei64P5wAIX9hmlgsmCdY=
		</data>
		<key>Modules/module.modulemap</key>
		<data>
		NjEcM+tTphxhJw3maFw3Ox04d64=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/WDCrypt.h</key>
		<dict>
			<key>hash</key>
			<data>
			AZ5e/tkE6M2+8mAI/oxerKKZSU8=
			</data>
			<key>hash2</key>
			<data>
			1d6TykY9fpd0fjwyIYr3x0D0FovGuRqQvfLOqUtucFI=
			</data>
		</dict>
		<key>Headers/WDCryptor.h</key>
		<dict>
			<key>hash</key>
			<data>
			u+OI/VRRyYAyOOGR4ZCf/2D2pic=
			</data>
			<key>hash2</key>
			<data>
			iA1Uctp0Wt7sb/C+bCn5qLfE7NKlD1Cd11QmCwmEnzo=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			NjEcM+tTphxhJw3maFw3Ox04d64=
			</data>
			<key>hash2</key>
			<data>
			HdhsGbtB9fszjZMhuTsdB2D/rTMfgufuwzVoNqvW8v4=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
