//
//  WDCryptor.h
//  WDCrypt
//
//  Created by <PERSON> on 2020/10/9.
//

#import <Foundation/Foundation.h>

@interface WDCryptor : NSObject
+ (NSInteger)initBook:(nonnull NSString *)path code:(nonnull NSString *)code user:(nonnull NSString *)user;
+ (nullable NSString *)getBookText:(nonnull NSString *)bookPath pagePath:(nonnull NSString *)pagePath;
+ (nullable NSString *)getFileText:(nonnull NSString *)bookPath fileName:(nonnull NSString *)fileName;

@end
