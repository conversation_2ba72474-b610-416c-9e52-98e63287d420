//
//  InAppPurchaseManager.swift
//  WDBook
//
//  Created by 杜文泽 on 2021/2/8.
//  Copyright © 2021 WeDevote Bible. All rights reserved.
//

import Foundation
import SwiftyStoreKit
import shared
import StoreKit

extension InAppPurchaseManager{
    static let rechargeFailNotification = Notification.Name("TopupView_rechargeFailNotification")
}

class InAppPurchaseManager:NSObject, SKProductsRequestDelegate {
    
    // 单例
    static let shared = InAppPurchaseManager()
    let filePath: String
    // 私有化构造方法，不允许外部创建
    private override init() {
        self.filePath = NSHomeDirectory() + "/Documents/RechargeOrder.plist"
    }
    
    func createOrder(product:InAppPurchaseProductEntity) {
        HUDManager.showLoadingBlockHUD()
        // 2、从服务端创建内购订单
        WDBookUserSDK.shared.createInAppPurchaseOrder(inAppProductId: product.productId, productPrice: String(format: "%.2f", product.price)) {[weak self] (result) in
            switch result {
            case .success( _):
                // 保存订单信息成功 开始购买
                self?.purchaseProduct(productId: product.productId)
//                self?.purchaseProductNative(productId: product.productId)
            case .failure(let error):
                HUDManager.hideLoadingHUD()
                if let sdkException = error as? SDKException {
                    Log.i(sdkException)
                } else {
                    Log.i(error)
                }
                Toaster.showToast(message: error.localizedDescription)
            }
        }
    }
    
    func purchaseProductNative(productId:String) {
        let request = SKProductsRequest(productIdentifiers: [productId])
        request.delegate = self
        request.start()
    }
    
    func productsRequest(_ request: SKProductsRequest, didReceive response: SKProductsResponse) {
        let retrievedProducts = Set<SKProduct>(response.products)
        let invalidProductIDs = Set<String>(response.invalidProductIdentifiers)
        debugPrint(invalidProductIDs)
    }
    
    func purchaseProduct(productId:String) {
        // wdbook_usd099 : productId
        SwiftyStoreKit.purchaseProduct(productId, quantity: 1, atomically: false, applicationUsername: "", simulatesAskToBuyInSandbox: false) { result in
            switch result {
            case .success(let purchase):
                guard let transactionId = purchase.transaction.transactionIdentifier else {
                    HUDManager.hideLoadingHUD()
                    InAppPurchaseManager.shared.clearRechargeOrder()  // 当做订单和凭据对不上处理
                    return
                }
                
                if let iapOrder = WDBookUserSDK.shared.getInAppPurchaseOrder(){
                    WDBookUserSDK.shared.updateInAppPurchaseAppleTransactionId(inAppPurchaseTransactionId: iapOrder.inAppTransactionId, appleTransactionId: transactionId)
                    let receipt = AppleReceiptValidatorX(transactionId: transactionId)
                    SwiftyStoreKit.verifyReceipt(using: receipt) { (result) in
                        HUDManager.hideLoadingHUD()
                        switch result {
                        case .success( _):
                            InAppPurchaseManager.shared.finishTransaction(purchase.transaction)
                            WDBookDataSyncManager.shared.syncAccountData(syncType: .dataRechargeSuccess)
                            break
                        case .error( _):
                            InAppPurchaseManager.shared.finishTransaction(purchase.transaction)
                            break
                        }
                    }
                }
            case .error(let error):
                //苹果会提示充值失败
                HUDManager.hideLoadingHUD()
                InAppPurchaseManager.shared.clearRechargeOrder()
                switch error.code {
                    case .unknown: Log.i("Unknown error. Please contact support")
                    case .clientInvalid: Log.i("Not allowed to make the payment")
                    case .paymentCancelled: break
                    case .paymentInvalid: Log.i("The purchase identifier was invalid")
                    case .paymentNotAllowed: Log.i("The device is not allowed to make the payment")
                    case .storeProductNotAvailable: Log.i("The product is not available in the current storefront")
                    case .cloudServicePermissionDenied: Log.i("Access to cloud service information is not allowed")
                    case .cloudServiceNetworkConnectionFailed: Log.i("Could not connect to the network")
                    case .cloudServiceRevoked: Log.i("User has revoked permission to use this cloud service")
                    default: break
                }
                //非取消的情况弹toast
                if error.code != .paymentCancelled{
                    NotificationCenter.default.post(name: InAppPurchaseManager.rechargeFailNotification, object: error)
//                    Toaster.showToast(message: error.localizedDescription)
                    
                }
            }
        }
    }
    
    func setupIAP() {
        // APP启动，检查未完成订单
        // atomically:立即交付，支付流程走完会自动finish
        SwiftyStoreKit.completeTransactions(atomically: false) { purchases in
            Log.d("completeTransactions_______________completeTransactions")
            for purchase in purchases {
                switch purchase.transaction.transactionState {
                case .purchased:
                    // receipt on Server
                    let downloads = purchase.transaction.downloads
                    if !downloads.isEmpty {
                        SwiftyStoreKit.start(downloads)
                    } else if purchase.needsFinishTransaction {
                        // 用户没有删除APP，本地数据依然保存可用
                        let receipt = AppleReceiptValidatorX( transactionId: purchase.transaction.transactionIdentifier!)
                        SwiftyStoreKit.verifyReceipt(using: receipt) { (result) in
                            switch result {
                            case .success(let receipt):
                                Log.d("receipt------->\(receipt)")
                                InAppPurchaseManager.shared.finishTransaction(purchase.transaction)
                                break
                            case .error(let error):
                                Log.d("error--->\(error)")
                                InAppPurchaseManager.shared.finishTransaction(purchase.transaction)
                                break
                            }
                        }
                    }
                    Log.i("\(purchase.transaction.transactionState.debugDescription): \(purchase.productId)")
                case .restored:
                    if purchase.needsFinishTransaction {
                        InAppPurchaseManager.shared.finishTransaction(purchase.transaction)
                    }
                case .failed, .purchasing, .deferred:
                    break // do nothing
                @unknown default:
                    break // do nothing
                }
            }
        }
    }
    
    // 检查未完成订单
    func checkForUnfilledOrders() -> (needFinishOrder:Bool, unfilledOrder:UserInAppPurchaseOrder?) {
        let iapOrder = WDBookUserSDK.shared.getInAppPurchaseOrder()
        var needFinishOrder = true
        // 有未完成订单
        if iapOrder != nil {
            if iapOrder!.receipt.utf16.count > 0 {
                // 充值未到账情况
                needFinishOrder = false
                let receipt = AppleReceiptValidatorX( transactionId: iapOrder!.appleTransactionId)
                SwiftyStoreKit.verifyReceipt(using: receipt) { (result) in
                    switch result {
                    case .success(let receipt):
                        Log.d("充值成功，如未到账，请刷新页面：receipt------->\(receipt)")
                        break
                    case .error(let error):
                        Log.d("充值票据验证失败，请检查网络并重试：error--->\(error)")
                        break
                    }
                }
            }
        }
        return (needFinishOrder,iapOrder)
    }
    
    // 结束订单
    func finishTransaction(_ transaction: PaymentTransaction) {
        Log.i("*********** finishTransactionId:\(transaction.transactionIdentifier) *********")
        SwiftyStoreKit.finishTransaction(transaction)
    }
    
    func clearRechargeOrder() -> Bool {
        let iapOrder = WDBookUserSDK.shared.getInAppPurchaseOrder()
        if iapOrder != nil {
            WDBookUserSDK.shared.deleteInAppPurchaseOrder(inAppPurchaseTransactionId: iapOrder!.inAppTransactionId)
        }
        return true
    }
    
    func getProductsList() {
        SwiftyStoreKit.retrieveProductsInfo(["wd_price_001","wd_price_002","wd_price_003","wd_price_004","wd_price_005","wd_price_006"]) { result in
            if let product = result.retrievedProducts.first {
                let priceString = product.localizedPrice!
                Log.i("Product:\(product.localizedDescription), price:\(priceString)")
                
            } else if let invalidProductId = result.invalidProductIDs.first {
                Log.i("Invalid Product identifier:\(invalidProductId)")
            } else {
                Log.i("Error:\(String(describing: result.error))")
            }
        }
    }
    
    // MARK: ************ 以下调试使用，先保留 ***********
//    // receipt失效或者过期调用此方法，也是订阅使用
//    func fetchReceipt() {
//        SwiftyStoreKit.fetchReceipt(forceRefresh: true) { result in
//            switch result {
//            case .success(let receiptData):
//                let encryptedReceipt = receiptData.base64EncodedString(options: [])
//                Log.i("Fetch receipt success:\n\(encryptedReceipt)")
//            case .error(let error):
//                Log.i("Fetch receipt failed: \(error)")
//            }
//        }
//    }
//
//    // 订阅功能使用
//    func fetchAndVerifyReceipt() {
//        let appleValidator = AppleReceiptValidator(service: .production, sharedSecret: "your-shared-secret")
//        SwiftyStoreKit.verifyReceipt(using: appleValidator, forceRefresh: false) { result in
//            switch result {
//            case .success(let receipt):
//                print("Verify receipt success: \(receipt)")
//            case .error(let error):
//                print("Verify receipt failed: \(error)")
//            }
//        }
//    }
//
//    // 我们在回调成功之前，需要先保证服务器验证成功
//    // sharedSecret可以为空
//    func verify() {
//        let appleValidator = AppleReceiptValidator(service: .production, sharedSecret: "")
//        SwiftyStoreKit.verifyReceipt(using: appleValidator) { result in
//            switch result {
//            case .success(let receipt):
//                let productId = "com.musevisions.SwiftyStoreKit.Purchase1"
//                // Verify the purchase of Consumable or NonConsumable
//                let purchaseResult = SwiftyStoreKit.verifyPurchase(
//                    productId: productId,
//                    inReceipt: receipt)
//
//                switch purchaseResult {
//                case .purchased(let receiptItem):
//                    print("\(productId) is purchased: \(receiptItem)")
//                case .notPurchased:
//                    print("The user has never purchased \(productId)")
//                }
//            case .error(let error):
//                print("Receipt verification failed: \(error)")
//            }
//        }
//    }
}









