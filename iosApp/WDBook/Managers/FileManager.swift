//
//  FileManager.swift
//  WDBook
//
//  Created by <PERSON> on 2020/9/8.
//  Copyright © 2020 WeDevote Bible. All rights reserved.
//

import Foundation

struct PathManager {
    
    static func zipPath(_ fileId: String) -> String{
        return userDirectory() + "/\(fileId).zip"
    }
    
    static func zipPathForSupport(_ fileId: String) -> String{
        return userDirectoryForSupport() + "/\(fileId).zip"
    }
    
    static func destPath(_ fileId: String) -> String{
        return userDirectory() + "/\(fileId)"
    }
    
    static func destPathForSupport(_ fileId:String) -> String{
        return userDirectoryForSupport() + "/\(fileId)"
    }
    
    static func zipPathRelative(_ fileId: String) -> String {
        return userDirectoryRelative() + "/\(fileId).zip"
    }
    
    //先查找support，再查找documents目录的目标文件。
    static func destFilePathForBothSearch(_ fileId:String) -> String{
        let destPath = destPathForSupport(fileId)
        if let contentsOfPath = try? FileManager.default.contentsOfDirectory(atPath: destPath).first {
            Log.d("path:\(String(describing: contentsOfPath))")
            return destPath + "/\(contentsOfPath)"
        }else{
            return destFilePath(fileId)
        }
    }
    
    static func fileExists(fileId: String) -> Bool {
        let fileManager = FileManager.default
        var state = false
        if fileId != "" {
            state = fileManager.fileExists(atPath: destPathForSupport(fileId)) || fileManager.fileExists(atPath: destPath(fileId)) || fileManager.fileExists(atPath: destPath(fileId)+".epub")
        }
        return state
    }
    

    //MARK: private
    private static func destFilePath(_ fileId: String) -> String {
        let destPath = destPath(fileId)
        //如果扫描到文件，返回
        if let contentsOfPath = try? FileManager.default.contentsOfDirectory(atPath: destPath).first {
            Log.d("path:\(String(describing: contentsOfPath))")
            return destPath + "/\(contentsOfPath)"
        } else {
            //很老的版本需要更改目录
            //如果存在错误文件，那么更名后，重新查找。
            if FileManager.default.fileExists(atPath: String(describing: destPath) + ".epub") {
                // 更名
                if changeFilePath(fileId) {
                    return destFilePath(fileId)
                }
            }
            
            //否则文件出错，重新下载。
            Toaster.showToast(message: "下载文件出错，请重新下载!".localized)
            let downloadDataInfo = WDBookDownloadSDK.shared.fetchDownloadFileEntity(fileId: fileId)
            downloadDataInfo?.downloadStatus = .empty
            WDBookDownloadSDK.shared.saveDownloadData(entity: downloadDataInfo)
            AppState.shared.archiveCount = 0
            AppState.shared.shelfCombineItems.removeAll()
            AppState.shared.shelfItems.removeAll()
            AppState.shared.refreshShelfList()
        }
        return ""
    }
    
    // 处理原先文件夹名称的问题
    private static func changeFilePath(_ fileId: String) -> Bool {
        let fileManager = FileManager.default
        var state = false
        do {
            try fileManager.moveItem(atPath: destPath(fileId) + ".epub", toPath: destPath(fileId))
            state = true
        } catch let error as NSError {
            Log.d("error:\(error)")
            state = false
        }
        return state
    }
    
    private static func userDirectory() -> String{
        let userid = AppState.shared.userInfo.userId ?? "default"
        let dir = FilePath.documentDictionary() + "/" + userid
        FilePath.checkOrCreateDic(dicPath: dir)
        return dir
    }
    
    private static func userDirectoryForSupport() -> String{
        let userid = AppState.shared.userInfo.userId ?? "default"
        let dir = FilePath.applicationSupportDictionary() + "/" + userid
        FilePath.checkOrCreateDic(dicPath: dir)
        return dir
    }
    
    private static func userDirectoryRelative() -> String{
        let userid = AppState.shared.userInfo.userId ?? "default"
//        let dir = "Documents/" + userid
        let dir = "Library/Application Support/" + userid //TODO:改成support
        return dir
    }
    
    //文件大小相同
    private static func zipFileIntact(_ downloadKey:String, size:Int64) -> Bool{
        let filePath = zipPath(downloadKey)
        let fileSize = getFileSize(cacheFilePath: filePath)
        return fileSize == size && fileSize > 0
    }
    
    private static func destFileIntact(_ downloadKey:String, size:Int64) -> Bool{
        let filePath = destPath(downloadKey)
        let fileSize = getFileSize(cacheFilePath: filePath)
        return fileSize == size && fileSize > 0
    }
    
    //计算不对。
    private static func epubFileIntact(_ downloadKey:String, size:Int64) -> Bool{
        let filePath = destPath(downloadKey)
        let fileSize = FileManager.default.folderSizeAtPath(path:filePath)
        return fileSize == size && fileSize > 0
    }
}

extension FileManager {
    static func sizeOfFolder(_ folderPath: String) -> Int64 {
        do {
            let contents = try FileManager.default.contentsOfDirectory(atPath: folderPath)
            var folderSize: Int64 = 0
            for content in contents {
                do {
                    let fullContentPath = folderPath + "/" + content
                    let fileAttributes = try FileManager.default.attributesOfItem(atPath: fullContentPath)
                    folderSize += fileAttributes[FileAttributeKey.size] as? Int64 ?? 0
                } catch _ {
                    continue
                }
            }
            return folderSize
        } catch let error {
            Log.i(error.localizedDescription)
            return 0
        }
    }
    
    //文件大小
    static func sizeOfFile(_ filePath: String) -> Int64 {
        do {
            let fileAttributes = try FileManager.default.attributesOfItem(atPath: filePath)
            let folderSize = fileAttributes[FileAttributeKey.size] as? Int64 ?? 0
            return folderSize
        } catch _ {
            return 0
        }
    }
    
    func fileSizeAtPath(path: String) -> Int64 {
        do {
            let fileAttributes = try attributesOfItem(atPath: path)
            let fileSizeNumber = fileAttributes[FileAttributeKey.size] as? NSNumber
            let fileSize = fileSizeNumber?.int64Value
            return fileSize ?? 0
        } catch {
            Log.i("error reading filesize, NSFileManager extension fileSizeAtPath")
            return 0
        }
    }

    func folderSizeAtPath(path: String) -> Int64 {
        var size : Int64 = 0
        do {
            let files = try subpathsOfDirectory(atPath: path)
            for i in 0 ..< files.count {
                size += fileSizeAtPath(path:path.appending("/"+files[i]))
            }
        } catch {
            Log.i("error reading directory, NSFileManager extension folderSizeAtPath")
        }
        return size
    }

    func format(size: Int64) -> String {
        let folderSizeStr = ByteCountFormatter.string(fromByteCount: size, countStyle: ByteCountFormatter.CountStyle.file)
        return folderSizeStr
    }
    
}
