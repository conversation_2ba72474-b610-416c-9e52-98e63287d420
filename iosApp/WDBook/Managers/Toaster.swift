//
//  Toaster.swift
//  WDBook
//
//  Created by <PERSON> on 2020/8/13.
//  Copyright © 2020 WeDevote Bible. All rights reserved.
//

import Foundation
import UIKit
import Toast_Swift

class Toaster {

    static var style:ToastStyle = {
        var style = ToastStyle()
        style.backgroundColor = dynamicToasterBgColor
        style.messageColor = dynamicColorGray05
        style.titleColor = dynamicColorGray05
        return style
    }()
    
    static var keyWindow:UIWindow?{
        return UIApplication.shared.windows.first { $0.isKeyWindow }
    }
    
    static func showToast(message:String){
        keyWindow?.makeToast(message, duration: ToastManager.shared.duration, position: .center, style: Toaster.style)
    }
    
    static func showToast(message:String,duration: TimeInterval = ToastManager.shared.duration,completion: ((Bool) -> Void)? = nil){
        keyWindow?.makeToast(message, duration: duration, position: .center, style: Toaster.style, completion: completion)
    }
    
    static func showToast(message:String, position:ToastPosition){
        keyWindow?.makeToast(message, duration: ToastManager.shared.duration, position: position, style: Toaster.style)
    }
    
    static func hideToast(){
        keyWindow?.hideToast()
    }
}
