//
//  HUDManager.swift
//  WDBook
//
//  Created by <PERSON> on 2020/8/13.
//  Copyright © 2020 WeDevote Bible. All rights reserved.
//

import Foundation
import UIKit
import JGProgressHUD

class HUDManager {
    static var loadingHUD = JGProgressHUD(style: .extraLight)

    static func showLoadingBlockHUD(text:String) {
        loadingHUD.textLabel.text = text
        loadingHUD.interactionType = .blockAllTouches
        if #available(iOS 13, *) {
            loadingHUD.show(in: UIApplication.shared.windows[0],animated:true)
        }else{
            loadingHUD.show(in: UIApplication.shared.keyWindow2!,animated:true)
        }
    }
    
    static func showLoadingBlockHUD(){
        loadingHUD.textLabel.text = "正在加载...".localized
        loadingHUD.interactionType = .blockAllTouches
        if #available(iOS 13, *) {
            loadingHUD.show(in: UIApplication.shared.windows[0],animated:true)
        } else {
            loadingHUD.show(in: UIApplication.shared.keyWindow2!,animated:true)
        }
    }
    
    static func showLoadingHUDOnView(view:UIView){
        loadingHUD.textLabel.text = "正在加载...".localized
        loadingHUD.interactionType = .blockNoTouches
        loadingHUD.show(in:view,animated:true)
    }
    
    static func hideLoadingHUD() {
        loadingHUD.dismiss(animated: false)
    }
    
    class func showSuccessHUD(message:String){
        let HUD = JGProgressHUD(style: .extraLight)
        HUD.textLabel.text = message
        HUD.indicatorView = JGProgressHUDSuccessIndicatorView()
        if #available(iOS 13, *) {
            HUD.show(in: UIApplication.shared.windows[0],animated:true)
        }else{
            HUD.show(in: UIApplication.shared.keyWindow2!,animated:true)
        }
        HUD.dismiss(afterDelay: 1.5, animated: true)
    }
    
    class func showErrorHUD(message:String){
        let HUD = JGProgressHUD(style: .extraLight)
        HUD.textLabel.text = message
        HUD.indicatorView = JGProgressHUDErrorIndicatorView()
        if #available(iOS 13, *) {
            HUD.show(in: UIApplication.shared.windows[0],animated:true)
        }else{
            HUD.show(in: UIApplication.shared.keyWindow2!,animated:true)
        }
        HUD.dismiss(afterDelay: 1.5, animated: true)
    }
}

