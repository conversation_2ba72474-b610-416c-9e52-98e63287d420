//
//  WDDownloadManager+App.swift
//  WDBook
//
//  Created by <PERSON> on 2020/9/7.
//  Copyright © 2020 WeDevote Bible. All rights reserved.
//

import Foundation
import shared

//import Zip
import SSZipArchive
import CryptoKit

extension Notification.Name{
    static let ALLOW_CELLULAR_DOWNLOAD_CHANGED = Notification.Name(rawValue: "Noti_Download_ALLOW_CELLULAR_DOWNLOAD_CHANGED")
}

class AppDownloadManager{

    public static let shared:AppDownloadManager = AppDownloadManager()
    
    let manager = WDDownloadManager.shared
    
    let RETRY_DURATION_DTIMEINTERVAL:TimeInterval = 5.0
    let MAX_RETRY_COUNT = 3
    var retryDic = Dictionary<String,Int>() //resourceid: 重试次数。从0开始，大于3结束
    
    private init(){
        
        NotificationCenter.default.addObserver(self, selector: #selector(downloadStartHandler(noti:)), name: Noti_Download_Start, object: nil)
        NotificationCenter.default.addObserver(self, selector: #selector(downloadWaitingHandler(noti:)), name: Noti_Download_Waiting, object: nil)
        NotificationCenter.default.addObserver(self, selector: #selector(downloadProgressHandler(noti:)), name: Noti_Download_Progress, object: nil)
        NotificationCenter.default.addObserver(self, selector: #selector(downloadFailsHandler(noti:)), name: Noti_Download_Fails, object: nil)
        NotificationCenter.default.addObserver(self, selector: #selector(downloadCompleteHandler(noti:)), name: Noti_Download_Complete, object: nil)
        
        NotificationCenter.default.addObserver(self, selector: #selector(downloadUnfoundFileHandler(noti:)), name: Noti_Download_UNFOUND_FILE, object: nil)
        
        NotificationCenter.default.addObserver(self, selector: #selector(loginHandler(noti:)), name: AuthLoginV.loginSuccessNotification, object: nil)
        NotificationCenter.default.addObserver(self, selector: #selector(logoutHandler(noti:)), name: AuthLoginV.logoutSuccessNotification, object: nil)

        NotificationCenter.default.addObserver(self, selector: #selector(allowCellularDownloadChanged(noti:)), name: Notification.Name.ALLOW_CELLULAR_DOWNLOAD_CHANGED, object: nil)
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self)
    }
    
    func resetConfig(){
        guard WDBookSessionSDK.shared.isLogin else {
            return
        }
        manager.configLogger(level: .debug)
        manager.allowCellularDownload = !AppState.shared.downloadOnlyOnWifi
        
        if WDBookSessionSDK.shared.isLogin && !WDBookSessionSDK.shared.userId.isEmpty{
            checkToStart()
        }
    }
    
    //不清除：失败重试无效时；下载成功时；
    //清除：其他情况，如同步，刷新，登录，网络变化等。
    func checkToStart(clearRetry:Bool = true){
        if clearRetry {
            retryDic.removeAll()
            manager.retryDic.removeAll()
        }
        manager.checkToStart()
    }
    
    func start(key:String, url:String, destinationFilePath:String, downloadListKey:String? = nil,md5:String? = nil,isReplace:Bool = false,isNeedSyncReadProgress:Bool = true){
        if isNeedSyncReadProgress{
            WDBookSyncSDK.shared.syncReadProgressData { result in }
        }
        WDDownloadManager.shared.start(key: key, url: url, destinationFilePath: destinationFilePath, downloadListKey: downloadListKey, md5: md5, isReplace: isReplace)
    }
    
    @objc func downloadStartHandler(noti:Notification){
        if let downloadInfo = noti.object as? DownloadDataEntity {
            AppState.shared.shelfItems.filter{$0.resourceId == downloadInfo.resourceId}.forEach { (item) in
                item.downloadState = .waiting
            }
            AppState.shared.shelfCombineItems.filter{$0.resourceId == downloadInfo.resourceId}.forEach { (item) in
                item.downloadState = .waiting
            }
            AppState.shared.purchasedListResources.filter{$0.resourceId == downloadInfo.resourceId}.forEach { (item) in
                item.downloadState = .waiting
            }
            
            WDBookUserSDK.shared.getEncryptionKey(fileId: downloadInfo.fileId ?? "") { result in
                switch result {
                case .success(let encryptionKey):
                    Log.d("获取加密key成功")
                    break
                case .failure(_):
                    Log.d("获取加密key失败")
                }
            }
        }
    }
    
    @objc func downloadWaitingHandler(noti:Notification){
        if let downloadInfo = noti.object as? DownloadDataEntity {
            AppState.shared.shelfItems.filter{$0.resourceId == downloadInfo.resourceId}.forEach { (item) in
                item.downloadState = .waiting
            }
            AppState.shared.shelfCombineItems.filter{$0.resourceId == downloadInfo.resourceId}.forEach { (item) in
                item.downloadState = .waiting
            }
            AppState.shared.purchasedListResources.filter{$0.resourceId == downloadInfo.resourceId}.forEach { (item) in
                item.downloadState = .waiting
            }
        }
    }
    
    @objc func downloadProgressHandler(noti:Notification){
        if let downloadInfo = noti.object as? DownloadDataEntity {
            AppState.shared.shelfItems.filter{$0.resourceId == downloadInfo.resourceId}.forEach { (item) in
                item.downloadProgress = downloadInfo.progress
                item.downloadState = .downloading
            }
            AppState.shared.shelfCombineItems.filter{$0.resourceId == downloadInfo.resourceId}.forEach { (item) in
                item.downloadProgress = downloadInfo.progress
                item.downloadState = .downloading
            }
            AppState.shared.purchasedListResources.filter{$0.resourceId == downloadInfo.resourceId}.forEach { (item) in
                item.downloadProgress = downloadInfo.progress
                item.downloadState = .downloading
            }
        }
    }
    
    @objc func downloadFailsHandler(noti:Notification){
        if let downloadInfo = noti.object as? DownloadDataEntity {
            AppState.shared.shelfItems.filter{$0.resourceId == downloadInfo.resourceId}.forEach { (item) in
                item.downloadState = .unDownload
            }
            AppState.shared.shelfCombineItems.filter{$0.resourceId == downloadInfo.resourceId}.forEach { (item) in
                item.downloadState = .unDownload
            }
            AppState.shared.purchasedListResources.filter{$0.resourceId == downloadInfo.resourceId}.forEach { (item) in
                item.downloadState = .unDownload
            }
//            Toaster.showToast(message: "下载失败".localized + ",resourceid:\(downloadInfo.resourceId),progress:\(downloadInfo.progress)")
            checkToStart(clearRetry: false)
        }
    }
    
    @objc func downloadUnfoundFileHandler(noti:Notification){
        if let info = noti.userInfo as? [String:String],
            let resourceId = info["resourceId"],
            let fileId = info["fileId"],
            let md5 = info["md5"]{
            
            retryStartDownloadWhenUnfoundFile(resourceId: resourceId, fileId: fileId, md5: md5)
        }
    }
    
    //没有获取到文件信息场景的重试。
    private func retryStartDownloadWhenUnfoundFile(resourceId:String,fileId:String,md5:String){
        if retryDic[resourceId] == nil{
            retryDic[resourceId] = 0
        }

        if retryDic[resourceId]! < MAX_RETRY_COUNT{

            let userId = WDBookSessionSDK.shared.userId
            DispatchQueue.global().asyncAfter(deadline: .now() + RETRY_DURATION_DTIMEINTERVAL){ [weak self] in
                guard let self = self else {return}
                guard WDBookSessionSDK.shared.isLogin else {
                    return
                }
                guard userId == WDBookSessionSDK.shared.userId else{
                    return
                }
                
                self.retryDic[resourceId]! += 1
                self.manager.startDownload(fileId: fileId, resourceId: resourceId, md5: md5)
            }
        }else{
            checkToStart(clearRetry: false)
        }
    }
    
    @objc func downloadCompleteHandler(noti:Notification){
        if let downloadInfo = noti.object as? DownloadDataEntity {
            
            let resourceDownloadInfo = WDBookDownloadSDK.shared.getResourceDownloadInfo(resourceId: downloadInfo.resourceId)
            WDBookUserSDK.shared.getEncryptionKey(fileId: resourceDownloadInfo?.fileId ?? "") { result in
                switch result {
                case .success(let encryptionKey):
                    Log.d("获取加密key成功")
                    break
                case .failure(_):
                    Log.d("获取加密key失败")
                }
            }
            
            let readProgress =  WDBookUserSDK.shared.getFileReadProgressEntity(resourceId: downloadInfo.resourceId)
            
            AppState.shared.shelfItems.filter{$0.resourceId == downloadInfo.resourceId}.forEach { (item) in
                item.downloadProgress = 1.0
                item.downloadState = .completed
                unzip(path: downloadInfo.fileId!) { success in
                    if success {
                        item.downloadState = .completed
                    } else {
                        item.downloadState = .unDownload
                    }
                }
                if readProgress != nil{
                    item.readProgress = Int(readProgress!.progress)
                }
            }
            AppState.shared.shelfCombineItems.filter{$0.resourceId == downloadInfo.resourceId}.forEach { (item) in
                item.downloadProgress = 1.0
                item.downloadState = .completed
                unzip(path: downloadInfo.fileId!) { success in
                    if success {
                        item.downloadState = .completed
                    } else {
                        item.downloadState = .unDownload
                    }
                }
                if readProgress != nil{
                    item.readProgress = Int(readProgress!.progress)
                }
            }
            AppState.shared.purchasedListResources.filter{$0.resourceId == downloadInfo.resourceId}.forEach { (item) in
                item.downloadProgress = 1.0
                item.downloadState = .completed
                unzip(path: downloadInfo.fileId!) { success in
                    if success {
                        item.downloadState = .completed
                    } else {
                        item.downloadState = .unDownload
                    }
                }
                if readProgress != nil{
                    item.readProgress = Int(readProgress!.progress)
                }
            }
            
            checkToStart(clearRetry: false)
        }
    }
    
    
    @objc func loginHandler(noti:Notification){
        //需要同步资源的情况下，延迟时间也不够。
//        DispatchQueue.main.asyncAfter(deadline: .now() + 3, execute: {[weak self] in
//            self?.resetConfig()
//        })
        
    }
    
    @objc func logoutHandler(noti:Notification){
        WDDownloadManager.shared.stopDownload()
    }

    @objc func allowCellularDownloadChanged(noti:Notification){
        manager.allowCellularDownload = !AppState.shared.downloadOnlyOnWifi
        manager.checkReabilityChanged()
    }
    
    func unzip(path:String , complete:((_ success:Bool)->())? = nil){
        let zipPath = PathManager.zipPathForSupport(path)
        let destPath = PathManager.destPathForSupport(path)
        unzip(zipPath: zipPath, destPath: destPath) { (success) in
            complete?(success)
        }
    }
    
    func unzip(zipPath:String, destPath:String, complete:((_ success:Bool)->())? = nil){
        DispatchQueue.global().async{
            do {
                try SSZipArchive.unzipFile(atPath: zipPath, toDestination: destPath, overwrite: true, password: nil)
                try FileManager.default.removeItem(atPath: zipPath)

                Log.i("解压成功\(zipPath)")
                DispatchQueue.main.async {
                    complete?(true)
                }
            }catch let error as NSError{
                Log.i("解压失败\(error)")
                DispatchQueue.main.async {
                    complete?(true)
                }
            }
        }
    }
}

//下载状态。 + 解压后文件大小校验 + formatVersion对应。
//重试使用pause，被放在正在下载中。只有三次重试失败，才改为fails，fails被放在未下载中。
enum AppDownloadState:String{
    case waiting //waiting
    case downloading //begin,downloading, pause, qeeue
    case completed //complated update + zip解压 + formatVersion对应。
    case unDownload //上述两种情况之外的其他：undownlaod，fails,empty,cancel
}

