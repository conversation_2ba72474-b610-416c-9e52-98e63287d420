//
//  ImageManager.swift
//  WDBook
//
//  Created by <PERSON> on 2020/12/16.
//  Copyright © 2020 WeDevote Bible. All rights reserved.
//

import Foundation
import SDWebImage
import SDWebImageSwiftUI
import wdNetwork

struct ImageManager {
    static func getImageUrl(_ urlPath: String?) -> String {
        return urlPath ?? ""
    }

    static func config() {
        let requestModifier = SDWebImageDownloaderRequestModifier { request -> URLRequest? in
            var mutableRequest = request
            mutableRequest.setValue(SHARED_CONSTANTS.WD_BOOK_HTTP_REFERER, forHTTPHeaderField: SHARED_API_HEADER.REFERER)
            NetworkUtils.getCftConnectionHeader().forEach { key, value in
                mutableRequest.setValue(value, forHTTPHeaderField: key)
            }
            return mutableRequest
        }
        SDWebImageDownloader.shared.requestModifier = requestModifier

        // Add multiple caches
        let cache = SDImageCache(namespace: bundleN<PERSON>())
        cache.config.maxMemoryCost = 100 * 1024 * 1024 // 100MB memory
        cache.config.maxDiskSize = 1024 * 1024 * 1024 // 1024MB disk
        cache.config.maxDiskAge = 3600 * 24 * 365 * 10 // 10 years
        cache.config.shouldRemoveExpiredDataWhenTerminate = false
        cache.config.shouldRemoveExpiredDataWhenEnterBackground = false
        SDImageCachesManager.shared.addCache(cache)
        SDWebImageManager.defaultImageCache = SDImageCachesManager.shared
    }

    static func getImageContext() -> [SDWebImageContextOption: Any]? {
        let configuration = URLSessionConfiguration.default
        configuration.protocolClasses = [WDUrlProtocol.self]
        NetworkUtils.configureCftHeaders(for: configuration)
        let config = SDWebImageDownloaderConfig()
        config.sessionConfiguration = configuration

        let downloader = SDWebImageDownloader(config: config)

        let manager = SDWebImageManager(cache: SDImageCache.shared, loader: downloader)
        var context: [SDWebImageContextOption: Any] = [:]
        context[SDWebImageContextOption.customManager] = manager
        Log.d("Load image with proxy")
        return context
    }

    static func getWebImage(url: URL?) -> WebImage {
        if let url = url {
            Log.d("Get web image for url \(url)")
        } else {
            Log.e("The url is empty")
        }
        return WebImage(url: url, context: getImageContext())
    }
}
