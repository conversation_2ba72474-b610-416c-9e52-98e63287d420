//
//  RoutableManager.swift
//  WDBook
//
//  Created by <PERSON><PERSON> <PERSON><PERSON> on 2022/4/14.
//  Copyright © 2022 WeDevote Bible. All rights reserved.
//

import Foundation
import NavigationRouter
import shared
import SwiftUI
import UIKit

class RoutableManager {
    static let shared = RoutableManager()
    private init() {}

    func config() {
        // Register modules
        RoutableModulesFactory.loadRoutableModules()
    }

    static func showBindingEmailV<V: View>(returnView _: V.Type) {
        RoutableManager.push(BindingEmailV<V>())
    }

    static func showWidgetSectionList(title: String, detailEntity: NewWidgetDetailEntity) {
        RoutableManager.push(BookSectionListV(title: title, detailData: detailEntity))
    }

    static func showShelfGroupListView(archiveItemIndex: Int) {
        guard WDBookSessionSDK.shared.isLogin else {
            AppState.shared.showLoginRegisterV()
            return
        }
        RoutableManager.push(ShelfGroupListView(shelfArchiveItem: AppState.shared.shelfCombineItems[archiveItemIndex])
//            .navigationBarHidden(true).edgesIgnoringSafeArea(.all)
        )
    }

    static func showNoteDetailV(noteDataId: String, dataObserable: NoteListObservable) {
        guard WDBookSessionSDK.shared.isLogin else {
            AppState.shared.showLoginRegisterV()
            return
        }
        RoutableManager.push(NoteDetailV(noteDataId: noteDataId).environmentObject(dataObserable))
    }

    static func showDeviceManagerV() {
        RoutableManager.push(DeviceManagerV(), animated: false)
    }

    static func showCouponDetailsV(coupon: Coupon) {
        RoutableManager.push(CouponDetailsV(coupon: coupon), animated: true)
    }

    static func pushBookReader(resourceId: String, needJumpBookmark: Bool = false, chapterPath: String? = nil, contentOffset: Int? = nil) {
        debugPrint("\(CFAbsoluteTimeGetCurrent()):阅读器 pushBookReader 加载")
        if AppState.shared.isShowReader {
            popToRoot()
        }

        guard let resourceDownloadInfo = WDBookDownloadSDK.shared.getResourceDownloadInfo(resourceId: resourceId) else {
            Toaster.showToast(message: "你还没购买本书，请去商城购买。".localized)
            return
        }

        AppState.shared.checkBookUpdate(resourceId: resourceId) {
            if resourceDownloadInfo.downloadStatus == .complete {
                if let fileId = resourceDownloadInfo.downloadInfo?.fileId, !fileId.isBlank {
                    // 0b256dcf7490418f40813cbc42ab12b1 fileid
                    WDBookUserSDK.shared.getEncryptionKey(fileId: fileId) { result in
                        switch result {
                        case let .success(encryptionKey):
                            if let key = encryptionKey {
                                let bookPath = PathManager.destFilePathForBothSearch(fileId)
                                let observable = OpenBookObservable()
                                if observable.openBook(path: bookPath, code: key, resourceId: resourceId, fileId: fileId) {
                                    let cPath = chapterPath ?? observable.chapterPath
                                    let cOffset = contentOffset ?? observable.contentOffset
                                    Log.d("\(CFAbsoluteTimeGetCurrent()):阅读器 pushBookReader2 加载")
                                    RoutableManager.push(
                                        WDReaderView(
                                            resourceId: resourceId,
                                            fileId: fileId,
                                            url: bookPath,
                                            code: observable.code,
                                            chapterPath: cPath,
                                            contentOffset: cOffset,
                                            needJumpBookmark: needJumpBookmark
                                        ).navigationBarTitle("阅读器".localized, displayMode: .inline)
                                            .navigationBarHidden(true)
                                            // .statusBar(hidden: isHideStatusBar)
                                            .edgesIgnoringSafeArea(.all)
                                    )
                                } else {
                                    AppState.shared.alert.showFileErrorAlert()
                                    WDBookStoreSDK.shared.getResourceFileEntity(resourceId: resourceId) { _ in }
                                }
                            } else {
                                Log.d("获取加密key失败")
                            }
                        case .failure:
                            Log.d("获取加密key失败")
                        }
                    }
                } else {
                    Log.d("获取加密key失败")
                }
            } else {
                // 未下载
                guard NetReachability.isReachability() else {
                    Toaster.showToast(message: "无网络连接，请稍后再试".localized)
                    return
                }

                guard WDBookSessionSDK.shared.isLogin else {
                    AppState.shared.showLoginRegisterVAndPopToStore()
                    return
                }

                guard WDBookDownloadSDK.shared.getResourceDownloadInfo(resourceId: resourceId) != nil else {
                    Toaster.showToast(message: "你还没购买本书，请去商城购买。".localized)
                    return
                }

                AppState.shared.alert.bookNoteDownloadAlert(title: "提示".localized, msg: "请先下载本书，才可进入阅读模式".localized) {
                    Log.d("开始下载")
                    if AppState.shared.shelfItems.filter({ $0.resourceId == resourceId }).first == nil {
                        AppState.shared.shelfItems.append(getShelfBookEntity(resourceId: resourceId))
                    }
                    NavigationRouter.main.rootViewController?.present(builder: {
                        if let item = AppState.shared.shelfItems.filter({ $0.resourceId == resourceId }).first {
                            SimpleBookDownloadingView(bookItem: item) {
                                RoutableManager.pushBookReader(resourceId: resourceId)
                            }
                        }
                    })
                } cancelHandler: {
                    Log.d("不下载，取消")
                }
            }
        }
    }

    static func navigate(toRoute route: RouterName) {
        NavigationRouter.main.navigate(toPath: route.rawValue)
    }

    static func navigate(toPath path: String) {
        NavigationRouter.main.navigate(toPath: path)
    }

    static func push<SomeView: View>(_ someView: SomeView, animated: Bool = true) {
        // presentedViewController可能不是navigationViewController.所以先退出。
        NavigationRouter.main.topViewController?.presentedViewController?.dismiss(animated: false)
        let host = UIHostingController(rootView: someView.environment(\.locale, AppState.shared.locale).environmentObject(AppState.shared))
        host.className = someView.className
        NavigationRouter.main.pushViewController(host, animated: animated)
    }

    static func present<SomeView: View>(_ someView: SomeView, animated: Bool = true) {
        // presentedViewController可能不是navigationViewController.所以先退出。
        NavigationRouter.main.topViewController?.presentedViewController?.dismiss(animated: false)
        NavigationRouter.main.topViewController?.present(UIHostingController(rootView: someView.environment(\.locale, AppState.shared.locale).environmentObject(AppState.shared)), animated: animated)
    }

    static func popViewController(animated: Bool = true) {
        NavigationRouter.main.topNavigationController?.popViewController(animated: animated)
    }

    static func popToRoot(_ animated: Bool = true) {
        NavigationRouter.main.popToRoot(animated: animated)
        AppState.shared.popToRootOnlyForSwiftUI()
    }

    static func popToView<SomeView: View>(_ targetType: SomeView.Type, animated: Bool = true) {
        let className = String(describing: targetType.self)

        let navTop = NavigationRouter.main.topNavigationController
        let navRoot = NavigationRouter.main.rootNavigationController

        if navTop != nil,
           popTo(className: className, nav: navTop!, animated: animated)
        {
            return
        } else {
            if navRoot != nil && navTop != navRoot {
                popTo(className: className, nav: navRoot!, animated: animated)
            }
        }
    }

    @discardableResult
    private static func popTo(className: String, nav: UINavigationController, animated: Bool = true) -> Bool {
        let childrenVc = nav.children
        var targetVC: UIViewController?
        for itemVc in childrenVc.reversed() {
            if itemVc.className == className {
                targetVC = itemVc
                break
            }
        }
        if let vc = targetVC {
            nav.popToViewController(vc, animated: animated)
            return true
        } else {
            return false
        }
    }
}

private var UIViewController_ClassName: UInt8 = 0
extension UIViewController {
    var className: String? {
        set {
            objc_setAssociatedObject(self, &UIViewController_ClassName, newValue, .OBJC_ASSOCIATION_RETAIN_NONATOMIC)
        }
        get {
            return objc_getAssociatedObject(self, &UIViewController_ClassName) as? String
        }
    }
}

extension View {
    var className: String? {
        if let first = String(describing: self).split(separator: "(").first {
            return String(first)
        } else {
            return nil
        }
    }
}
