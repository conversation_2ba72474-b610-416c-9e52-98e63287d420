//
//  LoginManager.swift
//  WDBook
//
//  Created by <PERSON><PERSON> <PERSON><PERSON> on 2023/6/2.
//  Copyright © 2023 WeDevote Bible. All rights reserved.
//

import Foundation
import SwiftUI

class LoginManager {
    static let shared = LoginManager()

    // 记录 ModalView 是否已经展示在屏幕上
    var isShowingLoginRegisterV = false
    
    private init() {}
    
    func showLoginRegisterV() {
        // 避免重复展示
        guard !isShowingLoginRegisterV else {
            return
        }

        // 在 UIWindow 上添加半透明背景，以及 ModalView
        if let window = UIApplication.shared.keyWindow {
            let backgroundView = UIView(frame: window.bounds)
            backgroundView.tag = -9901
            backgroundView.backgroundColor = UIColor.black.withAlphaComponent(0.6)
            window.addSubview(backgroundView)
            
            let g = UITapGestureRecognizer(target: self, action: #selector(onTappBackground))
            backgroundView.addGestureRecognizer(g)
            
            let modalView = UIHostingController(rootView: LoginRegisterAlert())
            modalView.view.tag = -9902
            modalView.view.backgroundColor = UIColor.clear
            window.addSubview(modalView.view)
            
            modalView.view.translatesAutoresizingMaskIntoConstraints = false
            if UIDevice.current.userInterfaceIdiom == .pad {
                NSLayoutConstraint.activate([
                    modalView.view.centerXAnchor.constraint(equalTo: window.centerXAnchor),
                    modalView.view.centerYAnchor.constraint(equalTo: window.centerYAnchor),
                    modalView.view.widthAnchor.constraint(equalToConstant: 300)
                ])
            } else {
                NSLayoutConstraint.activate([
                    modalView.view.leadingAnchor.constraint(equalTo: window.leadingAnchor),
                    modalView.view.trailingAnchor.constraint(equalTo: window.trailingAnchor),
                    modalView.view.bottomAnchor.constraint(equalTo: window.bottomAnchor),
                ])
            }
            
            
            // 记录 modal 展示状态
            isShowingLoginRegisterV = true
        }
    }
    
    @objc func onTappBackground(){
        hideLoginRegisterV()
    }
    
    func hideLoginRegisterV() {
        // 避免意外调用隐藏函数，导致没有 ModalView 展示在屏幕上
        guard isShowingLoginRegisterV else {
            return
        }
        
        // 从 UIWindow 上移除 ModalView
        if let window = UIApplication.shared.keyWindow {
            for subview in window.subviews {
                if [-9901,-9902].contains(subview.tag){
                    subview.removeFromSuperview()
                }
            }
            
            // 记录 modal 展示状态
            isShowingLoginRegisterV = false
        }
    }
}

