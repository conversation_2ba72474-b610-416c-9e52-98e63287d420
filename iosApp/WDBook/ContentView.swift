//
//  ContentView.swift
//  WDBook
//
//  Created by <PERSON> on 5/18/20.
//  Copyright © 2020 WeDevote Bible. All rights reserved.
//

import SwiftUI
import WDReader

struct ContentView: View {
    @State private var selection = 0
    
    var body: some View {
        NavigationView {
            TabView(selection: $selection){
                    VStack{
                        NavigationLink(destination: WDReaderView().navigationBarTitle("阅读器", displayMode: .inline).navigationBarHidden(true)
                        ) {
                            Text("去阅读")
                        }
                    }
                    .tabItem {
                        VStack {
                            Image("first")
                            Text("First")
                        }
                    }
                    .tag(0)
                Text("Second View")
                    .font(.title)
                    .tabItem {
                        VStack {
                            Image("second")
                            Text("Second")
                        }
                    }
                    .tag(1)
            }
        }
    }
}

struct ContentView_Previews: PreviewProvider {
    static var previews: some View {
        ContentView()
    }
}
