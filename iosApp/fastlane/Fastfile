# This file contains the fastlane.tools configuration
# You can find the documentation at https://docs.fastlane.tools
#
# For a list of all available actions, check out
#
#     https://docs.fastlane.tools/actions
#
# For a list of all available plugins, check out
#
#     https://docs.fastlane.tools/plugins/available-plugins
#

# Uncomment the line if you want fastlane to automatically update itself
# update_fastlane

default_platform(:ios)

platform :ios do
    desc "The configuration file for fastlane to run ios unit test"

    lane :test do
        run_tests(workspace: "WDBook.xcworkspace",
                  scheme: "WDBook",
                  device: "iPhone 16",
                  only_testing: "WDBookTests")
    end
end
