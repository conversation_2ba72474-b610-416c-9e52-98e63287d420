source 'https://github.com/CocoaPods/Specs.git'
platform :ios, '14.0'
use_frameworks!

def apppods()
    pod 'shared', :path => '../shared'

    pod 'Toast-Swift', '5.0.1'
    pod 'JGProgressHUD','2.2'
    pod 'SDWebImageSwiftUI','2.2.2'
    pod 'DeviceKit', '~> 5.1'
    pod 'SwiftyStoreKit','0.16.1'
    pod 'ReachabilitySwift','5.0.0'
    pod 'RealReachability', :git => 'https://github.com/OHeroJ/RealReachability.git'
    pod 'GRDB.swift' , '6.0.0'
    pod 'CocoaLumberjack/Swift','3.7.4'
    pod 'SnapKit','5.6.0'
    pod 'AEXML','4.6.1'
    pod 'SwiftyUserDefaults','5.3.0'
    pod 'SSZipArchive','2.4.3'
    pod 'ZIPFoundation', '~> 0.9.15'
    pod "PostHog", "~> 1.1"
    pod "CryptoSwift"
    pod 'Clarity', '3.0.7'
    pod 'FirebaseAnalytics'
    pod 'FirebaseCrashlytics'
    #pod 'FirebaseAnalytics/Core'

    pod 'NavigationRouter',:git => 'https://github.com/KevinZhouRafael/NavigationRouter.git',:tag => '*******'
    pod 'Sentry', :git => 'https://github.com/getsentry/sentry-cocoa.git', :tag => '8.51.1'

    pod 'DTCoreText',:git => 'ssh://******************:/wdapp/wddtcoretext.git', :tag => '********'
    pod 'AWSPinpoint'
    pod 'AWSCognito'
    pod 'wdebug', :git => 'ssh://******************:/wdapp/wdebug.git', :tag => '*******'

    pod 'wdNetwork/iOS', :git => 'ssh://******************:/wdapp/wdnetwork.git', :tag => '1.7.11-blast'
end

target 'WDBook' do
  apppods()
end

target 'wdbooktest' do
  apppods()
end

target 'wdbookinner' do
  apppods()
end

target 'wdbookbeta' do
  apppods()
end

post_install do |installer|
  installer.pods_project.targets.each do |target|
    target.build_configurations.each do |config|
      config.build_settings['SWIFT_VERSION'] = '5.0'
      config.build_settings.delete 'IPHONEOS_DEPLOYMENT_TARGET'
    end
  end
end

