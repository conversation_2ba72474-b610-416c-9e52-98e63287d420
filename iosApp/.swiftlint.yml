disabled_rules: # 执行时排除掉的规则
  - colon
  - comma
  - trailing_newline #空格相关
  - control_statement #空格相关
  - return_arrow_whitespace #空格相关
  - trailing_whitespace #空格相关
  - vertical_whitespace #空格相关
  - comment_spacing #空格相关
  - shorthand_operator
  - cyclomatic_complexity
  - unneeded_break_in_switch
  - opening_brace
  - statement_position
  - void_return
  - mark
  - computed_accessors_order
  - unused_closure_parameter
opt_in_rules: # 一些规则仅仅是可选的
  #- empty_count
  #- missing_docs
excluded:
  - Pods
  - Carthage
force_cast: warning # 隐式
force_try:
  severity: warning # 显式
# 同时有警告和错误等级的规则，可以只设置它的警告等级
# 隐式
line_length: 300
# 可以通过一个数组同时进行隐式设置
type_body_length:
  - 300 # warning
  - 1000 # error
function_body_length:
  - 100
  - 150
# 或者也可以同时进行显式设置
file_length:
  warning: 500
  error: 1200
# 命名规则可以设置最小长度和最大程度的警告/错误
# 此外它们也可以设置排除在外的名字
type_name:
  min_length: 4 # 只是警告
  max_length: # 警告和错误
    warning: 40
    error: 50
  excluded: iPhone # 排除某个名字
  allowed_symbols: "_"
identifier_name:
  allowed_symbols: "_$"
  validates_start_with_lowercase: false
  min_length: 1 # 只有最小长度
  max_length: 100
  warning: 4 # 只有错误
  excluded: # 排除某些名字
    - id
    - URL
    - GlobalAPIKey
reporter: "xcode" # 报告类型 (xcode, json, csv, checkstyle)
