pluginManagement {
    repositories {
        google()
        gradlePluginPortal()
        mavenCentral()
    }
}

dependencyResolutionManagement {
    repositories {
        google()
        mavenCentral()
    }
}

rootProject.name = "wdbook"
include(":app")
include(":shared")

include(":libbase")
project(":libbase").projectDir = File("$rootDir/androidModules/libbase")

include(":liblogger")
project(":liblogger").projectDir = File("$rootDir/androidModules/liblogger")

include(":libwidget")
project(":libwidget").projectDir = File("$rootDir/androidModules/libwidget")

include(":libdialogs")
project(":libdialogs").projectDir = File("$rootDir/androidModules/libdialogs")

include(":libdownloader")
project(":libdownloader").projectDir = File("androidModules/libdownloader")

include(":librefresh")
project(":librefresh").projectDir = File("androidModules/librefresh")

include(":libtools")
project(":libtools").projectDir = File("androidModules/libtools")
