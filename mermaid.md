# Book内容解析和排版引擎架构图

## 1. 模块间类关系图

```mermaid
classDiagram
    %% 书籍解析模块
    class XMLParser {
        -CSSParser cssParser
        -TextHelper textHelper
        -ArrayList~FormatTextRange~ anchors
        -ArrayList~FormatTextRange~ footNoteList
        +parseXml(xmlContent, paragraphIntend, showPageInfo) SpannableStringBuilder
        +checkLinkTag(event, parser, ssb)
        +checkImageTag(parser, ssb)
        +checkBlockTag(event, id)
        +checkFontTag(event, ssb, color)
    }
    
    class CSSParser {
        -HashMap~String,CssElement~ cssMap
        +getInstance() CSSParser
        +loadCSSText(cssText)
        +setTextSpans(ssb, tag, cls, start, end, flags)
        +getTextColor(tag) int
        +getListStyleString(type, index, level) String
    }
    
    class CssElement {
        -ArrayList~CSSAttribute~ attributes
        +getTextSpans() ArrayList~Object~
        +getColor() int
        +getListStyle() int
    }
    
    class DataFormatParser {
        +parseFloat(paramString) HeightUnit
        +parseColor(paramString) int
    }
    
    %% 排版引擎模块
    class TextDrawViewHelper {
        -TextPaint paint
        -CharSequence text
        +measureAll(parentWidth, parentHeight) TextPageList
        -measureUnknownText(textPageList) TextPageList
    }
    
    class TextPageList {
        -TextHelper textHelper
        -TextLineList currentLine
        -float contentWidth
        -float pageHeight
        +addSpanned(spanned, location)
        -addNewTextLine()
        -handleExceedLineMaxWidth()
        +findWord(line, x) Word
    }
    
    class TextLineList {
        -TextPaint paint
        -TextHelper textHelper
        -float lineWidth
        +addSpanned(content, location, wordWidth)
        +adjustWordPosition(maxLineWidth)
        +getLineHeight(originalHeight) float
        +drawBackground(canvas, width, offsetX, offsetY)
    }
    
    class Word {
        -float x
        -float y
        -int location
        -Spanned word
        -float wordWidth
        +draw(canvas, topMargin, height)
        +findLink() WDLinkSpan
    }
    
    class TextHelper {
        -float[] charWidthCache
        +getWordWidth(word) float
        +isSymbolCannotLineEnd(c) boolean
        +isSymbolCannotLineBegin(c) boolean
        +drawWord(canvas, text, x, y, paint, workPaint) float
    }
    
    class TextPageManager {
        -TextPageList textPageList
        -float lineHeight
        +initPageView(viewGroup, pageHeight)
        +getWordPosition(x, linePosition) int
    }
    
    %% 实时渲染模块
    class BookPageAdapter {
        -BookTextControllerHelper currentBookTextHelper
        -HashMap~Int,BookTextViewHolder~ hashMap
        +instantiateItem(container, position) Object
        +checkAndLoadText(position)
        +buildText(pathIndex) BookTextControllerHelper
    }
    
    class BookTextViewHolder {
        -TextPageManager textPageManager
        -FormatContentView formatContentView
        -TextSelectOperateLayout textSelectOperateLayout
        +reloadNoteListData()
        +processOnTouch(event) boolean
        +showTextSelectLayout(lineStart, lineEnd, wordStart, wordEnd)
    }
    
    class FormatContentView {
        -TextPageList textPageList
        -List~NoteEntity~ noteList
        -WordSelectionData wordSelectionData
        +dispatchDraw(canvas)
        +drawBookmarkBg(canvas, noteEntity, previousNoteY) float
        +drawSelection(canvas)
        +getTouchNoteData(touchX, touchY) NoteEntity
    }
    
    class TextLineView {
        -TextLineList line
        +onDraw(canvas)
    }
    
    %% 关系定义
    XMLParser --> CSSParser : 使用
    XMLParser --> TextHelper : 使用
    XMLParser --> DataFormatParser : 调用
    CSSParser --> CssElement : 创建
    CssElement --> DataFormatParser : 使用
    
    TextDrawViewHelper --> TextPageList : 创建
    TextPageList --> TextLineList : 创建
    TextPageList --> TextHelper : 使用
    TextLineList --> Word : 创建
    TextLineList --> TextHelper : 使用
    
    BookPageAdapter --> BookTextViewHolder : 创建
    BookPageAdapter --> BookTextControllerHelper : 管理
    BookTextViewHolder --> TextPageManager : 持有
    BookTextViewHolder --> FormatContentView : 包含
    FormatContentView --> TextPageList : 持有
    FormatContentView --> TextLineView : 包含
    TextLineView --> TextLineList : 持有
    TextPageManager --> TextPageList : 持有
```

## 2. 核心方法调用流程图

### 2.1 XML解析流程

```mermaid
sequenceDiagram
    participant Client
    participant XMLParser
    participant XmlPullParser
    participant CSSParser
    participant DataFormatParser
    participant SpannableStringBuilder
    
    Client->>XMLParser: parseXml(xmlContent)
    XMLParser->>XmlPullParser: setInput(inputStream)
    
    loop 遍历XML节点
        XMLParser->>XmlPullParser: next()
        alt 文本节点
            XMLParser->>XMLParser: processText()
            XMLParser->>SpannableStringBuilder: append(text)
        else 开始标签
            alt <a>标签
                XMLParser->>XMLParser: checkLinkTag()
            else <img>标签  
                XMLParser->>XMLParser: checkImageTag()
            else <font>标签
                XMLParser->>XMLParser: checkFontTag()
                XMLParser->>DataFormatParser: parseColor()
            else 其他标签
                XMLParser->>CSSParser: setTextSpans()
                CSSParser->>CSSParser: cssMap.get(tag)
                CSSParser->>SpannableStringBuilder: setSpan()
            end
        end
    end
    
    XMLParser-->>Client: SpannableStringBuilder
```

### 2.2 排版计算流程

```mermaid
sequenceDiagram
    participant TextDrawViewHelper
    participant TextPageList
    participant TextLineList
    participant TextHelper
    participant Word
    
    TextDrawViewHelper->>TextDrawViewHelper: measureAll()
    TextDrawViewHelper->>TextPageList: new TextPageList()
    TextDrawViewHelper->>TextDrawViewHelper: measureUnknownText()
    
    loop 遍历所有字符
        TextDrawViewHelper->>TextPageList: addSpanned(spanned, location)
        TextPageList->>TextPageList: initSpans()
        TextPageList->>TextHelper: getWordWidth()
        
        alt 需要换行
            TextPageList->>TextPageList: handleExceedLineMaxWidth()
            TextPageList->>TextLineList: adjustWordPosition()
            TextPageList->>TextPageList: addNewTextLine()
            TextPageList->>TextLineList: new TextLineList()
        end
        
        TextPageList->>TextLineList: addSpanned()
        TextLineList->>TextLineList: addWord()
        TextLineList->>Word: new Word()
    end
    
    TextDrawViewHelper-->>Client: TextPageList
```

### 2.3 渲染绘制流程

```mermaid
sequenceDiagram
    participant System
    participant FormatContentView
    participant TextLineList
    participant TextLineView
    participant Word
    participant Canvas
    
    System->>FormatContentView: dispatchDraw(canvas)
    
    FormatContentView->>FormatContentView: drawBackground()
    loop 每一行
        FormatContentView->>TextLineList: drawBackground()
        alt 图片行
            TextLineList->>Canvas: drawBitmap()
        else 块级元素
            TextLineList->>Canvas: drawRect()
        else 表格
            TextLineList->>Canvas: drawLine()
        end
    end
    
    FormatContentView->>FormatContentView: drawBookmarkBg()
    FormatContentView->>FormatContentView: drawHighlightBlock()
    FormatContentView->>Canvas: drawRect()
    
    FormatContentView->>FormatContentView: drawSelection()
    FormatContentView->>Canvas: drawLine()
    
    FormatContentView->>System: super.dispatchDraw()
    System->>TextLineView: onDraw()
    
    loop 每个单词
        TextLineView->>Word: draw()
        alt 特殊元素
            Word->>SpecialElementSpan: drawIt()
        else 普通文本
            Word->>TextHelper: drawWord()
            TextHelper->>Canvas: drawText()
        end
    end
```

### 2.4 用户交互处理流程

```mermaid
sequenceDiagram
    participant User
    participant FormatContentView
    participant BookTextViewHolder
    participant TextPageManager
    participant TextSelectOperateLayout
    
    User->>FormatContentView: onTouch(ACTION_DOWN)
    FormatContentView->>BookTextViewHolder: processOnTouch()
    BookTextViewHolder->>FormatContentView: getDragPosition()
    
    User->>FormatContentView: onTouch(ACTION_MOVE)
    FormatContentView->>BookTextViewHolder: processOnTouch()
    BookTextViewHolder->>FormatContentView: getLinePosition()
    BookTextViewHolder->>TextPageManager: getWordPosition()
    BookTextViewHolder->>FormatContentView: setSelectionPosition()
    
    User->>FormatContentView: onTouch(ACTION_UP)
    FormatContentView->>BookTextViewHolder: processOnTouch()
    BookTextViewHolder->>TextSelectOperateLayout: setSelectedContainerLayoutPosition()
    BookTextViewHolder->>TextSelectOperateLayout: setTextOperateViewShowState(true)
    
    alt 长按
        User->>FormatContentView: onLongClick()
        FormatContentView->>BookTextViewHolder: showTextSelectLayout()
        BookTextViewHolder->>FormatContentView: setSelectionPosition()
        BookTextViewHolder->>TextSelectOperateLayout: setSelectState(true)
    end
```

## 3. 数据流向图

```mermaid
graph LR
    subgraph "输入"
        XML[XML文本]
        Touch[触摸事件]
    end
    
    subgraph "解析层"
        XMLParser[XMLParser]
        CSSParser[CSSParser]
        SSB[SpannableStringBuilder]
    end
    
    subgraph "排版层"
        TextDrawViewHelper[TextDrawViewHelper]
        TextPageList[TextPageList]
        TextLineList[TextLineList]
        Word[Word矩阵]
    end
    
    subgraph "渲染层"
        FormatContentView[FormatContentView]
        TextLineView[TextLineView]
        Canvas[Canvas]
    end
    
    subgraph "交互层"
        BookTextViewHolder[BookTextViewHolder]
        TextSelectOperateLayout[操作栏]
        BookPageAdapter[页面适配器]
    end
    
    XML --> XMLParser
    XMLParser --> CSSParser
    XMLParser --> SSB
    
    SSB --> TextDrawViewHelper
    TextDrawViewHelper --> TextPageList
    TextPageList --> TextLineList
    TextLineList --> Word
    
    Word --> FormatContentView
    FormatContentView --> TextLineView
    TextLineView --> Canvas
    
    Touch --> BookTextViewHolder
    BookTextViewHolder --> FormatContentView
    BookTextViewHolder --> TextSelectOperateLayout
    BookPageAdapter --> BookTextViewHolder
    
    style XML fill:#f9f
    style SSB fill:#bbf
    style Word fill:#bfb
    style Canvas fill:#fbf
```
