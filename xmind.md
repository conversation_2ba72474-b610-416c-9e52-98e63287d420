# Book内容解析和排版引擎架构

## 系统架构
### 模块化架构设计
- XML解析流程
- 排版计算流程  
- 渲染展示流程

## 书籍解析模块
### XMLParser类
#### 主要作用
- 解析XML格式电子书内容
- 转换为SpannableStringBuilder
- 处理HTML标签
- 管理脚注和锚点

#### 核心方法
- parseXml() *(主解析入口)*
- checkLinkTag() *(处理超链接)*
- checkImageTag() *(处理图片)*
- checkBlockTag() *(处理注释块)*
- checkFontTag() *(处理字体)*

### CSSParser类
#### 主要作用
- 解析CSS样式文本
- 管理样式规则映射
- 应用样式到标签

#### 核心方法
- getInstance() *(单例模式)*
- loadCSSText() *(加载CSS)*
- setTextSpans() *(应用样式)*

### CssElement类
#### CSS属性类
- CSSColor → ForegroundColorSpan
- CSSFontSize → RelativeSizeSpan
- CSSDecoration → UnderlineSpan
- CSSTextAlign → AlignSpan
- CSSPadding/CSSBorder → BlockSpan

### DataFormatParser类
- parseFloat() *(解析数值单位)*
- parseColor() *(解析颜色值)*

## 排版引擎模块
### TextDrawViewHelper类
#### 主要作用
- 分割文本为单词单元
- 作为排版流程入口

#### 核心方法
- measureAll() *(测量所有文本)*
- measureUnknownText() *(分割文本)*

### TextPageList类
#### 主要作用
- 管理页面所有文本行
- 处理自动换行
- 处理特殊元素

#### 核心方法
- addSpanned() *(添加文本片段)*
- addNewTextLine() *(创建新行)*
- handleExceedLineMaxWidth() *(处理超宽)*

### TextLineList类
#### 主要作用
- 管理行内Word对象
- 处理文本对齐
- 支持特殊渲染

#### 核心方法
- addSpanned() *(添加文本到行)*
- adjustWordPosition() *(调整位置)*
- getLineHeight() *(计算行高)*
- drawBackground() *(绘制背景)*

### TextHelper类
#### 主要作用
- 计算文本宽度
- 判断换行规则
- 提供绘制功能

#### 核心方法
- getWordWidth() *(计算宽度)*
- isSymbolCannotLineEnd() *(行尾判断)*
- isSymbolCannotLineBegin() *(行首判断)*

### Word类
#### 主要作用
- 最小渲染单元
- 存储位置和样式

#### 核心属性
- x, y *(屏幕坐标)*
- location *(原文位置)*
- word *(文本内容)*
- wordWidth *(显示宽度)*

### TextPageManager类
- initPageView() *(初始化视图)*
- getWordPosition() *(获取位置)*

## 实时渲染模块
### FormatContentView类
#### 主要作用
- 自定义内容视图
- 处理用户交互
- 管理高亮显示

#### 核心方法
- dispatchDraw() *(主绘制)*
- drawBookmarkBg() *(绘制笔记)*
- drawSelection() *(绘制选择)*

### TextLineView类
- onDraw() *(绘制行内容)*

### BookTextViewHolder类
#### 主要作用
- ViewHolder实现
- 管理页面UI
- 处理交互逻辑

#### 核心方法
- reloadNoteListData() *(加载笔记)*
- processOnTouch() *(处理触摸)*
- showTextSelectLayout() *(显示选择)*

### BookPageAdapter类
#### 主要作用
- ViewPager适配器
- 管理页面切换
- 预加载优化

#### 核心方法
- instantiateItem() *(创建页面)*
- checkAndLoadText() *(预加载)*
- buildText() *(构建文本)*

## 样式处理模块
### Span类族
- AlignSpan *(文本对齐)*
- BlockSpan *(块级样式)*
- FootnoteSpan *(脚注图标)*
- PageInfoSpan *(页面信息)*
- WDImageSpan *(图片样式)*
- WDLeadingMarginSpan *(段落缩进)*

## 核心流程
### XML到Spanned转换
1. 接收XML内容
2. 解析XML节点
3. 查询CSS样式
4. 创建Span对象
5. 生成SpannableStringBuilder

### Spanned到TextPage排版
1. 文本分割
2. 计算宽度
3. 判断换行
4. 创建行列表
5. 形成Word矩阵

### TextPage到屏幕渲染
1. dispatchDraw调用
2. 绘制背景
3. 绘制高亮
4. 绘制文本行
5. Word绘制

## 数据流向
- **解析阶段**：XML → SpannableStringBuilder
- **排版阶段**：SpannableStringBuilder → TextPageList → TextLineList → Word
- **渲染阶段**：Word矩阵 → Canvas绘制
- **交互阶段**：触摸坐标 → 行/词索引 → 文本位置 → 操作响应

## 性能优化
### 缓存策略
- 字符宽度缓存
- 单例模式
- 分页渲染
- 三页缓存

### 关键参数
#### 位置参数
- location *(原文位置)*
- x, y *(屏幕坐标)*
- wordStartOffset/wordEndOffset *(笔记范围)*

#### 样式参数
- flags *(Span标志)*
- relativeSizeSpan *(字体大小)*
- styleSpan *(字体样式)*

#### 页面参数
- contentWidth *(内容宽度)*
- pageHeight *(页面高度)*
- lineHeight *(行高)*
- pathIndex *(章节索引)*

---

*注释：*

*1. parseXml()方法：处理XML内容，包括预处理&符号、创建解析器、遍历节点、调用标签处理方法*

*2. addSpanned()方法：核心排版方法，初始化样式、计算宽度、判断换行、处理特殊情况*

*3. dispatchDraw()方法：绘制入口，按顺序绘制背景、笔记高亮、选择状态、子视图*

*4. 三页缓存：保持当前页、上一页、下一页在内存中，优化翻页性能*

*5. 希伯来语支持：TextLineList.adjustWordPosition()中的reverse()方法处理RTL文本*
