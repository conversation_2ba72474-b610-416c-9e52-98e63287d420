#!/usr/bin/env python3
import sys
import os
import xml.etree.ElementTree as ET

ANDROID_NS = 'http://schemas.android.com/apk/res/android'
ATTR_PATH_DATA = f'{{{ANDROID_NS}}}pathData'
ATTR_FILL_TYPE = f'{{{ANDROID_NS}}}fillType'

# Safe max characters per pathData to avoid STRING_TOO_LARGE while
# keeping related subpaths (outer + holes) inside the same <path>.
# You can override via env PATH_SPLIT_MAX_CHARS.
MAX_CHARS = int(os.environ.get('PATH_SPLIT_MAX_CHARS', '8000'))


def split_path_data_if_needed(path_elem):
    """Split a <path> element with long pathData into multiple <path>s by 'M'/'m',
    but keep multiple subpaths aggregated per chunk (<= MAX_CHARS) so that
    holes (inner contours) remain within the same <path>.

    Returns list of replacement elements (one or many). If no split needed,
    returns [path_elem] unchanged.
    """
    d = path_elem.get(ATTR_PATH_DATA)
    if not d:
        return [path_elem]

    # If path is already small enough, keep as-is
    if len(d) <= MAX_CHARS:
        return [path_elem]

    # Split into subpaths by moveto commands 'M' and 'm' (absolute and relative)
    # Keep the moveto letter with each segment
    positions = [i for i, ch in enumerate(d) if ch == 'M' or ch == 'm']
    if not positions:
        # No 'M' found; fallback to hard split by MAX_CHARS
        chunks = [d[i:i+MAX_CHARS] for i in range(0, len(d), MAX_CHARS)]
        return [_clone_path_with_data(path_elem, chunk) for chunk in chunks]

    segments = []
    for idx, start in enumerate(positions):
        end = positions[idx + 1] if idx + 1 < len(positions) else len(d)
        seg = d[start:end]
        if seg:
            segments.append(seg)

    # Aggregate segments into chunks under MAX_CHARS
    replacements = []
    buf = []
    buf_len = 0
    for seg in segments:
        seg_len = len(seg)
        # +1 for a space joiner
        if buf and (buf_len + 1 + seg_len) > MAX_CHARS:
            chunk_data = ' '.join(buf)
            replacements.append(_clone_path_with_data(path_elem, chunk_data))
            buf = [seg]
            buf_len = seg_len
        else:
            if buf:
                buf_len += 1 + seg_len
            else:
                buf_len += seg_len
            buf.append(seg)

    if buf:
        chunk_data = ' '.join(buf)
        replacements.append(_clone_path_with_data(path_elem, chunk_data))

    return replacements


def _clone_path_with_data(template_elem, path_data):
    """Clone a <path> element, replacing only pathData."""
    new_path = ET.Element('path')
    for k, v in template_elem.attrib.items():
        if k == ATTR_PATH_DATA:
            continue
        new_path.set(k, v)
    new_path.set(ATTR_PATH_DATA, path_data)
    # Ensure holes render correctly when multiple subpaths exist
    subpath_count = (path_data.count('M') + path_data.count('m'))
    if subpath_count >= 2:
        new_path.set(ATTR_FILL_TYPE, 'evenOdd')
    return new_path


def process_group(elem):
    # First, recursively process nested groups to normalize children
    for child in list(elem):
        tag = child.tag.split('}', 1)[-1]
        if tag != 'path':
            process_group(child)

    # Second, merge consecutive <path> elements that share identical attributes
    _merge_consecutive_paths(elem)

    # Third, split overly long <path> elements by aggregating subpaths under MAX_CHARS
    i = 0
    while i < len(elem):
        child = elem[i]
        tag = child.tag.split('}', 1)[-1]
        if tag == 'path':
            replacements = split_path_data_if_needed(child)
            if len(replacements) == 1 and replacements[0] is child:
                i += 1
                continue
            elem.remove(child)
            for offset, rep in enumerate(replacements):
                elem.insert(i + offset, rep)
            i += len(replacements)
        else:
            i += 1


def _attrs_without_d(elem):
    return {k: v for k, v in elem.attrib.items() if k != ATTR_PATH_DATA}


def _merge_consecutive_paths(parent):
    """Merge adjacent <path> siblings with identical attributes (except pathData)
    into chunks whose pathData length <= MAX_CHARS, preserving order.
    """
    children = list(parent)
    if not children:
        return

    merged = []
    i = 0
    while i < len(children):
        node = children[i]
        tag = node.tag.split('}', 1)[-1]
        if tag != 'path':
            merged.append(node)
            i += 1
            continue

        # Start a run of paths with the same attributes
        run_attrs = _attrs_without_d(node)
        run_datas = [node.get(ATTR_PATH_DATA) or '']
        j = i + 1
        while j < len(children):
            next_node = children[j]
            if next_node.tag.split('}', 1)[-1] != 'path':
                break
            if _attrs_without_d(next_node) != run_attrs:
                break
            run_datas.append(next_node.get(ATTR_PATH_DATA) or '')
            j += 1

        # Build chunks for this run under MAX_CHARS
        chunk = []
        chunk_len = 0
        for d in run_datas:
            d = d or ''
            add_len = len(d) + (1 if chunk else 0)
            if chunk and (chunk_len + add_len) > MAX_CHARS:
                new_path = ET.Element('path')
                for k, v in run_attrs.items():
                    new_path.set(k, v)
                new_path.set(ATTR_PATH_DATA, ' '.join(chunk))
                d_joined = new_path.get(ATTR_PATH_DATA, '')
                if (d_joined.count('M') + d_joined.count('m')) >= 2:
                    new_path.set(ATTR_FILL_TYPE, 'evenOdd')
                merged.append(new_path)
                chunk = [d]
                chunk_len = len(d)
            else:
                chunk.append(d)
                chunk_len += add_len

        if chunk:
            new_path = ET.Element('path')
            for k, v in run_attrs.items():
                new_path.set(k, v)
            new_path.set(ATTR_PATH_DATA, ' '.join(chunk))
            d_joined = new_path.get(ATTR_PATH_DATA, '')
            if (d_joined.count('M') + d_joined.count('m')) >= 2:
                new_path.set(ATTR_FILL_TYPE, 'evenOdd')
            merged.append(new_path)

        # Advance past the run
        i = j

    # Replace children with merged list
    for node in list(parent):
        parent.remove(node)
    for node in merged:
        parent.append(node)


def process_file(path):
    try:
        ET.register_namespace('android', ANDROID_NS)
        tree = ET.parse(path)
        root = tree.getroot()
        process_group(root)
        # Write back atomically
        tmp_path = path + '.tmp'
        tree.write(tmp_path, encoding='utf-8', xml_declaration=False)
        os.replace(tmp_path, path)
        print(f"Processed: {path}")
    except Exception as e:
        print(f"ERROR processing {path}: {e}")
        raise


def main():
    if len(sys.argv) < 2:
        print("Usage: split_vector_paths.py <file1.xml> [file2.xml ...]")
        sys.exit(1)
    for f in sys.argv[1:]:
        process_file(f)


if __name__ == '__main__':
    main()


