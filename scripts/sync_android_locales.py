#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
同步 Android 香港繁體（values-zh-rHK）與台灣繁體（values-zh-rTW）文案：
- 從 multi_language.csv 讀取：Key, 简体中文, 繁体中文（台湾）, 繁体中文（香港）
- 以 values/strings.xml 的簡體值為基準，在 CSV 中匹配對應繁體，
  並在佔位符安全的情況下自動替換 values-zh-rTW/values-zh-rHK。

使用方式：
  python3 scripts/sync_android_locales.py --csv /abs/path/multi_language.csv \
    --base /abs/path/app/src/main/res/values/strings.xml \
    --tw /abs/path/app/src/main/res/values-zh-rTW/strings.xml \
    --hk /abs/path/app/src/main/res/values-zh-rHK/strings.xml \
    --dry-run  (僅輸出報告，不改檔)

  加上 --apply 可實際寫回檔案（會先生成 .bak 備份）。

注意：
- 僅對單行 <string> 進行安全替換；多行、CDATA 或佔位符數量不一致的條目，
  將僅列入報告，需手動檢查。
"""

from __future__ import annotations

import argparse
import csv
import os
import re
import sys
import shutil
import xml.etree.ElementTree as ET
from typing import Dict, List, Tuple, Optional


CSV_COL_KEY = "Key"
CSV_COL_ZH_CN = "简体中文"
CSV_COL_ZH_TW = "繁体中文（台湾）"
CSV_COL_ZH_HK = "繁体中文（香港）"


def read_csv_mapping(csv_path: str) -> Tuple[Dict[str, str], Dict[str, str]]:
    """讀取 CSV 並構建 CN->TW / CN->HK 映射。
    - 自動加入部分標點規範化的鍵（如 ':' -> '：'）。
    - 忽略空白、完全空值。
    """
    def norm_cn(s: str) -> str:
        if s is None:
            return ""
        s2 = s.strip()
        # 生成兩種常見冒號變體
        return s2

    cn_to_tw: Dict[str, str] = {}
    cn_to_hk: Dict[str, str] = {}

    with open(csv_path, "r", encoding="utf-8-sig", newline="") as f:
        reader = csv.DictReader(f)
        required = {CSV_COL_KEY, CSV_COL_ZH_CN, CSV_COL_ZH_TW, CSV_COL_ZH_HK}
        if not required.issubset(reader.fieldnames or []):
            raise RuntimeError(
                f"CSV 表頭需包含: {', '.join(sorted(required))}; 當前: {reader.fieldnames}"
            )

        for row in reader:
            cn = (row.get(CSV_COL_ZH_CN) or "").strip()
            tw = (row.get(CSV_COL_ZH_TW) or "").strip()
            hk = (row.get(CSV_COL_ZH_HK) or "").strip()
            if not cn:
                continue
            key_main = norm_cn(cn)
            if tw:
                cn_to_tw.setdefault(key_main, tw)
                # 同時加入 ':' -> '：' 變體
                if ":" in key_main and "：" not in key_main:
                    cn_to_tw.setdefault(key_main.replace(":", "："), tw)
            if hk:
                cn_to_hk.setdefault(key_main, hk)
                if ":" in key_main and "：" not in key_main:
                    cn_to_hk.setdefault(key_main.replace(":", "："), hk)

    return cn_to_tw, cn_to_hk


def parse_android_strings(xml_path: str) -> List[Tuple[str, str]]:
    """解析 Android strings.xml，返回 [(name, value)] 順序列表。
    僅讀取 <string name="...">value</string> 條目，忽略其它節點。
    """
    tree = ET.parse(xml_path)
    root = tree.getroot()
    results: List[Tuple[str, str]] = []
    for child in root:
        if child.tag != "string":
            continue
        name = child.attrib.get("name")
        if not name:
            continue
        # 合併子文本（考慮 tail/CDATA 在 ET 中也成為 text）
        value = child.text or ""
        results.append((name, value))
    return results


PLACEHOLDER_ANDROID_RE = re.compile(r"%(?:\d+\$)?[ds]")
PLACEHOLDER_CANDIDATE_RE = re.compile(r"%(@|lld|(?:\d+\$)?[ds])")


def extract_placeholders_android(s: str) -> List[str]:
    return PLACEHOLDER_ANDROID_RE.findall(s)


def extract_placeholders_candidate(s: str) -> List[str]:
    # 抽取候選中的 iOS/一般佔位符，依序
    tokens: List[str] = []
    for m in PLACEHOLDER_CANDIDATE_RE.finditer(s):
        tokens.append(m.group(0))
    return tokens


def replace_candidate_placeholders(candidate: str, base_placeholders: List[str]) -> Optional[str]:
    """將候選文字中的佔位符，按順序替換為 base 的佔位符。
    - 候選佔位符（%lld/%@/%d/%s/%1$s 等）數量需與 base 相等。
    - 僅在數量一致時返回新字串；否則返回 None。
    """
    cand_tokens = extract_placeholders_candidate(candidate)
    if len(cand_tokens) != len(base_placeholders):
        if len(base_placeholders) == 0 and len(cand_tokens) == 0:
            return candidate
        return None

    # 構造一個依序替換的函式
    idx = 0

    def repl(m: re.Match) -> str:
        nonlocal idx
        token = base_placeholders[idx]
        idx += 1
        return token

    new_text = PLACEHOLDER_CANDIDATE_RE.sub(repl, candidate)
    return new_text


def preserve_quote_style(base_value: str, new_value: str) -> str:
    """若 base 使用了 \" 風格，則對新值中的雙引號做同樣轉義。"""
    if '\\"' in base_value:
        return new_value.replace('"', '\\"')
    return new_value


def is_single_line_string_block(xml_text: str, name: str) -> bool:
    # 快速檢查是否為單行 <string name="name">...</string>
    # 僅用於是否嘗試正則替換
    pattern = rf"<string\s+name=\"{re.escape(name)}\"[^>]*>[^\n]*?</string>"
    return re.search(pattern, xml_text) is not None


def replace_string_value_in_xml(xml_text: str, name: str, new_value: str) -> Tuple[str, bool]:
    """在原始 xml 文字中，替換指定 name 的單行 <string> 內容。
    僅處理單行項；替換成功返回 (新文本, True)，否則 (原文本, False)。
    注意：不處理多行/CDATA，以免破壞縮排與格式。
    """
    # 僅匹配單行
    pattern = re.compile(rf"(<string\s+name=\"{re.escape(name)}\"[^>]*>)([^\n]*?)(</string>)")
    m = pattern.search(xml_text)
    if not m:
        return xml_text, False

    prefix, old_value, suffix = m.group(1), m.group(2), m.group(3)
    # 保持與原內容相同的 XML 寫法（不額外轉義，僅替換文本）
    new_block = f"{prefix}{new_value}{suffix}"
    new_text = xml_text[: m.start()] + new_block + xml_text[m.end() :]
    return new_text, True


def load_text(path: str) -> str:
    with open(path, "r", encoding="utf-8") as f:
        return f.read()


def save_text(path: str, content: str) -> None:
    with open(path, "w", encoding="utf-8") as f:
        f.write(content)


def process_locale(
    base_xml: str,
    target_xml: str,
    cn_to_locale: Dict[str, str],
    dry_run: bool,
    report_lines: List[str],
) -> None:
    base_items = parse_android_strings(base_xml)
    target_text = load_text(target_xml)

    total = 0
    replaced = 0
    skipped_multiline_or_nohit = 0
    placeholder_mismatch = 0

    for name, base_value in base_items:
        total += 1
        cn_key_variants = [base_value.strip()]
        if ":" in base_value and "：" not in base_value:
            cn_key_variants.append(base_value.replace(":", "：").strip())

        locale_value: Optional[str] = None
        for k in cn_key_variants:
            if not k:
                continue
            if k in cn_to_locale:
                locale_value = cn_to_locale[k]
                break

        if not locale_value:
            skipped_multiline_or_nohit += 1
            report_lines.append(f"[MISS] {name} | 基準: {base_value}")
            continue

        # 佔位符對齊
        base_ph = extract_placeholders_android(base_value)
        new_text = replace_candidate_placeholders(locale_value, base_ph)
        if new_text is None:
            placeholder_mismatch += 1
            report_lines.append(
                f"[SKIP-PLACEHOLDER] {name} | CN={base_value} | CSV={locale_value} | base_ph={base_ph}"
            )
            continue

        new_text = preserve_quote_style(base_value, new_text)

        # 單行替換
        if not is_single_line_string_block(target_text, name):
            skipped_multiline_or_nohit += 1
            report_lines.append(f"[SKIP-MULTILINE] {name} | 僅單行自動替換，請手動核對")
            continue

        # 僅在值不同時替換
        # 取目標檔現值（單行截取）
        m = re.search(rf"<string\s+name=\"{re.escape(name)}\"[^>]*>([^\n]*?)</string>", target_text)
        old_value = m.group(1) if m else ""
        if old_value == new_text:
            continue

        if not dry_run:
            target_text, ok = replace_string_value_in_xml(target_text, name, new_text)
            if ok:
                replaced += 1
                report_lines.append(f"[UPDATE] {name} | {old_value} -> {new_text}")
            else:
                skipped_multiline_or_nohit += 1
                report_lines.append(f"[SKIP] {name} | 正則替換未命中，請手動處理")
        else:
            # dry-run 僅輸出預覽
            replaced += 1
            report_lines.append(f"[DRY] {name} | {old_value} -> {new_text}")

    report_lines.append(
        f"統計: total={total}, changed={replaced}, skipped={skipped_multiline_or_nohit}, placeholder_mismatch={placeholder_mismatch}"
    )

    if not dry_run:
        # 寫回文件
        save_text(target_xml, target_text)


def main() -> None:
    parser = argparse.ArgumentParser(description="Sync Android zh-TW/zh-HK strings from CSV")
    parser.add_argument("--csv", required=True, help="multi_language.csv 的絕對路徑")
    parser.add_argument("--base", required=True, help="values/strings.xml 的絕對路徑")
    parser.add_argument("--tw", required=False, help="values-zh-rTW/strings.xml 的絕對路徑")
    parser.add_argument("--hk", required=False, help="values-zh-rHK/strings.xml 的絕對路徑")
    parser.add_argument("--apply", action="store_true", help="實際寫回變更（會先備份 .bak）")
    parser.add_argument("--dry-run", action="store_true", help="僅輸出預覽，不改檔")
    parser.add_argument("--report", required=False, default=None, help="報告輸出路徑（.md）")
    args = parser.parse_args()

    if not args.dry_run and not args.apply:
        # 默認為 dry-run
        args.dry_run = True

    cn_to_tw, cn_to_hk = read_csv_mapping(args.csv)

    report_lines: List[str] = []

    if args.tw:
        if args.apply and os.path.exists(args.tw):
            shutil.copyfile(args.tw, args.tw + ".bak")
        report_lines.append("# TW 同步\n")
        process_locale(args.base, args.tw, cn_to_tw, args.dry_run, report_lines)
        report_lines.append("")

    if args.hk:
        if args.apply and os.path.exists(args.hk):
            shutil.copyfile(args.hk, args.hk + ".bak")
        report_lines.append("# HK 同步\n")
        process_locale(args.base, args.hk, cn_to_hk, args.dry_run, report_lines)
        report_lines.append("")

    report_text = "\n".join(report_lines)

    if args.report:
        os.makedirs(os.path.dirname(args.report), exist_ok=True)
        save_text(args.report, report_text)
    else:
        print(report_text)


if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"[ERROR] {e}", file=sys.stderr)
        sys.exit(1)


