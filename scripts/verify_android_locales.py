#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
校驗 Android 多語系資源：
- 比對 base(簡體) vs zh-TW/zh-HK 的 keys 覆蓋率
- 檢查占位符一致性（%d/%s/%1$s 等）
- 檢查是否殘留 iOS 佔位符（%@、%lld）

使用：
  python3 scripts/verify_android_locales.py \
    --base app/src/main/res/values/strings.xml \
    --tw app/src/main/res/values-zh-rTW/strings.xml \
    --hk app/src/main/res/values-zh-rHK/strings.xml \
    --out scripts/verify_report.md
"""

from __future__ import annotations

import argparse
import os
import re
import xml.etree.ElementTree as ET
from typing import Dict, List, Tuple


PLACEHOLDER_ANDROID_RE = re.compile(r"%(?:\d+\$)?[ds]")
PLACEHOLDER_IOS_RE = re.compile(r"%@|%lld")


def parse_strings(path: str) -> Dict[str, str]:
    tree = ET.parse(path)
    root = tree.getroot()
    out: Dict[str, str] = {}
    for child in root:
        if child.tag != "string":
            continue
        name = child.attrib.get("name")
        if not name:
            continue
        out[name] = child.text or ""
    return out


def extract_android_placeholders(s: str) -> List[str]:
    return PLACEHOLDER_ANDROID_RE.findall(s)


def verify(base_path: str, tw_path: str, hk_path: str) -> str:
    base = parse_strings(base_path)
    tw = parse_strings(tw_path)
    hk = parse_strings(hk_path)

    lines: List[str] = []

    base_keys = set(base)
    tw_keys = set(tw)
    hk_keys = set(hk)

    miss_tw = sorted(base_keys - tw_keys)
    miss_hk = sorted(base_keys - hk_keys)
    extra_tw = sorted(tw_keys - base_keys)
    extra_hk = sorted(hk_keys - base_keys)

    lines.append("# Key 覆蓋率檢查\n")
    lines.append(f"base keys: {len(base_keys)} | tw: {len(tw_keys)} | hk: {len(hk_keys)}\n")
    lines.append(f"缺失於 TW: {len(miss_tw)} | 缺失於 HK: {len(miss_hk)}")
    if miss_tw:
        lines.append("- 缺失於 TW（前 50）: " + ", ".join(miss_tw[:50]))
    if miss_hk:
        lines.append("- 缺失於 HK（前 50）: " + ", ".join(miss_hk[:50]))
    if extra_tw:
        lines.append(f"- TW 额外 keys: {len(extra_tw)}（前 50）: " + ", ".join(extra_tw[:50]))
    if extra_hk:
        lines.append(f"- HK 额外 keys: {len(extra_hk)}（前 50）: " + ", ".join(extra_hk[:50]))
    lines.append("")

    # 佔位符一致性
    ph_mismatch_tw: List[str] = []
    ph_mismatch_hk: List[str] = []
    ios_residue_tw: List[str] = []
    ios_residue_hk: List[str] = []

    common_tw = sorted(base_keys & tw_keys)
    common_hk = sorted(base_keys & hk_keys)

    for k in common_tw:
        b = base[k]
        t = tw[k]
        if len(extract_android_placeholders(b)) != len(extract_android_placeholders(t)):
            ph_mismatch_tw.append(k)
        if PLACEHOLDER_IOS_RE.search(t):
            ios_residue_tw.append(k)

    for k in common_hk:
        b = base[k]
        h = hk[k]
        if len(extract_android_placeholders(b)) != len(extract_android_placeholders(h)):
            ph_mismatch_hk.append(k)
        if PLACEHOLDER_IOS_RE.search(h):
            ios_residue_hk.append(k)

    lines.append("# 占位符一致性\n")
    lines.append(f"TW 佔位符不一致: {len(ph_mismatch_tw)}")
    if ph_mismatch_tw:
        lines.append("- 前 50: " + ", ".join(ph_mismatch_tw[:50]))
    lines.append(f"HK 佔位符不一致: {len(ph_mismatch_hk)}")
    if ph_mismatch_hk:
        lines.append("- 前 50: " + ", ".join(ph_mismatch_hk[:50]))
    lines.append("")

    lines.append("# iOS 佔位符殘留檢查 (%@/%lld)\n")
    lines.append(f"TW: {len(ios_residue_tw)} | HK: {len(ios_residue_hk)}")
    if ios_residue_tw:
        lines.append("- TW 前 50: " + ", ".join(ios_residue_tw[:50]))
    if ios_residue_hk:
        lines.append("- HK 前 50: " + ", ".join(ios_residue_hk[:50]))

    return "\n".join(lines) + "\n"


def main() -> None:
    ap = argparse.ArgumentParser()
    ap.add_argument("--base", required=True)
    ap.add_argument("--tw", required=True)
    ap.add_argument("--hk", required=True)
    ap.add_argument("--out", required=True)
    args = ap.parse_args()

    report = verify(args.base, args.tw, args.hk)
    os.makedirs(os.path.dirname(args.out), exist_ok=True)
    with open(args.out, "w", encoding="utf-8") as f:
        f.write(report)


if __name__ == "__main__":
    main()


