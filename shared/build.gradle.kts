plugins {
    kotlin("multiplatform")
    // kotlin("native.cocoapods")
    kotlin("plugin.serialization")
    id("com.android.library")
    id("com.squareup.sqldelight")
    id("io.realm.kotlin")
}

fun parsePodfile(podName: String): Triple<String, String, String>? {
    val podfile = file("../iosApp/Podfile") // Replace with your Podfile path
    val lines = podfile.readLines()

    lines.forEach { line ->
        val trimmedLine = line.trim()
        if (trimmedLine.startsWith("pod")) {
            val currentPodName = trimmedLine.substringAfter("'").substringBefore("'").trim()
            if (currentPodName == podName) {
                val podGit = trimmedLine.substringAfter(":git => '").substringBefore("'").trim()
                val podTag = trimmedLine.substringAfter(":tag => '").substringBefore("'").trim()
                return Triple(podName, podGit, podTag)
            }
        }
    }

    return null
}

kotlin {
    androidTarget()
    // 注释掉iOS相关配置
    /*
    iosX64()
    iosArm64()
    iosSimulatorArm64()

    cocoapods {
        summary = "Some description for the Shared Module"
        homepage = "Link to the Shared Module homepage"
        version = "1.0"
        ios.deploymentTarget = "14.0"
        podfile = project.file("../iosApp/Podfile")

        val CAN_PREVIEW = false
        framework {
            baseName = "shared"
            if (!CAN_PREVIEW) {
                isStatic = true
            }
        }
    }
    */

    sourceSets {
        val commonMain by getting {
            dependencies {
                implementation(Dependencies.Kotlin.COROUTINES_CORE)
                implementation(Dependencies.Kotlin.datetime)
                implementation(Dependencies.Ktor.CORE)
                implementation(Dependencies.Ktor.AUTH)
                implementation(Dependencies.Ktor.negotiation)
                implementation(Dependencies.Ktor.json)
                implementation(Dependencies.Ktor.LOGGING)
                implementation(Dependencies.Kotlin.SERIALIZATION_JSON)
                implementation(Dependencies.SqlDelight.RUNTIME)
                implementation(Dependencies.NAPIER)
                implementation(Dependencies.MULTIPLATFORM_SETTINGS)
                implementation(Dependencies.Realm.base)
            }
        }
        val commonTest by getting {
            dependencies {
                implementation(kotlin("test"))
            }
        }
        val androidMain by getting {
            dependencies {
                implementation(Dependencies.MATERIAL)
                implementation(Dependencies.Ktor.OKHTTP)
                implementation(Dependencies.Ktor.ANDROID)
                implementation(Dependencies.SqlDelight.ANDROID_DRIVER)
                implementation(Dependencies.GSON)
            }
        }
        val androidUnitTest by getting {
            dependencies {
                implementation(kotlin("test-junit"))
                implementation("junit:junit:4.13.2")
                implementation(Dependencies.ROBOLECTRIC)
                implementation(Dependencies.MULTIPLATFORM_SETTINGS_TEST)
                implementation(Dependencies.AndroidXTest.CORE)
                implementation(Dependencies.AndroidXTest.JUNIT)
                implementation(Dependencies.AndroidXTest.RUNNER)
            }
        }
        // 注释掉iOS相关的sourceSet配置
        /*
        val iosX64Main by getting
        val iosArm64Main by getting
        val iosSimulatorArm64Main by getting
        val iosMain by creating {
            dependsOn(commonMain)
            iosX64Main.dependsOn(this)
            iosArm64Main.dependsOn(this)
            iosSimulatorArm64Main.dependsOn(this)
            dependencies {
                implementation(Dependencies.SqlDelight.NATIVE_DRIVER)
            }
        }
        val iosX64Test by getting
        val iosArm64Test by getting
        val iosSimulatorArm64Test by getting
        val iosTest by creating {
            dependsOn(commonTest)
            iosX64Test.dependsOn(this)
            iosArm64Test.dependsOn(this)
            iosSimulatorArm64Test.dependsOn(this)
        }
        */
    }

    jvmToolchain {
        languageVersion.set(JavaLanguageVersion.of(17))
    }
}

android {
    compileSdk = Versions.COMPILE_SDK
    sourceSets["main"].apply {
        manifest.srcFile("src/androidMain/AndroidManifest.xml")
        jniLibs.srcDirs("src/androidMain/libs")
    }

    defaultConfig {
        minSdk = Versions.MIN_SDK
    }

    lint {
        targetSdk = Versions.TARGET_SDK
    }

    namespace = "com.wedevote.wdbook.shared"

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }
}

sqldelight {
    database("UserDb") {
        packageName = "com.wedevote.wdbook.db.user"
        sourceFolders = listOf("sqlUser")
        verifyMigrations = true
        schemaOutputDirectory = file("build/dbs/userDb")
    }

    database("AppDb") {
        packageName = "com.wedevote.wdbook.db.app"
        sourceFolders = listOf("sqlApp")
        verifyMigrations = true
        schemaOutputDirectory = file("build/dbs/appDb")
    }

    database("BibleDb") {
        packageName = "com.wedevote.wdbook.db.bible"
        sourceFolders = listOf("sqlBible")
    }

    database("APIServerDb") {
        packageName = "com.wedevote.wdbook.db.server"
        sourceFolders = listOf("sqlServer")
    }
}
