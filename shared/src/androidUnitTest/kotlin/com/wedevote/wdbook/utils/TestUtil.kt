package com.wedevote.wdbook.utils

import android.content.Context
import androidx.test.core.app.ApplicationProvider
import com.russhwolf.settings.MapSettings
import com.wedevote.wdbook.SDKConfig
import com.wedevote.wdbook.constants.AppEnvironment
import com.wedevote.wdbook.db.DatabaseDriverFactory
import org.robolectric.shadows.ShadowBuild

/***
 * Created by <PERSON> on 2021/4/26 14:55
 *
 * @description
 */
internal actual fun getSDKConfig(): SDKConfig {
    val mockContext = ApplicationProvider.getApplicationContext<Context>()
    ShadowBuild.setManufacturer("MANUFACTURER")
    ShadowBuild.setModel("MODEL")

    return SDKConfig(
        platform = Platform(mockContext),
        env = AppEnvironment.TEST,
        settings = MapSettings(),
        databaseDriverFactory = DatabaseDriverFactory(context = mockContext, inMemory = true),
    )
}
