package com.wedevote.wdbook

import org.junit.Assert.assertTrue
import org.junit.Test
import java.net.URI
import kotlin.test.assertEquals

class AndroidGreetingTest {

    @Test
    fun testExample() {
        assertTrue("Check Android is mentioned", "Android123".contains("Android"))
    }

    @Test
    fun testReturnForEachIndex() {
        val list = arrayListOf(1, 2, 3, 4, 5, 6)
        run loop@{
            list.forEachIndexed { index, value ->
                println("$index -> $value")
                if (index == 2) {
                    println("Will return for each indexed")
                    return@loop
                }
                println("continue for $index")
            }
        }
        println("The list is $list")
    }

    @Test
    fun testExtractUrlPath() {
        assertEquals("product/detail/12345", extractUrlPath("wdbooktest://product/detail/12345"))
        assertEquals("product/detail/12345", extractUrlPath("https://wdbook.com/product/detail/12345"))
    }

    private fun extractUrlPath(link: String): String {
        val linkUri = URI(link)
        return when {
            linkUri.scheme == "wdbooktest" -> {
                linkUri.schemeSpecificPart.trimStart('/')
            }
            linkUri.host == "wdbook.com" -> {
                linkUri.path.trimStart('/')
            }
            else -> {
                ""
            }
        }
    }
}
