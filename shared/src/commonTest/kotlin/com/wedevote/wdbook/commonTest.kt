package com.wedevote.wdbook

import com.wedevote.wdbook.constants.LanguageMode
import com.wedevote.wdbook.entity.store.StoreCategoryEntity
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertTrue

class CommonGreetingTest {

    val config = Config("file_id_1")
    private val apiPath: String
        get() {
            return "api_path:${config.fileId}"
        }

    @Test
    fun testExample() {
        assertTrue("Hello 123".contains("Hello"), "Check 'Hello' is mentioned")
        assertEquals("/v1/downloadurl/file1", ApiPath.downloadUrl("file1"), "The api path should correct")
        assertEquals("/v1/downloadurl/file2", ApiPath.downloadUrl("file2"), "The api path should correct")
    }

    @Test
    fun testGetVal() {
        assertEquals("api_path:file_id_1", apiPath, "First time")
        config.fileId = "file_id_2"
        assertEquals("api_path:file_id_2", apiPath, "Second time")
    }

    @Test
    fun testJoin() {
        val productCategoryList = arrayListOf<StoreCategoryEntity>()
        productCategoryList.add(
            StoreCategoryEntity().apply {
                categoryId = 1
            }
        )
        productCategoryList.add(
            StoreCategoryEntity().apply {
                categoryId = 2
            }
        )
        productCategoryList.add(
            StoreCategoryEntity().apply {
                categoryId = 3
            }
        )
        val categoryIds = productCategoryList.map { it.categoryId }.joinToString(",")
        assertEquals("1,2,3", categoryIds, "The categoryIds should match")
    }

    @Test
    fun testMarket() {
        val enMarket = LanguageMode.contentOf("en")
        assertEquals(LanguageMode.English, enMarket, "The valueOf should be correct")
    }
}

object ApiPath {
    val downloadUrl = { fileId: String -> "/v1/downloadurl/$fileId" }
}

class Config(var fileId: String)
