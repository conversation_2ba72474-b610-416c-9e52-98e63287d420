package com.wedevote.wdbook.network

import com.wedevote.wdbook.entity.base.ApiResponse
import com.wedevote.wdbook.utils.JsonUtility
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertNotNull

/***
 * Created by <PERSON> on 2021/6/3 15:22
 *
 * @description
 */
class ApiResponseTest {
    @Test
    fun testEmptyResponse() {
        val responseJson = """
{
  "errno": 200,
  "msg": "success"
}"""
        val response = JsonUtility.decodeFromString<ApiResponse<EmptyResponse>>(responseJson)
        assertNotNull(response)
        assertEquals(200, response.errno, "The errno should be 200")
        assertEquals("success", response.msg, "The message should be success")
    }
}
