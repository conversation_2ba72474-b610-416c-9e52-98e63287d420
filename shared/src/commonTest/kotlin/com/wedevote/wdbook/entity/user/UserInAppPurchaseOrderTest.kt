package com.wedevote.wdbook.entity.user

import kotlin.test.Test
import kotlin.test.assertEquals

/***
 * Created by <PERSON> on 2021/6/7 16:58
 *
 * @description
 */
class UserInAppPurchaseOrderTest {
    @Test
    fun testApply() {
        val userInAppPurchaseOrder = UserInAppPurchaseOrder().apply {
            inAppProductId = "__product_id__"
            appleTransactionId = "__ios_transaction_id__"
        }
        assertEquals("__product_id__", userInAppPurchaseOrder.inAppProductId)
        assertEquals("__ios_transaction_id__", userInAppPurchaseOrder.appleTransactionId)
    }
}
