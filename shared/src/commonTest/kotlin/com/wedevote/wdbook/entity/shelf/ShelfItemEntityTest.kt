package com.wedevote.wdbook.entity.shelf

import com.wedevote.wdbook.utils.JsonUtility
import kotlin.test.Test

/***
 * Created by <PERSON> on 2021/6/18 14:38
 *
 * @description
 */
class ShelfItemEntityTest {
//    @Test
//    fun testEncodeShelfItemEntity() {
//        val shelfBookItemEntity = ShelfBookItemEntity().apply {
//            itemId = "book"
//            resourceId = "book_res"
//            resourceDownloadInfo = ResourceDownloadInfo().apply {
//                fileId = "file_id"
//                resourceId = "book_res"
//                downloadInfo = DownloadInfo().apply {
//                    fileType = "book_type"
//                    actualFileName = "actual_book_name"
//                }
//            }
//        }
//        assertEquals(
//            """{"itemId":"book","resourceId":"book_res","resourceTypeId":"","resourceName":"","cover":"","status":"0","syncKey":0,"clientArchiveId":"","archiveId":"","progress":0,"lastVisitTime":0,"createTime":0,"lastUpdateTime":0,"resourceDownloadInfo":{"resourceId":"book_res","fileId":"file_id","fileSize":0,"needUpgradeApp":false,"downloadStatus":"EMPTY","downloadInfo":{"fileId":"","fileType":"book_type","fileDir":"","actualFileName":"actual_book_name","fileName":"","md5":"","downloadSize":0,"fileSize":0,"fileFormatVersion":0}}}""",
//            JsonUtility.encodeToString(shelfBookItemEntity),
//        )
//
//        val shelfArchiveItemEntity = ShelfArchiveItemEntity().apply {
//            clientArchiveId = "client_archive_id"
//            archiveName = "archive_name"
//            archiveCoverList = listOf()
//        }
//        assertEquals(
//            """{"clientArchiveId":"client_archive_id","archiveName":"archive_name","archiveCoverList":[],"status":"0","syncKey":0,"createTime":0,"lastUpdateTime":0}""",
//            JsonUtility.encodeToString(shelfArchiveItemEntity),
//        )
//
// //        val shelfItemList = arrayListOf(shelfBookItemEntity, shelfArchiveItemEntity)
// //        assertEquals("""[{"type":"com.wedevote.wdbook.entity.shelf.ShelfBookItemEntity","itemId":"book","resourceId":"book_res","resourceTypeId":"","resourceName":"","cover":"","status":"0","syncKey":0,"clientArchiveId":"","archiveId":"","progress":0,"lastVisitTime":0,"createTime":0,"lastUpdateTime":0,"resourceDownloadInfo":{"fileId":"file_id","resourceId":"book_res","fileType":"book_type","actualFileName":"actual_book_name","actualFileSize":0,"downloadStatus":"wait","fileFormatVersion":0,"fileName":"","md5":"","fileDir":"","downloadSize":0,"fileSize":0}},{"type":"com.wedevote.wdbook.entity.shelf.ShelfArchiveItemEntity","clientArchiveId":"client_archive_id","archiveId":"archive_id","archiveName":"archive_name","archiveCoverList":[],"status":"0","syncKey":0,"createTime":0,"lastUpdateTime":0}]""", JsonUtility.encodeToString(shelfItemList))
//    }

    @Test
    fun testDecodeShelfItemEntity() {
        val jsonString =
            """{"itemId":"book","test":"test","resourceId":"book_res","resourceTypeId":"","resourceName":"","cover":"","status":"0","syncKey":0,"clientArchiveId":"","archiveId":"","progress":0,"lastVisitTime":0,"createTime":0,"lastUpdateTime":0,"resourceDownloadInfo":{"resourceId":"book_res","fileId":"file_id","fileSize":0,"needUpgradeApp":false,"downloadStatus":"EMPTY","downloadInfo":{"fileId":"","fileType":"book_type","fileDir":"","actualFileName":"actual_book_name","fileName":"","md5":"","downloadSize":0,"fileSize":0,"fileFormatVersion":0}}}"""
        val shelfBookItemEntity = JsonUtility.decodeFromString<ShelfBookItemEntity>(jsonString)
        println(shelfBookItemEntity)
    }
}
