package com.wedevote.wdbook.entity.user

import com.wedevote.wdbook.constants.TransactionType
import com.wedevote.wdbook.utils.JsonUtility
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertNotNull

/***
 * Created by <PERSON> on 2021/5/31 16:52
 *
 * @description
 */
class WalletTransactionEntityTest {
    @Test
    fun testJsonMarshal() {
        val walletTransactionEntity = WalletTransactionEntity(100, TransactionType.RECHARGE, 2.0f, 3)
        val entityJson = JsonUtility.encodeToString(walletTransactionEntity)
        val expectJson = "{\"transactionId\":100,\"transactionType\":\"1\",\"amount\":2.0,\"lastUpdateTime\":3}"
        assertEquals(expectJson, entityJson, "The json should be equal")
    }

    @Test
    fun testJsonUnmarshal() {
        val rechargeJson = "{\"transactionId\":1,\"transactionType\":\"1\",\"amount\":2.0,\"lastUpdateTime\":3}"
        val rechargeEntity = JsonUtility.decodeFromString<WalletTransactionEntity>(rechargeJson)
        assertEquals(TransactionType.RECHARGE, rechargeEntity.transactionType, "The transaction type should be recharge")

        val deductJson = "{\"transactionId\":1,\"transactionType\":\"2\",\"amount\":2.0,\"lastUpdateTime\":3}"
        val deductEntity = JsonUtility.decodeFromString<WalletTransactionEntity>(deductJson)
        assertEquals(TransactionType.DEDUCT, deductEntity.transactionType, "The transaction type should be deduct")

        var exception: Exception? = null
        try {
            val unknownJson = "{\"transactionId\":1,\"transactionType\":\"3\",\"amount\":2.0,\"lastUpdateTime\":3}"
            JsonUtility.decodeFromString<WalletTransactionEntity>(unknownJson)
        } catch (e: Exception) {
            exception = e
        }
        assertNotNull(exception, "Unmarshal should failed")
    }
}
