package com.wedevote.wdbook.db

/***
 * Created by <PERSON> on 2021/6/22 13:50
 *
 * @description
 */
// class AppDbHelperTest : BaseTest() {
//    private lateinit var appDbHelper: AppDbHelper

//    @BeforeTest
//    fun setup() {
//        val sdkConfig = getSDKConfig()
//        val dbCache = DbCache(sdkConfig)
//        appDbHelper = dbCache.appDbHelper
//    }

//    @Test
//    fun testSaveStoreCategoryData() {
//        val storeCatalogEntity = StoreCategoryEntity().apply {
//            categoryId = 1
//            categoryName = "Test"
//        }
//
//        appDbHelper.saveStoreCategoryData(storeCatalogEntity)
//        val list = appDbHelper.getLocalStoreCategoryEntityList()
//        assertNotNull(list)
//        assertEquals(1, list.count())
//        assertEquals("Test", list[0].categoryName)
//    }
// }
